## For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
#
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx1024m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
#
# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
#Sat Mar 07 22:50:10 ICT 2020

version=1.22.0-SNAPSHOT

org.gradle.jvmargs=-Xmx4096M -Dkotlin.daemon.jvm.options\="-Xmx4096M" -XX\:+HeapDumpOnOutOfMemoryError -Dfile.encoding\=UTF-8
org.gradle.daemon=true
org.gradle.daemon.idletimeout=600
org.gradle.configureondemand=true
org.gradle.parallel=true
org.gradle.caching=true

android.useAndroidX=true
android.enableJetifier=true

kapt.incremental.apt=true
kapt.include.compile.classpath=false

artifactUrl=https://artifactory.infra.devops.my.id/artifactory/styletheory-android-gradle
artifactUsername=admin
artifactPassword=Infinitewardrobe123

cacheServerUrl=https://gradle.styletheory.xyz/cache/
cacheServerUsername=android_ro
cacheServerPassword=f059460c95da0913724
android.nonTransitiveRClass=false
android.nonFinalResIds=false