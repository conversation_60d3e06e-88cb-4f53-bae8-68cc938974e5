This is your Style Theory Ops Outbound Application Android
========================================================

# Starting Up

Please setup macro in your Android Studio. The macro should do `Reformat Code`, `Optimise Imports`, and `Save All`. Step by step to setup macro:
	1. Edit -> Macros -> Start macro recording
	2. Do actions shortcuts: `Reformat Code` `Optimise Imports` `Save All`. You can see the shortcut keys in Code menu.
	3. Edit -> Macros -> Stop macro recording -> Enter you macro name -> Ok
	4. Preferences -> Keymap -> Macros -> Select your macro -> Double click -> Add keyboard shortcut -> Ctrl/Cmd + s -> save
By setup your macro, everytime you save your project, it will do several actions in one time. You're welcome! :)


# Running

## Checklist for staging upload
- Merge release with develop.
- Update your release notes for staging build type (app/src/staging/release_notes.txt). Update with your developed features checklist. Therefore, the QA will know what features they need to test.
- Push release branch
- <PERSON> will test, build, and distribute release branch to staging environment.

## Checklist for production upload
- Update your release notes for release build type (app/src/release/release_notes.txt). Update with release notes that you got from your PM!
- Check the version that has passed QA test in Staging project in Jenkins.
- Go to production projects in Jenkins.
- Build with parameters.
- Enter your QA test pass version.
- It will be build and distributed to Fabric - Beta. 
