apply plugin: 'jacoco'

jacoco {
    toolVersion = "0.8.10"  // Use latest stable version
}

android.testOptions {
    unitTests {
        returnDefaultValues = true
        includeAndroidResources = true
        all {
            testLogging {
                outputs.upToDateWhen { false }
                events "failed", "standardError"
                showCauses true
                showExceptions true
            }
        }
    }
}

tasks.withType(Test) {
    jacoco.includeNoLocationClasses = true
    outputs.upToDateWhen { false }
    jacoco{
        includeNoLocationClasses = true
//        destinationFile = file("${project.rootDir}/jacoco/${System.currentTimeMillis()}.exec")
    }
}

task jacocoTestReport(type: JacocoReport) {

    reports {
        xml.required = false  // Use required instead of enabled
        html.required = false // Use required instead of enabled
        csv.required = false
    }

    // List of files which must be excluded from the coverage analysis
    def fileFilter = [
            '**/R.class',
            '**/R$*.class',
            '**/BuildConfig.*',
            '**/Manifest*.*',
            '**/*Test*.*',
            'android/**/*.*',
            'androidx/**/*.*',
            '**/*Builder.*',
            '**/*_MembersInjector.class', //Dagger2 generated code
            '**/*_MembersInjector*.*', //Dagger2 generated code
            '**/*_*Factory*.*', //Dagger2 generated code
            '**/*Component*.*', //Dagger2 generated code
            '**/*Module*.*', //Dagger2 generated code
            '**/*Parcelable*.*', // Parcel generated code
            '**/BR.class',
            '**/databinding/*.*',

            '**/*Activity*',
            '**/*Fragment*',
            '**/*Application*',
            '**/*Adapter*',
            '**/*Dialog.class',
            '**/*Listener*',
            '**/*Event*',
            '**/general/**',
            '**/injection/*',
            '**/util/**',
            '**/networking/**',
            '**/view/**',
            '**/resources/**',
            '**/model/**',

            'build/**',
            '**/*.png',
            '*.iml',
            '**/*generated*',
            '**.xml'
    ]
    def javaClasses = fileTree(dir: "$project.buildDir/intermediates/classes/debug", excludes: fileFilter)
    def kotlinClasses = fileTree(dir: "$project.buildDir/tmp/kotlin-classes/debug", excludes: fileFilter)
    def javaSrc = "$project.projectDir/src/main/java"
    def kotlinSrc = "$project.projectDir/src/main/kotlin"

    sourceDirectories.from = [javaSrc, kotlinSrc]
    classDirectories.from = [javaClasses, kotlinClasses]
    executionData.from = fileTree(dir: project.rootDir, includes: [
            '**/*.exec', '**/*coverage.ec'
    ])
}
