include ':app'

def REMOTE_CACHE_ENABLED = System.getenv("REMOTE_CACHE_ENABLED")?.toBoolean() ?: false
buildCache {
    local {
        enabled = !REMOTE_CACHE_ENABLED
    }
    remote(HttpBuildCache) {
        enabled = REMOTE_CACHE_ENABLED
        push = REMOTE_CACHE_ENABLED
        url = "$cacheServerUrl"
        credentials {
            username = "$cacheServerUsername"
            password = "$cacheServerPassword"
        }
        allowUntrustedServer = true
    }
}