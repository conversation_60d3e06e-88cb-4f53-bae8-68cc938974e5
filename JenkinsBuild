def notifyGChat(String buildStatus = 'STARTED') {
    buildStatus = buildStatus ?: 'STARTED'
    def msg = "*${generateBuildName()} [$env.BUILD_NUMBER]* : *${buildStatus}*"
    generateChatCard(buildStatus, msg)
    executeNotifyGoogleChat()
}

def announceBuild() {
    def variant = "debug"
    if (BUILD_TYPE == "Production") {
        variant = "release"
    } else if (BUILD_TYPE == "Staging") {
        variant = "staging"
    } else if (BUILD_TYPE == "Debug") {
        variant = "debug"
    }
    def releaseNotes = sh(script: "cat app/src/${variant}/release_notes.txt", returnStdout: true).trim()
    def msg = "*Change Log :*\n```${releaseNotes}```"

    contentData = """'{
        "text": "$msg"
    }'"""

    executeNotifyGoogleChat()
}

static def getVariantType(String buildType = 'Debug') {
    def type = ''
    if (buildType == 'Debug') {
        type = 'Develop'
    } else if (buildType == 'Staging') {
        type = 'Staging'
    } else if (buildType == 'Production') {
        type = 'Release'
    }
    return type
}

static def getAssembleType(String buildType = 'Debug') {
    def type = ''
    if (buildType == 'Debug') {
        type = 'assembleDebug'
    } else if (buildType == 'Staging') {
        type = 'assembleStaging'
    } else if (buildType == 'Production') {
        type = 'assembleRelease'
    }
    return type
}

static def getUploadDistribution(String buildType = 'Debug') {
    def type = ''
    if (buildType == 'Debug') {
        type = 'appDistributionUploadDebug'
    } else if (buildType == 'Staging') {
        type = 'appDistributionUploadStaging'
    } else if (buildType == 'Production') {
        type = 'appDistributionUploadRelease'
    }
    return type
}

static def getCredential(String buildType = 'Debug') {
    def type = ''
    if (buildType == 'Debug') {
        type = 'styletheory-dev-firebase-admin'
    } else if (buildType == 'Staging') {
        type = 'styletheory-staging-firebase-admin'
    } else if (buildType == 'Production') {
        type = 'styletheory-release-firebase-admin'
    }
    return type
}

node('android') {
    notifyGChat('STARTED')
    try {
        def home = sh(script: "echo $HOME", returnStdout: true).trim()
        def SDKPath = "$home/Android/SDK"
        stage("Preparing SDK") {
            // Check SDK Downloaded
            def isSDKDownloaded = sh(script: "test -e sdk-tools-linux-4333796.zip && echo true || echo false", returnStdout: true).trim()
            if (isSDKDownloaded == "false") {
                // Download SDK
                sh "wget 'https://dl.google.com/android/repository/sdk-tools-linux-4333796.zip'"
            }
            // Check if SDK is Extracted
            def isExtracted = sh(script: "test -e $SDKPath/tools && echo true || echo false", returnStdout: true).trim()
            if (isExtracted == "false") {
                sh "mkdir -p $SDKPath"
                //Unzip SDK
                sh "unzip sdk-tools-linux-4333796.zip -d $SDKPath"
            }

            // Install SDK Tools
            sh "yes | $SDKPath/tools/bin/sdkmanager 'build-tools;30.0.3' 'platform-tools' 'platforms;android-30'"

            sh "ls $SDKPath/licenses"
            // See installed And Available SDK
            sh "$SDKPath/tools/bin/sdkmanager --list"
            // Accept All SDK Licences
            sh "yes | sudo $SDKPath/tools/bin/sdkmanager --licenses"
        }

        stage('Checkout') {
            if ((env.SELECTED_BRANCH) && SELECTED_BRANCH != 'null') {
                def selectedBranch = SELECTED_BRANCH.substring(7)
                git branch: selectedBranch, url: '*****************:styletheory/styletheory-ops-outbound.git'
            } else {
                env.SELECTED_BRANCH = sh(
                        script: "git branch | grep \\* | cut -d ' ' -f2",
                        returnStdout: true
                ).trim()
                git branch: env.SELECTED_BRANCH, url: '*****************:styletheory/styletheory-ops-outbound.git'
                echo "SCM Checkout $SELECTED_BRANCH"
            }
            sh './gradlew --stop'

            // Remove Existing local properties
            sh 'rm local.properties ||:'
            // Write sdk.dir Path into local properties file
            sh "echo 'sdk.dir=$SDKPath' >> local.properties"
        }

        stage('Build APK') {
            sh "./gradlew clean ${getAssembleType(BUILD_TYPE)}"
        }

        stage('Upload to App Distribution') {
            withCredentials([file(credentialsId: "${getCredential(BUILD_TYPE)}", variable: 'CREDENTIAL')]) {
                sh "export GOOGLE_APPLICATION_CREDENTIALS=$CREDENTIAL && ./gradlew ${getUploadDistribution(BUILD_TYPE)}"
            }
        }
    } catch (ex) {
        notifyGChat('FAILED')
        throw ex
    } finally {
        sh './gradlew --stop'
    }
    notifyGChat('SUCCESS')
    announceBuild()
    build job: 'android_outbound_test_runner', parameters: [
            string(name: 'SELECTED_BRANCH', value: SELECTED_BRANCH.toString())
    ], wait: false
}

def generateBuildName() {
    return "OUTBOUND APP BUILD ${getVariantType(BUILD_TYPE).toUpperCase()}"
}

def generateChatCard(String buildStatus, String msg) {
    def iconBuild = ""
    def versionBlock = ""
    if(buildStatus == 'STARTED') {
        iconBuild = "https:\\/\\/cdn2.iconfinder.com\\/data\\/icons\\/navigation-set-arrows-part-two\\/32\\/Double_Loop-512.png"
    } else if(buildStatus == 'SUCCESS') {
        def versionName = sh(script: "./gradlew -q printVersionName", returnStdout: true).trim()
        versionBlock = """'{
            "keyValue": {
                "topLabel": "Version",
                "content": "$versionName"
            }
        },'"""
        iconBuild = "https:\\/\\/cdn3.iconfinder.com\\/data\\/icons\\/flat-actions-icons-9\\/792\\/Tick_Mark_Dark-512.png"
    } else {
        iconBuild = "https:\\/\\/cdn0.iconfinder.com\\/data\\/icons\\/shift-free\\/32\\/Error-512.png"
    }

    contentData = """'{
            "text": "$msg",
            "cards": [
                {
                    "header": {
                        "title": "${generateBuildName()}",
                        "subtitle": "$SELECTED_BRANCH",
                        "imageUrl": "https:\\/\\/cdn1.iconfinder.com\\/data\\/icons\\/android-ui\\/154\\/android-settings-512.png",
                        "imageStyle": "IMAGE"
                    },
                    "sections": [
                    {
                        "widgets": [
                            {
                                "keyValue": {
                                    "topLabel": "Status",
                                    "content": "$buildStatus",
                                    "contentMultiline": "true",
                                    "iconUrl": "$iconBuild"
                                    }
                            }
                        ],
                    },
                    {
                        "widgets": [
                            '$versionBlock'
                            {
                                "keyValue": {
                                    "topLabel": "Build Number",
                                    "content": "#$env.BUILD_NUMBER",
                                    "contentMultiline": "true",
                                    "button": {
                                        "textButton": {
                                           "text": "VIEW",
                                           "onClick": {
                                               "openLink": {
                                                    "url": "$env.BUILD_URL"
                                               }
                                           }
                                        }
                                    }
                                }
                            },
                            {
                                "keyValue": {
                                    "topLabel": "Pipeline",
                                    "content": "$env.JOB_NAME"
                                }
                            }
                        ]
                    }]
                }]
        }'"""
}

def executeNotifyGoogleChat() {
    script {
        sh """curl -i \\
        -H \"Accept: application/json\" \\
        -H \"Content-Type:application/json\" \\
        -X POST --data $contentData \\
        \"https://chat.googleapis.com/v1/spaces/AAAAPzYyS7U/messages?threadKey=BUILD_TYPE&key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=hj8Rb99-Et4EaTBrAdDDmaSUwWuweGRES6qrRLi8KMY%3D\""""
    }
}