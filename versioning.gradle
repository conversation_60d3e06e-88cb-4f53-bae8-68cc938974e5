ext {
    /**
     * Builds an Android version code from the version of the project.
     * This is designed to handle the -SNAPSHOT and -RC format.
     *
     * I.e. during development the version ends with -SNAPSHOT. As the code stabilizes and release nears
     * one or many Release Candidates are tagged. These all end with "-RC1", "-RC2" etc.
     * And the final release is without any suffix.
     * @return
     */
    buildVersionCode = { ->
        //The rules is as follows:
        //-SNAPSHOT counts as 0
        //-RC* counts as the RC number, i.e. 1 to 98
        //final release counts as 99.
        //Thus you can only have 98 Release Candidates, which ought to be enough for everyone

        def candidate = "99"
        def (major, minor, patch) = version.toLowerCase().replaceAll('-', '').tokenize('.')
        if (patch.endsWith("snapshot")) {
            candidate = "0"
            patch = patch.replaceAll("[^0-9]", "")
        } else {
            def rc
            (patch, rc) = patch.tokenize("rc")
            if (rc) {
                candidate = rc
            }
        }

        (major, minor, patch, candidate) = [major, minor, patch, candidate].collect { it.toInteger() }

        (major * 10000000) + (minor * 10000) + (patch * 100) + candidate;
    }

    buildVersionName = { ->
        def build = Integer.valueOf(System.env.BUILD_NUMBER ?: 0)
        def buildType = System.env.BUILD_TYPE
        def buildString = (buildType == 'Debug' || buildType == 'Staging') ? " [" + build.toString() + "]" : ""
        version + buildString
    }
}