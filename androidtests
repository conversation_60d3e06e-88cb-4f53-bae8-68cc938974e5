#!/bin/bash

# Export Android Home
export ANDROID_HOME=~/ && export PATH=${PATH}:${ANDROID_HOME}/tools && export PATH=${PATH}:${ANDROID_HOME}/tools/bin && export PATH=${PATH}:${ANDROID_HOME}/platform-tools

#Start the emulator
$ANDROID_HOME/tools/emulator -avd Nexus_5X_API_24 -netdelay none -netspeed full -port 5554 &
EMULATOR_PID=$!

# Wait for Android to finish booting
WAIT_CMD="$ANDROID_HOME/platform-tools/adb wait-for-device shell getprop init.svc.bootanim"
until $WAIT_CMD | grep -m 1 stopped; do
  echo "Waiting..."
  sleep 1
done

# Unlock the Lock Screen
# $ANDROID_HOME/platform-tools/adb shell input keyevent 82

# Clear and capture logcat
$ANDROID_HOME/platform-tools/adb logcat -c
$ANDROID_HOME/platform-tools/adb logcat > build/logcat.log &
LOGCAT_PID=$!

# Run the Android tests
# ./gradlew connectedDebugAndroidTest -i

# For testing specific class
# ./gradlew connectedAndroidTest -P android.testInstrumentationRunnerArguments.class=co.styletheory.ops.outbound.android.SignInUITest

# Copy Jacoco Coverage Report
rm -r report/coverage-test ||:
mkdir -p report/coverage-test
cp -R app/build/reports/jacoco/jacocoTestReport/* report/coverage-test/

# Stop the background processes
kill $LOGCAT_PID
kill $EMULATOR_PID
$ANDROID_HOME/platform-tools/adb -s emulator-5554 emu kill
