{"project_info": {"project_number": "484735962422", "project_id": "st-core-development", "storage_bucket": "st-core-development.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:484735962422:android:ddfac831fb3b44b5503797", "android_client_info": {"package_name": "co.styletheory.ops.android.debug"}}, "oauth_client": [{"client_id": "484735962422-dimqovujo70n7bh6aob67n7eggropcuk.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDSHmYOCWvRy06ys8vdbHFt05EJ894cbF4"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "484735962422-dimqovujo70n7bh6aob67n7eggropcuk.apps.googleusercontent.com", "client_type": 3}, {"client_id": "484735962422-i5676sc51436otqlvn0ho3399eujlv1c.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonateDev"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:484735962422:android:6e22c99bcaadb479503797", "android_client_info": {"package_name": "co.styletheory.outbound.ops.android.debug"}}, "oauth_client": [{"client_id": "484735962422-dimqovujo70n7bh6aob67n7eggropcuk.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDSHmYOCWvRy06ys8vdbHFt05EJ894cbF4"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "484735962422-dimqovujo70n7bh6aob67n7eggropcuk.apps.googleusercontent.com", "client_type": 3}, {"client_id": "484735962422-i5676sc51436otqlvn0ho3399eujlv1c.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonateDev"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:484735962422:android:5a4d78848ca48554503797", "android_client_info": {"package_name": "com.styletheory.android.dev"}}, "oauth_client": [{"client_id": "484735962422-dimqovujo70n7bh6aob67n7eggropcuk.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDSHmYOCWvRy06ys8vdbHFt05EJ894cbF4"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "484735962422-dimqovujo70n7bh6aob67n7eggropcuk.apps.googleusercontent.com", "client_type": 3}, {"client_id": "484735962422-i5676sc51436otqlvn0ho3399eujlv1c.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonateDev"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:484735962422:android:ac451c111ea9d1c3503797", "android_client_info": {"package_name": "com.styletheory.impersonator.dev"}}, "oauth_client": [{"client_id": "484735962422-dimqovujo70n7bh6aob67n7eggropcuk.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDSHmYOCWvRy06ys8vdbHFt05EJ894cbF4"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "484735962422-dimqovujo70n7bh6aob67n7eggropcuk.apps.googleusercontent.com", "client_type": 3}, {"client_id": "484735962422-i5676sc51436otqlvn0ho3399eujlv1c.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonateDev"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:484735962422:android:1a1734cb0e63aebd503797", "android_client_info": {"package_name": "ops.styletheory.warehouse.debug"}}, "oauth_client": [{"client_id": "484735962422-dimqovujo70n7bh6aob67n7eggropcuk.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDSHmYOCWvRy06ys8vdbHFt05EJ894cbF4"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "484735962422-dimqovujo70n7bh6aob67n7eggropcuk.apps.googleusercontent.com", "client_type": 3}, {"client_id": "484735962422-i5676sc51436otqlvn0ho3399eujlv1c.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonateDev"}}]}}}], "configuration_version": "1"}