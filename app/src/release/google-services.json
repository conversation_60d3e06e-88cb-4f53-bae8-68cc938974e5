{"project_info": {"project_number": "541782983871", "firebase_url": "https://styletheory-4998f.firebaseio.com", "project_id": "styletheory-4998f", "storage_bucket": "styletheory-4998f.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:541782983871:android:994f2168b40efc07bf320a", "android_client_info": {"package_name": "co.styletheory.ops.android"}}, "oauth_client": [{"client_id": "541782983871-28i4vkqoeqg2l1sr5vb53rmpcdafthk4.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAob7jUt7NyD4COLy8P0cx-cSiU2ZeE7fE"}, {"current_key": "AIzaSyA9AC1eqkZpEJn7-A6UvfebnNkB0RV6raw"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "541782983871-28i4vkqoeqg2l1sr5vb53rmpcdafthk4.apps.googleusercontent.com", "client_type": 3}, {"client_id": "541782983871-49lgpekmvo7qnpbok2cm6lb8m11i3eis.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonate"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:541782983871:android:1f99cf106ebe9f28bf320a", "android_client_info": {"package_name": "co.styletheory.outbound.ops.android"}}, "oauth_client": [{"client_id": "541782983871-28i4vkqoeqg2l1sr5vb53rmpcdafthk4.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAob7jUt7NyD4COLy8P0cx-cSiU2ZeE7fE"}, {"current_key": "AIzaSyA9AC1eqkZpEJn7-A6UvfebnNkB0RV6raw"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "541782983871-28i4vkqoeqg2l1sr5vb53rmpcdafthk4.apps.googleusercontent.com", "client_type": 3}, {"client_id": "541782983871-49lgpekmvo7qnpbok2cm6lb8m11i3eis.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonate"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:541782983871:android:230bd03614cbccb6bf320a", "android_client_info": {"package_name": "com.styletheory.android"}}, "oauth_client": [{"client_id": "541782983871-hvacvecfav519olglaj0svtao018qd7d.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.styletheory.android", "certificate_hash": "f5170f5e020a437a5875c6cc784280a91d03dd58"}}, {"client_id": "541782983871-28i4vkqoeqg2l1sr5vb53rmpcdafthk4.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAob7jUt7NyD4COLy8P0cx-cSiU2ZeE7fE"}, {"current_key": "AIzaSyA9AC1eqkZpEJn7-A6UvfebnNkB0RV6raw"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "541782983871-28i4vkqoeqg2l1sr5vb53rmpcdafthk4.apps.googleusercontent.com", "client_type": 3}, {"client_id": "541782983871-49lgpekmvo7qnpbok2cm6lb8m11i3eis.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonate"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:541782983871:android:edf3236213c0ac44bf320a", "android_client_info": {"package_name": "com.styletheory.impersonator"}}, "oauth_client": [{"client_id": "541782983871-28i4vkqoeqg2l1sr5vb53rmpcdafthk4.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAob7jUt7NyD4COLy8P0cx-cSiU2ZeE7fE"}, {"current_key": "AIzaSyA9AC1eqkZpEJn7-A6UvfebnNkB0RV6raw"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "541782983871-28i4vkqoeqg2l1sr5vb53rmpcdafthk4.apps.googleusercontent.com", "client_type": 3}, {"client_id": "541782983871-49lgpekmvo7qnpbok2cm6lb8m11i3eis.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonate"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:541782983871:android:3ee164421b6c76b8bf320a", "android_client_info": {"package_name": "ops.styletheory.warehouse"}}, "oauth_client": [{"client_id": "541782983871-28i4vkqoeqg2l1sr5vb53rmpcdafthk4.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAob7jUt7NyD4COLy8P0cx-cSiU2ZeE7fE"}, {"current_key": "AIzaSyA9AC1eqkZpEJn7-A6UvfebnNkB0RV6raw"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "541782983871-28i4vkqoeqg2l1sr5vb53rmpcdafthk4.apps.googleusercontent.com", "client_type": 3}, {"client_id": "541782983871-49lgpekmvo7qnpbok2cm6lb8m11i3eis.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonate"}}]}}}], "configuration_version": "1"}