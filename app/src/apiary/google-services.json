{"project_info": {"project_number": "459589762883", "firebase_url": "https://styletheory-1254.firebaseio.com", "project_id": "styletheory-1254", "storage_bucket": "styletheory-1254.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:459589762883:android:c5ccd9c94449ff7ff7b9f6", "android_client_info": {"package_name": "co.styletheory.ops.android"}}, "oauth_client": [{"client_id": "459589762883-oa94pkte7nhrmupfaol1403371q7afl3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBr5DP_QsauBr07eBAjL3ZFR4tVOPGHOPQ"}, {"current_key": "AIzaSyDQ1iRymhopvYPVE_mCOSdf7HQ-BIxHyhg"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "459589762883-sbmojbu9971fovuo9sgcp7opad4h65o7.apps.googleusercontent.com", "client_type": 3}, {"client_id": "459589762883-6sfd1p29d960lvpa4i3i44qq5gvhm42c.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonate"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:459589762883:android:446c1edb9ba4b16ff7b9f6", "android_client_info": {"package_name": "co.styletheory.ops.android.apiary"}}, "oauth_client": [{"client_id": "459589762883-oa94pkte7nhrmupfaol1403371q7afl3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBr5DP_QsauBr07eBAjL3ZFR4tVOPGHOPQ"}, {"current_key": "AIzaSyDQ1iRymhopvYPVE_mCOSdf7HQ-BIxHyhg"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "459589762883-sbmojbu9971fovuo9sgcp7opad4h65o7.apps.googleusercontent.com", "client_type": 3}, {"client_id": "459589762883-6sfd1p29d960lvpa4i3i44qq5gvhm42c.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonate"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:459589762883:android:76c135903aa456e8f7b9f6", "android_client_info": {"package_name": "co.styletheory.ops.android.debug"}}, "oauth_client": [{"client_id": "459589762883-oa94pkte7nhrmupfaol1403371q7afl3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBr5DP_QsauBr07eBAjL3ZFR4tVOPGHOPQ"}, {"current_key": "AIzaSyDQ1iRymhopvYPVE_mCOSdf7HQ-BIxHyhg"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "459589762883-sbmojbu9971fovuo9sgcp7opad4h65o7.apps.googleusercontent.com", "client_type": 3}, {"client_id": "459589762883-6sfd1p29d960lvpa4i3i44qq5gvhm42c.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonate"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:459589762883:android:6f7fc6117593f29bf7b9f6", "android_client_info": {"package_name": "co.styletheory.ops.android.staging"}}, "oauth_client": [{"client_id": "459589762883-oa94pkte7nhrmupfaol1403371q7afl3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBr5DP_QsauBr07eBAjL3ZFR4tVOPGHOPQ"}, {"current_key": "AIzaSyDQ1iRymhopvYPVE_mCOSdf7HQ-BIxHyhg"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "459589762883-sbmojbu9971fovuo9sgcp7opad4h65o7.apps.googleusercontent.com", "client_type": 3}, {"client_id": "459589762883-6sfd1p29d960lvpa4i3i44qq5gvhm42c.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonate"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:459589762883:android:57504c649fee5c8df7b9f6", "android_client_info": {"package_name": "co.styletheory.outbound.ops.android"}}, "oauth_client": [{"client_id": "459589762883-oa94pkte7nhrmupfaol1403371q7afl3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBr5DP_QsauBr07eBAjL3ZFR4tVOPGHOPQ"}, {"current_key": "AIzaSyDQ1iRymhopvYPVE_mCOSdf7HQ-BIxHyhg"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "459589762883-sbmojbu9971fovuo9sgcp7opad4h65o7.apps.googleusercontent.com", "client_type": 3}, {"client_id": "459589762883-6sfd1p29d960lvpa4i3i44qq5gvhm42c.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonate"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:459589762883:android:1a078f94f95ca78df7b9f6", "android_client_info": {"package_name": "co.styletheory.outbound.ops.android.apiary"}}, "oauth_client": [{"client_id": "459589762883-oa94pkte7nhrmupfaol1403371q7afl3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBr5DP_QsauBr07eBAjL3ZFR4tVOPGHOPQ"}, {"current_key": "AIzaSyDQ1iRymhopvYPVE_mCOSdf7HQ-BIxHyhg"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "459589762883-sbmojbu9971fovuo9sgcp7opad4h65o7.apps.googleusercontent.com", "client_type": 3}, {"client_id": "459589762883-6sfd1p29d960lvpa4i3i44qq5gvhm42c.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonate"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:459589762883:android:6a3d4a743c188c98f7b9f6", "android_client_info": {"package_name": "co.styletheory.outbound.ops.android.debug"}}, "oauth_client": [{"client_id": "459589762883-oa94pkte7nhrmupfaol1403371q7afl3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBr5DP_QsauBr07eBAjL3ZFR4tVOPGHOPQ"}, {"current_key": "AIzaSyDQ1iRymhopvYPVE_mCOSdf7HQ-BIxHyhg"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "459589762883-sbmojbu9971fovuo9sgcp7opad4h65o7.apps.googleusercontent.com", "client_type": 3}, {"client_id": "459589762883-6sfd1p29d960lvpa4i3i44qq5gvhm42c.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonate"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:459589762883:android:10ded92abc874fbbf7b9f6", "android_client_info": {"package_name": "co.styletheory.outbound.ops.android.staging"}}, "oauth_client": [{"client_id": "459589762883-oa94pkte7nhrmupfaol1403371q7afl3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBr5DP_QsauBr07eBAjL3ZFR4tVOPGHOPQ"}, {"current_key": "AIzaSyDQ1iRymhopvYPVE_mCOSdf7HQ-BIxHyhg"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "459589762883-sbmojbu9971fovuo9sgcp7opad4h65o7.apps.googleusercontent.com", "client_type": 3}, {"client_id": "459589762883-6sfd1p29d960lvpa4i3i44qq5gvhm42c.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonate"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:459589762883:android:4aa76ccda5f827e5", "android_client_info": {"package_name": "com.styletheory.android"}}, "oauth_client": [{"client_id": "459589762883-26f89vhvun7giv6bd8odnjri66e0hhd1.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.styletheory.android", "certificate_hash": "f5170f5e020a437a5875c6cc784280a91d03dd58"}}, {"client_id": "459589762883-oa94pkte7nhrmupfaol1403371q7afl3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBr5DP_QsauBr07eBAjL3ZFR4tVOPGHOPQ"}, {"current_key": "AIzaSyDQ1iRymhopvYPVE_mCOSdf7HQ-BIxHyhg"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "459589762883-sbmojbu9971fovuo9sgcp7opad4h65o7.apps.googleusercontent.com", "client_type": 3}, {"client_id": "459589762883-6sfd1p29d960lvpa4i3i44qq5gvhm42c.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonate"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:459589762883:android:a73d58e84bf68c75", "android_client_info": {"package_name": "com.styletheory.android.apiary"}}, "oauth_client": [{"client_id": "459589762883-qgm0eo1u8vus5ud8vaasp8oou3cp6ikh.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.styletheory.android.apiary", "certificate_hash": "1f3c17a5b9bc2433916b69b8dd5f4a0ba6bd4cf1"}}, {"client_id": "459589762883-5uucl583n5lt4khufkemj46ud6bgq9i0.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.styletheory.android.apiary", "certificate_hash": "038ead24991d025e398b4e3d7cdc65961284c321"}}, {"client_id": "459589762883-oa94pkte7nhrmupfaol1403371q7afl3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBr5DP_QsauBr07eBAjL3ZFR4tVOPGHOPQ"}, {"current_key": "AIzaSyDQ1iRymhopvYPVE_mCOSdf7HQ-BIxHyhg"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "459589762883-sbmojbu9971fovuo9sgcp7opad4h65o7.apps.googleusercontent.com", "client_type": 3}, {"client_id": "459589762883-6sfd1p29d960lvpa4i3i44qq5gvhm42c.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonate"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:459589762883:android:2e15ac13f2807c88f7b9f6", "android_client_info": {"package_name": "com.styletheory.impersonator"}}, "oauth_client": [{"client_id": "459589762883-oa94pkte7nhrmupfaol1403371q7afl3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBr5DP_QsauBr07eBAjL3ZFR4tVOPGHOPQ"}, {"current_key": "AIzaSyDQ1iRymhopvYPVE_mCOSdf7HQ-BIxHyhg"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "459589762883-sbmojbu9971fovuo9sgcp7opad4h65o7.apps.googleusercontent.com", "client_type": 3}, {"client_id": "459589762883-6sfd1p29d960lvpa4i3i44qq5gvhm42c.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.StyleTheoryTechnologies.StyleTheoryImpersonate"}}]}}}], "configuration_version": "1"}