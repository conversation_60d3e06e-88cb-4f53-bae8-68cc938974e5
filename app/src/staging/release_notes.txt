OPS Outbound 1.21.1
Changelog:
- remove dependency library from jcenter() repo

OPS Outbound 1.21.0
Changelog:
- add Box confirmation Received By Warehouse at Packed tab
- update artifact

OPS Outbound 1.20.0
Changelog:
- remove Launch Darkly
- get feature flag value using local json
- add enum Piing Logistic Provider

OPS Outbound 1.19.0
Changelog:
- Remove SonarCloud code inspection
- Remove Short zone - Long Zone categorization info on Picking Tab - Batch Detail
- Add location info item field section in picking tab
- Create sort section in picking tab

OPS Outbound 1.18.1
Changelog:
- Fix crash on action button to accuracy swap in tab QC

OPS Outbound 1.18.0
Changelog:
- Add new tab section for new logistic provider (Style Theory SG) in packed view page
- Update latest library and sdk also using latest kotlin
- Moved out Batch step model companion object
- Update coverage test

OPS Outbound 1.17.1
Changelog:
- Showing Zones in Outbound Apps for HK Apparel

OPS Outbound 1.17.0
Changelog:
- region selection based on user role
- when version app detected, user should be logged out
- show menu tab HK as SG region
- After the QC was complete, the item status should be `PhotoQADone` for HK region
- after sign-out will show region based on staff user is assigned on
- Show SG-APPAREL default when there is no region staff role

OPS Outbound 1.16.1
Changelog:
- Update latest Kotlin version
- Change new Firebase Dev Environment
- Change new Firebase Staging Environment
- Update jacoco test config to fix sonar scanner
- Create unit test for ProcessDialogViewModel

OPS Outbound 1.16.0
Changelog:
- Sonarscanner update with JAVA15
- Update latest version of stylenetwork
- Fix error permission storage for Android 10