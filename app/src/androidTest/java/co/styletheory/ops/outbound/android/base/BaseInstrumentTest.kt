package co.styletheory.ops.outbound.android.base

import android.app.Activity
import co.styletheory.android.testkit.uiTest.base.BaseUITest
import co.styletheory.ops.outbound.android.StyletheoryOpsApplication
import co.styletheory.ops.outbound.android.injection.DaggerTestAppComponent
import co.styletheory.ops.outbound.android.injection.TestAppComponent
import co.styletheory.ops.outbound.android.injection.TestAppModule
import co.styletheory.ops.outbound.android.injection.module.ApiModule
import co.styletheory.ops.outbound.android.util.AppConstant


/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 10 November 2017.
 * Description
 *
 * <EMAIL>
 */
abstract class BaseInstrumentTest<T : Activity>(clazz: Class<T>) : BaseUITest<T>(clazz) {

    var testComponent: TestAppComponent? = null

    override fun setup() {
        super.setup()
        setupInjector()
        AppConstant.BASE_URL = server.getWebServerUrl()

        //TODO Setup OkHttp fo UI Test
        //        testComponent?.getAPIManager().notNull {
        //            setupOkHttpIdlingResource(it)
        //        }
    }

    private fun setupInjector() {
        if(application is StyletheoryOpsApplication) {
            with(application as StyletheoryOpsApplication) {
                testComponent = DaggerTestAppComponent.builder()
                        .testAppModule(TestAppModule(this))
                        .apiModule(ApiModule(this))
                        .build()
                this.initializeInjector(testComponent)
                this.appComponent = testComponent
            }
        }
    }


}