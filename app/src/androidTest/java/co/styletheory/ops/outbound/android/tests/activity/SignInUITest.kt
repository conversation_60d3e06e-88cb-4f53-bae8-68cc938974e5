package co.styletheory.ops.outbound.android.tests.activity

import android.content.Intent
import android.support.test.espresso.Espresso
import android.support.test.espresso.Espresso.onView
import android.support.test.espresso.action.ViewActions.*
import android.support.test.espresso.assertion.ViewAssertions.matches
import android.support.test.espresso.matcher.ViewMatchers.*
import co.styletheory.android.testkit.uiTest.util.ConditionWatcher
import co.styletheory.android.testkit.uiTest.util.Instruction
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.base.BaseInstrumentTest
import co.styletheory.ops.outbound.android.feature.signIn.SignInActivity
import co.styletheory.ops.outbound.android.main.view.MainActivity
import co.styletheory.ops.outbound.android.model.Session
import co.styletheory.ops.outbound.android.model.User
import co.styletheory.ops.outbound.android.util.MockResponseFactory
import co.styletheory.ops.outbound.android.util.ToastMatcher
import com.nhaarman.mockitokotlin2.*
import com.orhanobut.hawk.Hawk
import org.hamcrest.Matchers.not
import org.junit.Before
import org.junit.Test


/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 10 November 2017.
 * Description
 *
 * <EMAIL>
 */

class SignInUITest : BaseInstrumentTest<SignInActivity>(SignInActivity::class.java) {

    private val VALID_EMAIL = "<EMAIL>"
    private val VALID_PASSWORD = "password1"
    private val INVALID_EMAIL = "<EMAIL>"
    private val INVALID_PASSWORD = "password5"

    @Before
    override fun setup() {
        super.setup()
        testComponent?.inject(this)
        Hawk.deleteAll()
        activityRule.launchActivity(Intent())
    }

    @Test
    fun viewStateAtBeginning() {
        onView(withId(R.id.container_sign_in)).check(matches(isDisplayed()))
        onView(withId(R.id.edit_text_email)).check(matches(isDisplayed()))
        onView(withId(R.id.edit_text_password)).check(matches(isDisplayed()))
        onView(withId(R.id.button_sign_in)).check(matches(isDisplayed()))

        onView(withId(R.id.container_additional_field)).check(matches(withEffectiveVisibility(Visibility.GONE)))
        onView(withId(R.id.edit_text_name)).check(matches(withEffectiveVisibility(Visibility.GONE)))
        onView(withId(R.id.edit_text_new_password)).check(matches(withEffectiveVisibility(Visibility.GONE)))
        onView(withId(R.id.edit_text_confirm_new_password)).check(matches(withEffectiveVisibility(Visibility.GONE)))
        waitFor(1000)
    }

    @Test
    fun signInFailed_withInvalidEmail() {
        server.addQueueMockResponse(MockResponseFactory.LOGIN_INVALID_EMAIL_400())

        onView(withId(R.id.edit_text_email)).perform(typeText(INVALID_EMAIL), closeSoftKeyboard())
        onView(withId(R.id.edit_text_password)).perform(typeText(VALID_PASSWORD), closeSoftKeyboard())
        onView(withId(R.id.button_sign_in)).perform(click())

        onView(withText("400: " + context?.getString(R.string.err_user_does_not_exist)))
                .inRoot(ToastMatcher())
                .check(matches(isDisplayed()))
        waitFor(1000)
    }

    @Test
    fun signInFailed_withInvalidPassword() {
        server.addQueueMockResponse(MockResponseFactory.LOGIN_INVALID_PASSWORD_400())

        onView(withId(R.id.edit_text_email)).perform(typeText(VALID_EMAIL), closeSoftKeyboard())
        onView(withId(R.id.edit_text_password)).perform(typeText(INVALID_PASSWORD), closeSoftKeyboard())
        onView(withId(R.id.button_sign_in)).perform(click())

        onView(withText("400: " + context?.getString(R.string.err_incorrect_username_or_password)))
                .inRoot(ToastMatcher())
                .check(matches(isDisplayed()))
        waitFor(1000)
    }

    @Test
    fun signInSuccess_FieldEmpty() {
        onView(withId(R.id.container_sign_in)).check(matches(isDisplayed()))
        onView(withId(R.id.container_additional_field)).check(matches(not(isDisplayed())))

        onView(withId(R.id.edit_text_email)).perform(typeText(VALID_EMAIL))
        onView(withId(R.id.edit_text_password)).perform(clearText())
        onView(withId(R.id.button_sign_in)).perform(click())

        onView(withText(R.string.error_fill_required_field))
                .inRoot(ToastMatcher())
                .check(matches(isDisplayed()))

        onView(withId(R.id.edit_text_email)).perform(clearText())
        onView(withId(R.id.edit_text_password)).perform(typeText(VALID_PASSWORD))
        onView(withId(R.id.button_sign_in)).perform(click())

        onView(withText(R.string.error_fill_required_field))
                .inRoot(ToastMatcher())
                .check(matches(isDisplayed()))
        waitFor(1000)
    }

    @Test
    fun signInSuccess_IfisNewUserIsTrue_withInvalidData() {
        server.addQueueMockResponse(MockResponseFactory.LOGIN_NEW_USER_200())

        onView(withId(R.id.container_sign_in)).check(matches(isDisplayed()))
        onView(withId(R.id.container_additional_field)).check(matches(not(isDisplayed())))

        onView(withId(R.id.edit_text_email)).perform(typeText(VALID_EMAIL))
        onView(withId(R.id.edit_text_password)).perform(typeText(VALID_PASSWORD))
        Espresso.closeSoftKeyboard()
        onView(withId(R.id.button_sign_in)).perform(click())

        onView(withText(R.string.err_update_user_data))
                .inRoot(ToastMatcher())
                .check(matches(isDisplayed()))

        waitFor(1000)
        onView(withId(R.id.container_sign_in)).check(matches(not(isDisplayed())))
        onView(withId(R.id.container_additional_field)).check(matches(isDisplayed()))

        onView(withId(R.id.edit_text_name)).perform(typeText("Apri"))
        onView(withId(R.id.edit_text_new_password)).perform(typeText("Password1"))
        onView(withId(R.id.edit_text_confirm_new_password)).perform(clearText())
        Espresso.closeSoftKeyboard()
        onView(withId(R.id.button_sign_in)).perform(click())

        onView(withText(R.string.error_fill_required_field))
                .inRoot(ToastMatcher())
                .check(matches(isDisplayed()))

        onView(withId(R.id.edit_text_name)).perform(typeText("Apri"))
        onView(withId(R.id.edit_text_new_password)).perform(clearText())
        onView(withId(R.id.edit_text_confirm_new_password)).perform(typeText("Password"))
        Espresso.closeSoftKeyboard()
        onView(withId(R.id.button_sign_in)).perform(click())

        onView(withText(R.string.error_fill_required_field))
                .inRoot(ToastMatcher())
                .check(matches(isDisplayed()))

        onView(withId(R.id.edit_text_name)).perform(clearText())
        onView(withId(R.id.edit_text_new_password)).perform(typeText("Password1"))
        onView(withId(R.id.edit_text_confirm_new_password)).perform(typeText("Password1"))
        Espresso.closeSoftKeyboard()
        onView(withId(R.id.button_sign_in)).perform(click())

        onView(withText(R.string.error_fill_required_field))
                .inRoot(ToastMatcher())
                .check(matches(isDisplayed()))
        waitFor(1000)
    }

    @Test
    fun signInSuccess_IfisNewUserIsTrue_withValidData() {
        server.addQueueMockResponse(MockResponseFactory.LOGIN_NEW_USER_200())
        server.addQueueMockResponse(MockResponseFactory.LOGIN_200())
        server.addQueueMockResponse(MockResponseFactory.USER_DETAIL_200())
        server.addQueueMockResponse(MockResponseFactory.BATCH_200())
        server.addQueueMockResponse(MockResponseFactory.BATCH_200())

        onView(withId(R.id.container_sign_in)).check(matches(isDisplayed()))
        onView(withId(R.id.container_additional_field)).check(matches(not(isDisplayed())))

        onView(withId(R.id.edit_text_email)).perform(typeText(VALID_EMAIL))
        onView(withId(R.id.edit_text_password)).perform(typeText(VALID_PASSWORD))
        onView(withId(R.id.button_sign_in)).perform(click())

        onView(withText(R.string.err_update_user_data))
                .inRoot(ToastMatcher())
                .check(matches(isDisplayed()))

        onView(withId(R.id.container_sign_in)).check(matches(not(isDisplayed())))
        onView(withId(R.id.container_additional_field)).check(matches(isDisplayed()))


        onView(withId(R.id.edit_text_name)).perform(typeText("Apri"))
        onView(withId(R.id.edit_text_new_password)).perform(typeText("Password1"))
        onView(withId(R.id.edit_text_confirm_new_password)).perform(typeText("Password1"))
        onView(withId(R.id.button_sign_in)).perform(click())

        onView(withText(R.string.login_success_label))
                .inRoot(ToastMatcher())
                .check(matches(isDisplayed()))
        matcher.nextOpenActivityIs(MainActivity::class.java)
        waitFor(1000)
    }

    @Test
    fun signInSuccess() {
        server.addQueueMockResponse(MockResponseFactory.LOGIN_200())
        server.addQueueMockResponse(MockResponseFactory.USER_DETAIL_200())
        server.addQueueMockResponse(MockResponseFactory.BATCH_200())
        server.addQueueMockResponse(MockResponseFactory.BATCH_200())

        val userSession = mock<Session>()
        whenever(activityRule.activity.userStorage.getUserSession()).thenReturn(userSession)
        whenever(userSession.isTokenExpired()).thenReturn(false)

        onView(withId(R.id.edit_text_email)).perform(typeText(VALID_EMAIL))
        onView(withId(R.id.edit_text_password)).perform(typeText(VALID_PASSWORD))
        onView(withId(R.id.button_sign_in)).perform(click())

        onView(withText(R.string.login_success_label))
                .inRoot(ToastMatcher())
                .check(matches(isDisplayed()))
        matcher.nextOpenActivityIs(MainActivity::class.java)
        waitFor(1000)
    }


    @Test
    fun signInSuccess_whenSessionIsExist_ButUserLoginNull() {
        server.addQueueMockResponse(MockResponseFactory.USER_DETAIL_200().setBodyDelay(1000))
        server.addQueueMockResponse(MockResponseFactory.BATCH_200())
        server.addQueueMockResponse(MockResponseFactory.BATCH_200())

        val userSession = mock<Session>()
        whenever(activityRule.activity.userStorage.getUserSession()).thenReturn(userSession)
        whenever(activityRule.activity.userStorage.getUserLogin()).thenReturn(null)
        whenever(userSession.isTokenExpired()).thenReturn(false)
        whenever(activityRule.activity.userStorage.getUserRegion()).thenReturn("sg")

        activityRule.activity.afterCreate()

        ConditionWatcher.waitForCondition(object : Instruction() {
            override val description: String
                get() = "User login should not null"

            override fun checkCondition(): Boolean {
                return getActivity().isDestroyed
            }

        })
        onView(withText(R.string.login_success_label))
                .inRoot(ToastMatcher())
                .check(matches(isDisplayed()))

        matcher.nextOpenActivityIs(MainActivity::class.java)
        waitFor(1000)
    }

    @Test
    fun signInSuccess_whenSessionExist_AndUserLoginExist() {
        server.addQueueMockResponse(MockResponseFactory.BATCH_200())
        server.addQueueMockResponse(MockResponseFactory.BATCH_200())

        val userSession = mock<Session>()
        val userLogin = mock<User>()
        whenever(activityRule.activity.userStorage.getUserRegion()).thenReturn("sg")
        whenever(activityRule.activity.userStorage.getUserSession()).thenReturn(userSession)
        whenever(activityRule.activity.userStorage.getUserLogin()).thenReturn(userLogin)

        activityRule.activity.afterCreate()

        matcher.nextOpenActivityIs(MainActivity::class.java)
        waitFor(1000)
    }
}
