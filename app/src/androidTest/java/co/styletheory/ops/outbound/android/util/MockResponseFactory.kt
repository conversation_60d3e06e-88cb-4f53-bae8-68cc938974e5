package co.styletheory.ops.outbound.android.util

import co.styletheory.android.testkit.uiTest.network.ResponseProperty

/**
 * styletheory-ops-outbound-android
 * Created by dwi<PERSON><PERSON><PERSON> on 23 March 2018.
 * Description
 *
 * <EMAIL>
 */
object MockResponseFactory {

    fun LOGIN_200(): ResponseProperty {
        return ResponseProperty().setJsonAssetName("200-login.json")
    }

    fun LOGIN_NEW_USER_200(): ResponseProperty {
        return ResponseProperty().setJsonAssetName("200-login-new-user.json")
    }

    fun LOGIN_INVALID_EMAIL_400(): ResponseProperty {
        return ResponseProperty().setJsonAssetName("400-login-invalid-email.json")
    }

    fun LOGIN_INVALID_PASSWORD_400(): ResponseProperty {
        return ResponseProperty().setJsonAssetName("400-login-invalid-password.json")
    }

    fun USER_DETAIL_200(): ResponseProperty {
        return ResponseProperty().setJsonAssetName("200-user-detail.json")
    }

    fun BATCH_200(): ResponseProperty {
        return ResponseProperty().setJsonAssetName("200-batch.json")
    }

    fun BATCH_DETAIL_200(): ResponseProperty {
        return ResponseProperty().setJsonAssetName("200-batch-detail.json")
    }

    fun EMPTY_ARRAY_200(): ResponseProperty {
        return ResponseProperty().setJsonAssetName("200-empty-array.json")
    }

    fun EMPTY_OBJECT_200(): ResponseProperty {
        return ResponseProperty().setJsonAssetName("200-empty-object.json")
    }
}