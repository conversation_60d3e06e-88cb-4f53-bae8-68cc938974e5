package co.styletheory.ops.outbound.android.injection

import android.app.Application
import android.content.Context
import co.styletheory.ops.outbound.android.StyletheoryOpsApplication
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.injection.scope.PerApplication
import com.nhaarman.mockitokotlin2.*
import dagger.Module
import dagger.Provides
import org.greenrobot.eventbus.EventBus

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 12 December 2017.
 * Description
 *
 * <EMAIL>
 */


@Module
class TestAppModule(val application: StyletheoryOpsApplication) {

    @Provides
    @PerApplication
    fun provideAppContext(): Context = application

    @Provides
    @PerApplication
    fun provideApplication(): Application = application

    @Provides
    @PerApplication
    fun provideEventBus(): EventBus = EventBus.getDefault()

    @Provides
    @PerApplication
    fun provideUserStorage(): UserStorage {
        val mock: UserStorage = mock()
        whenever(mock.getUserRegion()).thenReturn("SG")
        return mock
    }


}