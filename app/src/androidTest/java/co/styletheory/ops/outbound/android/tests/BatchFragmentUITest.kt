package co.styletheory.ops.outbound.android.tests

import android.content.Intent
import android.graphics.Typeface
import android.support.test.espresso.Espresso.onView
import android.support.test.espresso.NoMatchingViewException
import android.support.test.espresso.action.ViewActions.swipeDown
import android.support.test.espresso.assertion.ViewAssertions.matches
import android.support.test.espresso.contrib.RecyclerViewActions
import android.support.test.espresso.matcher.ViewMatchers.*
import android.support.test.filters.MediumTest
import androidx.recyclerview.widget.RecyclerView
import android.view.View
import co.styletheory.android.testkit.uiTest.matcher.MatcherEx.hasTextStyle
import co.styletheory.android.testkit.uiTest.matcher.MatcherEx.isVisible
import co.styletheory.android.testkit.uiTest.matcher.MatcherEx.nextOpenActivityIs
import co.styletheory.android.testkit.uiTest.matcher.MatcherEx.recyclerChildMatcher
import co.styletheory.android.testkit.uiTest.matcher.RecyclerViewInteraction
import co.styletheory.android.testkit.uiTest.matcher.RecyclerViewInteraction.Companion.onRecyclerView
import co.styletheory.android.testkit.uiTest.matcher.ViewActions.clickChildViewWithId
import co.styletheory.android.testkit.uiTest.matcher.ViewActions.withCustomConstraints
import co.styletheory.android.testkit.uiTest.util.ConditionWatcher
import co.styletheory.android.testkit.uiTest.util.Instruction
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.base.BaseInstrumentTest
import co.styletheory.ops.outbound.android.feature.backlogDetail.view.BacklogDetailActivity
import co.styletheory.ops.outbound.android.feature.batch.view.BatchFragment
import co.styletheory.ops.outbound.android.feature.batch.viewModel.BatchListViewModel
import co.styletheory.ops.outbound.android.feature.photoDetail.view.PhotoDetailActivity
import co.styletheory.ops.outbound.android.feature.qcDetail.view.QcDetailActivity
import co.styletheory.ops.outbound.android.feature.packing.view.PackingDetailActivity
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.test.EmptyActivity
import co.styletheory.ops.outbound.android.viewModelComponent.HeaderTotalBatchViewModel
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.util.MockResponseFactory
import com.nhaarman.mockitokotlin2.*
import org.hamcrest.CoreMatchers.allOf
import org.hamcrest.CoreMatchers.not
import org.junit.Before
import org.junit.Test
import javax.inject.Inject


/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 04 December 2017.
 * Description
 *
 * <EMAIL>
 */

@MediumTest
class BatchFragmentUITest : BaseInstrumentTest<EmptyActivity>(EmptyActivity::class.java) {

    @Inject lateinit var userStorage: UserStorage

    private val fragment = BatchFragment.newInstance(BatchStatus.READY_FOR_PACKING)

    @Before
    override fun setup() {
        super.setup()
        testComponent?.inject(this)
        activityRule.launchActivity(Intent())
        activityRule.activity
                .supportFragmentManager
                .beginTransaction()
                .replace(android.R.id.content, fragment)
                .commit()
        whenever(userStorage.getUserEmail()).thenReturn("<EMAIL>")
    }

    @Test
    fun fetchBatch_SuccessWithItem() {
        server.addQueueMockResponse(MockResponseFactory.BATCH_200())

        waitDataLoaded()
        onView(withId(R.id.empty_content)).check(matches(not(isDisplayed())))
        onView(view.findSuperRecyclerView()).check(matches(allOf(matcher.hasItemCount(3), matcher.isVisible)))
    }

    @Test
    fun fetchBatch_SuccessWithEmptyItem() {
        server.addQueueMockResponse(MockResponseFactory.EMPTY_ARRAY_200())

        onView(view.findSuperRecyclerView()).check(matches(matcher.hasItemCount(0)))
        onView(view.findSuperRecyclerView()).check(matches(isDisplayed()))
        onView(withId(R.id.empty_content)).check(matches(isDisplayed()))

    }


    @Test
    fun checkBatchListElement() {
        server.addQueueMockResponse(MockResponseFactory.BATCH_200())

        waitDataLoaded()
        onRecyclerView(view.findSuperRecyclerView())
                .withRecyclerViewResource(object : RecyclerViewInteraction.RecyclerViewResource {
                    override fun withViewModel(index: Int): ViewModel? {
                        return fragment.viewModel?.getViewModelAtPosition(index)
                    }

                    override fun itemCount(): Int {
                        return fragment.viewModel?.totalItemCount ?: 0
                    }
                })
                .check(object : RecyclerViewInteraction.ItemViewAssertion {
                    override fun check(item: Any, binding: Any, view: View, e: NoMatchingViewException?) {
                        if(item is BatchListViewModel) {
                            matches(recyclerChildMatcher(
                                    withText(item.batchTitle.get()),
                                    matcher.hasTextStyle(Typeface.BOLD)
                            )).check(view, e)

                            matches(recyclerChildMatcher(
                                    withText(item.rack.get()),
                                    hasTextColor(R.color.very_dark_gray)
                            )).check(view, e)
                            matches(recyclerChildMatcher(
                                    withText(item.totalBox.get()),
                                    hasTextColor(R.color.very_dark_gray)
                            )).check(view, e)
                            matches(recyclerChildMatcher(
                                    withText(item.totalBoxAndItem.get()),
                                    hasTextColor(R.color.very_dark_gray)
                            )).check(view, e)
                            matches(recyclerChildMatcher(
                                    withText(item.totalItem.get()),
                                    hasTextColor(R.color.very_dark_gray)
                            )).check(view, e)
                            matches(recyclerChildMatcher(
                                    withText(item.itemLeft.get()),
                                    hasTextColor(R.color.dark_orange)
                            )).check(view, e)


                            for(label in item.labelItems) {
                                matches(recyclerChildMatcher(
                                        withText(context?.getString(label.labelType.get()!!.stringRes)),
                                        hasTextColor(label.labelType.get()!!.colorRes)
                                )).check(view, e)
                            }


                            for(step in item.batchStepItems) {
                                matches(recyclerChildMatcher(
                                        withText(step.pickerName.get()),
                                        hasTextStyle(Typeface.BOLD)
                                )).check(view, e)

                                matches(recyclerChildMatcher(withText(step.startTime.get()))).check(view, e)
                                matches(recyclerChildMatcher(withText(step.endTime.get()))).check(view, e)

                                if(step.showButton.get()) {
                                    matches(hasDescendant(allOf(
                                            withText(step.buttonText.get()),
                                            hasTextColor(R.color.white),
                                            isClickable(),
                                            isVisible
                                    ))).check(view, e)
                                } else {
                                    matches(hasDescendant(allOf(
                                            withText(step.buttonText.get()),
                                            not(isVisible)
                                    ))).check(view, e)
                                }
                            }
                        } else if(item is HeaderTotalBatchViewModel) {
                            matches(recyclerChildMatcher(
                                    withText(item.totalBatch.get()),
                                    matcher.hasTextStyle(Typeface.BOLD)
                            )).check(view, e)
                        }
                    }
                })
    }

    @Test
    fun refreshBatch() {
        server.addQueueMockResponse(MockResponseFactory.BATCH_200())
        server.addQueueMockResponse(MockResponseFactory.BATCH_200())
        waitDataLoaded()
        onView(withId(R.id.ptr_layout)).perform(withCustomConstraints(swipeDown(), isDisplayingAtLeast(85)))
        onView(withId(R.id.ptr_layout)).check(matches(isDisplayed()))
    }

    @Test
    fun takeBatch_whenBatchStatusIs_Backlog() {
        waitFragmentResume()
        server.addQueueMockResponse(MockResponseFactory.BATCH_200())
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200())
        fragment.viewModel?.setBatchStatus(BatchStatus.IN_BACKLOG)

        waitDataLoaded()
        onView(withId(android.R.id.list)).perform(RecyclerViewActions.actionOnItemAtPosition<RecyclerView.ViewHolder>(1, clickChildViewWithId(R.id.button_take)))

        nextOpenActivityIs(BacklogDetailActivity::class.java)
    }


    @Test
    fun takeBatch_whenBatchStatusIs_Picked() {
        waitFragmentResume()
        server.addQueueMockResponse(MockResponseFactory.BATCH_200())
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200())
        fragment.viewModel?.setBatchStatus(BatchStatus.PICKED)

        waitDataLoaded()
        onView(view.findSuperRecyclerView()).perform(RecyclerViewActions.actionOnItemAtPosition<RecyclerView.ViewHolder>(1, clickChildViewWithId(R.id.button_take)))

        nextOpenActivityIs(QcDetailActivity::class.java)
    }


    @Test
    fun takeBatch_whenBatchStatusIs_Photo() {
        waitFragmentResume()
        server.addQueueMockResponse(MockResponseFactory.BATCH_200())
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200())
        fragment.viewModel?.setBatchStatus(BatchStatus.QA_DONE)

        waitDataLoaded()
        onView(view.findSuperRecyclerView()).perform(RecyclerViewActions.actionOnItemAtPosition<RecyclerView.ViewHolder>(1, clickChildViewWithId(R.id.button_take)))

        nextOpenActivityIs(PhotoDetailActivity::class.java)
    }


    @Test
    fun takeBatch_whenBatchStatusIs_Ready() {
        waitFragmentResume()
        server.addQueueMockResponse(MockResponseFactory.BATCH_200())
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200())
        fragment.viewModel?.setBatchStatus(BatchStatus.READY_FOR_PACKING)

        waitDataLoaded()
        onView(view.findSuperRecyclerView()).perform(RecyclerViewActions.actionOnItemAtPosition<RecyclerView.ViewHolder>(1, clickChildViewWithId(R.id.button_take)))

        nextOpenActivityIs(PackingDetailActivity::class.java)
    }

    fun waitFragmentResume() {
        ConditionWatcher.waitForCondition(object : Instruction() {
            override val description: String = "Fragment is not resume"

            override fun checkCondition(): Boolean {
                return fragment.isResumed
            }
        })
    }

    fun waitDataLoaded() {
        ConditionWatcher.waitForCondition(object : Instruction() {
            override val description: String = "Data is not fetched"

            override fun checkCondition(): Boolean {
                return fragment.viewModel?.totalItemCount != 0
            }
        })
    }
}