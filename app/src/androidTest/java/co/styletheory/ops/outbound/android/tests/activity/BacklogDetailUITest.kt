package co.styletheory.ops.outbound.android.tests.activity

import android.content.Intent
import android.graphics.Typeface
import android.support.test.espresso.Espresso.onView
import android.support.test.espresso.NoMatchingViewException
import android.support.test.espresso.assertion.ViewAssertions.matches
import android.support.test.espresso.contrib.RecyclerViewActions
import android.support.test.espresso.matcher.ViewMatchers.*
import androidx.recyclerview.widget.RecyclerView
import android.view.View
import co.styletheory.android.testkit.uiTest.matcher.MatcherEx.hasTextColorSelector
import co.styletheory.android.testkit.uiTest.matcher.MatcherEx.hasTextStyle
import co.styletheory.android.testkit.uiTest.matcher.MatcherEx.recyclerChildMatcher
import co.styletheory.android.testkit.uiTest.matcher.RecyclerViewInteraction
import co.styletheory.android.testkit.uiTest.matcher.ViewActions
import co.styletheory.android.testkit.uiTest.util.ConditionWatcher
import co.styletheory.android.testkit.uiTest.util.Instruction
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.base.BaseInstrumentTest
import co.styletheory.ops.outbound.android.feature.backlogDetail.view.BacklogDetailActivity
import co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel.impl.BacklogDetailItemViewModelImpl
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.util.IntentConstant
import co.styletheory.ops.outbound.android.util.MockResponseFactory
import co.styletheory.ops.outbound.android.util.notNull
import co.styletheory.ops.outbound.android.viewModelComponent.FooterButtonViewModel
import org.hamcrest.CoreMatchers.not
import org.junit.Test

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 21 June 2018.
 * Description
 *
 * <EMAIL>
 */
class BacklogDetailUITest : BaseInstrumentTest<BacklogDetailActivity>(BacklogDetailActivity::class.java) {


    override fun setup() {
        super.setup()
        testComponent?.inject(this)
        val intent = Intent()
        intent.putExtra(IntentConstant.BATCH_ID, "1")
        intent.putExtra(IntentConstant.BATCH_STATUS, BatchStatus.IN_BACKLOG)
        activityRule.launchActivity(intent)
    }


    @Test
    fun viewStateAtBeginning() {
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200().replaceValue("Packed", "Picking"))

        waitDataLoaded()

        onView(withId(R.id.recycler_view)).check(matches(matcher.isVisible))
        view.findSuperRecyclerView().matches(matcher.hasItemCount(getActivity().viewModel?.totalItemCount
                ?: 0))

        onView(withId(R.id.toolbar_title)).check(matches(withText("${getActivity().viewModel?.toolbarViewModel()?.title?.get()}")))
        onView(withId(R.id.toolbar_subtitle)).check(matches(withText("${getActivity().viewModel?.toolbarViewModel()?.subtitle?.get()}")))
    }

    @Test
    fun checkBacklogListItem() {
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200().replaceValue("Packed", "Picking"))

        waitDataLoaded()
        RecyclerViewInteraction.onRecyclerView(view.findSuperRecyclerView())
                .withRecyclerViewResource(object : co.styletheory.android.testkit.uiTest.matcher.RecyclerViewInteraction.RecyclerViewResource {
                    override fun withViewModel(index: Int): ViewModel? {
                        return getActivity().viewModel?.getViewModelAtPosition(index)
                    }

                    override fun itemCount(): Int {
                        return getActivity().viewModel?.totalItemCount ?: 0
                    }

                }).check(object : RecyclerViewInteraction.ItemViewAssertion {
                    override fun check(item: Any, binding: Any, view: View, e: NoMatchingViewException?) {
                        if(item is BacklogDetailItemViewModelImpl) {
                            matches(recyclerChildMatcher(
                                    withId(R.id.image_view_item),
                                    matcher.isVisible
                            )).check(view, e)

                            matches(recyclerChildMatcher(
                                    withText(item.itemName.get()),
                                    matcher.isVisible,
                                    hasTextColor(R.color.very_dark_gray)
                            )).check(view, e)

                            matches(recyclerChildMatcher(
                                    withText(item.itemSize.get()),
                                    matcher.isVisible,
                                    hasTextColor(R.color.blue_cff)
                            )).check(view, e)

                            matches(recyclerChildMatcher(
                                    withText(item.category.get()),
                                    hasTextColor(R.color.black),
                                    hasTextStyle(Typeface.BOLD)
                            )).check(view, e)

                            matches(recyclerChildMatcher(
                                    withText(item.parts.get()),
                                    hasTextColor(R.color.black),
                                    hasTextStyle(Typeface.BOLD)
                            )).check(view, e)

                            matches(recyclerChildMatcher(
                                    withText(item.notes.get()),
                                    hasTextColor(R.color.very_dark_gray)
                            )).check(view, e)

                            matches(recyclerChildMatcher(
                                    withText(item.rack.get()),
                                    hasTextColor(R.color.light_green),
                                    hasTextStyle(Typeface.BOLD)
                            )).check(view, e)

                            matches(recyclerChildMatcher(
                                    withText(string(R.string.picked_label)),
                                    hasTextColorSelector(R.drawable.selector_text_white_black),
                                    matcher.isClickable
                            )).check(view, e)

                            matches(recyclerChildMatcher(
                                    withText(string(R.string.not_found_label)),
                                    hasTextColorSelector(R.drawable.selector_text_white_black),
                                    matcher.isClickable
                            )).check(view, e)

                            matches(recyclerChildMatcher(
                                    withText(string(R.string.swap_label)),
                                    hasTextColorSelector(R.drawable.selector_text_black_gray),
                                    not(isEnabled())
                            )).check(view, e)
                        } else if(item is FooterButtonViewModel) {
                            matches(recyclerChildMatcher(
                                    withText(string(R.string.complete_label)),
                                    hasTextColorSelector(R.drawable.selector_text_white_black),
                                    not(isEnabled())
                            )).check(view, e)
                        }
                    }
                })
    }

    @Test
    fun pickedButtonClicked() {
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200().replaceValue("Packed", "Picking"))
        server.addQueueMockResponse(MockResponseFactory.EMPTY_OBJECT_200())

        waitDataLoaded()

        onView(view.withSuperRecyclerView(1, R.id.overlay_loading)).check(matches(recyclerChildMatcher(not(isDisplayed()))))
        onView(view.findSuperRecyclerView()).perform(RecyclerViewActions.actionOnItemAtPosition<RecyclerView.ViewHolder>(1, ViewActions.clickChildViewWithId(R.id.text_view_picked)))
        onView(view.withSuperRecyclerView(1, R.id.overlay_loading)).check(matches(recyclerChildMatcher(matcher.isVisible)))
        waitFor(1000)
        onView(view.withSuperRecyclerView(1, R.id.text_view_picked)).check(matches(isChecked()))
        onView(view.withSuperRecyclerView(1, R.id.button_swap)).check(matches(not(isEnabled())))
    }

    @Test
    fun notFoundButtonClicked() {
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200().replaceValue("Packed", "Picking"))
        server.addQueueMockResponse(MockResponseFactory.EMPTY_OBJECT_200())

        waitDataLoaded()

        onView(view.withSuperRecyclerView(1, R.id.overlay_loading)).check(matches(recyclerChildMatcher(not(isDisplayed()))))
        onView(view.findSuperRecyclerView()).perform(RecyclerViewActions.actionOnItemAtPosition<RecyclerView.ViewHolder>(1, ViewActions.clickChildViewWithId(R.id.text_view_not_found)))
        onView(view.withSuperRecyclerView(1, R.id.overlay_loading)).check(matches(recyclerChildMatcher(matcher.isVisible)))
        waitFor(1000)
        onView(view.withSuperRecyclerView(1, R.id.text_view_not_found)).check(matches(isChecked()))
        onView(view.withSuperRecyclerView(1, R.id.button_swap)).check(matches(isEnabled()))
    }


    @Test
    fun whenPickedChecked_NotFoundShouldBeUncheck() {
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200().replaceValue("Packed", "Picking"))
        server.addQueueMockResponse(MockResponseFactory.EMPTY_OBJECT_200())

        waitDataLoaded()

        getActivity().viewModel?.detailItems?.get(1)?.missing?.set(true)
        waitFor(500)

        onView(view.withSuperRecyclerView(1, R.id.text_view_not_found)).check(matches(isChecked()))
        onView(view.withSuperRecyclerView(1, R.id.overlay_loading)).check(matches(recyclerChildMatcher(not(isDisplayed()))))
        onView(view.findSuperRecyclerView()).perform(RecyclerViewActions.actionOnItemAtPosition<RecyclerView.ViewHolder>(1, ViewActions.clickChildViewWithId(R.id.text_view_picked)))
        onView(view.withSuperRecyclerView(1, R.id.overlay_loading)).check(matches(recyclerChildMatcher(matcher.isVisible)))
        waitFor(1000)
        onView(view.withSuperRecyclerView(1, R.id.text_view_not_found)).check(matches(not(isChecked())))
        onView(view.withSuperRecyclerView(1, R.id.text_view_picked)).check(matches(isChecked()))
        onView(view.withSuperRecyclerView(1, R.id.button_swap)).check(matches(not(isEnabled())))
    }

    @Test
    fun whenNotFoundChecked_PickedShouldBeUncheck() {
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200().replaceValue("Packed", "Picking"))
        server.addQueueMockResponse(MockResponseFactory.EMPTY_OBJECT_200())

        waitDataLoaded()

        getActivity().viewModel?.detailItems?.get(1)?.picked?.set(true)
        waitFor(500)

        onView(view.withSuperRecyclerView(1, R.id.text_view_picked)).check(matches(isChecked()))
        onView(view.withSuperRecyclerView(1, R.id.overlay_loading)).check(matches(recyclerChildMatcher(not(isDisplayed()))))
        onView(view.findSuperRecyclerView()).perform(RecyclerViewActions.actionOnItemAtPosition<RecyclerView.ViewHolder>(1, ViewActions.clickChildViewWithId(R.id.text_view_not_found)))
        onView(view.withSuperRecyclerView(1, R.id.overlay_loading)).check(matches(recyclerChildMatcher(matcher.isVisible)))
        waitFor(1000)
        onView(view.withSuperRecyclerView(1, R.id.text_view_picked)).check(matches(not(isChecked())))
        onView(view.withSuperRecyclerView(1, R.id.text_view_not_found)).check(matches(isChecked()))
        onView(view.withSuperRecyclerView(1, R.id.button_swap)).check(matches(isEnabled()))
    }


    @Test
    fun whenAllItemIsPickedOrNotFound_CompleteButtonShouldBeEnable() {
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200().replaceValue("Packed", "Picking"))

        waitDataLoaded()

        for(it in getActivity().viewModel?.detailItems.orEmpty()) it.picked.set(true)
        RecyclerViewInteraction.onRecyclerView(view.findSuperRecyclerView())
                .withRecyclerViewResource(object : co.styletheory.android.testkit.uiTest.matcher.RecyclerViewInteraction.RecyclerViewResource {
                    override fun withViewModel(index: Int): ViewModel? {
                        return getActivity().viewModel?.getViewModelAtPosition(index)
                    }

                    override fun itemCount(): Int {
                        return getActivity().viewModel?.totalItemCount ?: 0
                    }

                }).check(object : co.styletheory.android.testkit.uiTest.matcher.RecyclerViewInteraction.ItemViewAssertion {
                    override fun check(item: Any, binding: Any, view: View, e: NoMatchingViewException?) {
                        if(item is FooterButtonViewModel) {
                            matches(recyclerChildMatcher(
                                    withText(string(R.string.complete_label)),
                                    hasTextColorSelector(R.drawable.selector_text_white_black),
                                    isEnabled()
                            )).check(view, e)
                        }
                    }
                })
    }

    fun waitDataLoaded() {
        ConditionWatcher.waitForCondition(object : Instruction() {
            override val description: String = "Data is not fetched"

            override fun checkCondition(): Boolean {
                getActivity().binding.notNull {
                    return it.recyclerView.adapter.itemCount > 1
                }
                return true
            }
        })
    }

}