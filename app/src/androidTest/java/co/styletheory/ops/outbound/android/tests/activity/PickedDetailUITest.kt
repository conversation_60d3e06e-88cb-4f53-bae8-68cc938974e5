package co.styletheory.ops.outbound.android.tests.activity

import android.app.Activity
import android.app.Instrumentation.ActivityResult
import android.content.Intent
import android.support.test.espresso.Espresso.onView
import android.support.test.espresso.action.ViewActions.*
import android.support.test.espresso.assertion.ViewAssertions.matches
import android.support.test.espresso.intent.Intents.intending
import android.support.test.espresso.intent.matcher.IntentMatchers.hasComponent
import android.support.test.espresso.matcher.ViewMatchers.withId
import android.support.test.espresso.matcher.ViewMatchers.withText
import co.styletheory.android.testkit.uiTest.util.ConditionWatcher
import co.styletheory.android.testkit.uiTest.util.Instruction
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.base.BaseInstrumentTest
import co.styletheory.ops.outbound.android.feature.qcDetail.event.QcDetailUIEvent
import co.styletheory.ops.outbound.android.feature.qcDetail.view.QcDetailActivity
import co.styletheory.ops.outbound.android.general.event.PreviewImageEvent
import co.styletheory.ops.outbound.android.general.view.FullScreenImageSlideShowActivity
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.util.MockResponseFactory
import co.styletheory.ops.outbound.android.util.IntentConstant
import co.styletheory.ops.outbound.android.util.RequestCode
import io.mockk.mockk
import io.mockk.verify
import org.greenrobot.eventbus.EventBus
import org.junit.Test
import javax.inject.Inject


/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 17 July 2018.
 * Description
 *
 * <EMAIL>
 */
class PickedDetailUITest : BaseInstrumentTest<QcDetailActivity>(QcDetailActivity::class.java) {

    @Inject lateinit var eventBus: EventBus

    override fun setup() {
        super.setup()
        testComponent?.inject(this)

        val intent = Intent()
        intent.putExtra(IntentConstant.BATCH_ID, "1")
        intent.putExtra(IntentConstant.BATCH_STATUS, BatchStatus.PICKED)
        activityRule.launchActivity(intent)
    }

    @Test
    fun viewStateAtTheBeginning() {
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200()
                .replaceValue("Packed", "QA")
                .setBodyDelay(1000))

        waitDataLoaded()

        onView(withId(R.id.toolbar_wrapper)).check(matches(matcher.isVisible))
        onView(withId(R.id.view_pager)).check(matches(matcher.isVisible))

        onView(withId(R.id.toolbar_title)).check(matches(withText(getActivity().viewModel?.batchName() + " - QA")))
        onView(withId(R.id.toolbar_subtitle)).check(matches(withText("1 of ${getActivity().viewModel?.totalViewCount} Box")))
    }

    @Test
    fun viewStateAtTheBeginning_withEmptyResult() {
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200()
                .setBodyDelay(1000))

        waitDataLoaded()

        onView(withId(R.id.toolbar_wrapper)).check(matches(matcher.isVisible))
        onView(withId(R.id.view_pager)).check(matches(matcher.isVisible))

        onView(withId(R.id.toolbar_title)).check(matches(withText(getActivity().viewModel?.batchName() + " - QA")))
        onView(withId(R.id.toolbar_subtitle)).check(matches(withText("empty picked box")))
    }

    @Test
    fun swipePager_test() {
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200()
                .replaceValue("Packed", "QA")
                .setBodyDelay(1000))

        waitDataLoaded()

        onView(withId(R.id.toolbar_subtitle)).check(matches(withText("${getActivity().binding?.viewPager?.currentItem?.plus(1)} of ${getActivity().viewModel?.totalViewCount} Box")))
        while(getActivity().binding?.viewPager?.currentItem != getActivity().viewModel?.totalViewCount?.minus(1)) {
            onView(withId(R.id.view_pager)).perform(swipeLeft())
            onView(withId(R.id.toolbar_subtitle)).check(matches(withText("${getActivity().binding?.viewPager?.currentItem?.plus(1)} of ${getActivity().viewModel?.totalViewCount} Box")))
        }

        while(getActivity().binding?.viewPager?.currentItem != 0) {
            onView(withId(R.id.view_pager)).perform(swipeRight())
            onView(withId(R.id.toolbar_subtitle)).check(matches(withText("${getActivity().binding?.viewPager?.currentItem?.plus(1)} of ${getActivity().viewModel?.totalViewCount} Box")))
        }
        onView(withId(R.id.toolbar_subtitle)).check(matches(withText("${getActivity().binding?.viewPager?.currentItem?.plus(1)} of ${getActivity().viewModel?.totalViewCount} Box")))
    }

    @Test
    fun completeButtonClick_withBatchNotCompleted() {
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200()
                .replaceValue("Packed", "QA")
                .setBodyDelay(1000))

        waitDataLoaded()

        onView(withId(R.id.action_complete)).perform(click())

        onView(withText("There is incomplete item in this batch")).check(matches(matcher.isVisible))
    }

    @Test
    fun completeButtonClick_withBatchCompleted() {
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200()
                .replaceValue("Packed", "QAPassed")
                .setBodyDelay(1000))
        server.addQueueMockResponse(MockResponseFactory.EMPTY_OBJECT_200())

        waitDataLoaded()

        onView(withId(R.id.action_complete)).perform(click())

        onView(withText("Batch ${getActivity().viewModel?.batchName()}, Complete")).check(matches(matcher.isVisible))
    }


    @Test
    fun refreshButtonClicked_pagerShouldStartOnLastSelectedIndex() {
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200()
                .replaceValue("Packed", "QAPassed"))
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200()
                .replaceValue("Packed", "QAPassed"))

        waitDataLoaded()

        onView(withId(R.id.toolbar_subtitle)).check(matches(withText("${getActivity().binding?.viewPager?.currentItem?.plus(1)} of ${getActivity().viewModel?.totalViewCount} Box")))
        onView(withId(R.id.view_pager)).perform(swipeLeft())

        onView(withId(R.id.action_refresh)).perform(click())

        onView(withId(R.id.toolbar_subtitle)).check(matches(withText("${getActivity().binding?.viewPager?.currentItem?.plus(1)} of ${getActivity().viewModel?.totalViewCount} Box")))
    }

    @Test
    fun activityResultAfterTakePicture_withExtrasResult() {
        getActivity().takePhotoCallback = mockk(relaxed = true)

        val result = Intent()
        result.putStringArrayListExtra(IntentConstant.PRODUCT_PHOTO_RESULT_QAIMAGES_EXTRA_CODE, arrayListOf("Photo1", "Photo2"))
        intending(hasComponent(QcDetailActivity::class.java.name)).respondWith(ActivityResult(Activity.RESULT_OK, result))
        activityRule.activity.startActivityForResult(Intent(context, QcDetailActivity::class.java), RequestCode.PHOTO_MANAGER)

        verify(exactly = 1) { getActivity().takePhotoCallback?.apply(any()) }
    }

    @Test
    fun activityResultAfterTakePicture_withNoExtrasResult() {
        getActivity().takePhotoCallback = mockk(relaxed = true)

        val result = Intent()
        intending(hasComponent(QcDetailActivity::class.java.name)).respondWith(ActivityResult(Activity.RESULT_OK, result))
        activityRule.activity.startActivityForResult(Intent(context, QcDetailActivity::class.java), RequestCode.PHOTO_MANAGER)

        verify(inverse = true) { getActivity().takePhotoCallback?.apply(any()) }
    }

    @Test
    fun whenPreviewImageEvent() {
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200()
                .replaceValue("Packed", "QAPassed"))
        eventBus.post(PreviewImageEvent("photo1"))

        matcher.nextOpenActivityIs(FullScreenImageSlideShowActivity::class.java)
    }

    @Test
    fun removeShipmentEvent() {
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200()
                .replaceValue("Packed", "QA"))
        server.addQueueMockResponse(MockResponseFactory.BATCH_DETAIL_200()
                .replaceValue("Packed", "QA"))

        waitDataLoaded()

        eventBus.post(QcDetailUIEvent.RemoveShipmentQaPicked(null))

        onView(withId(R.id.view_pager)).check(matches(matcher.hasItemCount(19)))
    }


    fun waitDataLoaded() {
        ConditionWatcher.waitForCondition(object : Instruction() {
            override val description: String = "Data is not fetched"

            override fun checkCondition(): Boolean {
                return getActivity().viewModel?.toolbarViewModel()?.title?.isEmpty() == false
            }
        })
    }
}