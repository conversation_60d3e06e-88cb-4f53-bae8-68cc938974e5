package co.styletheory.ops.outbound.android.runner

import android.app.Application
import android.content.Context
import android.support.test.runner.AndroidJUnitRunner
import co.styletheory.ops.outbound.android.BuildConfig
import com.github.tmurakami.dexopener.DexOpener


/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 12 December 2017.
 * Description
 *
 * <EMAIL>
 */
class OPSInstrumentMockRunner : AndroidJUnitRunner() {
    override fun newApplication(cl: ClassLoader, className: String?, context: Context): Application {
        DexOpener.builder(context)
                .buildConfig(BuildConfig::class.java) // Set the BuildConfig class
                .build()
                .installTo(cl)
        return super.newApplication(cl, className, context)
    }
}