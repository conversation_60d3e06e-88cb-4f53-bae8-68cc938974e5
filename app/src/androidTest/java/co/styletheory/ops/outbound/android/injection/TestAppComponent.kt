package co.styletheory.ops.outbound.android.injection

import co.styletheory.ops.outbound.android.injection.component.AppComponent
import co.styletheory.ops.outbound.android.injection.module.ApiModule
import co.styletheory.ops.outbound.android.injection.scope.PerApplication
import co.styletheory.ops.outbound.android.tests.BatchFragmentUITest
import co.styletheory.ops.outbound.android.tests.activity.BacklogDetailUITest
import co.styletheory.ops.outbound.android.tests.activity.PickedDetailUITest
import co.styletheory.ops.outbound.android.tests.activity.SignInUITest
import dagger.Component
import javax.inject.Singleton

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 10 November 2017.
 * Description
 *
 * <EMAIL>
 */

@Singleton
@PerApplication
@Component(modules = [TestAppModule::class, ApiModule::class])
interface TestAppComponent : AppComponent {

    fun inject(test: SignInUITest)
    fun inject(test: BacklogDetailUITest)
    fun inject(test: PickedDetailUITest)

    fun inject(test: BatchFragmentUITest)
}