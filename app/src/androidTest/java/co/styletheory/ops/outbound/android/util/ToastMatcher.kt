package co.styletheory.ops.outbound.android.util

import android.support.test.espresso.Root
import android.view.WindowManager
import org.hamcrest.Description
import org.hamcrest.TypeSafeMatcher


/**
 * styletheory-ops-outbound-android
 * Created by dwiaprian<PERSON> on 14 April 2018.
 * Description
 *
 * <EMAIL>
 */
class ToastMatcher : TypeSafeMatcher<Root>() {

    override fun describeTo(description: Description) {
        description.appendText("is toast")
    }

    override fun matchesSafely(root: Root): Boolean {
        val type = root.windowLayoutParams.get().type
        //        val layoutParam = if(Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
        //            WindowManager.LayoutParams.TYPE_TOAST
        //        } else {
        //            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        //        }
        if(type == WindowManager.LayoutParams.TYPE_TOAST) {
            val windowToken = root.decorView.windowToken
            val appToken = root.decorView.applicationWindowToken
            if(windowToken === appToken) {
                // windowToken == appToken means this window isn't contained by any other windows.
                // if it was a window for an activity, it would have TYPE_BASE_APPLICATION.
                return true
            }
        }
        return false
    }

}