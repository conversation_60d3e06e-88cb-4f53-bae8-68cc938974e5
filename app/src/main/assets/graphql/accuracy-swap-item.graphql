query getAccuracySwapItem($rfid: String!, $oldItemId: String!) {
      result:getAccuracySwapItem(rfid: $rfid, oldItemId: $oldItemId) {
            id
            createdAt
            releaseDate
            sizeCategory
            labelSize
            order
            status
            inboundQaImages
            outboundQaImages
            bustSize
            waistSize
            hipSize
            style {
                name
                primaryCategory
                gallery
                galleries {
                    images
                }
                designer {
                    name
                }
                colors {
                    name
                    colorCode
                }
            }
            additionalProperties {
                invoicePrice
                currency
                incoterms
            }
      }
}

mutation outboundAccuracySwap($oldItemUuid: String!, $oldItemBoxId: String!, $oldItemShipmentId: String!, $newItemUuid: String!) {
    outboundAccuracySwap(oldItemUuid: $oldItemUuid, oldItemBoxId: $oldItemBoxId, oldItemShipmentId: $oldItemShipmentId, newItemUuid: $newItemUuid)
 }