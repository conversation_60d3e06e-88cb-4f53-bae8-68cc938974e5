query batchDetail($id: String!) {
    result:batchById(id: $id) {
        id
        name
        pickers {
            name
            email
        }
        pickerStartTime
        pickerEndTime
        qas {
            name
            email
        }
        qaStartTime
        qaEndTime
        photographers {
            name
            email
        }
        photographerStartTime
        photographerEndTime
        packers {
            name
            email
        }
        packerStartTime
        packerEndTime
        isExpedited
        isFirstBox
        shipmentOrder
        logisticProviders {
            id
            name
            logo {
                url
            }
        }
        items {
            id
            label
            labelSize
            order
            sizeCategory
            status
            notes
            outboundQaImages
            outboundBeforePackImages
            qaFailedCategory
            qaFailedReason
            parts
            rfid
            rfidFormatted
            length
            resellingInventoryType
            additionalProperties {
                detachable
                detachableDetail
            }
            style {
                id
                name
                primaryCategory
                gallery
                galleries {
                    images
                }
                designer {
                    name
                }
                colors {
                    name
                    colorCode
                }
            }
            shipment {
                id
            }
            warehouseRackSlotItemLocation{
                areaRow{
                    name
                    sortOrder
                }
                rack{
                    name
                    sortOrder
                }
                status
            }
        }
        shipments {
            id
            boxId
            isExpedited
            businessMethod
            procedureType
            box {
                id
                status
                isFirstBox
                boxReward{
                    title
                    description
                }
            }
            customer {
                email
                name
                status
                isVip
                noPaper
                noToteBag
            }
            tracking {
                id
                status
            }
            logisticProvider {
                id
                name
            }
            recipient {
                email
                address
                floor
                unit
                postal
                city
                name
                phone
                instructions
            }
            time{
                scheduledPickup{
                    start
                    end
                }
            }
            items {
                id
                label
                labelSize
                order
                sizeCategory
                status
                notes
                outboundQaImages
                outboundBeforePackImages
                qaFailedCategory
                qaFailedReason
                parts
                rfid
                rfidFormatted
                length
                resellingInventoryType
                additionalProperties {
                    detachable
                    detachableDetail
                }
                style {
                    id
                    name
                    primaryCategory
                    gallery
                    galleries {
                        images
                    }
                    designer {
                        name
                    }
                    colors {
                        name
                        colorCode
                    }
                }
                shipment {
                    id
                }
            }
            customer {
                id
                name
                email
            }
        }
        rack {
            id
            name
        }
    }
}