query batchList($batchStatus: BatchStatusType, $batchCreatedAt: String!) {
    result:batchList(batchStatus: $batchStatus, batchCreatedAt: $batchCreatedAt) {
        id
        name
        pickers {
            name
            email
        }
        pickerStartTime
        pickerEndTime
        qas {
            name
            email
        }
        qaStartTime
        qaEndTime
        photographers {
            name
            email
        }
        photographerStartTime
        photographerEndTime
        packers {
            name
            email
        }
        packerStartTime
        packerEndTime
        isFirstBox
        isExpedited
        logisticProviders{
            id
            name
        }
        shipments{
            id
            items{
                id
                status
            }
        }
        items {
            id
            status
        }
        rack{
            id
            name
        }
    }
}
