query shipmentListByProvider ($date:String!, $logisticProvider: String!, $page:Int, $keyword: String, $logisticOrdered: Boolean){
    result:batchShipmentListByDate(date:$date, batchStatus:PACKED, logisticProvider:$logisticProvider, page:$page, pageSize:10,
    keyword:$keyword, logisticOrdered: $logisticOrdered){
        shipments{
            id
            deliveryType
            status
            customer{ name }
            isExpedited
            isVip
            isNoPaper
            isFirstBox
            time{
                scheduledPickup{
                    start
                    end
                }
            }
            tracking{
                id
                status
            }
            courier{
                name
                phone
                image
            }
        }
        meta{
            shipmentCount
            shipmentIds
        }
    }
}