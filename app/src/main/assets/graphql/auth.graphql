query login($email: String!, $password: String!) {
    result:login(email: $email, password: $password) {
        accessToken
        refreshToken
        exp
        isNewUser
    }
}
mutation createPassword($name: String!, $email: String!, $password: String!, $newPassword: String!){
    result:createPassword(name: $name, email: $email, password: $password, newPassword: $newPassword){
        accessToken
        refreshToken
        exp
        isNewUser
    }
}
query refreshSession($refreshToken: String!){
    result:refreshSession (refreshToken: $refreshToken){
        accessToken
        refreshToken
        exp
        isNewUser
    }
}

query logout{
    result:logout
}