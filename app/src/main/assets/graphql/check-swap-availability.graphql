query checkSwapAvailability($styleId: String!, $labelSize: String!,$status: [ItemStatusType]){
    result:itemList(styleId: $styleId, labelSize: $labelSize, status:$status){
        items{
            id
            label
            labelSize
            order
            sizeCategory
            status
            notes
            outboundQaImages
            outboundBeforePackImages
            qaFailedCategory
            qaFailedReason
            parts
            style {
                name
                primaryCategory
                gallery
                galleries {
                    images
                }
                designer {
                    name
                }
                colors {
                    name
                    colorCode
                }
            }
            shipment {
                id
            }
        }
    }
}