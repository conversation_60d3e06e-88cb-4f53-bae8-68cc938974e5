package co.styletheory.ops.outbound.android.feature.batch.viewModel

import androidx.databinding.ObservableBoolean
import co.styletheory.android.network.core.RequestResult
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.batch.event.BatchStepActionClickEvent
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseInjectedViewModel
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.model.enums.StepType
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.BeginBatchResource
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.util.impl.StringUtil
import co.styletheory.ops.outbound.android.util.notNull
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 26 October 2017.
 * Description
 *
 * <EMAIL>
 */

class BatchStepViewModel @Inject constructor() : BaseInjectedViewModel() {
    var emails = mutableListOf<String>()
    var batch: Batch? = null
    var batchStatus: BatchStatus? = null
    val pickerName = ObservableString()
    val startTime = ObservableString()
    val endTime = ObservableString()
    val stepType = ObservableString()
    val buttonText = ObservableString()
    val showProgress = ObservableBoolean(false)
    val showButton = ObservableBoolean(false)
    val progressBy = ObservableString()

    @Inject
    lateinit var userStorage: UserStorage

    @Inject
    lateinit var dataService: DataService

    @Inject
    lateinit var errorResponse: ErrorResponse

    fun setBatch(batch: Batch): BatchStepViewModel {
        this.batch = batch
        return this
    }

    fun setBatchStatus(item: BatchStatus): BatchStepViewModel {
        this.batchStatus = item
        return this
    }

    fun setEmail(email: String): BatchStepViewModel {
        this.emails.add(email)
        return this
    }

    fun bindViewModel(stepType: StepType, names: List<String>, emails: List<String>, start: String? = "-", end: String? = "-", actionText: String, showAction: Boolean = false) {
        this.emails.clear()
        this.emails.addAll(emails)
        this.stepType.set(stepType.text)
        buttonText.set(actionText)

        showButton.set(showAction)
        pickerName.set(if(names.isEmpty()) "-" else StringUtil.stringArraySeparateWithComma(names.toTypedArray(), true))
        startTime.set(if(start.isNullOrEmpty()) "-" else start)
        endTime.set(if(end.isNullOrEmpty()) "-" else end)

        if(showAction) {
            handleActionStep()
        }
    }

    fun handleActionStep() {
        showButton.set(true)
        if(emails.contains(userStorage.getUserEmail())) {
            buttonText.set(string(R.string.continue_label))
        } else {
            if(emails.isNotEmpty()) {
                buttonText.set(string(R.string.join_label))
            }
        }
    }

    fun getBatchType(batchStatus: BatchStatus?): BeginBatchResource.Type? {
        return when(batchStatus) {
            BatchStatus.IN_BACKLOG,
            BatchStatus.PICKING -> BeginBatchResource.Type.PICK
            BatchStatus.PICKED,
            BatchStatus.QA_PROCESSING -> BeginBatchResource.Type.QA
            BatchStatus.QA_DONE,
            BatchStatus.PHOTO -> BeginBatchResource.Type.PHOTO
            BatchStatus.PACKING,
            BatchStatus.PHOTO_QA_DONE,
            BatchStatus.READY_FOR_PACKING -> BeginBatchResource.Type.PACKING
            else -> null
        }
    }

    fun stepActionButtonClick() {
        val batchType = getBatchType(batchStatus)

        if(!emails.contains(userStorage.getUserEmail())) {
            batchType.notNull { type ->
                showProgressDialog()
                dataService.beginBatch(
                        BeginBatchResource(batch?.id.orEmpty(), type),
                        RequestResult {
                            onSuccess {
                                dismissProgressDialog()
                                navigateTo(batch)
                            }
                            onError {
                                dismissProgressDialog()
                                showToast(errorResponse.getErrorBodyDescription(it), false)
                            }
                        }
                )
            }
        } else {
            navigateTo(batch)
        }
    }

    private fun navigateTo(batch: Batch?) {
        eventBus.post(BatchStepActionClickEvent(batch?.id, batchStatus))
    }
}