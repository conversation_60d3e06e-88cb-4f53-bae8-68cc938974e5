package co.styletheory.ops.outbound.android.model.request

import co.styletheory.ops.outbound.android.util.notNull
import com.google.gson.annotations.SerializedName

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 22 December 2017.
 * Description
 *
 * <EMAIL>
 */
class UpdateBatchStatusBody {
    @SerializedName("shipmentId")
    var shipmentId: String? = ""
    @SerializedName("itemIds")
    val itemIds = ArrayList<String>()
    @SerializedName("status")
    var status: String? = null
    @SerializedName("failReason")
    var failReason: String? = null
    @SerializedName("failCategory")
    var failCategory: String? = null

    fun addIds(ids: ArrayList<String>?){
        ids.notNull { this.itemIds.addAll(it) }
    }
}