package co.styletheory.ops.outbound.android.viewModelComponent

import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseInjectedViewModel

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 06 December 2017.
 * Description
 *
 * <EMAIL>
 */
class ColorItemViewModel : BaseInjectedViewModel() {

    val colorName = ObservableString()
    val colorHex = ObservableString()
}