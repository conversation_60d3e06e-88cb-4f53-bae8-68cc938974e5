package co.styletheory.ops.outbound.android.feature.backlogDetail.event

import co.styletheory.ops.outbound.android.general.base.BaseEvent
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.util.Next

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 25 October 2017.
 * Description
 *
 * <EMAIL>
 */

data class BacklogItemSwapClickEvent constructor(
        val shipmentId: String,
        val boxId: String,
        val customerEmail: String,
        val batchItem: BatchItem?,
        val callback: Next<BatchItem>
) : BaseEvent()
