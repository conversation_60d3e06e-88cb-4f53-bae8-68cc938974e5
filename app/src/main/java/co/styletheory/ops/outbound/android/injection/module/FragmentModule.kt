package co.styletheory.ops.outbound.android.injection.module

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import co.styletheory.ops.outbound.android.general.Navigator
import co.styletheory.ops.outbound.android.injection.scope.PerFragment
import dagger.Module
import dagger.Provides

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 19 October 2017.
 * Description
 *
 * <EMAIL>
 */

@Module
class FragmentModule (val fragment: Fragment){

    @Provides
    @PerFragment
    fun provideActivity() : FragmentActivity = fragment.requireActivity()

    @Provides
    @PerFragment
    fun provideNavigator(): Navigator = Navigator(provideActivity())
}