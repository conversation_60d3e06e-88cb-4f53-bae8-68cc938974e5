package co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel

import co.styletheory.ops.outbound.android.general.adapter.impl.MultipleTypeRecyclerViewAdapter
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.listener.OnKeyListener
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 24 October 2017.
 * Description
 *
 * <EMAIL>
 */
interface BacklogDetailViewModel : ViewModel, MultipleTypeRecyclerViewAdapter.MultipleTypeRecycleViewSource {

    var batch: Batch?
    val batchId: ObservableString
    val batchName: ObservableString
    val scannedRfidText: ObservableString
    var batchStatus: BatchStatus?
    var detailItems: ArrayList<BacklogDetailItemViewModel>
    val onEditTextKeyListener: OnKeyListener

    fun toolbarViewModel(): GeneralToolbarViewModel
    fun refreshCompleteStatusButton()
    fun fetchBatchDetail(callback: Result<Void?, String?>?)
    fun refreshBatchDetail(callback: Result<Void?, String?>?)
    fun completeBacklog(callback: Result<Void?, String?>?)
    fun checkScannedText()
    fun checkBatchItemStatus()
}