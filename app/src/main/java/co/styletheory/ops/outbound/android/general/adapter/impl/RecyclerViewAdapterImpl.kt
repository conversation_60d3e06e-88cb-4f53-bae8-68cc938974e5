package co.styletheory.ops.outbound.android.general.adapter.impl

import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.RecyclerView
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.general.adapter.ListAdapter
import co.styletheory.ops.outbound.android.general.listener.RecyclerViewInterceptorListener
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.BaseFastScrollerViewModel
import com.viethoa.RecyclerViewFastScroller
import javax.inject.Inject

/**
 * Created by elroyset<PERSON><PERSON><PERSON> on 9/14/16.
 */

class RecyclerViewAdapterImpl @Inject constructor() : RecyclerView.Adapter<RecyclerViewHolder>(), ListAdapter<Any>, RecyclerViewFastScroller.BubbleTextGetter {

    var layoutId: Int = 0
    private var showFooterLoading: Boolean = false
    var interceptor: RecyclerViewInterceptorListener? = null
    var isAttachToFastScroller = false

    override var items: MutableList<Any> = mutableListOf()

    fun showFooterLoading() {
        try {
            this.showFooterLoading = true
            if(items.isNotEmpty()) Handler(Looper.getMainLooper()).post { notifyItemChanged(itemCount) }
        } catch(ex: Exception) {
            //Not implemented yet
        }
    }

    fun hideFooterLoading() {
        try {
            this.showFooterLoading = false
            if(items.isNotEmpty()) Handler(Looper.getMainLooper()).post { notifyDataSetChanged() }
        } catch(ex: Exception) {
            //Not implemented yet
        }
    }


    override fun addItem(item: Any) {
        items.add(item)
    }

    override fun addItems(items: MutableList<Any>) {
        this.items.addAll(items)
    }

    override fun getItem(position: Int): Any {
        return this.items[position]
    }

    override fun clear() {
        this.items.clear()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerViewHolder {
        val binding: ViewDataBinding = if(viewType == 1) {
            DataBindingUtil.inflate<ViewDataBinding>(LayoutInflater.from(parent.context), layoutId, parent, false)
        } else {
            DataBindingUtil.inflate<ViewDataBinding>(LayoutInflater.from(parent.context), R.layout.footer_loading_view, parent, false)
        }
        return RecyclerViewHolder(binding)
    }

    override fun onBindViewHolder(holder: RecyclerViewHolder, position: Int) {
        if(position < items.size) {
            holder.bindViewModel(getItem(position) as ViewModel)
        }
        interceptor?.onBindView(holder)
    }

    override fun getItemViewType(position: Int): Int {
        return if(position < items.size) 1 else 2
    }

    override fun getItemCount(): Int {
        return if(showFooterLoading) items.size + 1 else items.size
    }

    override fun getTextToShowInBubble(pos: Int): String? {
        if(isAttachToFastScroller) {
            if(pos < 0 || pos >= items.size) return null

            val item = items[pos] as BaseFastScrollerViewModel
            val name = item.name.get().toString()
            return if(name.isEmpty()) null else name.substring(0, 1)
        } else {
            return null
        }
    }
}
