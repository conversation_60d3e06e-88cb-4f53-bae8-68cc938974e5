package co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel

import androidx.databinding.ObservableArrayList
import co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel.impl.ItemLogisticProviderNameViewModelImpl
import co.styletheory.ops.outbound.android.general.recyclerView.RecyclerViewSetting
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.LogisticProvider

/**
 * Created by <PERSON> on 05/01/22.
 */
interface LogisticProviderNameViewModel : ViewModel {
    val logisticNameItems: ObservableArrayList<ItemLogisticProviderNameViewModelImpl>
    val recyclerViewLogisticName: RecyclerViewSetting

    fun bindViewModel(providers: List<LogisticProvider>)
    fun bindViewModelProvider(provider: LogisticProvider?)
}