package co.styletheory.ops.outbound.android.model.enums

import com.google.gson.annotations.SerializedName


/**
 * styletheory-ops-outbound-android
 * Created by dwiap<PERSON><PERSON> on 09 November 2017.
 * Description
 *
 * <EMAIL>
 */

enum class BatchStatus(val textName: String) {
    @SerializedName("IN_BACKLOG")
    IN_BACKLOG("IN_BACKLOG"),
    @SerializedName("PICKED")
    PICKED("PICKED"),
    @SerializedName("PICKING")
    PICKING("PICKING"),
    @SerializedName("QA")
    QA("QA"),
    @SerializedName("QA_DONE")
    QA_DONE("QA_DONE"),
    @SerializedName("QA_PROCESSING")
    QA_PROCESSING("QA_PROCESSING"),
    @SerializedName("PHOTO")
    PHOTO("PHOTO"),
    @SerializedName("PHOTO_QA_DONE")
    PHOTO_QA_DONE("PHOTO_QA_DONE"),
    @SerializedName("READY_FOR_PACKING")
    READY_FOR_PACKING("READY_FOR_PACKING"),
    @SerializedName("PACKING")
    PACKING("PACKING"),
    @SerializedName("PACKED")
    PACKED("PACKED");
}