package co.styletheory.ops.outbound.android.general.base

import android.content.DialogInterface
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.annotation.LayoutRes
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import co.styletheory.ops.outbound.android.BR
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.general.event.DismissDialogEvent
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 27 October 2017.
 * Description
 *
 * <EMAIL>
 */
class BaseDialogFragment : DialogFragment() {

    private var layoutId: Int = 0
    private var viewModel: Any? = null
    var binding: ViewDataBinding? = null
    private val eventBus = EventBus.getDefault()
    private var dialogInterface: DialogInterface? = null
    private var dismissDelay: Long = 0

    companion object {
        fun setViewModelAndLayoutId(viewModel: Any, @LayoutRes layoutResID: Int): BaseDialogFragment {
            val dialog = BaseDialogFragment()
            dialog.viewModel = viewModel
            dialog.layoutId = layoutResID
            return dialog
        }
    }

    fun onDialogDismiss(dialogInterface: DialogInterface): BaseDialogFragment {
        this.dialogInterface = dialogInterface
        return this
    }

    fun setDismissAfter(milliSecond: Long): BaseDialogFragment {
        this.dismissDelay = milliSecond
        return this
    }

    fun setIsCancelable(isCancelable: Boolean): BaseDialogFragment {
        this.isCancelable = isCancelable
        return this
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NO_FRAME, R.style.DialogStyle90)
    }

    override fun onStart() {
        super.onStart()
        val window = dialog?.window
        val windowParams = window?.attributes
        if(windowParams != null) {
            windowParams.dimAmount = 0.60f
            windowParams.flags = windowParams.flags or WindowManager.LayoutParams.FLAG_DIM_BEHIND
            window.attributes = windowParams
        }

        if(!eventBus.isRegistered(this)) {
            eventBus.register(this)
        }
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        try {
            binding = DataBindingUtil.inflate(inflater, layoutId, container, false)
        } catch(e: Exception) {
            //Not implemented yet
        }
        binding?.setVariable(BR.viewModel, viewModel)
        return binding?.root
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        dialogInterface?.dismiss()
    }

    @Suppress("unused", "UNUSED_PARAMETER")
    @Subscribe
    fun onDismissDialogEvent(event: DismissDialogEvent) {
        if(tag.isNullOrEmpty()) {
            dismissAllowingStateLoss()
        } else {
            if(event.tag == tag) dismissAllowingStateLoss()
        }
    }

    fun show(manager: FragmentManager) {
        manager.beginTransaction().add(this, "").commitAllowingStateLoss()
        if(dismissDelay > 0) {
            Handler(Looper.getMainLooper()).postDelayed({ dismissAllowingStateLoss() }, dismissDelay)
        }
    }

    override fun onDestroy() {
        if(eventBus.isRegistered(this)) {
            eventBus.unregister(this)
        }
        binding = null
        viewModel = null
        super.onDestroy()
    }
}