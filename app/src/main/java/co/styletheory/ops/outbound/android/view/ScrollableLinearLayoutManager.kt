package co.styletheory.ops.outbound.android.view

import android.content.Context
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import timber.log.Timber

/**
 * Created by <PERSON><PERSON><PERSON> on 7/29/18
 */
class ScrollableLinearLayoutManager(context: Context?) : LinearLayoutManager(context) {
    var canScrollVertically = true
    var canScrollHorizontally = true

    override fun supportsPredictiveItemAnimations(): Boolean {
        return false
    }

    constructor(context: Context?, @RecyclerView.Orientation orientation: Int, reverseLayout: Boolean) : this(context) {
        setOrientation(orientation)
        setReverseLayout(reverseLayout)
    }

    override fun onLayoutChildren(recycler: RecyclerView.Recycler?, state: RecyclerView.State) {
        try {
            super.onLayoutChildren(recycler, state)
        } catch(e: IndexOutOfBoundsException) {
            Timber.tag("Error").e("IndexOutOfBoundsException in RecyclerView happens")
        }
    }

    override fun canScrollVertically(): Boolean = canScrollVertically && super.canScrollVertically()

    override fun canScrollHorizontally(): Boolean = canScrollHorizontally && super.canScrollHorizontally()
}