package co.styletheory.ops.outbound.android.feature.accuracySwap.viewModel

import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.viewModelComponent.FooterButtonViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel

/**
 * Created by Yoga C<PERSON> on 05/05/20.
 * Android Engineer
 */
interface AccuracySwapViewModel : ViewModel {

    var swapItem: SwapItem

    fun toolbarViewModel(): GeneralToolbarViewModel
    fun footerButtonViewModel(): FooterButtonViewModel
    fun selectedSwapReasonPosition(selectedPosition: Int)
}