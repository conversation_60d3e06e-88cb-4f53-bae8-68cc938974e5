package co.styletheory.ops.outbound.android.model.enums

import com.google.gson.annotations.SerializedName

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 23/06/20.
 */
enum class ErrorSwapType(val errorSwapType: String) {
    @SerializedName("CONFIRM")
    CONFIRM("CONFIRM"),

    @SerializedName("RETRY")
    RETRY("RETRY"),

    @SerializedName("SCAN_OTHER_ITEM")
    SCAN_OTHER_ITEM("SCAN_OTHER_ITEM"),

    @SerializedName("SCAN_PARENT_ITEM")
    SCAN_PARENT_ITEM("SCAN_PARENT_ITEM");
}