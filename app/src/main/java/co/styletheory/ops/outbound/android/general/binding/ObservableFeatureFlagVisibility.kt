package co.styletheory.ops.outbound.android.general.binding

import android.view.View
import androidx.databinding.ObservableInt
import co.styletheory.ops.outbound.android.StyletheoryOpsApplication
import co.styletheory.ops.outbound.android.util.featureflag.FeatureFlag
import co.styletheory.ops.outbound.android.util.featureflag.FeatureFlagToggleListener
import co.styletheory.ops.outbound.android.util.featureflag.FeatureFlagVisibilityListener
import javax.inject.Inject

/**
 * Created by Yoga C. Pranata on 02/04/19.
 * Android Engineer
 */
class ObservableFeatureFlagVisibility constructor(val key: String, val defaultValue: Boolean = false) : ObservableInt() {
    @Inject
    @JvmField
    var featureFlag: FeatureFlag? = null

    var callback: FeatureFlagVisibilityListener? = null

    init {
        StyletheoryOpsApplication.directInject(this)

        val toggleListener = object : FeatureFlagToggleListener {
            override fun onToggleChange(toggleState: Boolean) {
                set(if (toggleState) View.VISIBLE else View.GONE)
                callback?.onToggleChange(get())
            }
        }

        set(if (featureFlag?.isToggleOn(key, toggleListener, defaultValue) == true) View.VISIBLE else View.GONE)
    }

    fun unregisterToggleListener() {
        featureFlag?.turnOffToggleListener(key)
    }

}