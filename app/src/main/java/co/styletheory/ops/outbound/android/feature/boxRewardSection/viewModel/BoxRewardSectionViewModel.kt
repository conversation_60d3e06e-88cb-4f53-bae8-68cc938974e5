package co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel

import androidx.databinding.ObservableArrayList
import co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.impl.ItemBoxRewardSectionViewModelImpl
import co.styletheory.ops.outbound.android.general.recyclerView.RecyclerViewSetting
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.BoxReward

/**
 * Created by <PERSON> on 11/20/20.
 */
interface BoxRewardSectionViewModel : ViewModel {
    val rewardItems: ObservableArrayList<ItemBoxRewardSectionViewModelImpl>
    val recyclerViewSetting: RecyclerViewSetting
    fun setBoxRewards(rewards: List<BoxReward>, isCompleted: <PERSON><PERSON><PERSON>)
}