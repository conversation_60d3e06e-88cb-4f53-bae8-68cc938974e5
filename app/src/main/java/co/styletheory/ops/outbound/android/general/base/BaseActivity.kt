package co.styletheory.ops.outbound.android.general.base

import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.Bundle
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.annotation.*
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import co.styletheory.android.network.event.NoInternetConnectionEvent
import co.styletheory.android.network.event.NotAuthorizeEvent
import co.styletheory.ops.outbound.android.BR
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.StyletheoryOpsApplication
import co.styletheory.ops.outbound.android.general.Navigator
import co.styletheory.ops.outbound.android.general.dialog.CustomProgressDialog
import co.styletheory.ops.outbound.android.general.event.DismissProgressDialogEvent
import co.styletheory.ops.outbound.android.general.event.GeneralUIEvent
import co.styletheory.ops.outbound.android.general.event.ShowProgressDialogEvent
import co.styletheory.ops.outbound.android.general.event.ShowToastEvent
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.injection.component.ActivityComponent
import co.styletheory.ops.outbound.android.injection.component.DaggerActivityComponent
import co.styletheory.ops.outbound.android.injection.module.ActivityModule
import co.styletheory.ops.outbound.android.util.EdgeToEdgeUtil
import co.styletheory.ops.outbound.android.util.featureflag.FeatureFlagUtil
import com.viven.imagezoom.ImageZoomHelper
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import javax.inject.Inject
import androidx.core.view.isNotEmpty

/**
 * Base activity class that provides common functionality for all activities in the app.
 * Handles dependency injection, data binding, event handling, and UI operations.
 *
 * @param VB ViewDataBinding type for the activity layout
 * @param VM ViewModel type for the activity
 */
@Suppress("UNUSED_PARAMETER", "unused")
abstract class BaseActivity<VB : ViewDataBinding, VM : ViewModel> : AppCompatActivity() {

    var activityComponent: ActivityComponent? = null
        get() {
            if(field == null) {
                field = DaggerActivityComponent.builder()
                    .activityModule(ActivityModule(this))
                    .appComponent(StyletheoryOpsApplication.instance?.appComponent)
                    .build()
            }
            return field
        }

    @Inject
    @JvmField
    var viewModel: VM? = null

    @JvmField
    var binding: VB? = null

    @Inject
    lateinit var eventBus: EventBus
    @Inject
    lateinit var navigator: Navigator
    @Inject
    lateinit var progressDialog: CustomProgressDialog
    @Inject
    lateinit var userStorage: UserStorage
    @Inject
    lateinit var featureFlagUtil: FeatureFlagUtil

    private var imageZoomHelper: ImageZoomHelper? = null
    private lateinit var noInternetDialog: AlertDialog

    override fun onCreate(savedInstanceState: Bundle?) {
        // Enable edge-to-edge display
        enableEdgeToEdge()

        super.onCreate(savedInstanceState)

        // Setup window insets for edge-to-edge display
        setupEdgeToEdgeInsets()

        noInternetDialog = AlertDialog.Builder(this)
            .setTitle("No Internet Connection")
            .setMessage("Try Again ?")
            .setNegativeButton("No") { dialogInterface, _ ->
                dialogInterface.dismiss()
            }.create()
    }

    /**
     * Sets up edge-to-edge display and handles system insets.
     * Override for custom inset handling.
     */
    protected open fun setupEdgeToEdgeInsets() {

        // Configure edge-to-edge display
        EdgeToEdgeUtil.setupEdgeToEdge(this)

        // Apply insets to root view
        val rootView = findViewById<View>(android.R.id.content)
        ViewCompat.setOnApplyWindowInsetsListener(rootView) { view, windowInsets ->
            val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())
            val isKeyboardVisible = windowInsets.isVisible(WindowInsetsCompat.Type.ime())

            // Apply top padding only if no toolbar
            val topPadding = if (hasToolbar()) 0 else insets.top

            // Apply only left/right/top insets to root view
            view.setPadding(insets.left, topPadding, insets.right, 0)

            // Bottom insets handled by applyBottomInsetToContent()
            windowInsets
        }
    }

    /**
     * Sets up keyboard insets handling for input fields.
     */
    protected fun setupKeyboardInsets(
        rootView: View,
        footerView: View? = null,
        contentView: View? = null
    ) {
        EdgeToEdgeUtil.handleKeyboardInsets(rootView, footerView, contentView)
    }

    override fun onStart() {
        super.onStart()
        viewModel?.afterInject()
        if(!eventBus.isRegistered(this)) {
            eventBus.register(this)
        }
    }

    override fun onStop() {
        if(eventBus.isRegistered(this)) {
            eventBus.unregister(this)
        }
        super.onStop()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if(!eventBus.isRegistered(this)) {
            eventBus.register(this)
        }

        for(fragment in supportFragmentManager.fragments) {
            fragment.onActivityResult(requestCode, resultCode, data)
        }
    }

    override fun onDestroy() {
        viewModel?.onDestroy()
        if(eventBus.isRegistered(this)) {
            eventBus.unregister(this)
        }
        binding = null
        viewModel = null
        activityComponent = null
        super.onDestroy()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if(item.itemId == android.R.id.home) onBackPressedDispatcher.onBackPressed()
        return super.onOptionsItemSelected(item)
    }

    @Suppress("UNUSED_PARAMETER")
    @Subscribe
    fun onEventMainThread(event: BaseEvent) {
        //Not implemented yet
    }

    @Subscribe
    fun showToastEvent(event: ShowToastEvent) {
        if(event.isLong) {
            showLongToast(event.message ?: string(R.string.err_something_when_wrong))
        } else {
            showShortToast(event.message ?: string(R.string.err_something_when_wrong))
        }
    }

    fun showProgressDialog(withText: String = "") {
        progressDialog.showWithText(withText, supportFragmentManager)
    }

    fun dismissProgressDialog() {
        progressDialog.dismissProgressDialog()
    }

    @Subscribe
    fun showProgressDialogEvent(event: ShowProgressDialogEvent) {
        progressDialog.show(supportFragmentManager, CustomProgressDialog.CUSTOM_PROGRESS_DIALOG)
    }

    @Subscribe
    fun dismissProgressDialogEvent(event: DismissProgressDialogEvent) {
        dismissProgressDialog()
    }

    @Subscribe
    fun onFeatureFlagNeedToRefreshPage(event: GeneralUIEvent.FeatureFlagNeedToRefreshPage) {
        navigator.toSignIn()
    }

    @Subscribe
    open fun noInternetConnectionEvent(event: NoInternetConnectionEvent) {
        runOnUiThread {
            if(!noInternetDialog.isShowing && !isFinishing) {
                noInternetDialog.setButton(AlertDialog.BUTTON_POSITIVE, "Yes") { dialogInterface, _ ->
                    event.retryCallback.tryAgain()
                    dialogInterface.dismiss()
                }
                noInternetDialog.setOnDismissListener {
                    event.retryCallback.clearFailureRequest()
                }
                noInternetDialog.show()
            }
        }
    }

    @Subscribe
    fun notAuthorizeEvent(event: NotAuthorizeEvent) {
        showShortToast("Need Relogin")
        userStorage.clearUserStorage()
        navigator.clearStack().toSignIn()
        finish()
    }


    fun <T> pushArgument(arg: Any) {
        eventBus.postSticky(arg)
    }

    fun <T> popArgumentsAs(tClass: Class<T>): T? {
        val stickyArgument = eventBus.getStickyEvent(tClass)
        eventBus.removeStickyEvent(tClass)

        return stickyArgument
    }

    /**
     * Sets the content view, creates the binding and attaches the view to the view model.
     *
     * Also applies appropriate insets to prevent content from being hidden under system bars.
     *
     * @param layoutResID The layout resource ID to inflate
     * @throws IllegalStateException if viewModel is null
     */
    protected fun bindContentView(@LayoutRes layoutResID: Int) {
        if(viewModel == null) {
            throw IllegalStateException("viewModel must already be set via injection")
        }

        binding = DataBindingUtil.setContentView(this, layoutResID)
        binding?.setVariable(BR.viewModel, viewModel)
        binding?.executePendingBindings()

        // Apply insets to the root view after binding
        binding?.root?.let { rootView ->
            ViewCompat.setOnApplyWindowInsetsListener(rootView) { view, windowInsets ->
                val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())

                // For bound views, we need to be more careful with insets
                // If this is a layout with a toolbar, we want the toolbar to handle the top inset
                if (hasToolbar()) {
                    // Find the toolbar in the layout and apply top inset to it
                    val toolbar = findToolbarInLayout(rootView)
                    toolbar?.setPadding(
                        toolbar.paddingLeft,
                        insets.top,
                        toolbar.paddingRight,
                        toolbar.paddingBottom
                    )

                    // For the root view, don't apply top padding since toolbar handles it
                    // Apply only left and right insets, bottom inset will be handled by specific views
                    view.setPadding(insets.left, 0, insets.right, 0)

                    // Apply bottom inset only to the content container or bottom views if needed
                    applyBottomInsetToContent(rootView, insets.bottom)
                } else {
                    // No toolbar, so apply top, left, and right insets to the root view
                    // Bottom inset will be handled separately
                    view.setPadding(insets.left, insets.top, insets.right, 0)

                    // Apply bottom inset only to the content container or bottom views if needed
                    applyBottomInsetToContent(rootView, insets.bottom)
                }

                // Return the insets so child views can also apply them if needed
                windowInsets
            }

            // Request insets be applied
            ViewCompat.requestApplyInsets(rootView)
        }
    }

    protected fun enableZoomImageView() {
        imageZoomHelper = ImageZoomHelper(this)
    }

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        return try {
            imageZoomHelper?.onDispatchTouchEvent(ev) == true || super.dispatchTouchEvent(ev)
        } catch(ex: Exception) {
            ex.printStackTrace()
            false
        }
    }

    fun showShortToast(message: String) {
        Toast.makeText(applicationContext, message, Toast.LENGTH_SHORT).show()
    }

    fun showLongToast(message: String) {
        Toast.makeText(applicationContext, message, Toast.LENGTH_LONG).show()
    }

    fun dimen(@DimenRes resId: Int): Int = resources.getDimension(resId).toInt()

    fun color(@ColorRes resId: Int): Int = ContextCompat.getColor(this, resId)

    fun drawable(@DrawableRes resId: Int): Drawable? = ContextCompat.getDrawable(this, resId)

    fun integer(@IntegerRes resId: Int): Int = resources.getInteger(resId)

    fun string(@StringRes resId: Int): String = resources.getString(resId)

    /**
     * Checks if activity has a toolbar. Override in activities without toolbar.
     */
    protected open fun hasToolbar(): Boolean = true

    /**
     * Finds a toolbar in the layout hierarchy.
     *
     * Searches for toolbar by ID, tag, and class name to apply insets correctly.
     */

    /**
     * Applies bottom inset only to views that need it.
     */
    protected open fun applyBottomInsetToContent(rootView: View, bottomInset: Int) {
        // Common container IDs that might need bottom inset
        val contentIds = arrayOf(
            android.R.id.content,
            R.id.main_content,
            R.id.content_container,
            R.id.view_pager,
            R.id.recycler_view
        )

        // Find and apply to content container
        var contentFound = false
        for (id in contentIds) {
            try {
                rootView.findViewById<View>(id)?.let { contentView ->
                    // Apply reduced inset (80%)
                    val adjustedInset = (bottomInset * 0.8f).toInt()
                    contentView.setPadding(
                        contentView.paddingLeft,
                        contentView.paddingTop,
                        contentView.paddingRight,
                        adjustedInset
                    )
                    contentFound = true
                    return
                }
            } catch (e: Exception) { /* Ignore - ID might not exist */ }
        }

        // Fallback: apply smaller inset (50%) to compatible root views
        if (!contentFound) {
            val rootClassName = rootView.javaClass.simpleName
            if (!rootClassName.contains("Coordinator") &&
                !rootClassName.contains("DrawerLayout") &&
                !rootClassName.contains("ConstraintLayout")) {

                val adjustedInset = (bottomInset * 0.5f).toInt()
                rootView.setPadding(
                    rootView.paddingLeft,
                    rootView.paddingTop,
                    rootView.paddingRight,
                    adjustedInset
                )
            }
        }
    }

    /** Finds toolbar in layout hierarchy. */
    protected open fun findToolbarInLayout(rootView: View): View? {
        // Try to find by common toolbar IDs
        val toolbarIds = arrayOf(R.id.toolbar, R.id.toolbar_wrapper, R.id.inset_toolbar)
        for (id in toolbarIds) {
            try {
                rootView.findViewById<View>(id)?.let { return it }
            } catch (e: Exception) { /* Ignore */ }
        }

        // Try to find by tag
        arrayOf("toolbar", "toolbar_wrapper", "actionBar", "appBar").forEach { tag ->
            rootView.findViewWithTag<View>(tag)?.let { return it }
        }

        // Try to find by class name
        if (rootView is ViewGroup) {
            // Check first few children
            for (i in 0 until minOf(rootView.childCount, 5)) {
                val child = rootView.getChildAt(i)
                val className = child.javaClass.name

                // Check if child is toolbar
                if (className.contains("Toolbar") ||
                    className.contains("ActionBar") ||
                    className.contains("AppBar")) {
                    return child
                }

                // Check one level deeper
                if (child is ViewGroup && child.isNotEmpty()) {
                    for (j in 0 until minOf(child.childCount, 3)) {
                        val grandchild = child.getChildAt(j)
                        if (grandchild.javaClass.name.contains("Toolbar") ||
                            grandchild.javaClass.name.contains("ActionBar")) {
                            return grandchild
                        }
                    }
                }
            }
        }
        return null
    }
}