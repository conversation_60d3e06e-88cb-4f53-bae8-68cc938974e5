package co.styletheory.ops.outbound.android.feature.packing.viewModel

import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableInt
import co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.impl.BoxRewardSectionViewModelImpl
import co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.impl.ItemBoxRewardSectionViewModelImpl
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.BoxReward
import co.styletheory.ops.outbound.android.model.LogisticProvider
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.viewModelComponent.LabelBoxViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.ServiceMethodWidgetViewModel

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 19 December 2017.
 * Description
 *
 * <EMAIL>
 */
interface PackingDetailItemViewModel : ViewModel {
    var rfidList: MutableMap<String, Boolean>
    val rackSection: ObservableString
    val rackName: ObservableString
    val batchId: ObservableString
    var shipments: Shipment?
    var logisticProviders: List<LogisticProvider>?
    val labelItemVisibility: ObservableInt
    val businessMethodNameList: ObservableArrayList<ServiceMethodWidgetViewModel>
    val logisticNameList: ObservableArrayList<ServiceMethodWidgetViewModel>
    val procedureTypeList: ObservableArrayList<ServiceMethodWidgetViewModel>
    val procedureTypeVisibility: ObservableInt
    val boxRewardsSectionVisibility: ObservableInt
    var selectedBoxRewards: ArrayList<BoxReward>

    fun setBoxRewardViewModel(): BoxRewardSectionViewModelImpl
    fun setupBoxReward(isComplete: Boolean)

    fun mapShipment()
    fun checkRfidText(rfidText: String)
    fun setupLabelVisibility(list: List<LabelBoxViewModel>)
    fun setSelectedBoxRewards(modelReward: ItemBoxRewardSectionViewModelImpl)
}