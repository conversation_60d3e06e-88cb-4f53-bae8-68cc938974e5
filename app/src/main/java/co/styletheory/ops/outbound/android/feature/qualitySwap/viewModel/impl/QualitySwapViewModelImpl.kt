package co.styletheory.ops.outbound.android.feature.qualitySwap.viewModel.impl

import android.view.View
import androidx.databinding.ObservableInt
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.qualitySwap.event.QualitySwapUIEvent
import co.styletheory.ops.outbound.android.feature.qualitySwap.viewModel.QualitySwapViewModel
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.viewModelComponent.FooterButtonViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel
import com.styletheory.android.mvvm.general.binding.ObservableText
import java.util.*
import javax.inject.Inject

/**
 * Created by Yoga C. Pranata on 18/06/20.
 * Android Engineer
 */
class QualitySwapViewModelImpl @Inject constructor() : BaseViewModel<QualitySwapViewModelImpl>(), QualitySwapViewModel {
    @Inject
    lateinit var toolbarViewModel: GeneralToolbarViewModel

    @Inject
    lateinit var footerButtonViewModel: FooterButtonViewModel

    @Inject
    lateinit var userStorage: UserStorage

    init {
        viewModelClass = QualitySwapViewModelImpl::class
    }

    override var batchId: String = ""
    override var shipmentId: String = ""
    override var swapItem: SwapItem = SwapItem()
    override var batchStatus: BatchStatus? = null
    override val selectedFailReason: ObservableString = ObservableString("")
    override val inputNotes: ObservableString = ObservableString("")
    override var photos: List<String> = listOf()
    override val infoVisibility: ObservableInt = ObservableInt(View.VISIBLE)

    override fun toolbarViewModel(): GeneralToolbarViewModel = toolbarViewModel
    override fun footerButtonViewModel(): FooterButtonViewModel = footerButtonViewModel

    override fun afterInject() {
        val textButton = if(isResellingOrBagsItem()) {
            string(R.string.quality_swap_now_confirm_title).uppercase()
        } else {
            string(R.string.quality_swap_now_title).uppercase()
        }

        footerButtonViewModel.buttonTitle.set(textButton)
        footerButtonViewModel.enable.set(false)

        if(isResellingOrBagsItem()) {
            toolbarViewModel.title.set(string(R.string.quality_check_title))
        } else {
            toolbarViewModel.title.set(string(R.string.quality_swap_title))
        }
    }

    override fun selectedSwapReasonPosition(selectedPosition: Int) {
        if(selectedPosition > 0) {
            checkRequiredFields()
        } else {
            disableButton()
        }
    }

    override fun uploadPhotoClick() {
        eventBus.post(QualitySwapUIEvent.TakePictureEvent(batchId, batchStatus))
    }

    override fun checkRequiredFields() {
        if(isFailedReasonNotEmpty() && photos.isNotEmpty()) {
            enableButton()
        } else {
            disableButton()
        }
    }

    override fun getFailReasonList(): Array<String> {
        return if(userStorage.isVerticalTypeBags()) {
            context.resources.getStringArray(R.array.bags_fail_reason_list)
        } else {
            context.resources.getStringArray(R.array.apparel_fail_reason_list)
        }
    }

    override fun clearAllFields() {
        batchId = ""
        shipmentId = ""
        swapItem = SwapItem()
        batchStatus = null
        selectedFailReason.set("")
        inputNotes.set("")
        photos = listOf()
    }

    override fun setupInfoVisibility() {
        if(isResellingOrBagsItem()) {
            infoVisibility.set(View.GONE)
        } else {
            infoVisibility.set(View.VISIBLE)
        }
    }

    private fun isFailedReasonNotEmpty(): Boolean {
        return (selectedFailReason.get().isNotEmpty() && !selectedFailReason.get()
            .lowercase(Locale.ROOT)
            .contains("select"))
    }

    private fun isResellingOrBagsItem(): Boolean {
        return (swapItem.item?.isResellingItem() == true || userStorage.isVerticalTypeBags() || userStorage.isUserOnDemandAndRegionID())
    }

    private fun enableButton() {
        footerButtonViewModel.enable.set(true)
    }

    private fun disableButton() {
        footerButtonViewModel.enable.set(false)
    }


}