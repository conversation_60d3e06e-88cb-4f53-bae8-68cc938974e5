package co.styletheory.ops.outbound.android.viewModelComponent

import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseInjectedViewModel

/**
 * Created by <PERSON> C<PERSON> on 02/06/20.
 * Android Engineer
 */
class ServiceMethodWidgetViewModel : BaseInjectedViewModel() {

    val serviceName = ObservableString()

    companion object {
        fun create(serviceName: String?) : ServiceMethodWidgetViewModel {
            val serviceMethodWidget = ServiceMethodWidgetViewModel()
            serviceMethodWidget.bindViewModel(serviceName)
            return serviceMethodWidget
        }
    }

    fun bindViewModel(serviceName: String?) {
        this.serviceName.set(serviceName)
    }
}