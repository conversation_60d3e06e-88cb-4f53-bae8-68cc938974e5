package co.styletheory.ops.outbound.android.general.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import co.styletheory.ops.outbound.android.R
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 08 November 2017.
 * Description
 *
 * <EMAIL>
 */
class CustomProgressDialog @Inject constructor() : DialogFragment() {

    companion object {
        const val CUSTOM_PROGRESS_DIALOG = "CustomProgressDialog"
    }

    var bodyText: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NO_FRAME, R.style.DialogStyle90)
        isCancelable = false
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? =
            inflater.inflate(R.layout.custom_progress_dialog, container, false)

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val textView: TextView? = view.findViewById(R.id.text_view_body)
        textView?.visibility = if(bodyText.isEmpty()) View.GONE else View.VISIBLE
        textView?.text = bodyText
    }

    fun showWithText(bodyText: String, fragmentManager: FragmentManager) {
        this.bodyText = bodyText
        if(!isAdded) {
            show(fragmentManager, CUSTOM_PROGRESS_DIALOG)
        }
    }

    fun dismissProgressDialog() {
        if(isAdded) {
            dismissAllowingStateLoss()
        }
    }

}