package co.styletheory.ops.outbound.android.main.view

import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.MainActivityBinding
import co.styletheory.ops.outbound.android.feature.batch.view.BatchFragment
import co.styletheory.ops.outbound.android.feature.packed.view.PackedFragment
import co.styletheory.ops.outbound.android.general.adapter.PagerSource
import co.styletheory.ops.outbound.android.general.adapter.impl.PagerAdapter
import co.styletheory.ops.outbound.android.general.auth.event.SignOutFailedEvent
import co.styletheory.ops.outbound.android.general.auth.event.SignOutSuccessEvent
import co.styletheory.ops.outbound.android.general.base.BaseActivity
import co.styletheory.ops.outbound.android.general.powerMenu.CustomMenuAdapter
import co.styletheory.ops.outbound.android.general.powerMenu.CustomMenuItem
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.main.impl.MainViewModel
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.model.enums.MainMenuTab
import co.styletheory.ops.outbound.android.util.AppConstant
import co.styletheory.ops.outbound.android.util.DateConstant
import co.styletheory.ops.outbound.android.util.DateUtil
import co.styletheory.ops.outbound.android.util.EdgeToEdgeUtil
import co.styletheory.ops.outbound.android.util.IntentConstant
import co.styletheory.ops.outbound.android.util.OSUtil
import co.styletheory.ops.outbound.android.util.ViewUtils
import co.styletheory.ops.outbound.android.util.notNullOrEmpty
import com.skydoves.powermenu.CustomPowerMenu
import com.skydoves.powermenu.MenuAnimation
import com.skydoves.powermenu.OnMenuItemClickListener
import org.greenrobot.eventbus.Subscribe
import java.util.Locale
import javax.inject.Inject

/**
 * Main activity with tabbed interface and edge-to-edge display.
 *
 * Implements a multi-tab interface with proper inset handling for modern Android UI.
 */
@Suppress("UNUSED_PARAMETER", "unused")
class MainActivity : BaseActivity<MainActivityBinding, MainViewModel>(), PagerSource, LifecycleOwner {

    @Inject
    lateinit var pagerAdapter: PagerAdapter

    private var customMenu: CustomPowerMenu<*, *>? = null
    private val TOTAL_MAIN_MENU = if(UserStorage().isUserRegionID()) 5 else 4

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        activityComponent?.inject(this)
        bindContentView(R.layout.main_activity)
        checkUserLogin()
    }

    /**
     * Configures edge-to-edge display with proper inset handling.
     *
     * Applies system window insets to appropriate views while preventing double padding.
     */
    override fun setupEdgeToEdgeInsets() {
        if(OSUtil().isAndroid15OrHigher()) {
            EdgeToEdgeUtil.setupEdgeToEdgeForAndroid15Plus(this)

            // Apply insets to views
            binding?.root?.let { rootView ->
                ViewCompat.setOnApplyWindowInsetsListener(rootView) { _, windowInsets ->
                    val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())

                    // Apply insets to specific views
                    applyToolbarInsets(insets.top)
                    applyRootViewInsets(rootView, insets.left, insets.right)
                    applyBottomInsets(insets.bottom)

                    // Return insets for potential child views
                    windowInsets
                }
                ViewCompat.requestApplyInsets(rootView)
            }
        }
    }

    /** Applies top inset to toolbar. */
    private fun applyToolbarInsets(topInset: Int) {
        binding?.toolbarWrapper?.toolbar?.apply {
            setPadding(paddingLeft, topInset, paddingRight, paddingBottom)
        }
    }

    /** Applies horizontal insets to root view. */
    private fun applyRootViewInsets(rootView: View, leftInset: Int, rightInset: Int) {
        rootView.setPadding(leftInset, 0, rightInset, 0)
    }

    /** Applies bottom inset to tabs and viewPager. */
    private fun applyBottomInsets(bottomInset: Int) {
        binding?.tabs?.apply {
            setPadding(paddingLeft, paddingTop, paddingRight, bottomInset)
        }

        binding?.viewPager?.apply {
            setPadding(paddingLeft, 0, paddingRight, bottomInset)
        }
    }

    /**
     * Configures toolbar with user info and proper status bar color.
     */
    private fun setupToolbar() {
            // Configure toolbar
            binding?.toolbarWrapper?.toolbar?.let { toolbar ->
                setSupportActionBar(toolbar as Toolbar)

                // Set horizontal content insets
                val inset = ViewUtils.dpToPixel(resources, 16f)
                toolbar.setContentInsetsAbsolute(inset, inset)

                // Set user info in toolbar
                binding?.toolbarWrapper?.apply {
                    toolbarTitle?.text = userStorage.getUserLogin()?.name
                    toolbarSubtitle?.text = DateUtil.createDateStringFrom(
                        userStorage.getBatchDate().time,
                        DateConstant.PATTERN.EEEEMMMMMddyyyy
                    )
                    toolbarSubtitle?.visibility = View.VISIBLE
                }
            }
    }

    /** Verifies login status and initializes UI or redirects to login. */
    private fun checkUserLogin() {
        if(!userStorage.getUserLogin()?.name.isNullOrEmpty()) {
            setupToolbar()
            setupPowerMenu()
            initView()
            setupSnowView()
        } else {
            viewModel?.doSignOut()
        }
    }

    /** Sets up view pager with tabs for main navigation. */
    private fun initView() {
        pagerAdapter.source = this
        binding?.apply {
            viewPager.adapter = pagerAdapter
            tabs.setupWithViewPager(viewPager)
        }
    }

    /** Initializes snow animation effect. */
    private fun setupSnowView() {
        viewModel?.setupSnow()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_main, menu)
        return true
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if(data?.hasExtra(IntentConstant.MAIN_TAB_POSITION) == true) {
            val position = data.getStringExtra(IntentConstant.MAIN_TAB_POSITION)
            redirectToTabPosition(position)
        }
    }

    override fun onPrepareOptionsMenu(menu: Menu?): Boolean {
        val actionView = menu?.findItem(R.id.action_selected_region)?.actionView
        val regionText = "${userStorage.getUserRegion().id?.uppercase()} - ${
            userStorage.getUserVerticalType().code?.uppercase(
                Locale.getDefault()
            )
        }"
        actionView?.findViewById<TextView>(R.id.textViewRegion)?.text = regionText
        actionView?.setOnClickListener { customMenu?.showAsDropDown(it) }
        return super.onPrepareOptionsMenu(menu)
    }

    /** Navigates to specific tab position. */
    private fun redirectToTabPosition(position: String?) {
        position.notNullOrEmpty {
            binding?.viewPager?.currentItem = it.toInt()
        }
    }

    /** Configures power menu with app options. */
    private fun setupPowerMenu() {
        customMenu = CustomPowerMenu.Builder<CustomMenuItem, CustomMenuAdapter>(this, CustomMenuAdapter())
            .addItem(CustomMenuItem(string(R.string.generate_title)))
            .addItem(CustomMenuItem(string(R.string.settings_title)))
            .addItem(CustomMenuItem(string(R.string.sign_out_label), true))
            .setOnMenuItemClickListener(OnMenuItemClickListener<CustomMenuItem> { position, _ ->
                when(position) {
                    0 -> navigateToSettings(AppConstant.GENERATE_BATCH)
                    1 -> navigateToSettings()
                    2 -> {
                        showProgressDialog(getString(R.string.signing_out_label))
                        viewModel?.doSignOut()
                        customMenu?.dismiss()
                    }
                }
            })
            .setAnimation(MenuAnimation.DROP_DOWN)
            .setLifecycleOwner(this)
            .build()
    }

    /** Navigates to settings screen and finishes current activity. */
    private fun navigateToSettings(param: String? = null) {
        if(param != null) {
            navigator.toSettings(param)
        } else {
            navigator.toSettings()
        }
        customMenu?.dismiss()
        finish()
    }

    /** Handles successful sign out event. */
    @Subscribe
    fun onSignOutSuccessEvent(event: SignOutSuccessEvent) {
        dismissProgressDialog()
        navigator.toSignIn()
        finish()
    }

    /** Handles failed sign out event. */
    @Subscribe
    fun onSignOutFailedEvent(event: SignOutFailedEvent) {
        dismissProgressDialog()
        showShortToast(event.error ?: string(R.string.err_something_when_wrong))
    }

    override val totalViewCount: Int = TOTAL_MAIN_MENU

    /** Returns appropriate fragment for tab position based on user region. */
    override fun getItemTypeAtPosition(position: Int): Fragment {
        return if(userStorage.isUserRegionID()) {
            when(position) {
                MainMenuTab.IDMENU.BACKLOG.index -> BatchFragment.newInstance(BatchStatus.IN_BACKLOG)
                MainMenuTab.IDMENU.PICKED.index -> BatchFragment.newInstance(BatchStatus.PICKED)
                MainMenuTab.IDMENU.PHOTO.index -> BatchFragment.newInstance(BatchStatus.QA_DONE)
                MainMenuTab.IDMENU.READY.index -> BatchFragment.newInstance(BatchStatus.READY_FOR_PACKING)
                MainMenuTab.IDMENU.PACKED.index -> PackedFragment()
                else -> PlaceholderFragment.newInstance(position + 1)
            }
        } else {
            when(position) {
                MainMenuTab.MAIN.BACKLOG.index -> BatchFragment.newInstance(BatchStatus.IN_BACKLOG)
                MainMenuTab.MAIN.PICKED.index -> BatchFragment.newInstance(BatchStatus.PICKED)
                MainMenuTab.MAIN.READY.index -> BatchFragment.newInstance(BatchStatus.READY_FOR_PACKING)
                MainMenuTab.MAIN.PACKED.index -> PackedFragment()
                else -> PlaceholderFragment.newInstance(position + 1)
            }
        }
    }

    /** Enables fragment caching for better performance. */
    override fun cacheFragment(): Boolean = true

    /** Returns tab title for the given position. */
    override fun getPageTitleAtPosition(position: Int): CharSequence {
        return viewModel?.getTitleTabAtIndex(position) ?: "Empty"
    }

    /** Simple placeholder fragment for empty tab positions. */
    class PlaceholderFragment : Fragment() {
        override fun onCreateView(
            inflater: LayoutInflater,
            container: ViewGroup?,
            savedInstanceState: Bundle?
        ): View? = inflater.inflate(R.layout.section_fragment, container, false)

        companion object {
            private const val ARG_SECTION_NUMBER = "section_number"

            fun newInstance(sectionNumber: Int) = PlaceholderFragment().apply {
                arguments = Bundle().apply { putInt(ARG_SECTION_NUMBER, sectionNumber) }
            }
        }
    }
}
