package co.styletheory.ops.outbound.android.feature.qualitySwap.view

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Parcelable
import android.view.View
import android.view.WindowManager
import android.widget.AdapterView
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.ActivityQualitySwapBinding
import co.styletheory.ops.outbound.android.feature.photoManager.view.PhotoManagerActivity
import co.styletheory.ops.outbound.android.feature.qualitySwap.event.QualitySwapUIEvent
import co.styletheory.ops.outbound.android.feature.qualitySwap.viewModel.QualitySwapViewModel
import co.styletheory.ops.outbound.android.general.adapter.SpinnerAdapter
import co.styletheory.ops.outbound.android.general.base.BaseActivity
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.model.enums.AttachPhotoType
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.util.EdgeToEdgeUtil
import co.styletheory.ops.outbound.android.util.IntentConstant
import co.styletheory.ops.outbound.android.util.PermissionHandlerActivity
import co.styletheory.ops.outbound.android.util.PermissionUtils
import co.styletheory.ops.outbound.android.util.RequestCode
import co.styletheory.ops.outbound.android.util.getParcelableArrayListExtraProvider
import co.styletheory.ops.outbound.android.util.getParcelableExtraProvider
import co.styletheory.ops.outbound.android.util.getSerializableExtraProvider
import co.styletheory.ops.outbound.android.util.notNull
import co.styletheory.ops.outbound.android.viewModelComponent.FooterButtonViewModel
import com.styletheory.camera.screens.home.PhotosPickerActivity
import com.styletheory.camera.shared.models.Photo
import org.greenrobot.eventbus.Subscribe
import org.parceler.Parcels

/**
 * Created by Yoga C. Pranata on 18/06/20.
 * Android Engineer
 */
@Suppress("UNUSED_PARAMETER")
class QualitySwapActivity : BaseActivity<ActivityQualitySwapBinding, QualitySwapViewModel>(), PermissionHandlerActivity {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        activityComponent?.inject(this)
        bindContentView(R.layout.activity_quality_swap)
        setupSwapNowButton()
        setupSwapReasonSpinner()
        initExtras()
        setupToolbar()
        viewModel?.setupInfoVisibility()

        // Handle back button press with the new API
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                navigator.finishWithResult()
            }
        })
    }

    /**
     * Sets up keyboard behavior to ensure focused views are visible when keyboard appears
     * and the footer button is always visible above the keyboard
     */
    private fun setupKeyboardBehavior() {
        // Find the EditText for notes
        binding?.root?.findViewById<View>(R.id.notesContent)?.let { notesEditText ->
            // Set focus change listener to ensure the view is visible when focused
            notesEditText.setOnFocusChangeListener { view, hasFocus ->
                if(hasFocus) {
                    // Delay scrolling to the view to ensure keyboard is fully shown
                    Handler(Looper.getMainLooper()).postDelayed({
                        binding?.root?.findViewById<View>(R.id.scrollView)?.let { scrollView ->
                            if(scrollView is androidx.core.widget.NestedScrollView) {
                                // Scroll to the focused view
                                scrollView.smoothScrollTo(0, view.top)
                            }
                        }
                    }, 300)
                }
            }
        }

        // Use the EdgeToEdgeUtil to handle keyboard insets properly
        binding?.root?.let { rootView ->
            binding?.footerContainer?.let { footerView ->
                binding?.scrollView?.let { scrollView ->
                    EdgeToEdgeUtil.handleKeyboardInsets(rootView, footerView, scrollView)
                }
            }
        }
    }

    /**
     * Sets up edge-to-edge display with proper insets handling for all system bars and keyboard
     */
    override fun setupEdgeToEdgeInsets() {
        // First call the base implementation to set up basic edge-to-edge display
        super.setupEdgeToEdgeInsets()

        // Configure window to adjust resize mode to ensure content stays above keyboard
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)

        // Apply insets to toolbar
        binding?.toolbarWrapper?.toolbar?.let { toolbar ->
            EdgeToEdgeUtil.fixToolbarInsets(this, toolbar)
        }

        // Ensure the footer is always visible by setting its elevation and background
        binding?.footerContainer?.let { footer ->
            footer.elevation = resources.getDimension(R.dimen.space_medium)
            footer.setBackgroundColor(ContextCompat.getColor(this, R.color.white))
            footer.bringToFront()

            // Apply bottom insets to footer
            EdgeToEdgeUtil.applyBottomInsets(footer, true, 1.0f)
        }

        // Use the setupKeyboardBehavior method to handle keyboard insets
        setupKeyboardBehavior()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if(resultCode == RESULT_OK && requestCode == RequestCode.PHOTO_MANAGER) {
            data.notNull {
                if(it.hasExtra(IntentConstant.PRODUCT_PHOTO_RESULT_QAIMAGES_EXTRA_CODE)) {
                    val takenImages = it.getStringArrayListExtra(IntentConstant.PRODUCT_PHOTO_RESULT_QAIMAGES_EXTRA_CODE)?.toList()
                        ?: listOf()

                    if(takenImages.isNotEmpty()) {
                        viewModel?.photos = takenImages
                        showShortToast(string(R.string.quality_swap_success_upload_photos))
                        Handler(Looper.getMainLooper()).postDelayed({ viewModel?.checkRequiredFields() }, IntentConstant.DELAY_TIME_500)
                    }
                }
            }
        } else if(requestCode == RequestCode.REQ_PHOTOS) {
            val photos = data?.getParcelableArrayListExtraProvider<Photo>(PhotosPickerActivity.EXTRA_PHOTOS) ?: return
            navigateToPhotoManager(photos)
        }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding?.toolbarWrapper?.toolbar as Toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setHomeAsUpIndicator(R.drawable.ic_close)
        binding?.toolbarVM = viewModel?.toolbarViewModel()
        binding?.toolbarWrapper?.toolbar?.setBackgroundColor(ContextCompat.getColor(this, R.color.white))
        binding?.toolbarWrapper?.toolbarTitle?.setTextColor(ContextCompat.getColor(this, R.color.very_dark_gray))

        // Apply proper insets to the toolbar for edge-to-edge display
        binding?.toolbarWrapper?.toolbar?.let { toolbar ->
            EdgeToEdgeUtil.fixToolbarInsets(this, toolbar)
        }
    }

    private fun setupSwapNowButton() {
        binding?.footerButtonVM = viewModel?.footerButtonViewModel()
        binding?.swapNowButton?.footerButton?.setBackgroundResource(R.drawable.selector_button_rect_green_gray_rad_5)
    }

    private fun initExtras() {
        intent?.getStringExtra(IntentConstant.BATCH_ID).notNull { viewModel?.batchId = it }
        intent?.getStringExtra(IntentConstant.SHIPMENT_ID).notNull { viewModel?.shipmentId = it }
        intent?.getParcelableExtraProvider<Parcelable>(IntentConstant.SWAP_ITEM).notNull { viewModel?.swapItem = Parcels.unwrap(it) }
        getSerializableExtraProvider<BatchStatus>(intent, IntentConstant.BATCH_STATUS).notNull { viewModel?.batchStatus = it }
    }

    private fun setupSwapReasonSpinner() {
        val reasonList = viewModel?.getFailReasonList()
            ?: resources.getStringArray(R.array.apparel_fail_reason_list)
        val adapter = SpinnerAdapter(this, reasonList, true)
        binding?.swapReasonSpinner?.adapter = adapter
        binding?.swapReasonSpinner?.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onNothingSelected(p0: AdapterView<*>?) {
                // Do Nothing
            }

            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                val selectedText = parent?.getItemAtPosition(position).toString()
                viewModel?.selectedFailReason?.set(selectedText)
                viewModel?.selectedSwapReasonPosition(position)
            }

        }
    }

    private fun navigateToPhotoManager(photos: ArrayList<Photo>) {
        navigator.withExtras(
            PhotoManagerActivity.createExtras(
                viewModel?.shipmentId
                    .orEmpty(), viewModel?.swapItem?.item, AttachPhotoType.PICKED_PHOTO, photos
            )
        )
            .startForResult(RequestCode.PHOTO_MANAGER)
            .toPhotoManager()
    }

    // Implementation of PermissionHandlerActivity interface
    override fun getActivity(): AppCompatActivity = this

    override fun onAllPermissionsGranted(requestCode: Int) {
        EdgeToEdgeUtil.preparePhotoPickerActivityEdgeToEdge()
        PhotosPickerActivity.launch(this, RequestCode.REQ_PHOTOS)
    }

    private fun showPhotoGalleryOrTakePictureActivity() {
        // Use the new permission utility to handle permissions
        PermissionUtils.launchPhotoPicker(this, RequestCode.PHOTO_MANAGER)
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        // Let the permission utility handle the result
        PermissionUtils.handlePermissionResult(this, requestCode, permissions, grantResults)
    }

    @Subscribe
    fun onUploadPhotoClicked(event: QualitySwapUIEvent.TakePictureEvent) {
        showPhotoGalleryOrTakePictureActivity()
    }

    @Subscribe
    fun onClickSwapNowButton(event: FooterButtonViewModel.CompleteButtonEvent) {
        val swapItem = SwapItem(
            viewModel?.swapItem?.item, viewModel?.swapItem?.boxId.orEmpty(),
            viewModel?.swapItem?.shipmentId.orEmpty(), viewModel?.swapItem?.customerEmail.orEmpty(),
            viewModel?.selectedFailReason?.get().orEmpty(), viewModel?.inputNotes?.get().orEmpty()
        )

        navigator.withExtraResult(IntentConstant.BOTTOM_SHEET_ARGS, IntentConstant.SHOW_BOTTOMSHEET)
            .withExtraResult(IntentConstant.SWAP_ITEM, Parcels.wrap(swapItem))
            .finishWithResult()

        viewModel?.clearAllFields()
    }

}