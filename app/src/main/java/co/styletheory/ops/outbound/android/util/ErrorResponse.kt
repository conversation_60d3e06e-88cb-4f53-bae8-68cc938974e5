package co.styletheory.ops.outbound.android.util

import co.styletheory.ops.outbound.android.model.ErrorBody
import com.google.gson.Gson
import javax.inject.Inject

/**
 * Created by Yoga C. Pranata on 2019-09-11.
 * Android Engineer
 */
class ErrorResponse @Inject constructor() {

    fun getErrorBodyDescription(error: String?): String {
        return try {
            val errors = error?.substringAfter("value:")?.trim()
            val errorBody = Gson().from<PERSON>son(errors, ErrorBody::class.java)
            errorBody.description
        } catch(exception: Exception) {
            error
        }.orEmpty()
    }

    fun getErrorBodyCode(error: String?): String {
        return try {
            val errors = error?.substringAfter("value:")?.trim()
            val errorBody = Gson().from<PERSON>son(errors, ErrorBody::class.java)
            errorBody.code
        } catch(exception: Exception) {
            error
        }.orEmpty()
    }
}