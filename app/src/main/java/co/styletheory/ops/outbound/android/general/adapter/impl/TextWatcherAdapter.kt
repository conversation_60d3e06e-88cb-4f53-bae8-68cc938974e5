package co.styletheory.ops.outbound.android.general.adapter.impl

import android.text.Editable
import android.text.TextWatcher

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 02 November 2017.
 * Description
 *
 * <EMAIL>
 */
open class TextWatcherAdapter : TextWatcher {
    override fun afterTextChanged(editable: Editable) {
        //Not implemented yet
    }

    override fun beforeTextChanged(char: CharSequence, arg1: Int, arg2: Int, arg3: Int) {
        //Not implemented yet
    }

    override fun onTextChanged(char: CharSequence, start: Int, before: Int, count: Int) {
        //Not implemented yet
    }
}