package co.styletheory.ops.outbound.android.view

import android.content.Context
import android.database.DataSetObserver
import android.util.AttributeSet
import android.view.View
import android.widget.LinearLayout
import android.widget.ListAdapter

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 26 October 2017.
 * Description
 *
 * <EMAIL>
 */
class AdapterLinearLayout @JvmOverloads constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private var listAdapter: ListAdapter? = null
    private var convertViews: Array<View?>? = null

    fun setAdapter(listAdapter: ListAdapter) {
        this.listAdapter = listAdapter

        refreshView()

        listAdapter.registerDataSetObserver(object : DataSetObserver() {
            override fun onChanged() {
                super.onChanged()
                refreshView()
            }
        })
    }

    fun refreshView() {
        convertViews = arrayOfNulls(size = listAdapter!!.count)
        removeAllViews()
        for (i in 0 until listAdapter!!.count) {
            convertViews?.set(i, listAdapter!!.getView(i, convertViews?.get(i), this))
            addView(convertViews?.get(i))
        }
    }
}