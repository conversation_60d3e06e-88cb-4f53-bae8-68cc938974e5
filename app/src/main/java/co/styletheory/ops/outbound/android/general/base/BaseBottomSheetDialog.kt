package co.styletheory.ops.outbound.android.general.base

import android.app.Dialog
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.Toast
import androidx.annotation.*
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import co.styletheory.ops.outbound.android.BR
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.general.Navigator
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.injection.component.DaggerFragmentComponent
import co.styletheory.ops.outbound.android.injection.component.FragmentComponent
import co.styletheory.ops.outbound.android.injection.module.FragmentModule
import co.styletheory.ops.outbound.android.util.ViewUtils
import co.styletheory.ops.outbound.android.util.getStatusBarHeight
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import javax.inject.Inject

/**
 * Created by Yoga C. Pranata on 13/05/20.
 * Android Engineer
 */
abstract class BaseBottomSheetDialog<VB : ViewDataBinding, VM : ViewModel> : BottomSheetDialogFragment() {

    @Inject
    lateinit var eventBus: EventBus

    @Inject
    lateinit var navigator: Navigator

    @Inject
    lateinit var userStorage: UserStorage

    var fragmentComponent: FragmentComponent? = null
        get() {
            if(field == null) {
                field = DaggerFragmentComponent.builder()
                        .activityComponent((activity as? BaseActivity<*, *>)?.activityComponent)
                        .fragmentModule(FragmentModule(this))
                        .build()
            }
            return field
        }

    @Inject
    @JvmField
    var viewModel: VM? = null

    @JvmField
    var binding: VB? = null

    /**
     * Getting Layout ID from activity
     * */
    abstract fun getLayoutId(): Int

    /**
     * This method will be executed after view has been create in fragment
     */
    protected abstract fun onLoadBottomSheetDialog(view: View, saveInstance: Bundle?)

    private fun registerEventBus() {
        if(!eventBus.isRegistered(this)) {
            eventBus.register(this)
        }
    }

    private fun unRegisterEventBus() {
        if(eventBus.isRegistered(this)) {
            eventBus.unregister(this)
        }
    }

    override fun onStart() {
        super.onStart()
        viewModel?.afterInject()
        registerEventBus()
    }

    override fun onStop() {
        unRegisterEventBus()
        super.onStop()
    }

    override fun onDestroy() {
        binding = null
        viewModel = null
        fragmentComponent = null
        super.onDestroy()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        if(viewModel == null) {
            throw IllegalStateException("viewModel must already be set via injection")
        }
        binding = DataBindingUtil.inflate(layoutInflater, getLayoutId(), container, false)
        binding?.setVariable(BR.viewModel, viewModel)
        binding?.executePendingBindings()
        return binding?.root
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState) as BottomSheetDialog

        dialog.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            val bottomSheet = bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout
            bottomSheet.background = ResourcesCompat.getDrawable(resources, R.drawable.bg_dialog_white_top_rounded, null)
            val behavior = BottomSheetBehavior.from(bottomSheet)
            behavior.state = BottomSheetBehavior.STATE_EXPANDED
            behavior.addBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
                override fun onSlide(bottomSheet: View, slideOffset: Float) {
                    // no need implementatiton
                }

                override fun onStateChanged(bottomSheet: View, newState: Int) {
                    if(newState == BottomSheetBehavior.STATE_DRAGGING) {
                        behavior.state = BottomSheetBehavior.STATE_EXPANDED
                    }
                }
            })
        }
        return dialog
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        onLoadBottomSheetDialog(view, savedInstanceState)
    }

    @Suppress("UNUSED_PARAMETER")
    @Subscribe
    fun onEventMainThread(event: BaseEvent) {
        // Do Nothing
    }

    fun getWindowHeight(): Int {
        return activity?.let { ViewUtils.getDisplayHeight(it) } ?: 0 - (context?.getStatusBarHeight() ?: 0)
    }

    fun showShortToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }

    fun dimen(@DimenRes resId: Int): Int = resources.getDimensionPixelSize(resId)
    fun color(@ColorRes resId: Int): Int = ContextCompat.getColor(requireContext(), resId)
    fun drawable(@DrawableRes resId: Int): Drawable? = ContextCompat.getDrawable(requireContext(), resId)
    fun integer(@IntegerRes resId: Int): Int = resources.getInteger(resId)
    fun string(@StringRes resId: Int): String = resources.getString(resId)
}