package co.styletheory.ops.outbound.android.networking

import co.styletheory.android.network.core.APIManager
import co.styletheory.android.network.core.RequestBundle
import co.styletheory.android.network.core.RequestResult
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.StyletheoryOpsApplication
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.BatchConfig
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.LogisticProvider
import co.styletheory.ops.outbound.android.model.Session
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.model.User
import co.styletheory.ops.outbound.android.model.response.CheckSwapAvailabilityResponse
import co.styletheory.ops.outbound.android.model.response.OrderGojekResponse
import co.styletheory.ops.outbound.android.model.response.SendShipmentResponse
import co.styletheory.ops.outbound.android.model.response.ShipmentResponse
import co.styletheory.ops.outbound.android.resources.AccuracySwapItemResource
import co.styletheory.ops.outbound.android.resources.AttachPhotoToItemResource
import co.styletheory.ops.outbound.android.resources.BatchConfigsResource
import co.styletheory.ops.outbound.android.resources.BatchDetailResource
import co.styletheory.ops.outbound.android.resources.BatchItemResource
import co.styletheory.ops.outbound.android.resources.BatchResource
import co.styletheory.ops.outbound.android.resources.BeginBatchResource
import co.styletheory.ops.outbound.android.resources.CancelGojekResource
import co.styletheory.ops.outbound.android.resources.CheckSwapItemAvailabilityResource
import co.styletheory.ops.outbound.android.resources.CompleteBatchResource
import co.styletheory.ops.outbound.android.resources.CreateImageUrlResource
import co.styletheory.ops.outbound.android.resources.CreatePasswordResource
import co.styletheory.ops.outbound.android.resources.GenerateBatchListResource
import co.styletheory.ops.outbound.android.resources.GetAccuracySwapItemResource
import co.styletheory.ops.outbound.android.resources.LogisticProviderResource
import co.styletheory.ops.outbound.android.resources.OrderGojekResource
import co.styletheory.ops.outbound.android.resources.SendShipmentResource
import co.styletheory.ops.outbound.android.resources.ShipmentListResource
import co.styletheory.ops.outbound.android.resources.ShipmentReceivedResource
import co.styletheory.ops.outbound.android.resources.SignInResource
import co.styletheory.ops.outbound.android.resources.SignOutResource
import co.styletheory.ops.outbound.android.resources.SwapBoxItemResource
import co.styletheory.ops.outbound.android.resources.SwapItemResource
import co.styletheory.ops.outbound.android.resources.UploadImageResource
import co.styletheory.ops.outbound.android.resources.UserDetailResource
import retrofit2.Call
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 28 October 2017.
 * Description
 *
 * <EMAIL>
 */
class DataService {

    init {
        StyletheoryOpsApplication.instance?.appComponent?.inject(this)
    }

    @Inject
    lateinit var apiManager: APIManager

    fun login(resource: SignInResource, callback: RequestResult<GraphQLData<Session>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun logout(resource: SignOutResource, callback: RequestResult<GraphQLData<Boolean>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun createPassword(resource: CreatePasswordResource, callback: RequestResult<GraphQLData<Session>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun userDetail(resource: UserDetailResource, callback: RequestResult<GraphQLData<User>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun fetchBatch(resource: BatchResource, callback: RequestResult<GraphQLData<List<Batch>>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun fetchLogisticProvider(resource: LogisticProviderResource, callback: RequestResult<GraphQLData<List<LogisticProvider>>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun fetchBatchDetail(resource: BatchDetailResource, callback: RequestResult<GraphQLData<Batch>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun updateBatchItemStatus(resource: BatchItemResource, callback: RequestResult<GraphQLData<Void>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun completeBatch(resource: CompleteBatchResource, callback: RequestResult<GraphQLData<Void>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun beginBatch(resource: BeginBatchResource, callback: RequestResult<GraphQLData<Void>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun swapItem(resource: SwapItemResource, callback: RequestResult<GraphQLData<Void>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun swapBoxItem(resource: SwapBoxItemResource, callback: RequestResult<GraphQLData<Void>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun checkSwapItemAvailability(resource: CheckSwapItemAvailabilityResource, callback: RequestResult<GraphQLData<CheckSwapAvailabilityResponse>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun fetchImageUploadUrl(resource: CreateImageUrlResource, callback: RequestResult<GraphQLData<String>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun attachImageToItem(resource: AttachPhotoToItemResource, callback: RequestResult<GraphQLData<Void>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun uploadUrlImage(resource: UploadImageResource, callback: RequestResult<Void, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun fetchShipmentByLogisticProvider(resource: ShipmentListResource, callback: RequestResult<GraphQLData<ShipmentResponse>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun sendShipment(resource: SendShipmentResource, callback: RequestResult<GraphQLData<SendShipmentResponse>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun orderGojekShipments(resource: OrderGojekResource, callback: RequestResult<GraphQLData<OrderGojekResponse>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun cancelGojekShipments(resource: CancelGojekResource, callback: RequestResult<GraphQLData<List<Batch>>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun generateBatchList(resource: GenerateBatchListResource, callback: RequestResult<GraphQLData<Batch>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun fetchBatchConfigs(resource: BatchConfigsResource, callback: RequestResult<GraphQLData<List<BatchConfig.Result>>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun accuracySwapItem(resource: AccuracySwapItemResource, callback: RequestResult<GraphQLData<Void>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun getAccuracySwapItem(resource: GetAccuracySwapItemResource, callback: RequestResult<GraphQLData<BatchItem>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }

    fun markShipmentAsReceivedByWarehouse(resource: ShipmentReceivedResource,callback: RequestResult<GraphQLData<Void>, String>) {
        apiManager.load(RequestBundle(resource, callback))
    }
}