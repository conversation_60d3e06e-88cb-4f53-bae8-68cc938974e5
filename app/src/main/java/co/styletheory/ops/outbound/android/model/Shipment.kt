package co.styletheory.ops.outbound.android.model

import co.styletheory.ops.outbound.android.model.enums.DeliveryType
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.model.enums.ShipmentStatus
import co.styletheory.ops.outbound.android.util.DateConstant.PATTERN.HHmm
import co.styletheory.ops.outbound.android.util.DateConstant.PATTERN.ddMMMMMyyyy
import co.styletheory.ops.outbound.android.util.IntentConstant
import com.google.gson.annotations.SerializedName
import org.joda.time.DateTime
import org.parceler.Parcel

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 03 November 2017.
 * Description
 *
 * <EMAIL>
 */

@Parcel
data class Shipment(
    val id: String = "",
    val boxId: String? = "",
    val shipmentType: String = "",
    val deliveryType: String = "",
    val status: String = "",
    val tracking: Tracking? = null,
    val logisticProvider: LogisticProvider? = null,
    @SerializedName("time")
    val shipmentTime: Time? = null,
    val shipmentRecipient: Recipient? = null,
    val createdAt: DateTime? = null,
    val shipmentReview: Review? = null,
    val logisticMethod: String = "",
    val shipmentIsLate: Boolean = false,
    val courier: Courier? = null,
    val region: String = "",
    val shipmentItems: List<String> = emptyList(),
    val businessMethod: String = "",
    val procedureType: String = "",
    val isExpedited: Boolean = false,
    var isComplete: Boolean = false,
    val items: List<BatchItem> = emptyList(),
    val customer: Customer? = null,
    val soldTo: String = "",
    val box: Box? = null
) {

    @Parcel
    data class Courier(
        @SerializedName("name")
        val name: String = "",
        @SerializedName("phone")
        val phone: String = "",
        @SerializedName("image")
        val imageUrl: String = ""
    )

    @Parcel
    data class Recipient(
        val address: String = "",
        val buildingFloor: String = "",
        @SerializedName("unit_number")
        val unitNumber: String = "",
        val postal: String = "",
        val city: String = "",
        val name: String = "",
        val phoneNumber: String = "",
        val country: String = "",
        val instructions: String = ""
    )

    @Parcel
    data class Review(val review: String = "", val reviewOption: String = "")

    @Parcel
    data class Time(
        val scheduledPickup: EstimateTime? = null,
        val estimatedDelivery: EstimateTime? = null,
        val actualPickup: DateTime? = null,
        val actualDelivery: DateTime? = null,
        val received: DateTime? = null
    ) {

        fun getScheduledPickupFormattedText(): String {
            return if(scheduledPickup?.startDateTime?.toLocalDate()?.isEqual(scheduledPickup.endDateTime?.toLocalDate()) == true) {
                scheduledPickup.startDateTime.toString(HHmm) + " - " + scheduledPickup.endDateTime?.toString(HHmm) + ", " +
                    scheduledPickup.startDateTime.toString(ddMMMMMyyyy)
            } else {
                scheduledPickup?.startDateTime?.toString(HHmm) + ", " + scheduledPickup?.startDateTime?.toString(ddMMMMMyyyy) + " - " +
                    scheduledPickup?.endDateTime?.toString(HHmm) + ", " + scheduledPickup?.endDateTime?.toString(ddMMMMMyyyy)
            }
        }

    }

    @Parcel
    data class Tracking(val id: String = "", val status: String = "")

    fun isQAPickedItemsAvailableSG(): Boolean {
        val count = items.count {
            it.status == ProductStatus.QA_PASSED ||
                it.status == ProductStatus.QA_FAILED ||
                it.status == ProductStatus.QA ||
                it.status == ProductStatus.PICKED ||
                it.status == ProductStatus.PHOTO_QA_DONE
        }
        return count != 0
    }

    fun isQAPickedItemsAvailableID(): Boolean {
        val count = items.count {
            it.status == ProductStatus.QA_PASSED ||
                it.status == ProductStatus.QA_FAILED ||
                it.status == ProductStatus.QA ||
                it.status == ProductStatus.PICKED
        }
        return count != 0
    }

    fun isPhotoItemsAvailable(): Boolean {
        val count = items.count {
            it.status == ProductStatus.PHOTO_QA ||
                it.status == ProductStatus.PHOTO_QA_DONE ||
                it.status == ProductStatus.QA_PASSED
        }
        return count != 0
    }

    fun isQABoxItemComplete(): Boolean {
        val count = items.count { it.status == ProductStatus.QA_PASSED || it.status == ProductStatus.QA_FAILED }
        return count == items.size
    }

    fun isProcedureTypeRegular(): Boolean {
        return (procedureType.lowercase() == IntentConstant.PROCEDURE_TYPE.REGULAR)
    }

    fun isProcedureTypeRegularWithLock(): Boolean {
        return (procedureType.lowercase() == IntentConstant.PROCEDURE_TYPE.REGULAR_WITH_LOCK)
    }

    fun isProcedureTypeOneToOne(): Boolean {
        return (procedureType.lowercase() == IntentConstant.PROCEDURE_TYPE.ONE_TO_ONE)
    }

    fun isProcedureTypeOneToOneWithLock(): Boolean {
        return (procedureType.lowercase() == IntentConstant.PROCEDURE_TYPE.ONE_TO_ONE_WITH_LOCK)
    }

    fun isBusinessMethodReselling(): Boolean {
        return (businessMethod.lowercase() == IntentConstant.BUSINESS_METHOD_RESELLING)
    }

    fun isBusinessMethodSurpriseKit(): Boolean {
        return (businessMethod.lowercase() == IntentConstant.BUSINESS_METHOD_SURPRISE_KIT)
    }

    fun isShipmentMarkedAsReceived(): Boolean {
        return (deliveryType == DeliveryType.RETURN.toString() && status == ShipmentStatus.RECEIVED_BY_WAREHOUSE.toString())
    }
}