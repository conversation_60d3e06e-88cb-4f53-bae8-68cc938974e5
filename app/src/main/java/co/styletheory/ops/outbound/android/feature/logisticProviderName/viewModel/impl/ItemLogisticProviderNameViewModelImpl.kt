package co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel.impl

import androidx.databinding.ObservableInt
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.StyletheoryOpsApplication
import co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel.ItemLogisticProviderNameViewModel
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.model.LogisticProvider
import co.styletheory.ops.outbound.android.util.notNull
import com.styletheory.android.mvvm.general.binding.ObservableText
import javax.inject.Inject

/**
 * Created by <PERSON> on 30/12/21.
 */
class ItemLogisticProviderNameViewModelImpl @Inject constructor() : BaseViewModel<ItemLogisticProviderNameViewModelImpl>(), ItemLogisticProviderNameViewModel {
    override val providerTextColor = ObservableInt()
    override val providerName = ObservableText()
    override val backgroundColorResource = ObservableInt()

    init {
        StyletheoryOpsApplication.directInject(this)
    }

    override fun bindViewModel(logisticProvider: LogisticProvider?): ItemLogisticProviderNameViewModelImpl {
        backgroundColorResource.set(R.drawable.rect_black_26_rad_4)
        if(logisticProvider?.getProvider() == null) {
            providerName.set(logisticProvider?.name)
            providerTextColor.set(R.color.black_26)
        } else {
            logisticProvider.getProvider()?.notNull {
                providerName.set(string(it.stringRes))
                providerTextColor.set(it.colorRes)
            }
        }
        return this
    }
}