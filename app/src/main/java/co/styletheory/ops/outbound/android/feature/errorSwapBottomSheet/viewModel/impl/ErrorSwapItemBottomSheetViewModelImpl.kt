package co.styletheory.ops.outbound.android.feature.errorSwapBottomSheet.viewModel.impl

import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.errorSwapBottomSheet.event.ErrorSwapItemBottomSheetUIEvent
import co.styletheory.ops.outbound.android.feature.errorSwapBottomSheet.viewModel.ErrorSwapItemBottomSheetViewModel
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.model.enums.ErrorSwapType
import co.styletheory.ops.outbound.android.model.enums.SwapType
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.util.AppConstant.ERROR_OUTBOUND_ACCURACY_SWAP.CANNOT_SCAN_PART_ITEM
import co.styletheory.ops.outbound.android.util.AppConstant.ERROR_OUTBOUND_ACCURACY_SWAP.DIFFERENT_STYLE_AND_SIZE
import co.styletheory.ops.outbound.android.util.AppConstant.ERROR_OUTBOUND_ACCURACY_SWAP.NO_NETWORK
import co.styletheory.ops.outbound.android.util.ErrorResponse
import com.styletheory.android.mvvm.general.binding.ObservableText
import javax.inject.Inject

/**
 * Created by Eminarti Sianturi on 23/06/20.
 */
class ErrorSwapItemBottomSheetViewModelImpl @Inject constructor() : BaseViewModel<ErrorSwapItemBottomSheetViewModelImpl>(), ErrorSwapItemBottomSheetViewModel {

    @Inject
    lateinit var errorResponse: ErrorResponse

    @Inject
    lateinit var dataService: DataService

    override val errorMessage = ObservableText()
    override val errorButtonLabel = ObservableText()
    override var swapType: String = SwapType.QUALITY_SWAP.swapType

    var errorSwapType = ErrorSwapType.CONFIRM

    override fun bindError(error: String) {
        errorMessage.set(errorResponse.getErrorBodyDescription(error))
        setErrorButtonLabel(error)
    }

    override fun onClose() {
        eventBus.post(ErrorSwapItemBottomSheetUIEvent.OnCloseClicked)
    }

    override fun onClickErrorButton() {
        if(swapType == SwapType.QUALITY_SWAP.swapType) {
            eventBus.post(ErrorSwapItemBottomSheetUIEvent.OnConfirmQualitySwapClick)
        } else {
            if(errorSwapType == ErrorSwapType.SCAN_OTHER_ITEM || errorSwapType == ErrorSwapType.SCAN_PARENT_ITEM) {
                eventBus.post(ErrorSwapItemBottomSheetUIEvent.OnScanAgainClick)
            } else if (errorSwapType == ErrorSwapType.RETRY) {
                eventBus.post(ErrorSwapItemBottomSheetUIEvent.OnRetryClick)
            } else if (errorSwapType == ErrorSwapType.CONFIRM) {
                eventBus.post(ErrorSwapItemBottomSheetUIEvent.OnConfirmClick)
            }
        }
    }

    private fun setErrorButtonLabel(error: String) {
        val code = errorResponse.getErrorBodyCode(error)
        when(code) {
            DIFFERENT_STYLE_AND_SIZE -> {
                errorButtonLabel.set(R.string.error_scan_other_item)
                errorSwapType = ErrorSwapType.SCAN_OTHER_ITEM
            }
            CANNOT_SCAN_PART_ITEM -> {
                errorButtonLabel.set(R.string.error_scan_parent_item)
                errorSwapType = ErrorSwapType.SCAN_PARENT_ITEM
            }
            NO_NETWORK -> {
                errorButtonLabel.set(R.string.error_retry)
                errorMessage.set(R.string.error_swap_message_no_network)
                errorSwapType = ErrorSwapType.RETRY
            }
            else -> {
                errorButtonLabel.set(R.string.error_confirm)
                errorSwapType = ErrorSwapType.CONFIRM
            }
        }
    }

}