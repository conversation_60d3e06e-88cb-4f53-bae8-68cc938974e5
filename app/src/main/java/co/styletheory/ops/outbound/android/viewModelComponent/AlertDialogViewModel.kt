package co.styletheory.ops.outbound.android.viewModelComponent

import android.view.View
import androidx.databinding.ObservableInt
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel.impl.LogisticProviderNameViewModelImpl
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.event.DismissDialogEvent
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseInjectedViewModel
import co.styletheory.ops.outbound.android.model.LogisticProvider
import co.styletheory.ops.outbound.android.util.notNull
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 25 January 2018.
 * Description
 *
 * <EMAIL>
 */
class AlertDialogViewModel : BaseInjectedViewModel() {

    companion object {
        const val TAG = "AlertDialogViewModel"
    }

    @Inject
    lateinit var logisticProviderNameSectionVM: LogisticProviderNameViewModelImpl

    private var logisticProvider: LogisticProvider? = null
    val dialogTitle = ObservableString()
    val dialogBody = ObservableString()
    val batchRegion = ObservableString()
    val batchVertical = ObservableString()
    val batchTime = ObservableString()
    val rightButtonText = ObservableString()
    val leftButtonText = ObservableString()
    var leftButtonVisibility = ObservableInt(View.GONE)
    val logisticNameVisibility = ObservableInt(View.GONE)
    var rightButtonOnClickListener: OnClickListener? = null
    var leftButtonOnClickListener: OnClickListener? = null
    fun setupLogisticProviderNameViewModel(): LogisticProviderNameViewModelImpl = logisticProviderNameSectionVM

    fun bindViewModel(totalBox: Int, logisticProvider: LogisticProvider?) {
        this.logisticProvider = logisticProvider
        logisticProvider.notNull {
            dialogTitle.set(string(R.string.send_shipment_dialog_title, totalBox.toString(), it.name))
        }
        dialogBody.set(string(R.string.send_shipment_dialog_body))
        rightButtonText.set(string(R.string.send_box_label))
        leftButtonText.set(string(R.string.cancel_label))
        setupLogisticVisibility(logisticProvider)
    }

    fun setTitle(title: String): AlertDialogViewModel {
        dialogTitle.set(title)
        return this
    }

    fun setBody(body: String): AlertDialogViewModel {
        dialogBody.set(body)
        return this
    }

    fun setBatchGeneration(region: String?, vertical: String?, time: String?): AlertDialogViewModel {
        this.batchRegion.set(region)
        this.batchVertical.set(vertical)
        this.batchTime.set(time)
        return this
    }

    fun setRightButtonText(text: String): AlertDialogViewModel {
        rightButtonText.set(text)
        return this
    }

    fun setLeftButtonText(text: String): AlertDialogViewModel {
        leftButtonVisibility.set(View.VISIBLE)
        leftButtonText.set(text)
        return this
    }

    fun setLeftButtonClickListener(listener: OnClickListener): AlertDialogViewModel {
        this.leftButtonOnClickListener = listener
        return this
    }

    fun setRightButtonClickListener(listener: OnClickListener): AlertDialogViewModel {
        this.rightButtonOnClickListener = listener
        return this
    }

    fun rightButtonClicked() {
        rightButtonOnClickListener?.onClick()
    }

    fun leftButtonClicked() {
        if(leftButtonOnClickListener != null) {
            leftButtonOnClickListener?.onClick()
        } else {
            eventBus.post(DismissDialogEvent(TAG))
        }
    }

    private fun setupLogisticVisibility(provider: LogisticProvider?) {
        logisticProviderNameSectionVM.bindViewModelProvider(provider)
        logisticProvider.notNull {
            logisticNameVisibility.set(View.VISIBLE)
        }
    }

    interface OnClickListener {
        fun onClick()
    }
}