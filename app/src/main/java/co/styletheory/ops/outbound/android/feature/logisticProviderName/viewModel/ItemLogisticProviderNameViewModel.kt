package co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel

import androidx.databinding.ObservableInt
import co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel.impl.ItemLogisticProviderNameViewModelImpl
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.LogisticProvider
import com.styletheory.android.mvvm.general.binding.ObservableText

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 31 October 2017.
 * Description
 *
 * <EMAIL>
 */
interface ItemLogisticProviderNameViewModel : ViewModel {
    val providerTextColor: ObservableInt
    val providerName: ObservableText
    val backgroundColorResource: ObservableInt

    fun bindViewModel(logisticProvider: LogisticProvider?): ItemLogisticProviderNameViewModelImpl
}