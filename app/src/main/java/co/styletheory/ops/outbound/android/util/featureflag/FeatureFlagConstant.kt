package co.styletheory.ops.outbound.android.util.featureflag

/**
 * Created by <PERSON> C. <PERSON> on 02/04/19.
 * Android Engineer
 */
object FeatureFlagConstant {
    const val OUTBOUND_BARCODE_BAGS = "outbound-barcode-bags"
    const val OUTBOUND_BARCODE_APPAREL = "outbound-barcode-apparel"
    const val OUTBOUND_ACCURACY_SWAP_ITEM = "outbound-accuracy-swap"
    const val OUTBOUND_OLD_QUALITY_CHECK_SWAP_ITEM = "outbound-old-quality-check-swap"
    const val DISABLE_QUALITY_SWAP_RESELLING = "disable-quality-swap-reselling"
    const val ENABLE_OUTBOUND_PICKING_SWAP = "enable-outbound-picking-swap"
    const val INTERNAL_APP_SNOWFALL = "internal-app-snowfall"
}