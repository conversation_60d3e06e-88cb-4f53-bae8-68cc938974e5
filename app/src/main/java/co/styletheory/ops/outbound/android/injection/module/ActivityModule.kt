package co.styletheory.ops.outbound.android.injection.module

import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentManager
import co.styletheory.ops.outbound.android.general.Navigator
import co.styletheory.ops.outbound.android.general.dialog.CustomProgressDialog
import co.styletheory.ops.outbound.android.injection.scope.PerActivity
import dagger.Module
import dagger.Provides

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 19 October 2017.
 * Description
 *
 * <EMAIL>
 */

@Module
class ActivityModule (val activity: AppCompatActivity) {

    @Provides
    @PerActivity
    fun provideActivity(): AppCompatActivity = activity

    @Provides
    @PerActivity
    fun provideFragmentManager(): FragmentManager = activity.supportFragmentManager

    @Provides
    @PerActivity
    fun provideNavigator(): Navigator = Navigator(activity)

    @Provides
    @PerActivity
    fun provideProgressDialog(): CustomProgressDialog = CustomProgressDialog()
}