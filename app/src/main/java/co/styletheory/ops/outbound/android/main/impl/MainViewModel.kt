package co.styletheory.ops.outbound.android.main.impl

import androidx.databinding.ObservableInt
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 20 October 2017.
 * Description
 *
 * <EMAIL>
 */
interface MainViewModel : ViewModel {
    val snowVisibility: ObservableInt

    fun setupSnow()
    fun getTitleTabAtIndex(index: Int): CharSequence
    fun doSignOut()
}