package co.styletheory.ops.outbound.android.injection.component

import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import co.styletheory.ops.outbound.android.feature.batch.view.BatchFragment
import co.styletheory.ops.outbound.android.feature.errorSwapBottomSheet.view.ErrorSwapItemBottomSheetDialog
import co.styletheory.ops.outbound.android.feature.goSend.view.GoSendFragment
import co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.view.LoadingScanBarcodeBottomSheetDialog
import co.styletheory.ops.outbound.android.feature.packed.view.PackedChildFragment
import co.styletheory.ops.outbound.android.feature.packed.view.PackedFragment
import co.styletheory.ops.outbound.android.feature.packing.view.PackingDetailItemFragment
import co.styletheory.ops.outbound.android.feature.photoDetail.view.PhotoDetailItemFragment
import co.styletheory.ops.outbound.android.feature.qcDetail.view.QcDetailItemFragment
import co.styletheory.ops.outbound.android.feature.scanBarcodeBottomSheet.view.ScanBarcodeBottomSheetDialog
import co.styletheory.ops.outbound.android.feature.swapConfirmationBottomSheet.view.SwapConfirmationBottomSheetDialogFragment
import co.styletheory.ops.outbound.android.injection.module.FragmentModule
import co.styletheory.ops.outbound.android.injection.module.ViewModelModule
import co.styletheory.ops.outbound.android.injection.scope.PerFragment
import dagger.Component

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 19 October 2017.
 * Description
 *
 * <EMAIL>
 */


@PerFragment
@Component(dependencies = [ActivityComponent::class], modules = [FragmentModule::class, ViewModelModule::class])
interface FragmentComponent {

    fun getFragmentActivity(): FragmentActivity
    fun getChildFragmentManager(): FragmentManager

    // Create Inject Methods Here
    fun inject(fragment: BatchFragment)

    fun inject(fragment: PackedFragment)
    fun inject(fragment: QcDetailItemFragment)
    fun inject(fragment: PackedChildFragment)
    fun inject(fragment: PhotoDetailItemFragment)
    fun inject(fragment: PackingDetailItemFragment)
    fun inject(fragment: GoSendFragment)

    // BottomSheet Dialog
    fun inject(dialogFragment: ScanBarcodeBottomSheetDialog)
    fun inject(dialogFragment: SwapConfirmationBottomSheetDialogFragment)
    fun inject(dialogFragment: LoadingScanBarcodeBottomSheetDialog)
    fun inject(dialogFragment: ErrorSwapItemBottomSheetDialog)
}