package co.styletheory.ops.outbound.android.feature.photoDetail.viewModel

import co.styletheory.ops.outbound.android.general.adapter.PagerSource
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 15 December 2017.
 * Description
 *
 * <EMAIL>
 */
interface PhotoDetailViewModel : ViewModel, PagerSource {
    var shipmentId: String
    var batchItem: BatchItem?

    fun batchName(): String
    fun setBatchId(batchId: String)
    fun setBatchStatus(batchStatus: BatchStatus)
    fun generalToolbarViewModel(): GeneralToolbarViewModel
    fun changeToolbarSubtitle(subtitle: String?)
    fun fetchBatchDetail(callback: Result<Void?, String?>?)
    fun isAllBatchItemShipmentCompletedPhoto(): Boolean
    fun completeBatch(callback: Result<Void?, String?>?)
}
