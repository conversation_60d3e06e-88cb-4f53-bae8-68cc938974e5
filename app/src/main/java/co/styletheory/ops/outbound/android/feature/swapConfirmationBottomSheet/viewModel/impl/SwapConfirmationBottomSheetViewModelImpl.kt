package co.styletheory.ops.outbound.android.feature.swapConfirmationBottomSheet.viewModel.impl

import android.view.View
import androidx.databinding.ObservableInt
import co.styletheory.android.network.core.RequestResult
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.swapConfirmationBottomSheet.event.SwapConfirmationBottomDialogNetworkEvent
import co.styletheory.ops.outbound.android.feature.swapConfirmationBottomSheet.event.SwapConfirmationBottomDialogUIEvent
import co.styletheory.ops.outbound.android.feature.swapConfirmationBottomSheet.viewModel.SwapConfirmationBottomSheetViewModel
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.model.enums.SwapType
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.AccuracySwapItemResource
import co.styletheory.ops.outbound.android.resources.BatchItemResource
import co.styletheory.ops.outbound.android.resources.SwapBoxItemResource
import co.styletheory.ops.outbound.android.resources.SwapItemResource
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.util.notNull
import com.styletheory.android.mvvm.general.binding.ObservableText
import javax.inject.Inject

class SwapConfirmationBottomSheetViewModelImpl @Inject constructor() : BaseViewModel<SwapConfirmationBottomSheetViewModel>(), SwapConfirmationBottomSheetViewModel {

    override val itemFoundVisibility = ObservableInt(View.VISIBLE)
    override val loadingStateVisibility = ObservableInt(View.GONE)

    override var swapType: String = SwapType.QUALITY_SWAP.swapType
    override var swapItem: SwapItem = SwapItem()
    override var batchId: String = ""

    override val oldItemImage = ObservableString()
    override val oldItemName = ObservableString()
    override val oldItemDesignerName = ObservableString()
    override val oldItemSizeNote = ObservableString()
    override val oldItemStyleCategory = ObservableString()
    override val newItemImage = ObservableString()
    override val newItemName = ObservableString()
    override val newItemDesignerName = ObservableString()
    override val newItemSizeNote = ObservableString()
    override val newItemStyleCategory = ObservableString()
    override val swapInformation = ObservableText()

    private var oldItem: BatchItem? = null
    private lateinit var newItem: BatchItem
    private var oldItemBoxId = ""
    private var oldItemShipmentId = ""
    private var customerEmail = ""

    @Inject
    lateinit var dataService: DataService

    @Inject
    lateinit var errorResponse: ErrorResponse

    override fun setItems(swapItem: SwapItem, newItem: BatchItem, swapType: String, batchId: String) {
        this.swapItem = swapItem
        this.swapType = swapType
        this.batchId = batchId
        this.customerEmail = swapItem.customerEmail

        this.oldItem = swapItem.item
        this.oldItemBoxId = swapItem.boxId
        this.oldItemShipmentId = swapItem.shipmentId

        this.newItem = newItem

        oldItem.notNull { item ->
            item.style?.gallery?.firstOrNull().notNull { oldItemImage.set(it) }
            item.style?.name?.notNull { oldItemName.set(it) }
            item.style?.designer?.name.notNull { oldItemDesignerName.set(it) }
            item.style?.primaryCategory.notNull { oldItemStyleCategory.set(it) }
            oldItemSizeNote.set("${item.labelSize} [${item.order}]")
        }

        newItem.apply {
            style?.gallery?.firstOrNull().notNull { newItemImage.set(it) }
            style?.name?.notNull { newItemName.set(it) }
            style?.designer?.name.notNull { newItemDesignerName.set(it) }
            style?.primaryCategory.notNull { newItemStyleCategory.set(it) }
            newItemSizeNote.set("$labelSize [$order]")
        }

        showItemFoundState()
    }

    override fun onConfirmButtonClick() {
        showLoadingState()
        confirmSwap()
    }

    override fun onCloseClick() {
        eventBus.post(SwapConfirmationBottomDialogUIEvent.OnCloseClick)
    }

    override fun updateItemToFailed(oldItem: BatchItem) {
        val resource = BatchItemResource(
                batchId,
                swapItem.shipmentId,
                listOf(swapItem.item?.id.orEmpty()),
                ProductStatus.QA_FAILED,
                swapItem.failCategory,
                swapItem.failReason
        )
        dataService.updateBatchItemStatus(
                resource,
                RequestResult {
                    onSuccess { callSwapItemInBox(oldItem) }
                    onError {
                        showItemFoundState()
                        eventBus.post(SwapConfirmationBottomDialogNetworkEvent.OnSwapFailed(errorResponse.getErrorBodyDescription(it)))
                    }
                }
        )
    }

    private fun showLoadingState() {
        loadingStateVisibility.set(View.VISIBLE)
        itemFoundVisibility.set(View.GONE)
    }

    private fun showItemFoundState() {
        loadingStateVisibility.set(View.GONE)
        itemFoundVisibility.set(View.VISIBLE)
        showSwapInfo()
    }

    private fun showSwapInfo() {
        if(swapType == SwapType.ACCURACY_SWAP.swapType) {
            swapInformation.set(R.string.item_found_swap_note)
        } else {
            swapInformation.set(R.string.swap_by_scan_qc_info)
        }
    }

    private fun confirmSwap() {
        oldItem.notNull {
            if(swapType == SwapType.ACCURACY_SWAP.swapType) {
                callAccuracySwap(it.id)
            } else {
                updateItemToFailed(it)
            }
        }
    }

    private fun callAccuracySwap(oldItemId: String) {
        dataService.accuracySwapItem(
                AccuracySwapItemResource(oldItemId, oldItemBoxId, oldItemShipmentId, newItem.id),
                RequestResult {
                    onSuccess { eventBus.post(SwapConfirmationBottomDialogNetworkEvent.OnSwapSuccess) }
                    onError {
                        showItemFoundState()
                        eventBus.post(SwapConfirmationBottomDialogNetworkEvent.OnSwapFailed(errorResponse.getErrorBodyDescription(it)))
                    }
                }
        )
    }

    private fun callSwapItemInBox(oldItem: BatchItem) {
        dataService.swapBoxItem(
                SwapBoxItemResource(oldItemBoxId, oldItem.id, newItem.id, customerEmail),
                RequestResult {
                    onSuccess { callSwapItemInShipment() }
                    onError {
                        showItemFoundState()
                        eventBus.post(SwapConfirmationBottomDialogNetworkEvent.OnSwapFailed(errorResponse.getErrorBodyDescription(it)))
                    }
                }
        )
    }

    private fun callSwapItemInShipment() {
        dataService.swapItem(
                SwapItemResource(oldItemShipmentId, oldItem?.id.orEmpty(), newItem.id),
                RequestResult {
                    onSuccess { eventBus.post(SwapConfirmationBottomDialogNetworkEvent.OnSwapSuccess) }
                    onError {
                        showItemFoundState()
                        eventBus.post(SwapConfirmationBottomDialogNetworkEvent.OnSwapFailed(errorResponse.getErrorBodyDescription(it)))
                    }
                }
        )
    }

}