package co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.impl

import androidx.fragment.app.Fragment
import co.styletheory.android.network.core.RequestResult
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.qcDetail.event.QcDetailUIEvent
import co.styletheory.ops.outbound.android.feature.qcDetail.view.QcDetailItemFragment
import co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.QcDetailViewModel
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.listener.OnKeyListener
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.model.enums.SwapType
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.BatchDetailResource
import co.styletheory.ops.outbound.android.resources.BatchItemResource
import co.styletheory.ops.outbound.android.resources.CompleteBatchResource
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.util.notNull
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 31 October 2017.
 * Description
 *
 * <EMAIL>
 */

class QcDetailViewModelImpl @Inject constructor() : BaseViewModel<QcDetailViewModelImpl>(), QcDetailViewModel, OnKeyListener {

    @Inject
    lateinit var toolbarViewModel: GeneralToolbarViewModel
    @Inject
    lateinit var dataService: DataService
    @Inject
    lateinit var errorResponse: ErrorResponse
    @Inject
    lateinit var userStorage: UserStorage

    init {
        viewModelClass = QcDetailViewModelImpl::class
    }

    var batch: Batch? = null
    val shipments = mutableListOf<Shipment>()

    override var batchId: String = ""
    override var batchStatus: BatchStatus? = null
    override var shipmentId: String = ""
    override var batchItem: BatchItem? = null
    override val scannedRfidText = ObservableString()
    override val onEditTextKeyListener: OnKeyListener = this

    override var swapItem: SwapItem = SwapItem()
    override var swapType: SwapType = SwapType.QUALITY_SWAP
    override var rfid: String = ""
    override var failCategory: String = ""
    override var failReason: String = ""
    override var customerEmail: String = ""

    override fun batchName(): String = batch?.name.orEmpty()
    override fun toolbarViewModel(): GeneralToolbarViewModel = toolbarViewModel

    val updateStatusCallback = RequestResult<GraphQLData<Void>, String> {
        onSuccess { eventBus.post(QcDetailUIEvent.OnUpdateItemSuccess) }
        onError { eventBus.post(QcDetailUIEvent.OnErrorSwapItem(it.orEmpty())) }
    }

    override fun changeToolbarTitle(title: String) {
        toolbarViewModel.title.set(title)
    }

    override fun changeToolbarSubtitle(subtitle: String) {
        toolbarViewModel.subtitle.set(subtitle)
    }

    override fun fetchBatchDetail(callback: Result<Void?, String>?) {
        dataService.fetchBatchDetail(
                BatchDetailResource(batchId),
                RequestResult {
                    onSuccess {
                        it?.data?.result.notNull { batch ->
                            onFetchBatchDetailSuccess(batch)
                        }
                        callback?.success(null)
                    }
                    onError { callback?.failure(errorResponse.getErrorBodyDescription(it)) }
                }
        )
    }

    fun onFetchBatchDetailSuccess(batch: Batch) {
        shipments.clear()
        this.batch = batch

        for(data in batch.shipments) {
            if(userStorage.isUserRegionID()) {
                if(data.isQAPickedItemsAvailableID()) {
                    this.shipments.add(data)
                }
            } else {
                if(data.isQAPickedItemsAvailableSG()) {
                    this.shipments.add(data)
                }
            }
        }

        changeToolbarTitle(string(R.string.qc_title).format(batch.name))
        if(shipments.size > 0) {
            changeToolbarSubtitle("1 of ${shipments.size} Box")
        } else {
            changeToolbarSubtitle("empty picked box")
        }
    }

    override fun completeBatch(callback: Result<Void?, String>?) {
        dataService.completeBatch(
                CompleteBatchResource(batchId, CompleteBatchResource.Type.QA),
                RequestResult {
                    onSuccess { callback?.success(null) }
                    onError { callback?.failure(errorResponse.getErrorBodyDescription(it)) }
                }
        )
    }

    override fun isAllBatchItemShipmentCompletedQA(): Boolean {
        if(totalViewCount == 0) return false
        var completeByViewModel = true
        var completeByModel = true
        for(item in shipments) {
            if(!item.isComplete) {
                completeByViewModel = false
            }
            if(!item.isQABoxItemComplete()) {
                completeByModel = false
            }
        }
        return (completeByModel || completeByViewModel)
    }

    override fun updateItemToFailed() {
        val resource = BatchItemResource(
                batchId,
                swapItem.shipmentId,
                listOf(swapItem.item?.id.orEmpty()),
                ProductStatus.QA_FAILED,
                swapItem.failCategory,
                swapItem.failReason
        )
        dataService.updateBatchItemStatus(resource, updateStatusCallback)
    }

    //region PAGER SOURCE
    override val totalViewCount: Int get() = shipments.size

    override fun getItemTypeAtPosition(position: Int): Fragment {
        val shipment = shipments[position]
        val rackSection = if(shipment.items.isNotEmpty()) {
            batch?.getRackSectionFromBatchItemId(shipment.items[0].id)
        } else {
            ""
        }
        return QcDetailItemFragment.newInstance(shipment, batch?.rack?.name, rackSection, batchId)
    }

    override fun cacheFragment(): Boolean = true
    override fun getPageTitleAtPosition(position: Int): CharSequence = string(R.string.box_title).format(position + 1)
    //endregion

    //scanner
    override fun onKeyTapped() {
        if(featureFlagUtil.isBarcodeSettingIsOn()) {
            checkScannedText()
        }
    }

    fun checkScannedText() {
        val position = shipments.indexOfFirst { shipment ->
            shipment.items.map { it.rfid }.contains(scannedRfidText.get())
        }

        if(position >= 0) {
            checkBatchItemStatus(position)
        } else {
            setErrorMessage()
        }
    }

    fun checkBatchItemStatus(position: Int) {
        val batchItem = batch?.items?.firstOrNull { it.rfid.equals(scannedRfidText.get(), true) }

        if(batchItem != null && !batchItem.isInQA()) {
            eventBus.post(QcDetailUIEvent.OnScannedRfidTextNotFound(string(R.string.warn_rfid_can_not_process_title), string(R.string.warn_rfid_current_status_message).format(batchItem.status, scannedRfidText.get())))
        } else {
            eventBus.post(QcDetailUIEvent.OnScannedRfidText(position, scannedRfidText.get()))
        }
    }

    fun setErrorMessage() {
        val batchItem = batch?.items?.firstOrNull { it.rfid.equals(scannedRfidText.get(), true) }

        if(batchItem != null && !batchItem.isInQA()) {
            eventBus.post(QcDetailUIEvent.OnScannedRfidTextNotFound(string(R.string.warn_rfid_can_not_process_title), string(R.string.warn_rfid_current_status_message).format(batchItem.status, scannedRfidText.get())))
        } else {
            eventBus.post(QcDetailUIEvent.OnScannedRfidTextNotFound(string(R.string.warn_rfid_notfound_title), string(R.string.warn_rfid_notfound_message).format(scannedRfidText.get())))
        }
    }
    //endregion
}
