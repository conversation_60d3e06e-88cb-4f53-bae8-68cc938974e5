package co.styletheory.ops.outbound.android.viewModelComponent

import android.view.View
import androidx.databinding.ObservableInt
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.event.PreviewImageEvent
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.model.enums.ResellingInventoryType
import com.styletheory.android.mvvm.general.binding.ObservableText
import javax.inject.Inject

/**
 * Created by <PERSON> on 21/10/20.
 */
class PhotoWithLabelViewModel @Inject constructor() : BaseViewModel<PhotoWithLabelViewModel>() {
    init {
        viewModelClass = PhotoWithLabelViewModel::class
    }

    val imageUrl = ObservableString()

    val resellingInventoryType = ObservableText()
    val resellingInventoryTypeVisibility = ObservableInt(View.GONE)
    val photoLabelBgColor = ObservableInt()

    fun imageClick() {
        eventBus.post(PreviewImageEvent(imageUrl.get()))
    }

    fun bindPhotoWithLabel(image: String?, resellingInventory: String?) {
        imageUrl.set(image)

        resellingInventory?.let { type ->
            when(type) {
                ResellingInventoryType.WAREHOUSE.type -> {
                    resellingInventoryTypeVisibility.set(View.VISIBLE)
                    photoLabelBgColor.set(R.drawable.bg_orange_top_radius_4)
                    resellingInventoryType.set(R.string.warehouse_sale_label)
                }
                else -> {
                    resellingInventoryTypeVisibility.set(View.GONE)
                }
            }
        } ?: run {
            resellingInventoryTypeVisibility.set(View.GONE)
        }
    }

}