package co.styletheory.ops.outbound.android.model.enums

import androidx.annotation.ColorRes
import androidx.annotation.StringRes
import co.styletheory.ops.outbound.android.R

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 24 October 2017.
 * Description
 *
 * <EMAIL>
 */
enum class LogisticProviderName constructor(@ColorRes val colorRes: Int, @StringRes val stringRes: Int){
    GOJEK(R.color.dark_blue, R.string.gojek_logistic_provider),
    SINGAPORE_POST(R.color.light_green, R.string.singapore_post_logistic_provider),
    HONESTBEE(R.color.orange, R.string.honestbee_logistic_provider),
    SELF_COLLECT(R.color.black, R.string.self_collect_logistic_provider),
    STYLETHEORY_SG(R.color.blue_0cc, R.string.styletheory_logistic_provider),
    PIING(R.color.orange_bright, R.string.piing_logistic_provider),
    UNKNOWN(R.color.color_primary, R.string.unknown_logistic_provider);
}