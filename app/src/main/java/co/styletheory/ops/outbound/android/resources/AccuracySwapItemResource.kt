package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import retrofit2.Call
import retrofit2.Retrofit

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 22/06/20.
 */
class AccuracySwapItemResource constructor(private val oldItemUuid: String,
                                           private val oldItemBoxId: String,
                                           private val oldItemShipmentId: String,
                                           private val newItemUuid: String) : GraphQLResource<GraphQLData<Void>>() {

    override fun graphQLRequest(): GraphQLRequest = GraphQLRequestList.ACCURACY_SWAP_ITEM

    override fun bodyParameter(): MutableMap<String, Any> {
        bodyParameter["oldItemUuid"] = oldItemUuid
        bodyParameter["oldItemBoxId"] = oldItemBoxId
        bodyParameter["oldItemShipmentId"] = oldItemShipmentId
        bodyParameter["newItemUuid"] = newItemUuid
        return bodyParameter
    }

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<Void>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.accuracySwapItem(uri, createRequestBody()))
    }
}