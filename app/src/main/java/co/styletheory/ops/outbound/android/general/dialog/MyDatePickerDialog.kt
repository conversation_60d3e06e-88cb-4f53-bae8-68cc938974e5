package co.styletheory.ops.outbound.android.general.dialog

import android.app.DatePickerDialog
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.widget.DatePicker
import androidx.fragment.app.DialogFragment
import java.util.*

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 21 February 2018.
 * Description
 *
 * <EMAIL>
 */
class MyDatePickerDialog : DialogFragment(), DatePickerDialog.OnDateSetListener {

    var callback: DatePickerDialog.OnDateSetListener? = null
    var calendar = Calendar.getInstance()
    var maxDate: Long = 0
    var minDate: Long = 0

    lateinit var dialogContext: Context

    companion object {
        fun newInstance(context: Context, calendar: Calendar, callback: DatePickerDialog.OnDateSetListener): MyDatePickerDialog {
            val instance = MyDatePickerDialog()
            instance.dialogContext = context
            instance.callback = callback
            instance.calendar = calendar
            return instance
        }

        fun newInstance(context: Context, calendar: Calendar, minDate: Long, maxDate: Long, callback: DatePickerDialog.OnDateSetListener): MyDatePickerDialog {
            val instance = MyDatePickerDialog()
            instance.dialogContext = context
            instance.callback = callback
            instance.calendar = calendar
            instance.maxDate = maxDate
            instance.minDate = minDate
            return instance
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialogPicker = DatePickerDialog(dialogContext, this,
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH))

        if(maxDate > 0) {
            dialogPicker.datePicker.maxDate = maxDate
        }

        if(minDate > 0) {
            dialogPicker.datePicker.minDate = minDate
        }

        return dialogPicker

    }

    override fun onDateSet(view: DatePicker, year: Int, monthOfYear: Int, dayOfMonth: Int) {
        callback?.onDateSet(view, year, monthOfYear, dayOfMonth)
    }


}