package co.styletheory.ops.android.viewModelComponent.recyclerItemDecoration

/**
 * Created by <PERSON> on 26-02-2025.
 */
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import co.styletheory.ops.outbound.android.R

/**
 * Custom horizontal divider decoration for RecyclerView
 */
class HorizontalDividerItemDecoration(
    private val context: Context,
    private val dividerColor: Int,
    private val dividerSizeResId: Int
) : RecyclerView.ItemDecoration() {

    private val paint = Paint().apply {
        color = dividerColor
        style = Paint.Style.FILL
    }

    private val dividerHeight: Int by lazy {
        context.resources.getDimensionPixelSize(dividerSizeResId)
    }

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        // Add space at the bottom of each item except the last one
        val position = parent.getChildAdapterPosition(view)
        if(position < state.itemCount - 1) {
            outRect.bottom = dividerHeight
        }
    }

    override fun onDraw(canvas: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        val left = parent.paddingLeft
        val right = parent.width - parent.paddingRight

        // Draw divider below each item except the last one
        for(i in 0 until parent.childCount - 1) {
            val child = parent.getChildAt(i)
            val params = child.layoutParams as RecyclerView.LayoutParams

            val top = child.bottom + params.bottomMargin
            val bottom = top + dividerHeight

            canvas.drawRect(left.toFloat(), top.toFloat(), right.toFloat(), bottom.toFloat(), paint)
        }
    }

    /**
     * Builder class for CustomHorizontalDividerDecoration
     */
    class Builder(private val context: Context) {
        private var dividerColor: Int = 0
        private var dividerSizeResId: Int = R.dimen.size_divider

        fun color(color: Int): Builder {
            this.dividerColor = color
            return this
        }

        fun sizeResId(sizeResId: Int): Builder {
            this.dividerSizeResId = sizeResId
            return this
        }

        fun build(): HorizontalDividerItemDecoration {
            return HorizontalDividerItemDecoration(context, dividerColor, dividerSizeResId)
        }
    }
}