package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import retrofit2.Call
import retrofit2.Retrofit

class ShipmentReceivedResource(
    private val shipmentId: String
) : GraphQLResource<GraphQLData<Void>>() {
    override fun graphQLRequest(): GraphQLRequest = GraphQLRequestList.MARK_SHIPMENT_AS_RECEIVED

    override fun bodyParameter(): MutableMap<String, Any> {
        bodyParameter["shipmentId"] = shipmentId
        return bodyParameter
    }

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<Void>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.markShipmentAsReceivedByWarehouse(uri, createRequestBody()))
    }
}