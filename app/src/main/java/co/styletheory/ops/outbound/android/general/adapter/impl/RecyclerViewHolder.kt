package co.styletheory.ops.outbound.android.general.adapter.impl

import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.RecyclerView
import co.styletheory.ops.outbound.android.BR
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 24 October 2017.
 * Description
 *
 * <EMAIL>
 */
class RecyclerViewHolder(var binding: ViewDataBinding) : RecyclerView.ViewHolder(binding.root) {
    fun bindViewModel(viewModel: ViewModel?) {
        binding.setVariable(BR.viewModel, viewModel)
        binding.executePendingBindings()
    }
}