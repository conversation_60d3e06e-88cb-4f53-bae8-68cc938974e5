package co.styletheory.ops.outbound.android.util

/**
 * styletheory-ops-outbound-android
 * Created by dwi<PERSON><PERSON><PERSON> on 19 October 2017.
 * Description
 *
 * <EMAIL>
 */
object IntentConstant {
    const val BATCH_STATUS = "batch_status"
    const val BATCH_ID = "batch_id"
    const val BATCH = "batch"
    const val SHIPMENT = "shipment"
    const val RACK_ID = "rack_id"
    const val RACK_NAME = "rack_name"
    const val RACK_SECTION = "rack_section"
    const val BATCH_ITEM = "batch_item"
    const val PICKED_DETAIL_VIEW_MODEL = "shipment"
    const val SELECTED_IMAGE_POSITION_EXTRA_KEY = "selectedImagePositionExtraKey"
    const val IMAGE_SOURCE_URLS_EXTRA_KEY = "imageSourceUrlsExtraKey"
    const val PRODUCT_PHOTO_RESULT_QAIMAGES_EXTRA_CODE = "productPhotoResultQaImages"
    const val PRODUCT_PHOTO_RESULT_PRODUCT_EXTRA_CODE = "productPhotoResultProduct"
    const val ITEM = "item"
    const val SHIPMENT_ID = "shipmentId"
    const val ATTACH_PHOTO_TYPE = "attachPhotoType"
    const val LOGISTIC_PROVIDER = "logistic_provider"
    const val SELECTED_DATE = "selected_date"
    const val LOGISTIC_ORDERED = "logisticOrdered"
    const val SETTINGS_GENERATE = "settingsGenerate"
    const val BOTTOM_SHEET_ARGS = "bottomSheetArguments"
    const val SHOW_BOTTOMSHEET = "showBottomSheet"
    const val MAIN_TAB_POSITION = "mainTabPosition"
    const val RESELLING_ITEM = "purchased"
    const val BUSINESS_METHOD_SURPRISE_KIT = "surprisekit"
    const val BUSINESS_METHOD_RESELLING = "reselling"

    const val NEW_SWAP_ITEM = "newSwapItem"
    const val OLD_SWAP_ITEM = "oldSwapItem"

    const val SWAP_ITEM = "swap-item"
    const val SWAP_TYPE = "swap-type"
    const val ERROR_SWAP_ITEM = "error-swap-item"
    const val NEW_ITEM_UUID = "new_item_uuid"

    object PROCEDURE_TYPE {
        const val REGULAR = "regular"
        const val REGULAR_WITH_LOCK = "regularwithlock"
        const val ONE_TO_ONE_WITH_LOCK = "onetoonewithlock"
        const val ONE_TO_ONE = "onetoone"
    }

    const val DELAY_TIME_200: Long = 200
    const val DELAY_TIME_500: Long = 500
}