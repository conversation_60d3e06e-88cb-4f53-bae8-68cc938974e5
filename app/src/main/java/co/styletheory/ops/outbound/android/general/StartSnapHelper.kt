package co.styletheory.ops.outbound.android.general

import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSnapHelper
import androidx.recyclerview.widget.OrientationHelper
import androidx.recyclerview.widget.RecyclerView
import timber.log.Timber

/**
 * Created by amitshekhar
 */
class StartSnapHelper : LinearSnapHelper() {
    private var mVerticalHelper: OrientationHelper? = null
    private var mHorizontalHelper: OrientationHelper? = null

    override fun attachToRecyclerView(recyclerView: RecyclerView?) {
        try {
            super.attachToRecyclerView(recyclerView)
        } catch(ie: IllegalStateException) {
            Timber.i(ie)
        }
    }

    override fun calculateDistanceToFinalSnap(layoutManager: RecyclerView.LayoutManager, targetView: View): IntArray {
        val out = IntArray(2)
        out[0] = 0
        if(layoutManager.canScrollHorizontally())
            out[0] = distanceToStart(targetView, getHorizontalHelper(layoutManager))
        out[1] = 0
        if(layoutManager.canScrollVertically())
            out[1] = distanceToStart(targetView, getVerticalHelper(layoutManager))
        return out
    }

    private fun distanceToStart(targetView: View, helper: OrientationHelper): Int {
        return helper.getDecoratedStart(targetView) - helper.startAfterPadding
    }

    private fun getVerticalHelper(layoutManager: RecyclerView.LayoutManager): OrientationHelper {
        return mVerticalHelper ?: OrientationHelper.createVerticalHelper(layoutManager)
    }

    private fun getHorizontalHelper(layoutManager: RecyclerView.LayoutManager): OrientationHelper {
        return mHorizontalHelper ?: OrientationHelper.createHorizontalHelper(layoutManager)
    }

    override fun findSnapView(layoutManager: RecyclerView.LayoutManager): View? {
        return if(layoutManager is LinearLayoutManager) {
            if(layoutManager.canScrollHorizontally())
                getStartView(layoutManager, getHorizontalHelper(layoutManager))
            else
                getStartView(layoutManager, getVerticalHelper(layoutManager))
        } else super.findSnapView(layoutManager)
    }

    private fun getStartView(layoutManager: RecyclerView.LayoutManager, helper: OrientationHelper): View? {
        if(layoutManager is LinearLayoutManager) {
            val firstChild = layoutManager.findFirstVisibleItemPosition()
            val isLastItem = layoutManager.findLastCompletelyVisibleItemPosition() == layoutManager.getItemCount() - 1
            if(firstChild == RecyclerView.NO_POSITION || isLastItem) return null
            val child = layoutManager.findViewByPosition(firstChild)
            return if(helper.getDecoratedEnd(child) >= helper.getDecoratedMeasurement(child) / 2 && helper.getDecoratedEnd(child) > 0) {
                child
            } else {
                if(layoutManager.findLastCompletelyVisibleItemPosition() == layoutManager.getItemCount() - 1)
                    null
                else
                    layoutManager.findViewByPosition(firstChild + 1)
            }
        }
        return super.findSnapView(layoutManager)
    }
}
