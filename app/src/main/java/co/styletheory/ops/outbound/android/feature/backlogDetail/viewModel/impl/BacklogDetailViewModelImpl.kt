package co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel.impl

import co.styletheory.android.network.core.RequestResult
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.backlogDetail.event.BacklogItemUIEvent
import co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel.BacklogDetailItemViewModel
import co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel.BacklogDetailViewModel
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.listener.OnKeyListener
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.injection.scope.PerActivity
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.BatchDetailResource
import co.styletheory.ops.outbound.android.resources.CompleteBatchResource
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.util.notNull
import co.styletheory.ops.outbound.android.util.notNullOrEmpty
import co.styletheory.ops.outbound.android.viewModelComponent.FooterButtonViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 24 October 2017.
 * Description
 *
 * <EMAIL>
 */

@PerActivity
class BacklogDetailViewModelImpl @Inject constructor() : BaseViewModel<BacklogDetailViewModelImpl>(), BacklogDetailViewModel, OnKeyListener {

    private val BACKLOG_DETAIL_ITEM = 0
    private val COMPLETE_BUTTON_ITEM = 1

    init {
        viewModelClass = BacklogDetailViewModelImpl::class
    }

    @Inject
    lateinit var toolbarViewModel: GeneralToolbarViewModel

    @Inject
    lateinit var footerButtonViewModel: FooterButtonViewModel

    @Inject
    lateinit var userStorage: UserStorage

    @Inject
    lateinit var dataService: DataService

    @Inject
    lateinit var errorResponse: ErrorResponse

    override var batch: Batch? = null
    override val batchName = ObservableString()
    override var batchStatus: BatchStatus? = null
    override val batchId = ObservableString()
    override val scannedRfidText = ObservableString()
    override var detailItems: ArrayList<BacklogDetailItemViewModel> = arrayListOf()
    override val onEditTextKeyListener: OnKeyListener = this

    override fun toolbarViewModel(): GeneralToolbarViewModel = toolbarViewModel

    override fun afterInject() {
        super.afterInject()
        footerButtonViewModel.buttonTitle.set(string(R.string.complete_batch_label))
        footerButtonViewModel.enable.set(false)
    }

    override fun fetchBatchDetail(callback: Result<Void?, String?>?) {
        fetchBatchDetail(false, callback)
    }

    override fun refreshBatchDetail(callback: Result<Void?, String?>?) {
        fetchBatchDetail(true, callback)
    }

    fun mapBatchDetailItem(batch: Batch) {
        val items = if(userStorage.isVerticalTypeApparel()) batch.sortedItems else batch.items
        for(batchItem in items) {
            with(batchItem) {
                when(status) {
                    ProductStatus.RENTED,
                    ProductStatus.PICKING,
                    ProductStatus.PICKED,
                    ProductStatus.NOT_FOUND,
                    ProductStatus.PAID -> {
                        bindBatchItemViewModel(batch, this)
                    }
                    else -> {
                        //Not implemented yet
                    }
                }
            }
        }
    }

    fun bindBatchItemViewModel(batch: Batch, batchItem: BatchItem) {
        val item = BacklogDetailItemViewModelImpl()
        item.boxId = batch.getBoxIdFromBatchItemId(batchItem.id)
        item.customerEmail = batch.getCustomerEmailFromBatchItemId(batchItem.id)
        item.setShipmentId(batch.getShipmentIdFromBatchItemId(batchItem.id))
        item.setRackSection(batch.getRackSectionFromBatchItemId(batchItem.id))
        item.bindViewModel(batch, batchItem)
        detailItems.add(item)
        setupBagsOrApparelItems(item)
    }

    fun setupBagsOrApparelItems(item: BacklogDetailItemViewModelImpl) {
        if(userStorage.isVerticalTypeBags()) {
            item.showBagsItems()
        } else {
            item.showApparelItems()
        }
    }

    private fun fetchBatchDetail(isRefresh: Boolean, callback: Result<Void?, String?>?) {
        dataService.fetchBatchDetail(
            BatchDetailResource(batchId.get()),
            RequestResult {
                onSuccess {
                    it?.data?.result.notNull { batch ->
                        if(isRefresh) detailItems.clear()
                        setupToolbar(batch)
                        mapBatchDetailItem(batch)
                    }
                    callback?.success(null)
                }
                onError { callback?.failure(errorResponse.getErrorBodyDescription(it)) }
            }
        )
    }

    private fun setupToolbar(batch: Batch) {
        this.batch = batch
        batchName.set(batch.name)
        toolbarViewModel.title.set("${batch.name} - ${context.getString(R.string.backlog_label)}")
        toolbarViewModel.subtitle.set("${batch.getBatchTotalItemComplete(batchStatus)} ${string(R.string.items_completed_label)} - ${batch.getBatchTotalItemLeft(batchStatus)} ${string(R.string.items_left_label)}")
    }

    override fun completeBacklog(callback: Result<Void?, String?>?) {
        dataService.completeBatch(
            CompleteBatchResource(batchId.get(), CompleteBatchResource.Type.PICKING),
            RequestResult {
                onSuccess { callback?.success(null) }
                onError { callback?.failure(errorResponse.getErrorBodyDescription(it)) }
            }
        )
    }

    override fun refreshCompleteStatusButton() {
        val completedItem = "${detailItems.count { it.isComplete() && !it.isHeaderSection }} ${string(R.string.items_completed_label)}"
        val itemLeft = "${detailItems.count { !it.isComplete() && !it.isHeaderSection }} ${string(R.string.items_left_label)}"
        toolbarViewModel.subtitle.set("$completedItem - $itemLeft")
        footerButtonViewModel.enable.set(isBacklogDetailComplete())
    }

    private fun isBacklogDetailComplete(): Boolean {
        for(item in detailItems) {
            if(!item.isComplete()) {
                return false
            }
        }
        return true
    }

    override fun onKeyTapped() {
        scannedRfidText.get().notNullOrEmpty {
            if(featureFlagUtil.isBarcodeSettingIsOn()) {
                checkScannedText()
            }
        }
    }

    override fun checkScannedText() {
        val position = detailItems.indexOfFirst { it.batchItem?.rfid.equals(scannedRfidText.get(), true) }

        if(position >= 0) {
            eventBus.post(BacklogItemUIEvent.OnScannedRfidFoundPosition(position))
            detailItems[position].pickedClick()
        } else {
            checkBatchItemStatus()
        }
    }

    override fun checkBatchItemStatus() {
        val batchItem = batch?.items?.firstOrNull { it.rfid.equals(scannedRfidText.get(), true) }
        if(batchItem?.isInBacklog() == false) {
            eventBus.post(
                BacklogItemUIEvent.OnScannedRfidTextNotFound(
                    string(R.string.warn_rfid_can_not_process_title),
                    string(R.string.warn_rfid_current_status_message).format(batchItem.status, scannedRfidText.get())
                )
            )
        } else {
            eventBus.post(
                BacklogItemUIEvent.OnScannedRfidTextNotFound(
                    string(R.string.warn_rfid_notfound_title),
                    string(R.string.warn_rfid_notfound_message).format(scannedRfidText.get())
                )
            )
        }
    }

    /**
     * Override method for MultipleTypeRecycleViewSource
     */
    override val listData: MutableList<*> = detailItems
    override val totalItemCount: Int get() = detailItems.size + 1

    @Suppress("UNCHECKED_CAST")
    override fun <VM : ViewModel> getViewModelAtPosition(position: Int): VM {
        if(getItemTypeAtPosition(position) == COMPLETE_BUTTON_ITEM) {
            return footerButtonViewModel as VM
        }
        return detailItems[position] as VM
    }

    override fun getItemTypeAtPosition(position: Int): Int {
        if(position + 1 == totalItemCount) {
            return COMPLETE_BUTTON_ITEM
        }
        return BACKLOG_DETAIL_ITEM
    }

    override fun getLayoutIdForItemType(itemType: Int): Int {
        if(itemType == COMPLETE_BUTTON_ITEM) {
            return R.layout.footer_button_item
        }
        return R.layout.backlog_detail_list_item
    }

}