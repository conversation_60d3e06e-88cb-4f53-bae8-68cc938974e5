package co.styletheory.ops.outbound.android.model.enums

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty
import com.google.gson.annotations.SerializedName

/**
 * <AUTHOR>
 * @since January 12, 2022
 * Created by <PERSON><PERSON><PERSON> on 12/01/2022.
 * Copyright (c) 2022 Style Theory. All rights reserved.
 */
enum class DeliveryType(val deliveryType: String) {
    @JsonProperty("Return")
    @SerializedName("Return")
    RETURN("Return"),
    @JsonProperty("Delivery")
    @SerializedName("Delivery")
    DELIVERY("Delivery");

    override fun toString(): String {
        return this.deliveryType
    }

    companion object {
        private val types = DeliveryType.values().associateBy(DeliveryType::deliveryType)

        @JvmStatic
        @JsonCreator
        fun findValue(status: String): DeliveryType? = types.filterKeys { it == status }.asSequence().firstOrNull()?.value
    }
}