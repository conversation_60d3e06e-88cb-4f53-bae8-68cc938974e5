package co.styletheory.ops.android.viewModelComponent.recyclerItemDecoration

/**
 * Created by <PERSON> on 26-02-2025.
 */
import android.content.Context
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import co.styletheory.ops.outbound.android.R

/**
 * Factory class to create different types of divider item decorations for RecyclerView
 */
object DividerDecorationFactory {

    /**
     * Creates a horizontal divider item decoration with default styling
     *
     * @param context The context to access resources
     * @return RecyclerView.ItemDecoration
     */
    @JvmStatic
    fun createHorizontalDivider(context: Context): RecyclerView.ItemDecoration {
        return HorizontalDividerItemDecoration.Builder(context)
            .color(ContextCompat.getColor(context, R.color.gray_2e8))
            .sizeResId(R.dimen.space_small)
            .build()
    }

    /**
     * Creates a horizontal divider item decoration with custom color and size
     *
     * @param context The context to access resources
     * @param colorResId The resource ID for the divider color
     * @param sizeResId The resource ID for the divider size
     * @return RecyclerView.ItemDecoration
     */
    @JvmStatic
    fun createHorizontalDivider(
        context: Context,
        colorResId: Int,
        sizeResId: Int
    ): RecyclerView.ItemDecoration {
        return HorizontalDividerItemDecoration.Builder(context)
            .color(ContextCompat.getColor(context, colorResId))
            .sizeResId(sizeResId)
            .build()
    }
}