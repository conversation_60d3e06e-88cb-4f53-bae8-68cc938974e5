package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.model.BatchConfig
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import retrofit2.Call
import retrofit2.Retrofit

/**
 * Created by Yoga C. P<PERSON> on 2019-08-14.
 * Android Engineer
 */
class BatchConfigsResource : GraphQLResource<GraphQLData<List<BatchConfig.Result>>>() {

    override fun graphQLRequest(): GraphQLRequest = GraphQLRequestList.BATCH_CONFIGS

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<List<BatchConfig.Result>>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.fetchBatchConfigs(baseUrl, createRequestBody()))
    }
}