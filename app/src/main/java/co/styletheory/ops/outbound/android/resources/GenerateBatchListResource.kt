package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.model.GenerateBatchList
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import retrofit2.Call
import retrofit2.Retrofit

/**
 * Created by Yoga C. <PERSON> on 17/01/2019.
 * Android Engineer
 */
class GenerateBatchListResource constructor(private val timeGeneration: String) : GraphQLResource<GraphQLData<GenerateBatchList>>() {

    override fun graphQLRequest(): GraphQLRequest = GraphQLRequestList.GENERATE_BATCH_LIST

    override fun bodyParameter(): MutableMap<String, Any> {
        bodyParameter["dateTime"] = timeGeneration
        return bodyParameter
    }

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<GenerateBatchList>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.generateBatchList(baseUrl, createRequestBody()))
    }

}