package co.styletheory.ops.outbound.android.viewModelComponent

import android.content.Context
import androidx.databinding.ObservableBoolean
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.event.DismissDialogEvent
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseInjectedViewModel

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 02 November 2017.
 * Description
 *
 * <EMAIL>
 */

class ProcessDialogViewModel : BaseInjectedViewModel(){

    val dialogTitle = ObservableString("")
    val showSuccess = ObservableBoolean(false)
    val showError = ObservableBoolean(false)
    val showProgress = ObservableBoolean(false)

    fun containerClick(){
        eventBus.post(DismissDialogEvent())
    }

    fun showLoading(show: Boolean = true){
        showProgress.set(show)
        showSuccess.set(!show)
        showError.set(!show)
    }

    fun showSuccess(show: Boolean = true){
        showSuccess.set(show)
        showProgress.set(!show)
        showError.set(!show)
    }

    fun showFailed(show: Boolean = true){
        showError.set(show)
        showSuccess.set(!show)
        showProgress.set(!show)
    }

    companion object {
        const val TAG = "ProcessDialog"
        fun success(batchName: String = "", context: Context): ProcessDialogViewModel {
            val viewModel = ProcessDialogViewModel()
            viewModel.dialogTitle.set(context.getString(R.string.process_dialog_complete_dialog_title_success).format(batchName))
            viewModel.showSuccess.set(true)
            return viewModel
        }
        fun failed(context: Context): ProcessDialogViewModel {
            val viewModel = ProcessDialogViewModel()
            viewModel.dialogTitle.set(context.getString(R.string.process_dialog_complete_dialog_title_error))
            viewModel.showError.set(true)
            return viewModel
        }

        fun sendShipment(): ProcessDialogViewModel {
            val viewModel = ProcessDialogViewModel()
            viewModel.resetSendShipment()
            return viewModel
        }
    }

    fun resetSendShipment(){
        dialogTitle.set("Currently sending box")
        showProgress.set(true)
    }
}
