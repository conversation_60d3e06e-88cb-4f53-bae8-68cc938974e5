package co.styletheory.ops.outbound.android.viewModelComponent

import androidx.databinding.ObservableField
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseInjectedViewModel
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.model.enums.LabelType
import com.styletheory.android.mvvm.general.binding.ObservableText

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 31 October 2017.
 * Description
 *
 * <EMAIL>
 */
class LabelBoxViewModel : BaseInjectedViewModel() {

    var labelType = ObservableField<LabelType>()
    var labelName = ObservableText()

    companion object {
        fun create(shipment: Shipment): List<LabelBoxViewModel> {
            val labels = mutableListOf<LabelBoxViewModel>()
            if(shipment.customer?.isVip == true) {
                labels.add(create(LabelType.VIP))
            }
            if(shipment.box?.isFirstBox == true) {
                labels.add(create(LabelType.FIRST_BOX))
            }
            if(shipment.customer?.noPaper == true) {
                labels.add(create(LabelType.NO_PAPER))
            }
            if(shipment.customer?.noToteBag == true) {
                labels.add(create(LabelType.NO_TOTE_BAG))
            }
            return labels
        }

        fun create(labelType: LabelType): LabelBoxViewModel {
            val model = LabelBoxViewModel()
            model.bindViewModel(labelType)
            return model
        }
    }

    fun bindViewModel(labelType: LabelType): LabelBoxViewModel {
        this.labelType.set(labelType)
        this.labelName.set(labelType.stringRes)
        return this
    }
}