package co.styletheory.ops.outbound.android.feature.batch.viewModel

import android.view.View
import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableInt
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel.impl.LogisticProviderNameViewModelImpl
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseInjectedViewModel
import co.styletheory.ops.outbound.android.injection.scope.PerFragment
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.model.enums.LabelType
import co.styletheory.ops.outbound.android.util.GenerateBatchByPerStatus
import co.styletheory.ops.outbound.android.util.notNull
import co.styletheory.ops.outbound.android.viewModelComponent.LabelBoxViewModel
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 23 October 2017.
 * Description
 *
 * <EMAIL>
 */

@PerFragment
class BatchListViewModel @Inject constructor() : BaseInjectedViewModel() {
    @Inject
    lateinit var generateBatch: GenerateBatchByPerStatus

    @Inject
    lateinit var logisticProviderNameSectionVM: LogisticProviderNameViewModelImpl

    lateinit var batch: Batch
    val batchTitle = ObservableString()
    val totalBoxAndItem = ObservableString()
    val rack = ObservableString()
    val totalItem = ObservableString()
    val totalBox = ObservableString()
    val itemLeft = ObservableString()
    val labelItems = ObservableArrayList<LabelBoxViewModel>()
    val logisticProviderNameVisibility = ObservableInt(View.GONE)
    val batchStepItems = ObservableArrayList<BatchStepViewModel>()
    fun setupLogisticProviderNameViewModel(): LogisticProviderNameViewModelImpl = logisticProviderNameSectionVM

    fun bindViewModel(batch: Batch, batchStatus: BatchStatus?) {
        this.batch = batch
        batchTitle.set("${string(R.string.rack_label)} ${batch.name}")
        totalBoxAndItem.set("${batch.getTotalBox(batchStatus)} ${string(R.string.boxes_label)} - ${batch.getTotalBatchItem(batchStatus)} ${string(R.string.items_label)}")
        rack.set("")
        itemLeft.set("${batch.getBatchTotalItemLeft(batchStatus)} ${string(R.string.items_left_label)}")
        batch.logisticProviders.notNull {
            logisticProviderNameSectionVM.bindViewModel(batch.logisticProviders)
            logisticProviderNameVisibility.set(View.VISIBLE)
        }
        batchStepItems.addAll(generateBatch.createStepFromBatch(batch, batchStatus))
        setupTotalBoxAndItem(batchStatus)
        setupLabelItems(batch)
    }

    fun setupTotalBoxAndItem(batchStatus: BatchStatus?) {
        when(batchStatus) {
            BatchStatus.PICKING,
            BatchStatus.IN_BACKLOG -> {
                totalBox.set("${batch.getTotalBoxComplete(batchStatus)} ${string(R.string.boxes_picked_label)}")
                totalItem.set("${batch.getBatchTotalItemComplete(batchStatus)} ${string(R.string.items_picked_label)}")
            }
            BatchStatus.QA_PROCESSING,
            BatchStatus.PICKED -> {
                totalBox.set("${batch.getTotalBoxComplete(batchStatus)} ${string(R.string.boxes_qa_label)}")
                totalItem.set("${batch.getBatchTotalItemComplete(batchStatus)} ${string(R.string.items_qa_label)}")
            }
            BatchStatus.PHOTO,
            BatchStatus.QA_DONE -> {
                totalBox.set("${batch.getTotalBoxComplete(batchStatus)} ${string(R.string.boxes_photo_label)}")
                totalItem.set("${batch.getBatchTotalItemComplete(batchStatus)} ${string(R.string.items_photo_label)}")
            }
            BatchStatus.PACKING,
            BatchStatus.READY_FOR_PACKING -> {
                totalBox.set("${batch.getTotalBoxComplete(batchStatus)} ${string(R.string.boxes_packed_label)}")
                totalItem.set("${batch.getBatchTotalItemComplete(batchStatus)} ${string(R.string.items_packed_label)}")
            }
            else -> {
                totalBox.set("0 ${string(R.string.boxes_picked_label)}")
                totalItem.set("0 ${string(R.string.items_picked_label)}")
            }
        }
    }

    fun setupLabelItems(batch: Batch) {
        if(batch.isVip) {
            labelItems.add(LabelBoxViewModel.create(LabelType.VIP))
        }
        if(batch.isNoPaper) {
            labelItems.add(LabelBoxViewModel.create(LabelType.NO_PAPER))
        }
        if(batch.isFirstBox) {
            labelItems.add(LabelBoxViewModel.create(LabelType.FIRST_BOX))
        }
    }

}