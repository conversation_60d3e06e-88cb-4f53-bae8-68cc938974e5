package co.styletheory.ops.outbound.android.viewModelComponent

import android.view.View
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableInt
import co.styletheory.android.network.core.RequestResult
import co.styletheory.ops.outbound.android.feature.photoManager.event.PhotoManagerDeleteEvent
import co.styletheory.ops.outbound.android.feature.photoManager.event.RequestErrorEvent
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.event.GeneralPhotoGridItemClickedEvent
import co.styletheory.ops.outbound.android.general.event.GeneralPhotoGridItemUploadedEvent
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseInjectedViewModel
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.CreateImageUrlResource
import co.styletheory.ops.outbound.android.resources.UploadImageResource
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.util.Next
import co.styletheory.ops.outbound.android.util.impl.URLUtilImpl
import java.io.File
import javax.inject.Inject

/**
 * Created by giorgygunawan on 11/8/17.
 *
 */
class GeneralPhotoGridItemViewModel @Inject constructor() : BaseInjectedViewModel() {

    @Inject
    lateinit var dataService: DataService
    @Inject
    lateinit var errorResponse: ErrorResponse

    var position: Int = 0
    var imageUrl = ObservableString()
    var editable = ObservableBoolean()
    var isLocalUrl: Boolean = false
    var shipmentId: String? = null

    val loadingIndicatorStatus = ObservableInt(View.GONE)
    val localImageUrl = ObservableString("")

    fun bindPhoto(position: Int, imageUrl: ObservableString, shipmentId: String?, isLocalUrl: Boolean, editable: ObservableBoolean) {
        this.position = position
        this.imageUrl = imageUrl
        this.shipmentId = shipmentId
        this.isLocalUrl = isLocalUrl
        this.editable = editable

        initImageUrl()
        uploadImageIfNeeded()
    }

    private fun initImageUrl() {
        if(isLocalUrl) {
            localImageUrl.set(imageUrl.get())
            imageUrl.set(null)
        } else {
            localImageUrl.set(null)
        }
    }

    private fun setLoadImage(loadImage: Boolean) {
        if(loadImage) {
            loadingIndicatorStatus.set(View.VISIBLE)
        } else {
            loadingIndicatorStatus.set(View.GONE)
        }
    }

    fun getIsLoadImage(): Boolean = loadingIndicatorStatus.get() == View.VISIBLE

    private fun uploadImageIfNeeded() {
        if(imageUrl.get().isEmpty()) {
            setLoadImage(true)
            requestCreateUploadPhotoUrl(localImageUrl.get(), object : Next<String?> {
                override fun apply(t: String?) {
                    setLoadImage(false)
                    imageUrl.set(t)
                }
            })
        } else {
            setLoadImage(false)
        }
    }

    fun onButtonDeleteClicked() {
        eventBus.post(PhotoManagerDeleteEvent(this))
    }

    private fun requestCreateUploadPhotoUrl(imageUrl: String, callBack: Next<String?>) {
        dataService.fetchImageUploadUrl(
                CreateImageUrlResource(shipmentId.orEmpty()),
                RequestResult {
                    onSuccess {
                        val url = it?.data?.result.orEmpty()
                        if(url.isNotEmpty()) {
                            val file = File(imageUrl)
                            requestUploadImage(file, url, callBack)
                        } else {
                            eventBus.post(RequestErrorEvent("URL Empty"))
                        }
                    }
                    onError { eventBus.post(RequestErrorEvent(errorResponse.getErrorBodyDescription(it))) }
                }
        )
    }

    fun requestUploadImage(file: File, url: String, callBack: Next<String?>) {
        dataService.uploadUrlImage(
                UploadImageResource(file, url),
                RequestResult {
                    onSuccess {
                        val urlUtil = URLUtilImpl(url)
                        callBack.apply(urlUtil.getURL())
                        eventBus.post(GeneralPhotoGridItemUploadedEvent(this@GeneralPhotoGridItemViewModel))
                    }
                    onError { eventBus.post(RequestErrorEvent(errorResponse.getErrorBodyDescription(it))) }
                }
        )
    }

    fun onImageClicked() {
        val url = if(imageUrl.get().isEmpty()) localImageUrl.get() else imageUrl.get()
        eventBus.post(GeneralPhotoGridItemClickedEvent(position, url))
    }
}