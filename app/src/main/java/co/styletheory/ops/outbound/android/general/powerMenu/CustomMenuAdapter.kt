package co.styletheory.ops.outbound.android.general.powerMenu

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import co.styletheory.ops.outbound.android.R
import com.skydoves.powermenu.MenuBaseAdapter


/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 07 June 2018.
 * Description
 *
 * <EMAIL>
 */
class CustomMenuAdapter : MenuBaseAdapter<CustomMenuItem>() {

    override fun getView(index: Int, view: View?, viewGroup: ViewGroup?): View {
        var customView = view
        if(customView == null) {
            customView = LayoutInflater.from(viewGroup?.context).inflate(R.layout.menu_item_with_divider, viewGroup, false)
        }
        val item = getItem(index) as CustomMenuItem

        customView?.findViewById<TextView>(android.R.id.text1)?.text = item.text
        customView?.findViewById<View>(R.id.divider_top)?.visibility = if(item.showTopDivider) View.VISIBLE else View.GONE
        customView?.findViewById<View>(R.id.divider_bottom)?.visibility = if(item.showBottomDivider) View.VISIBLE else View.GONE

        return super.getView(index, customView, viewGroup)
    }
}