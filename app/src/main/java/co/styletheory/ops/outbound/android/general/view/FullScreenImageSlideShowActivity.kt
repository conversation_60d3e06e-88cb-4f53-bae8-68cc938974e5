package co.styletheory.ops.outbound.android.general.view

import android.net.Uri
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.viewpager.widget.PagerAdapter
import androidx.viewpager.widget.ViewPager
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.util.IntentConstant
import com.bumptech.glide.Glide
import com.github.chrisbanes.photoview.PhotoView
import java.io.File
import java.net.MalformedURLException
import java.net.URL

/**
 * Created by g<PERSON><PERSON><PERSON><PERSON><PERSON> on 11/15/17.
 */
class FullScreenImageSlideShowActivity : AppCompatActivity() {

    internal var pagerAdapter: PagerAdapterImpl? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.full_screen_image_slide_show)
        val mViewPager: HackyViewPager = findViewById(R.id.view_pager)
        pagerAdapter = PagerAdapterImpl(intent?.getStringArrayListExtra(IntentConstant.IMAGE_SOURCE_URLS_EXTRA_KEY) ?: arrayListOf())
        mViewPager.adapter = pagerAdapter
        mViewPager.currentItem = intent.getIntExtra(IntentConstant.SELECTED_IMAGE_POSITION_EXTRA_KEY, 0)
    }

    internal class PagerAdapterImpl(private val mUrls: ArrayList<String>) : PagerAdapter() {

        override fun getCount(): Int = mUrls.size

        override fun instantiateItem(container: ViewGroup, position: Int): View {
            val photoView = PhotoView(container.context)
            if(isUrl(mUrls[position])) {
                Glide.with(photoView).load(mUrls[position])
                        .fitCenter()
                        .centerCrop()
                        .into(photoView)
            } else {
                Glide.with(container.context).load(Uri.fromFile(File(mUrls[position])))
                        .fitCenter()
                        .centerCrop()
                        .into(photoView)
            }

            container.addView(photoView, ViewPager.LayoutParams.MATCH_PARENT, ViewPager.LayoutParams.MATCH_PARENT)

            return photoView
        }

        override fun destroyItem(container: ViewGroup, position: Int, imageViewObj: Any) {
            if(imageViewObj is ImageView) {
                imageViewObj.setImageDrawable(null)
                imageViewObj.setImageBitmap(null)
                Glide.with(imageViewObj).clear(imageViewObj)
            }
            container.removeView(imageViewObj as View)
        }

        override fun isViewFromObject(view: View, viewObj: Any): Boolean = view === viewObj

        private fun isUrl(text: String): Boolean {
            try {
                URL(text)
            } catch(e: MalformedURLException) {
                return false
            }
            return true
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        pagerAdapter = null
    }
}