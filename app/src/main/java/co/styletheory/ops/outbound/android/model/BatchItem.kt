package co.styletheory.ops.outbound.android.model

import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.util.IntentConstant
import com.google.gson.annotations.SerializedName
import org.parceler.Parcel

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 03 November 2017.
 * Description
 *
 * <EMAIL>
 */

@Parcel
data class BatchItem(
    val id: String = "",
    val label: String? = "",
    val labelSize: String = "",
    val sizeCategory: String = "",
    val status: ProductStatus? = null,
    val order: String = "",
    val qaFailedCategory: String = "",
    val qaFailedReason: String = "",
    val notes: String = "",
    val outboundQaImages: List<String> = emptyList(),
    val outboundBeforePackImages: List<String> = emptyList(),
    val parts: List<String>? = emptyList(),
    val rfid: String? = null,
    val rfidFormatted: String? = null,
    val length: Int = 0,
    val bustSize: Int = 0,
    val waistSize: Int = 0,
    val hipSize: Int = 0,
    val style: Style? = null,
    val additionalProperties: ItemPropertiesType? = null,
    val resellingInventoryType: String? = "",
    @SerializedName("warehouseRackSlotItemLocation")
    val rackSlotLocation: RackSlotLocation? = null
) {

    private val UPPER_LIMIT_LENGTH = 100
    private val MINIMUM_LIMIT_LENGTH = 0

    @Parcel
    data class RackSlotLocation(
        @SerializedName("areaRow")
        val row: Row? = null,
        @SerializedName("rack")
        val rack: Rack? = null
    ) {
        @Parcel
        data class Row(
            @SerializedName("name")
            val name: String = "",
            @SerializedName("sortOrder")
            val sortOrder: Int = 0
        )

        @Parcel
        data class Rack(
            @SerializedName("name")
            val name: String = "",
            @SerializedName("sortOrder")
            val sortOrder: Int = 0
        )
    }

    val rowName: String
        get() = rackSlotLocation?.row?.name.orEmpty().ifEmpty { "-" }

    val rackName: String
        get() = rackSlotLocation?.rack?.name.orEmpty().ifEmpty { "-" }

    val rowRackOrder: Int
        get() {
            val rowOrder = rackSlotLocation?.row?.sortOrder?.or(0).toString()
            val rackOrder = rackSlotLocation?.rack?.sortOrder?.or(0).toString()
            return ("$rowOrder$rackOrder").toIntOrNull() ?: 0
        }

    fun isInBacklog(): Boolean {
        return when(status) {
            ProductStatus.RENTED,
            ProductStatus.PICKING,
            ProductStatus.PICKED,
            ProductStatus.NOT_FOUND,
            ProductStatus.PAID -> true
            else -> false
        }
    }

    fun isInQA(): Boolean {
        return when(status) {
            ProductStatus.QA_PASSED,
            ProductStatus.QA_FAILED,
            ProductStatus.QA,
            ProductStatus.PICKED -> true
            else -> false
        }
    }

    fun isInReadyTab(): Boolean {
        return when(status) {
            ProductStatus.PACKED,
            ProductStatus.PACKING,
            ProductStatus.RECEIVED_BY_CUSTOMER,
            ProductStatus.TRANSIT_TO_CUSTOMER,
            ProductStatus.PHOTO_QA_DONE -> true
            else -> false
        }
    }

    fun isShortZoneItem(): Boolean {
        return (length in MINIMUM_LIMIT_LENGTH..UPPER_LIMIT_LENGTH)
    }

    fun isLongZoneItem(): Boolean {
        return (length > UPPER_LIMIT_LENGTH)
    }

    fun isAccessoriesZoneItem(): Boolean {
        return (length == -1)
    }

    fun isResellingItem(): Boolean {
        return (label?.lowercase()?.contains(IntentConstant.RESELLING_ITEM) == true)
    }
}