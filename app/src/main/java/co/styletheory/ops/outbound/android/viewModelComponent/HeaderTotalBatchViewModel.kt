package co.styletheory.ops.outbound.android.viewModelComponent

import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 26 October 2017.
 * Description
 *
 * <EMAIL>
 */
class HeaderTotalBatchViewModel @Inject constructor(): BaseViewModel<HeaderTotalBatchViewModel>() {
    init {
        viewModelClass = HeaderTotalBatchViewModel::class
    }
    val totalBatch = ObservableString("0")
}