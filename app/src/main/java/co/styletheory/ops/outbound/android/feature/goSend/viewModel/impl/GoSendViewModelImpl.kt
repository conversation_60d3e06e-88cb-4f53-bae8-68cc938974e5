package co.styletheory.ops.outbound.android.feature.goSend.viewModel.impl

import co.styletheory.ops.outbound.android.feature.goSend.event.GojekSelectedTabChangeEvent
import co.styletheory.ops.outbound.android.feature.goSend.viewModel.GoSendViewModel
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.model.LogisticProvider
import org.joda.time.DateTime
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 11 June 2018.
 * Description
 *
 * <EMAIL>
 */
class GoSendViewModelImpl @Inject constructor() : BaseViewModel<GoSendViewModelImpl>(), GoSendViewModel {

    override var logisticProvider: LogisticProvider? = null
    override var selectedDate: DateTime? = null

    init {
        viewModelClass = GoSendViewModelImpl::class
    }

    override fun onRadioButtonSelected(position: Int) {
        eventBus.post(GojekSelectedTabChangeEvent(position))
    }
}
