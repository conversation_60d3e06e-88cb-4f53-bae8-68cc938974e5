package co.styletheory.ops.outbound.android.model.enums

import androidx.annotation.StringRes
import co.styletheory.ops.outbound.android.R

/**
 * Created by <PERSON> <PERSON> on 04/06/20.
 * Android Engineer
 */
enum class ProcedureType constructor(@StringRes val stringRes: Int) {
    ONE_TO_ONE(R.string.procedure_type_one_to_one),
    ONE_TO_ONE_WITH_LOCK(R.string.procedure_type_one_to_one_with_lock),
    REGULAR_WITH_LOCK(R.string.procedure_type_regular_with_lock)
}