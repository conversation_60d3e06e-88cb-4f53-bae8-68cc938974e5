package co.styletheory.ops.outbound.android.feature.packing.event

/**
 * Created by Yoga C. <PERSON> on 15/03/19.
 * Android Engineer
 */
sealed class PackingItemUIEvent {
    data class OnScannedRfidFoundPosition(val itemPosition: Int, val rfidText: String)
    data class OnScannedRfidTextNotFound(val title: String, val message: String)
    data class OnScrollToPosition(val position: Int)
    data class OnScannedRfidTextFetched(val rfidText: String)
}