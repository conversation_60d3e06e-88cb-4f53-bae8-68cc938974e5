package co.styletheory.ops.outbound.android.model

import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import org.joda.time.DateTime
import org.parceler.Parcel

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 09 November 2017.
 * Description
 *
 * <EMAIL>
 */

@Parcel
data class Batch(
        val id: String = "",
        val name: String = "",
        val rackId: String = "",
        val batchItemIds: List<BatchItem> = emptyList(),

        val pickerName: String = "",
        val pickerEmail: String = "",
        val pickers: List<User> = emptyList(),
        val pickerStartTime: DateTime? = null,
        val pickerEndTime: DateTime? = null,

        val qaName: String = "",
        val qaEmail: String = "",
        val qas: List<User> = emptyList(),
        val qaStartTime: DateTime? = null,
        val qaEndTime: DateTime? = null,

        val photographerName: String = "",
        val photographerEmail: String = "",
        val photographers: List<User> = emptyList(),
        val photographerStartTime: DateTime? = null,
        val photographerEndTime: DateTime? = null,

        val packerName: String = "",
        val packerEmail: String = "",
        val packers: List<User> = emptyList(),
        val packerStartTime: DateTime? = null,
        val packerEndTime: DateTime? = null,

        val shipmentOrder: List<String> = emptyList(),
        val isFirstBox: Boolean = false,
        val isExpedited: Boolean = false,
        val isVip: Boolean = false,
        val isNoPaper: Boolean = false,
        val dateCreated: DateTime? = null,

        val shipments: List<Shipment> = emptyList(),
        val items: List<BatchItem> = emptyList(),
        val logisticProviders: List<LogisticProvider> = emptyList(),
        val rack: Rack? = null
) {

    val sortedItems: List<BatchItem>
        get() {
            val itemsNoLocation = items.filter { it.rowRackOrder == 0 }
            val itemsLocation = items.filterNot { it.rowRackOrder == 0 }.sortedBy { it.rowRackOrder }
            return itemsLocation + itemsNoLocation
        }

    fun getPickerEmails(): List<String> {
        val emails = pickers.mapTo(ArrayList()) { it.email }
        if(pickerEmail.isNotEmpty() && !emails.contains(pickerEmail)) emails.add(pickerEmail)
        return emails
    }

    fun getQAEmails(): List<String> {
        val emails = qas.mapTo(ArrayList()) { it.email }
        if(qaEmail.isNotEmpty() && !emails.contains(qaEmail)) emails.add(qaEmail)
        return emails
    }

    fun getPhotographerEmails(): List<String> {
        val emails = photographers.mapTo(ArrayList()) { it.email }
        if(photographerEmail.isNotEmpty() && !emails.contains(photographerEmail)) emails.add(photographerEmail)
        return emails
    }

    fun getPackerEmails(): List<String> {
        val emails = packers.mapTo(ArrayList()) { it.email }
        if(packerEmail.isNotEmpty() && !emails.contains(packerEmail)) emails.add(packerEmail)
        return emails
    }

    fun getPickerNames(): List<String> {
        val names = pickers.mapTo(ArrayList()) { it.name }
        if(pickerEmail.isNotEmpty()) names.add(pickerName)
        return names
    }

    fun getQANames(): List<String> {
        val names = qas.mapTo(ArrayList()) { it.name }
        if(qaEmail.isNotEmpty()) names.add(qaName)
        return names
    }

    fun getPhotographerNames(): List<String> {
        val names = photographers.mapTo(ArrayList()) { it.name }
        if(photographerEmail.isNotEmpty()) names.add(photographerName)
        return names
    }

    fun getPackerNames(): List<String> {
        val names = packers.mapTo(ArrayList()) { it.name }
        if(packerEmail.isNotEmpty()) names.add(packerName)
        return names
    }

    fun getShipmentIdFromBatchItemId(batchItemId: String): String {
        return shipments.firstOrNull { shipment ->
            shipment.items.any { it.id.equals(batchItemId, ignoreCase = true) }
        }?.id.orEmpty()
    }

    fun getBoxIdFromBatchItemId(batchItemId: String): String {
        return shipments.firstOrNull { shipment ->
            shipment.items.any { it.id.equals(batchItemId, ignoreCase = true) }
        }?.boxId.orEmpty()
    }

    fun getCustomerEmailFromBatchItemId(batchItemId: String): String {
        return shipments.firstOrNull { shipment ->
            shipment.items.any { it.id.equals(batchItemId, ignoreCase = true) }
        }?.customer?.email.orEmpty()
    }

    fun getRackSectionFromBatchItemId(batchItemId: String): String {
        val shipmentId = getShipmentIdFromBatchItemId(batchItemId)
        return mapNumericToAlphabet(getRackSectionFromShipmentId(shipmentId))
    }

    private fun mapNumericToAlphabet(number: Int?): String {
        val alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        val shipment = number?.let { position -> shipments[position] }

        return when {
            shipment?.isExpedited == true -> number.plus(1).toString()
            (number != null && number < alphabet.length - 1) -> alphabet[number].toString()
            else -> ""
        }
    }

    fun getRackSectionFromShipmentId(shipmentId: String): Int = shipments.map { it.id }.indexOf(shipmentId)

    fun getTotalBatchItem(batchStatus: BatchStatus?, items: List<BatchItem> = this.items): Int {
        when(batchStatus) {
            BatchStatus.PICKING,
            BatchStatus.IN_BACKLOG -> {
                return items.filter {
                    it.status == ProductStatus.RENTED ||
                            it.status == ProductStatus.PICKING ||
                            it.status == ProductStatus.PICKED ||
                            it.status == ProductStatus.NOT_FOUND ||
                            it.status == ProductStatus.PAID
                }.count()
            }
            BatchStatus.QA_PROCESSING,
            BatchStatus.PICKED -> {
                return items.filter {
                    it.status == ProductStatus.PICKED ||
                            it.status == ProductStatus.QA ||
                            it.status == ProductStatus.QA_PASSED ||
                            it.status == ProductStatus.QA_FAILED
                }.count()
            }
            BatchStatus.QA_DONE,
            BatchStatus.PHOTO -> {
                return items.filter {
                    it.status == ProductStatus.QA_PASSED ||
                            it.status == ProductStatus.PHOTO_QA ||
                            it.status == ProductStatus.PHOTO_QA_DONE
                }.count()
            }
            BatchStatus.READY_FOR_PACKING -> {
                return items.count()
            }
            else -> {
                return 0
            }
        }
    }

    fun getTotalBox(batchStatus: BatchStatus?): Int {
        var boxCount = 0
        for(item in shipments) {
            val count = getTotalBatchItem(batchStatus, item.items)
            if(count > 0) {
                boxCount++
            }
        }
        return boxCount
    }

    fun getBatchTotalItemComplete(batchStatus: BatchStatus?): Int {
        var count = 0
        for(item in items) {
            count += if(batchItemComplete(batchStatus, item)) 1 else 0
        }
        return count
    }

    fun getBatchTotalItemLeft(batchStatus: BatchStatus?): Int {
        var count = 0
        for(item in items) {
            count += if(batchItemInComplete(batchStatus, item)) 1 else 0
        }
        return count
    }

    fun getTotalBoxComplete(batchStatus: BatchStatus?): Int {
        var boxComplete = 0
        for(item in shipments) {
            var count = 0
            for(batchItem in item.items) {
                count += if(batchItemComplete(batchStatus, batchItem)) 1 else 0
            }
            if(count == getTotalBatchItem(batchStatus, item.items) && count > 0) {
                boxComplete++
            }
        }
        return boxComplete
    }

    private fun batchItemComplete(batchStatus: BatchStatus?, batchItem: BatchItem): Boolean {
        when(batchStatus) {
            BatchStatus.PICKING,
            BatchStatus.IN_BACKLOG -> {
                if(batchItem.status == ProductStatus.PICKED) {
                    return true
                }
            }
            BatchStatus.QA_PROCESSING,
            BatchStatus.PICKED -> {
                if(batchItem.status == ProductStatus.QA_PASSED || batchItem.status == ProductStatus.QA_FAILED) {
                    return true
                }
            }
            BatchStatus.QA_DONE,
            BatchStatus.PHOTO -> {
                if(batchItem.status == ProductStatus.PHOTO_QA_DONE) {
                    return true
                }
            }
            BatchStatus.READY_FOR_PACKING -> {
                if(batchItem.status == ProductStatus.PACKED) {
                    return true
                }
            }
            else -> {
                //Not implemented yet
            }
        }
        return false
    }

    private fun batchItemInComplete(batchStatus: BatchStatus?, batchItem: BatchItem): Boolean {
        when(batchStatus) {
            BatchStatus.PICKING,
            BatchStatus.IN_BACKLOG -> {
                if(batchItem.status == ProductStatus.RENTED
                        || batchItem.status == ProductStatus.PICKING
                        || batchItem.status == ProductStatus.PAID) {
                    return true
                }
            }
            BatchStatus.QA_PROCESSING,
            BatchStatus.PICKED -> {
                if(batchItem.status == ProductStatus.PICKED || batchItem.status == ProductStatus.QA) {
                    return true
                }
            }
            BatchStatus.QA_DONE,
            BatchStatus.PHOTO -> {
                if(batchItem.status == ProductStatus.PHOTO_QA || batchItem.status == ProductStatus.QA_PASSED
                        || batchItem.status == ProductStatus.QA_FAILED) {
                    return true
                }
            }
            BatchStatus.READY_FOR_PACKING -> {
                if(batchItem.status != ProductStatus.PACKED
                        && batchItem.status != ProductStatus.TRANSIT_TO_CUSTOMER
                        && batchItem.status != ProductStatus.RECEIVED_BY_CUSTOMER) {
                    return true
                }
            }
            else -> {
                //Not implemented yet
            }
        }
        return false
    }

}
