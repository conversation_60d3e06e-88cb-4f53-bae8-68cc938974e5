package co.styletheory.ops.outbound.android.feature.goSend.view

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.viewpager.widget.ViewPager
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.GoSendFragmentBinding
import co.styletheory.ops.outbound.android.feature.goSend.event.GojekSelectedTabChangeEvent
import co.styletheory.ops.outbound.android.feature.goSend.viewModel.GoSendViewModel
import co.styletheory.ops.outbound.android.feature.packed.view.PackedChildFragment
import co.styletheory.ops.outbound.android.general.adapter.PagerSource
import co.styletheory.ops.outbound.android.general.adapter.impl.PagerAdapter
import co.styletheory.ops.outbound.android.general.base.BaseFragment
import co.styletheory.ops.outbound.android.model.LogisticProvider
import co.styletheory.ops.outbound.android.util.IntentConstant
import co.styletheory.ops.outbound.android.util.notNull
import org.greenrobot.eventbus.Subscribe
import org.joda.time.DateTime
import org.parceler.Parcels

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 11 June 2018.
 * Description
 *
 * <EMAIL>
 */
class GoSendFragment : BaseFragment<GoSendFragmentBinding, GoSendViewModel>(), PagerSource {

    companion object {
        fun newInstance(logisticProvider: LogisticProvider?, dateTime: DateTime): GoSendFragment {
            val fragment = GoSendFragment()
            val bundle = Bundle()
            bundle.putParcelable(IntentConstant.LOGISTIC_PROVIDER, Parcels.wrap(logisticProvider))
            bundle.putSerializable(IntentConstant.SELECTED_DATE, dateTime)
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if(viewModel == null) {
            fragmentComponent?.inject(this)
        }
        initArgument()
    }

    private fun initArgument() {
        arguments?.getParcelable<Parcelable>(IntentConstant.LOGISTIC_PROVIDER).notNull { viewModel?.logisticProvider = Parcels.unwrap(it) }
        arguments?.getSerializable(IntentConstant.LOGISTIC_PROVIDER).notNull { viewModel?.selectedDate = it as DateTime }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return bindContentView(inflater, container, R.layout.go_send_fragment)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupPager()
        refreshToggleProductLine(0)
        setupGosendToggle()
    }

    private fun setupPager() {
        val pagerAdapter = PagerAdapter(childFragmentManager)
        pagerAdapter.source = this
        binding?.viewPager?.adapter = pagerAdapter
        binding?.viewPager?.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrollStateChanged(state: Int) {
                //Not implemented yet
            }

            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                //Not implemented yet
            }

            override fun onPageSelected(position: Int) {
                eventBus.post(GojekSelectedTabChangeEvent(position, position != 0))
                refreshToggleProductLine(position)
            }
        })
    }

    private fun setupGosendToggle() {
        binding?.toggleGosend?.onToggledListener = { toggle, _ ->
            when(toggle.title) {
                getString(R.string.gosend_to_be_ordered) -> {
                    eventBus.post(GojekSelectedTabChangeEvent(0, false))
                }
                getString(R.string.gosend_ordered) -> {
                    eventBus.post(GojekSelectedTabChangeEvent(1, true))
                }
            }
        }
    }

    private fun refreshToggleProductLine(position: Int) {
        when(position) {
            0 -> {
                binding?.toggleGosend?.setToggled(R.id.gosend_to_be_ordered, true)
                binding?.viewPager?.setCurrentItem(0, true)
            }
            1 -> {
                binding?.toggleGosend?.setToggled(R.id.gosend_ordered, true)
                binding?.viewPager?.setCurrentItem(1, true)
            }
        }
    }

    //region Event Bus
    @Subscribe
    fun onGojekSelectedTabChangeEvent(event: GojekSelectedTabChangeEvent) {
        refreshToggleProductLine(event.position)
    }
    //endregion

    //region Pager Source
    override val totalViewCount: Int get() = 2

    override fun getItemTypeAtPosition(position: Int): Fragment {
        return when(position) {
            0 -> PackedChildFragment.newInstance(viewModel?.logisticProvider, viewModel?.selectedDate, false)
            1 -> PackedChildFragment.newInstance(viewModel?.logisticProvider, viewModel?.selectedDate, true)
            else -> Fragment()
        }
    }

    override fun getPageTitleAtPosition(position: Int): CharSequence {
        return ""
    }

    //endregion
}