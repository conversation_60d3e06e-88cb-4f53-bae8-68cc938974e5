package co.styletheory.ops.outbound.android.feature.scanBarcodeBottomSheet.viewModel

import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.listener.OnKeyListener
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.SwapItem

/**
 * Created by Yoga C. Pranata on 13/05/20.
 * Android Engineer
 */
interface ScanBarcodeBottomSheetViewModel : ViewModel {

    var swapItem: SwapItem
    val scannedRfidText: ObservableString
    val onEditTextKeyListener: OnKeyListener

    fun onClose()
}