package co.styletheory.ops.outbound.android.model.response

import co.styletheory.ops.outbound.android.model.Shipment

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 03 November 2017.
 * Description
 *
 * <EMAIL>
 */
data class ShipmentResponse(val shipments: List<Shipment> = listOf(), val meta: Meta? = null) {
    data class Meta(val shipmentCount: Int = 0, val shipmentIds: List<String>? = listOf())
}