package co.styletheory.ops.outbound.android.feature.settings.viewModel

import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableInt
import co.styletheory.ops.outbound.android.feature.settings.viewModel.impl.BatchConfigItemViewModel
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.BatchConfig
import co.styletheory.ops.outbound.android.model.BatchGenerationTime
import co.styletheory.ops.outbound.android.model.Region
import co.styletheory.ops.outbound.android.model.VerticalType
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel
import java.util.*

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 05 June 2018.
 * Description
 *
 * <EMAIL>
 */
interface SettingsViewModel : ViewModel {

    val isGeneratePage: ObservableBoolean
    val barcodeApparelRadioButton: ObservableInt
    val barcodeBagsRadioButton: ObservableInt
    val btnGenerateBatchVisibility: ObservableInt
    val barcodeApparelVisibility: ObservableInt
    val barcodeBagsVisibility: ObservableInt
    val shimmerVisibility: ObservableInt
    val sectionVisibility: ObservableInt
    val regionList: ArrayList<Region>
    val verticalTypeList: ArrayList<VerticalType>
    val batchGenerationTimeList: ArrayList<BatchGenerationTime>
    val regionConfigViewModelList: ObservableArrayList<BatchConfigItemViewModel>
    val verticalTypeConfigViewModelList: ObservableArrayList<BatchConfigItemViewModel>
    val batchDate: ObservableString
    val selectedCalendar: Calendar
    val settingsPageVisibility: ObservableInt
    val outboundAppVersion: ObservableString

    fun initializeViewModel()
    fun toolbarViewModel(): GeneralToolbarViewModel
    fun onRadioButtonChecked(inputString: String)
    fun onBtnGenerateBatchClicked()
    fun setupBtnGenerateBatchVisibility()
    fun generateBatch(timeGeneration: String)
    fun getBarcodeApparelStatus(): Int
    fun getBarcodeBagsStatus(): Int
    fun showAllSection()
    fun fetchBatchConfigs()
    fun processBatchConfigs(configList: List<BatchConfig.Result>)
    fun saveCheckedRegionByName(region: String)
    fun saveCheckedVerticalTypeByName(verticalType: String)
    fun findNearestHourGenerationTime()
    fun setSelectedRegion(selectedRegion: String)
    fun setSelectedVertical(selectedVertical: String)
    fun clearVerticalSelection()
    fun clearRegionSelection()
    fun batchDateClicked()
    fun setBatchDate(year: Int, monthOfYear: Int, dayOfMonth: Int)
    fun showGeneratePage()
    fun showSettingsPage()
    fun onBtnApplyClicked()
}