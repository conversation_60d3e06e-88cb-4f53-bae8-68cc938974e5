package co.styletheory.ops.outbound.android.feature.swap

import androidx.databinding.ObservableBoolean
import co.styletheory.android.network.core.RequestResult
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.event.DismissDialogEvent
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseInjectedViewModel
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.model.response.CheckSwapAvailabilityResponse
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.CheckSwapItemAvailabilityResource
import co.styletheory.ops.outbound.android.resources.SwapBoxItemResource
import co.styletheory.ops.outbound.android.resources.SwapItemResource
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.util.Next
import co.styletheory.ops.outbound.android.util.notNull
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 27 October 2017.
 * Description
 *
 * <EMAIL>
 */
class SwapItemDialogViewModel : BaseInjectedViewModel() {

    var shipmentId: String = ""
    var boxId: String = ""
    var customerEmail = ""
    var batchItem: BatchItem? = null
    var swapCallback: Next<BatchItem>? = null
    var newBatchItem: BatchItem? = null

    val imageUrl = ObservableString()
    val itemName = ObservableString()
    val itemSize = ObservableString()
    val dialogTitle = ObservableString()
    val dialogBody = ObservableString()
    val dialogLeftButtonText = ObservableString()
    val dialogRightButtonText = ObservableString()
    val showLoading = ObservableBoolean(false)
    val showCloseButton = ObservableBoolean(false)
    val hideActionButton = ObservableBoolean(false)

    @Inject
    lateinit var dataService: DataService
    @Inject
    lateinit var errorResponse: ErrorResponse

    fun bindViewModel(shipmentId: String, boxId: String, customerEmail: String, batchItem: BatchItem?, swapCallback: Next<BatchItem>) {
        this.shipmentId = shipmentId
        this.batchItem = batchItem
        this.swapCallback = swapCallback
        this.boxId = boxId
        this.customerEmail = customerEmail

        imageUrl.set(batchItem?.style?.gallery?.getOrElse(0) { "" })
        dialogLeftButtonText.set(string(R.string.cancel_label))
        dialogRightButtonText.set(string(R.string.swap_item_label))
        dialogTitle.set(string(R.string.swap_dialog_title))
        dialogBody.set(string(R.string.swap_dialog_body))
        itemName.set(batchItem?.style?.name)
        itemSize.set("${batchItem?.labelSize} [${batchItem?.order}]")
    }

    fun swapItemClick() {
        batchItem.notNull {
            showLoading.set(true)
            hideActionButton.set(true)
            dialogTitle.set(string(R.string.swap_dialog_loading_title))
            dialogBody.set("")

            val resource = CheckSwapItemAvailabilityResource(it.style?.id
                   .orEmpty(), it.labelSize, ProductStatus.AVAILABLE)
            dataService.checkSwapItemAvailability(resource, checkAvailabilityCallback)
        }
    }

    val checkAvailabilityCallback = RequestResult<GraphQLData<CheckSwapAvailabilityResponse>, String> {
        onSuccess {
            it?.data?.result.notNull { swap ->
                if(swap.items.isNotEmpty()) {
                    newBatchItem = swap.items[0]
                    swapBoxItem()
                } else {
                    dialogTitle.set(string(R.string.swap_dialog_item_not_found))
                    dialogBody.set(string(R.string.swap_dialog_contact_help))
                    showLoading.set(false)
                    showCloseButton.set(true)
                }
            }
        }
        onError {
            showLoading.set(false)
            showCloseButton.set(true)
            showToast(errorResponse.getErrorBodyDescription(it))
        }
    }

    fun swapBoxItem() {
        val resource = SwapBoxItemResource(boxId, batchItem?.id.orEmpty(), newBatchItem?.id.orEmpty(), customerEmail)
        dataService.swapBoxItem(resource, swapBoxItemCallback)
    }

    fun swapItem() {
        val resource = SwapItemResource(shipmentId, batchItem?.id.orEmpty(), newBatchItem?.id.orEmpty())
        dataService.swapItem(resource, swapItemCallback)
    }

    val swapItemCallback = RequestResult<GraphQLData<Void>, String> {
        onSuccess {
            eventBus.post(DismissDialogEvent())
            newBatchItem.notNull { swapCallback?.apply(it) }
        }
        onError {
            eventBus.post(DismissDialogEvent())
            showToast(errorResponse.getErrorBodyDescription(it))
        }
    }

    val swapBoxItemCallback = RequestResult<GraphQLData<Void>, String> {
        onSuccess { swapItem() }
        onError {
            eventBus.post(DismissDialogEvent())
            showToast(errorResponse.getErrorBodyDescription(it))
        }
    }

    fun leftButtonClick() {
        eventBus.post(DismissDialogEvent())
    }
}