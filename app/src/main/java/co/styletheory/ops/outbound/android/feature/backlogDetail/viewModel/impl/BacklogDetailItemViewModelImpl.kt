package co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel.impl

import android.view.View
import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableInt
import co.styletheory.android.network.core.RequestResult
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.backlogDetail.event.BacklogItemSwapClickEvent
import co.styletheory.ops.outbound.android.feature.backlogDetail.event.RefreshCompleteButtonEvent
import co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel.BacklogDetailItemViewModel
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseItemViewModel
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.BatchItemResource
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.util.Next
import co.styletheory.ops.outbound.android.util.impl.StringUtil
import co.styletheory.ops.outbound.android.util.notNullOrEmpty
import co.styletheory.ops.outbound.android.viewModelComponent.ColorItemViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.PhotoWithLabelViewModel
import javax.inject.Inject

/**
 * Created by Eminarti Sianturi on 2019-09-24.
 */
open class BacklogDetailItemViewModelImpl @Inject constructor() : BaseItemViewModel<BacklogDetailItemViewModelImpl>(), BacklogDetailItemViewModel {

    @Inject
    lateinit var userStorage: UserStorage

    @Inject
    lateinit var dataService: DataService

    @Inject
    lateinit var errorResponse: ErrorResponse

    @Inject
    lateinit var photoWithLabelVM: PhotoWithLabelViewModel

    init {
        viewModelClass = BacklogDetailItemViewModelImpl::class
    }

    private var rackSection: String? = ""
    private var shipmentId: String = ""

    override var isHeaderSection = false
    override var batch: Batch? = null
    override var boxId: String = ""
    override var customerEmail: String = ""
    override var batchItem: BatchItem? = null

    override val itemName = ObservableString()
    override val itemSize = ObservableString()
    override val location = ObservableString()
    override val brand = ObservableString()
    override val category = ObservableString()
    override val parts = ObservableString()
    override val rack = ObservableString()
    override val soldTo = ObservableString()
    override val errorMessage = ObservableString()
    override val swap = ObservableBoolean(false)
    override val picked = ObservableBoolean(false)
    override val missing = ObservableBoolean(false)
    override val showOverlayLoading = ObservableBoolean(false)
    override val notes = ObservableString()
    override val productOrder = ObservableString()
    override val colorItems = ObservableArrayList<ColorItemViewModel>()
    override val detachable = ObservableString()
    override val partsVisibility = ObservableInt(View.GONE)
    override val detachableVisibility = ObservableInt(View.GONE)
    override val scannerIconVisibility = ObservableInt(View.GONE)
    override val isPickedButtonEnabled = ObservableBoolean(true)
    override val swapButtonVisibility = ObservableInt(View.GONE)
    override val itemSectionVisibility = ObservableInt(View.VISIBLE)
    override val itemPurchasedVisibility = ObservableInt(View.GONE)
    override val isShowLocation = ObservableInt(View.GONE)

    override fun setPhotoWithLabelViewModel(): PhotoWithLabelViewModel = photoWithLabelVM

    override val swapCallback = object : Next<BatchItem> {
        override fun apply(t: BatchItem) {
            bindViewModel(batch, t)
            eventBus.post(RefreshCompleteButtonEvent())
        }
    }

    override fun setRackSection(rackSection: String?) {
        this.rackSection = rackSection
    }

    override fun setShipmentId(shipmentId: String) {
        this.shipmentId = shipmentId
    }

    override fun showApparelItems() {
        partsVisibility.set(View.VISIBLE)
    }

    override fun showBagsItems() {
        detachableVisibility.set(View.VISIBLE)
    }

    override fun setupPickedButton(isEnabled: Boolean) {
        if(!picked.get()) {
            isPickedButtonEnabled.set(isEnabled)
        }
    }

    override fun bindViewModel(batch: Batch?, batchItem: BatchItem) {
        this.batch = batch
        this.batchItem = batchItem
        location.set(batchItem.rowName)
        itemName.set(batchItem.rackName)
        brand.set(batchItem.style?.designer?.name)
        itemSize.set(batchItem.labelSize)
        productOrder.set("[${batchItem.order}]")
        category.set(batchItem.style?.primaryCategory)
        parts.set(StringUtil.stringArraySeparateWithComma(batchItem.parts))
        detachable.set(batchItem.additionalProperties?.detachableDetail)
        notes.set(batchItem.notes)
        colorItems.clear()
        missing.set(false)
        picked.set(false)

        setupLocationInfo()
        setupPhotoWithLabel(batchItem)
        setColorItems(batchItem)
        setMissingAndPickedButton(batchItem)
        setupItemContainRfid()
        setupRackName(batch)
        setupPurchasedItem(batchItem)
        setupSwapButtonVisibility(batchItem)
        swap.set(missing.get())
    }

    private fun setupLocationInfo() {
        if(userStorage.isVerticalTypeApparel()) {
            isShowLocation.set(View.VISIBLE)
        } else {
            itemName.set(brand.get())
        }
    }

    override fun setColorItems(batchItem: BatchItem) {
        for(item in batchItem.style?.colors?.filter { it.colorCode.isNotEmpty() }.orEmpty()) {
            val model = ColorItemViewModel()
            model.colorName.set(item.name)
            model.colorHex.set(item.colorCode)
            colorItems.add(model)
        }
    }

    @Suppress("NON_EXHAUSTIVE_WHEN")
    override fun setMissingAndPickedButton(batchItem: BatchItem) {
        when(batchItem.status) {
            ProductStatus.PICKING,
            ProductStatus.RENTED,
            ProductStatus.PAID -> {
                missing.set(false)
                picked.set(false)
            }
            ProductStatus.PICKED -> {
                missing.set(false)
                picked.set(true)
            }
            ProductStatus.MISSING,
            ProductStatus.NOT_FOUND -> {
                missing.set(true)
                picked.set(false)
            }
            else -> {
                //Not implemented yet
            }
        }
    }

    override fun swapClick() {
        eventBus.post(BacklogItemSwapClickEvent(shipmentId, boxId, customerEmail, batchItem, swapCallback))
    }

    override fun pickedClick() {
        if(!picked.get() && !isHeaderSection) {
            updateBatchStatus(ProductStatus.PICKED)
        }
    }

    override fun missingClick() {
        if(!missing.get() && !isHeaderSection) {
            updateBatchStatus(ProductStatus.NOT_FOUND)
        }
    }

    override fun updateBatchStatus(status: ProductStatus) {
        val resource = BatchItemResource(batch?.id.orEmpty(), shipmentId, listOf(batchItem?.id.orEmpty()), status)
        showOverlayLoading.set(true)
        dataService.updateBatchItemStatus(
                resource,
                RequestResult {
                    onSuccess {
                        updateViewModelStateWith(status)
                        showHideError("")
                    }
                    onError {
                        errorMessage.set(errorResponse.getErrorBodyDescription(it))
                        showOverlayLoading.set(false)
                    }
                }
        )
    }

    @Suppress("NON_EXHAUSTIVE_WHEN")
    override fun updateViewModelStateWith(status: ProductStatus) {
        when(status) {
            ProductStatus.PICKED -> {
                picked.set(!picked.get())
                missing.set(false)
                swap.set(false)
            }
            ProductStatus.NOT_FOUND -> {
                missing.set(!missing.get())
                picked.set(false)
                swap.set(true)
            }
            else -> {
                //Not implemented yet
            }
        }

        setupItemContainRfid()
        eventBus.post(RefreshCompleteButtonEvent())
    }

    override fun showHideError(error: String?) {
        errorMessage.set(error)
        showOverlayLoading.set(false)
    }

    override fun showScannerIcon() {
        scannerIconVisibility.set(View.VISIBLE)
    }

    override fun isComplete(): Boolean {
        if(picked.get() || isHeaderSection) {
            return true
        }
        return false
    }

    override fun setupItemContainRfid() {
        batchItem?.rfid.notNullOrEmpty {
            if(featureFlagUtil.isBarcodeSettingIsOn()) {
                showScannerIcon()
            }
        }
    }

    override fun setupSwapButtonVisibility(batchItem: BatchItem) {
        if(featureFlagUtil.isPickingSwapButtonEnabled()) {
            if(userStorage.isUserOnDemandAndRegionID()
                    || userStorage.isVerticalTypeBags()
                    || featureFlagUtil.isQualitySwapResellingDisabled(batchItem)) {
                swapButtonVisibility.set(View.GONE)
            } else {
                swapButtonVisibility.set(View.VISIBLE)
            }
        } else {
            swapButtonVisibility.set(View.GONE)
        }
    }

    override fun setupRackName(batch: Batch?) {
        val rackName = if(userStorage.isUserOnDemandAndRegionID()) {
            R.string.rack_name_on_demand
        } else {
            R.string.rack_name_regular
        }
        rack.set(string(rackName).format(batch?.rack?.name, rackSection))
    }

    override fun setupPurchasedItem(batchItem: BatchItem) {
        if(!batchItem.label.isNullOrEmpty()) {
            itemPurchasedVisibility.set(View.VISIBLE)
            soldTo.set(batchItem.label)
        } else {
            itemPurchasedVisibility.set(View.GONE)
            soldTo.clear()
        }
    }

    private fun setupPhotoWithLabel(batchItem: BatchItem) {
        photoWithLabelVM.bindPhotoWithLabel(batchItem.style?.imageUrl, batchItem.resellingInventoryType)
    }
}