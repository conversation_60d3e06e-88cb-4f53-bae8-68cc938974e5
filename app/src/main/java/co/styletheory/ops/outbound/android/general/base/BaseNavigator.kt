package co.styletheory.ops.outbound.android.general.base

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Parcelable
import android.provider.MediaStore
import androidx.fragment.app.Fragment
import org.greenrobot.eventbus.EventBus
import java.io.Serializable
import javax.inject.Inject


/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 18 October 2017.
 * Description
 *
 * <EMAIL>
 */

@Suppress("unchecked_cast")
abstract class BaseNavigator<T : BaseNavigator<T>> constructor(val activity: Activity) {

    @Inject
    lateinit var eventBus: EventBus

    private var flags = 0
    private var forResult = false
    private var requestCode = 0
    private var resultCode = 0
    private var fragment: Fragment? = null
    private val bundle = Bundle()
    private val resultBundle = Bundle()
    private var noAnimation = false

    fun clearStack(): T {
        this.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        return this as T
    }

    protected fun intent(clazz: Class<*>): Intent {
        val intent = Intent(this.activity, clazz)
        if(this.flags != 0) intent.flags = this.flags
        intent.putExtras(bundle)

        return intent
    }

    protected fun intent(url: String): Intent {
        val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        if(this.flags != 0) browserIntent.flags = this.flags
        browserIntent.putExtras(bundle)

        return browserIntent
    }

    protected fun callIntent(telephone: String): Intent {
        return Intent(Intent.ACTION_CALL, Uri.parse("tel:" + telephone))
    }

    protected fun intentResult(): Intent {
        val intent = Intent()
        intent.putExtras(resultBundle)
        return intent
    }


    fun withExtras(data: Bundle): T {
        bundle.putAll(data)
        return this as T
    }

    fun withExtra(key: String, value: String): T {
        bundle.putString(key, value)
        return this as T
    }

    fun withExtra(key: String, value: Boolean): T {
        bundle.putBoolean(key, value)
        return this as T
    }

    fun withExtra(key: String, parcel: Parcelable): T {
        bundle.putParcelable(key, parcel)
        return this as T
    }

    fun withExtra(key: String, extra: Serializable): T {
        bundle.putSerializable(key, extra)
        return this as T
    }

    fun withExtra(key: String, extra: Long): T {
        bundle.putSerializable(key, extra)
        return this as T
    }

    fun withExtra(key: String, extra: Int): T {
        bundle.putSerializable(key, extra)
        return this as T
    }

    fun withExtra(key: Any, extra: Serializable): T {
        bundle.putSerializable(key.toString(), extra)
        return this as T
    }

    fun withArrayListStringExtra(key: String, strings: ArrayList<String>): T {
        bundle.putStringArrayList(key, strings)
        return this as T
    }

    fun withExtraResult(key: String, parcel: Parcelable): T {
        resultBundle.putParcelable(key, parcel)
        return this as T
    }

    fun withExtraResultParcelable(key: String, parcelArray: ArrayList<out Parcelable>): T {
        resultBundle.putParcelableArrayList(key, parcelArray)
        return this as T
    }

    fun withExtraResult(key: String, stringArrayList: ArrayList<String>): T {
        resultBundle.putStringArrayList(key, stringArrayList)
        return this as T
    }

    fun withExtraResult(key: String, string: String): T {
        resultBundle.putString(key, string)
        return this as T
    }

    fun withExtraResult(key: String, value: Long): T {
        resultBundle.putLong(key, value)
        return this as T
    }

    fun withExtraResult(key: String, value: Boolean): T {
        resultBundle.putBoolean(key, value)
        return this as T
    }

    fun startForResult(): T {
        forResult = true
        return this as T
    }

    fun withFlags(flags: Int): T {
        this.flags = flags
        return this as T
    }


    fun startForResult(requestCode: Int): T {
        forResult = true
        this.requestCode = requestCode
        return this as T
    }

    fun withRequestCode(requestCode: Int): T {
        this.requestCode = requestCode
        return this as T
    }

    fun withResultCode(resultCode: Int): T {
        this.resultCode = resultCode
        return this as T
    }

    fun fromFragment(fragment: Fragment): T {
        if(fragment.parentFragment != null)
            this.fragment = fragment.parentFragment
        else
            this.fragment = fragment
        return this as T
    }

    fun noAnimation(): T {
        noAnimation = true
        return this as T
    }

    protected fun photoGalleryIntent(): Intent {
        val intent = Intent()
        intent.type = "image/*"
        intent.action = Intent.ACTION_GET_CONTENT
        intent.putExtras(bundle)
        return Intent.createChooser(intent, "Select picture")
    }

    protected fun cameraIntent(): Intent {
        val intent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
        if(this.flags != 0) intent.flags = this.flags
        intent.putExtras(bundle)
        return intent
    }

    protected fun startActivity(clazz: Class<*>) {
        if(fragment != null) {
            if(forResult)
                fragment?.startActivityForResult(intent(clazz), requestCode)
            else
                fragment?.startActivity(intent(clazz))

        } else {
            if(forResult)
                activity.startActivityForResult(intent(clazz), requestCode)
            else
                activity.startActivity(intent(clazz))
        }
        if(noAnimation) {
            activity.overridePendingTransition(0, 0)
            noAnimation = false
        }
    }

    protected fun startActivity(url: String) {
        if(fragment != null) {
            if(forResult)
                fragment?.startActivityForResult(intent(url), requestCode)
            else
                fragment?.startActivity(intent(url))
        } else {
            if(forResult)
                activity.startActivityForResult(intent(url), requestCode)
            else
                activity.startActivity(intent(url))
        }
    }

    fun toPhotoGallery() {
        if(fragment != null) {
            if(forResult)
                fragment?.startActivityForResult(photoGalleryIntent(), requestCode)
            else
                fragment?.startActivity(photoGalleryIntent())
        } else {
            if(forResult)
                activity.startActivityForResult(photoGalleryIntent(), requestCode)
            else
                activity.startActivity(photoGalleryIntent())
        }
    }

    fun toCamera() {
        val cameraIntent = cameraIntent()
        if(fragment != null) {
            if(forResult)
                fragment?.startActivityForResult(cameraIntent, requestCode)
            else
                fragment?.startActivity(cameraIntent)
        } else {
            if(forResult)
                activity.startActivityForResult(cameraIntent, requestCode)
            else
                activity.startActivity(cameraIntent)
        }
    }

    fun makeCall(telephone: String) {
        if(fragment != null) {
            if(forResult)
                fragment?.startActivityForResult(callIntent(telephone), requestCode)
            else
                fragment?.startActivity(callIntent(telephone))
        } else {
            if(forResult)
                activity.startActivityForResult(callIntent(telephone), requestCode)
            else
                activity.startActivity(callIntent(telephone))
        }
    }

    fun finishWithResult() {
        activity.setResult(resultCode, intentResult())
        activity.finish()

        if(noAnimation) {
            activity.overridePendingTransition(0, 0)
            noAnimation = false
        }
    }

    fun phoneCall(phoneNumber: String) {
        activity.startActivity(Intent(Intent.ACTION_DIAL, Uri.fromParts("tel", phoneNumber, null)))
    }
}