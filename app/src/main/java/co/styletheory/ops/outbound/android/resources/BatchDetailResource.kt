package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import retrofit2.Call
import retrofit2.Retrofit

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 14 November 2017.
 * Description
 *
 * <EMAIL>
 */
class BatchDetailResource constructor(private val batchId: String) : GraphQLResource<GraphQLData<Batch>>() {
    override fun graphQLRequest(): GraphQLRequest = GraphQLRequestList.BATCH_DETAIL

    override fun bodyParameter(): MutableMap<String, Any> {
        bodyParameter["id"] = batchId
        return bodyParameter
    }

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<Batch>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.fetchBatchDetail(uri, createRequestBody()))
    }

}