package co.styletheory.ops.outbound.android.feature.packing.viewModel.impl

import androidx.fragment.app.Fragment
import co.styletheory.android.network.core.RequestResult
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.packing.event.PackingItemUIEvent
import co.styletheory.ops.outbound.android.feature.packing.view.PackingDetailItemFragment
import co.styletheory.ops.outbound.android.feature.packing.viewModel.PackingDetailViewModel
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.listener.OnKeyListener
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.BatchDetailResource
import co.styletheory.ops.outbound.android.resources.CompleteBatchResource
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.util.notNull
import co.styletheory.ops.outbound.android.util.notNullOrEmpty
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel
import javax.inject.Inject

/**
 * Created by giorgygunawan on 11/29/17.
 *
 */
class PackingDetailViewModelImpl @Inject constructor() : BaseViewModel<PackingDetailViewModelImpl>(), PackingDetailViewModel, OnKeyListener {
    @Inject
    lateinit var toolbarViewModel: GeneralToolbarViewModel
    @Inject
    lateinit var dataService: DataService
    @Inject
    lateinit var errorResponse: ErrorResponse

    init {
        viewModelClass = PackingDetailViewModelImpl::class
    }

    override val shipments = mutableListOf<Shipment>()
    override var batchId: String = ""
    override var batch: Batch? = null
    override var batchStatus: BatchStatus? = null
    override val onEditTextKeyListener: OnKeyListener = this
    override val scannedRfidText = ObservableString()

    override fun onKeyTapped() {
        scannedRfidText.get().notNullOrEmpty {
            if(featureFlagUtil.isBarcodeSettingIsOn()) {
                checkScannedText()
            }
        }
    }

    override fun changeToolbarSubtitle(subtitle: String?) {
        toolbarViewModel.subtitle.set(subtitle)
    }

    override fun generalToolbarViewModel(): GeneralToolbarViewModel = toolbarViewModel


    override fun fetchBatchDetail(callback: Result<Void?, String?>?) {
        //TODO: might be fetching a different endpoint according to backend updates
        dataService.fetchBatchDetail(
            BatchDetailResource(batchId),
            RequestResult {
                onSuccess {
                    it?.data?.result.notNull { data ->
                        shipments.clear()
                        batch = data
                        batch?.shipments?.let { it1 -> shipments.addAll(it1) }
                        toolbarViewModel.title.set("${batch?.name} - Packing")
                        if(shipments.size > 0) {
                            toolbarViewModel.subtitle.set("1 of ${shipments.size} box")
                        } else {
                            toolbarViewModel.subtitle.set("Empty picked box")
                        }
                    }
                    callback?.success(null)
                }
                onError { callback?.failure(errorResponse.getErrorBodyDescription(it)) }
            }
        )
    }

    override fun completeBatch(callback: Result<Void?, String?>?) {
        dataService.completeBatch(
            CompleteBatchResource(batchId, CompleteBatchResource.Type.PACKING),
            RequestResult {
                onSuccess { callback?.success(null) }
                onError { callback?.failure(errorResponse.getErrorBodyDescription(it)) }
            }
        )
    }

    override fun isAllBatchItemShipmentCompletedPacking(): Boolean {
        if(shipments.size == 0) return false
        for(item in shipments) {
            if(!item.isComplete) {
                return false
            }
        }
        return true
    }

    override fun checkScannedText() {
        val rfidText = scannedRfidText.get()
        val position = shipments.indexOfFirst { shipment -> shipment.items.map { it.rfid }.contains(rfidText) }

        if(position >= 0) {
            checkBatchItemStatus(position, rfidText)
            eventBus.post(PackingItemUIEvent.OnScannedRfidFoundPosition(position, rfidText))
        } else {
            eventBus.post(
                PackingItemUIEvent.OnScannedRfidTextNotFound(
                    string(R.string.warn_rfid_notfound_title),
                    string(R.string.warn_rfid_notfound_message).format(scannedRfidText.get())
                )
            )
        }
    }

    override fun checkBatchItemStatus(position: Int, rfidText: String) {
        val batchItem = batch?.items?.firstOrNull { it.rfid.equals(rfidText, true) }

        if(batchItem?.isInReadyTab() == false) {
            eventBus.post(
                    PackingItemUIEvent.OnScannedRfidTextNotFound(
                            string(R.string.warn_rfid_can_not_process_title),
                            string(R.string.warn_rfid_current_status_message).format(batchItem.status, scannedRfidText.get())
                    )
            )
        }
    }

    //region PAGER ADAPTER SOURCE
    override val totalViewCount: Int get() = shipments.size

    override fun getItemTypeAtPosition(position: Int): Fragment {
        if(position > shipments.size) return Fragment()
        val shipment = shipments[position]
        val rackSection = if(shipment.items.isNotEmpty()) {
            batch?.getRackSectionFromBatchItemId(shipment.items[0].id)
        } else {
            ""
        }
        return PackingDetailItemFragment.newInstance(batch, shipment, rackSection, batch?.rack?.name)
    }

    override fun cacheFragment(): Boolean = true
    override fun getPageTitleAtPosition(position: Int): CharSequence = ""

    //endregion
}