package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.model.response.OrderGojekResponse
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat
import retrofit2.Call
import retrofit2.Retrofit

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 19 June 2018.
 * Description
 *
 * <EMAIL>
 */
class OrderGojekResource(val date: DateTime, val shipmentIds: List<String>) : GraphQLResource<GraphQLData<OrderGojekResponse>>() {

    override fun graphQLRequest(): GraphQLRequest = GraphQLRequestList.ORDER_GOJEK_SHIPMENTS

    override fun bodyParameter(): MutableMap<String, Any> {
        DateTimeFormat.shortDate()
        bodyParameter["date"] = date.toString("yyyy-MM-dd")
        bodyParameter["shipmentIds"] = shipmentIds
        return super.bodyParameter()
    }

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<OrderGojekResponse>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.orderGojekShipments(baseUrl, createRequestBody()))
    }
}