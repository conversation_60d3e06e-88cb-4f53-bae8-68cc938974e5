package co.styletheory.ops.outbound.android.general.viewModel.base

import android.content.Context
import android.graphics.drawable.Drawable
import androidx.annotation.*
import androidx.core.content.ContextCompat
import co.styletheory.ops.outbound.android.general.base.BaseEvent
import co.styletheory.ops.outbound.android.general.binding.ObservableFeatureFlagBoolean
import co.styletheory.ops.outbound.android.general.binding.ObservableFeatureFlagVisibility
import co.styletheory.ops.outbound.android.general.event.DismissProgressDialogEvent
import co.styletheory.ops.outbound.android.general.event.ShowProgressDialogEvent
import co.styletheory.ops.outbound.android.general.event.ShowToastEvent
import co.styletheory.ops.outbound.android.util.featureflag.FeatureFlagUtil
import co.styletheory.ops.outbound.android.util.notNull
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import javax.inject.Inject
import kotlin.reflect.KClass
import kotlin.reflect.full.isSubtypeOf
import kotlin.reflect.full.memberProperties
import kotlin.reflect.full.starProjectedType
import kotlin.reflect.jvm.isAccessible


abstract class BaseViewModel<T : ViewModel> : ViewModel {

    @Inject
    lateinit var eventBus: EventBus
    @Inject
    lateinit var context: Context
    @Inject
    lateinit var featureFlagUtil: FeatureFlagUtil

    lateinit var viewModelClass: KClass<T>

    override fun onDestroy() {
        removeToggleListener()
    }

    override fun afterInject() {
        if(!eventBus.isRegistered(this)) {
            eventBus.register(this)
        }
    }

    @Suppress("UNCHECKED_CAST")
    fun removeToggleListener() {
        try {
            viewModelClass.notNull {
                for(prop in it.memberProperties) {
                    if(prop.returnType.isSubtypeOf(ObservableFeatureFlagVisibility::class.starProjectedType)) {
                        prop.isAccessible = true
                        (prop.get(this as T) as ObservableFeatureFlagVisibility).unregisterToggleListener()
                    } else if(prop.returnType.isSubtypeOf(ObservableFeatureFlagBoolean::class.starProjectedType)) {
                        prop.isAccessible = true
                        (prop.get(this as T) as ObservableFeatureFlagBoolean).unregisterToggleListener()
                    }
                }
            }
        } catch(e: UninitializedPropertyAccessException) {
            //Not implemented yet
        }
    }

    @Suppress("UNUSED_PARAMETER", "unused")
    @Subscribe
    fun onEventMainThread(event: BaseEvent) {
        //Not implemented yet
    }

    fun showProgressDialog() = eventBus.post(ShowProgressDialogEvent())
    fun dismissProgressDialog() = eventBus.post(DismissProgressDialogEvent())
    fun showToast(message: String?, isLong: Boolean = false) = eventBus.post(ShowToastEvent(message, isLong))

    fun dimen(@DimenRes resId: Int): Int = context.resources.getDimension(resId).toInt()

    fun color(@ColorRes resId: Int): Int = ContextCompat.getColor(context, resId)

    fun drawable(@DrawableRes resId: Int): Drawable? = ContextCompat.getDrawable(context, resId)

    fun integer(@IntegerRes resId: Int): Int = context.resources.getInteger(resId)

    fun string(@StringRes resId: Int): String = context.getString(resId)
}
