package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import retrofit2.Call
import retrofit2.Retrofit

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 20 November 2017.
 * Description
 *
 * <EMAIL>
 */
class SwapItemResource constructor(private val shipmentId: String,
                                   private val oldItemUUID: String,
                                   private val newItemUUID: String) : GraphQLResource<GraphQLData<Void>>() {

    override fun graphQLRequest(): GraphQLRequest = GraphQLRequestList.SWAP_SHIPMENT_ITEM

    override fun bodyParameter(): MutableMap<String, Any> {
        bodyParameter["shipmentId"] = shipmentId
        bodyParameter["oldItemId"] = oldItemUUID
        bodyParameter["newItemId"] = newItemUUID
        return bodyParameter
    }

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<Void>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.swapItem(uri, createRequestBody()))
    }
}