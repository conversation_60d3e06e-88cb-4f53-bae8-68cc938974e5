package co.styletheory.ops.outbound.android.general.recyclerView

import androidx.recyclerview.widget.LinearLayoutManager
import co.styletheory.ops.outbound.android.general.base.BaseRecyclerViewInterceptor

/**
 *   Created by Alham Wa on 26/02/19
 */
class RecyclerViewSetting {
    var layoutId: Int = 0
    var column: Int = 0
    var orientation: Int = LinearLayoutManager.HORIZONTAL
    var layoutManagerType: LayoutManagerType = LayoutManagerType.LINEAR
    var isAttachToSnap: Boolean = false
    var interceptor: BaseRecyclerViewInterceptor? = null
    var isItemPrefetchCountActive: Boolean = false
    var isNestedScrolling: Boolean? = null
    var isAttachToFastScroller: Boolean? = null

    enum class LayoutManagerType {
        LINEAR, GRID, SCROLLABLE, CUSTOM_GRID
    }
}