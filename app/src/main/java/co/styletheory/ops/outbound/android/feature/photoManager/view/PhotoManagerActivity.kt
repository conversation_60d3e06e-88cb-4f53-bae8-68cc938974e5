package co.styletheory.ops.outbound.android.feature.photoManager.view

import android.content.Intent
import android.os.Bundle
import android.os.Parcelable
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.recyclerview.widget.GridLayoutManager
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.PhotoManagerActivityBinding
import co.styletheory.ops.outbound.android.feature.photoManager.event.PhotoManagerDeleteEvent
import co.styletheory.ops.outbound.android.feature.photoManager.event.RequestErrorEvent
import co.styletheory.ops.outbound.android.feature.photoManager.viewModel.PhotoManagerViewModel
import co.styletheory.ops.outbound.android.general.adapter.impl.RecyclerViewAdapterImpl
import co.styletheory.ops.outbound.android.general.base.BaseActivity
import co.styletheory.ops.outbound.android.general.event.GeneralPhotoGridItemClickedEvent
import co.styletheory.ops.outbound.android.general.event.GeneralPhotoGridItemUploadedEvent
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.enums.AttachPhotoType
import co.styletheory.ops.outbound.android.util.EdgeToEdgeUtil
import co.styletheory.ops.outbound.android.util.IntentConstant
import co.styletheory.ops.outbound.android.util.PermissionHandlerActivity
import co.styletheory.ops.outbound.android.util.PermissionUtils
import co.styletheory.ops.outbound.android.util.RequestCode
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.util.getParcelableArrayListExtraProvider
import co.styletheory.ops.outbound.android.util.getParcelableExtraProvider
import co.styletheory.ops.outbound.android.util.getSerializableExtraProvider
import co.styletheory.ops.outbound.android.util.notNull
import com.nguyenhoanglam.imagepicker.widget.GridSpacingItemDecoration
import com.styletheory.camera.screens.home.PhotosPickerActivity
import com.styletheory.camera.shared.models.Photo
import org.greenrobot.eventbus.Subscribe
import org.parceler.Parcels
import javax.inject.Inject

/**
 * Created by giorgygunawan on 11/7/17.
 *
 */
class PhotoManagerActivity : BaseActivity<PhotoManagerActivityBinding, PhotoManagerViewModel>(), PermissionHandlerActivity {
    companion object {
        fun createExtras(shipmentId: String, item: BatchItem?, attachPhotoType: AttachPhotoType, photos: ArrayList<Photo>?): Bundle {
            val bundle = Bundle()
            bundle.putString(IntentConstant.SHIPMENT_ID, shipmentId)
            bundle.putSerializable(IntentConstant.ATTACH_PHOTO_TYPE, attachPhotoType)
            bundle.putParcelable(IntentConstant.BATCH_ITEM, Parcels.wrap(item))
            bundle.putParcelableArrayList(
                PhotosPickerActivity.EXTRA_PHOTOS, photos
                    ?: arrayListOf<Photo>()
            )
            return bundle
        }
    }

    private var batchItem: BatchItem? = null
    private var shipmentId: String? = null
    private var attachPhotoType: AttachPhotoType? = null
    private var photos: List<Photo> = emptyList()

    @Inject
    lateinit var adapter: RecyclerViewAdapterImpl

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        activityComponent?.inject(this)
        bindContentView(R.layout.photo_manager_activity)
        configure()

        // Handle back button press with the new API
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                shouldFinish()
            }
        })
    }

    private fun configure() {
        configureExtras()
        configureToolbar()
        configureViewModel()
        configureView()
        configureAdapter()
    }

    private fun configureExtras() {
        intent.getStringExtra(IntentConstant.SHIPMENT_ID).notNull { shipmentId = it }
        getSerializableExtraProvider<AttachPhotoType>(intent, IntentConstant.ATTACH_PHOTO_TYPE).notNull { attachPhotoType = it }
        intent.getParcelableExtraProvider<Parcelable>(IntentConstant.BATCH_ITEM).notNull { batchItem = Parcels.unwrap(it) }
        intent.getParcelableArrayListExtraProvider<Photo>(PhotosPickerActivity.EXTRA_PHOTOS).notNull { photos = it }
    }

    private fun configureToolbar() {
        setSupportActionBar(binding?.toolbarWrapper?.toolbar as Toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        binding?.toolbarVM = viewModel?.toolbarViewModel
    }

    private fun configureView() {
        val layoutManager = GridLayoutManager(this, 3)
        binding?.recylerView?.setHasFixedSize(true)
        binding?.recylerView?.layoutManager = layoutManager
        binding?.recylerView?.addItemDecoration(GridSpacingItemDecoration(3, resources.getDimensionPixelSize(R.dimen.size_spacing_grid), true))
    }

    private fun configureViewModel() {
        if(shipmentId != null && batchItem != null) {
            viewModel?.fillNecessaryFields(
                shipmentId!!,
                batchItem!!,
                true,
                object : PhotoManagerViewModel.Listener {
                    override fun onSelectChooseOrTakePhotoCallback() {
                        showPhotoGalleryOrTakePictureActivity()
                    }
                })
        } else {
            Toast.makeText(this, "not enough arguments for this activity", Toast.LENGTH_LONG).show()
            finish()
        }
    }

    @Suppress("UNCHECKED_CAST")
    private fun configureAdapter() {
        adapter.layoutId = R.layout.general_photo_grid_item
        adapter.items = viewModel?.photoViewModels as MutableList<Any>
        binding?.recylerView?.adapter = adapter
        processData(photos)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if(resultCode != RESULT_OK) {
            return
        }
        if(requestCode == RequestCode.REQ_PHOTOS) {
            val photos = data?.getParcelableArrayListExtraProvider<Photo>(PhotosPickerActivity.EXTRA_PHOTOS) ?: return
            processData(photos)
        }
    }

    private fun shouldFinish() {
        for(photo in viewModel!!.photoViewModels) {
            if(photo.getIsLoadImage() && !isFinishing) {
                AlertDialog.Builder(this)
                    .setCancelable(true)
                    .setMessage("Image is being uploaded")
                    .setNegativeButton("Okay", null)
                    .show()
                return
            }
        }
    }

    private fun shouldAutoBackAfterUpload() {
        if(viewModel?.photoViewModels?.isNotEmpty() == true) {
            doAttachImageToBatchItem()
        } else {
            finish()
        }
    }

    private fun doAttachImageToBatchItem() {
        if(attachPhotoType != null) {
            showProgressDialog("Attaching Image to Item")
            viewModel?.attachImageToItem(attachPhotoType, object : Result<Void?, String?> {
                override fun success(success: Void?) {
                    dismissProgressDialog()
                    finishActivity()
                }

                override fun failure(error: String?) {
                    dismissProgressDialog()
                    showShortToast(error ?: string(R.string.err_something_when_wrong))
                    AlertDialog.Builder(this@PhotoManagerActivity)
                        .setMessage("Failed to attach")
                        .setPositiveButton("Try Again") { action, _ ->
                            action.dismiss()
                            shouldAutoBackAfterUpload()
                        }.setNegativeButton("Exit") { action, _ ->
                            action.dismiss()
                            finish()
                        }.show()
                }
            })
        } else {
            showShortToast("Photo item not attached")
        }
    }

    private fun processData(photos: List<Photo>) {
        val imageUrls = photos.mapNotNull { it.uri.path }
        viewModel?.addNewPhotoUrls(imageUrls)
        adapter.notifyDataSetChanged()
    }

    //region EVENT BUS
    @Subscribe
    fun onPhotoDeleted(event: PhotoManagerDeleteEvent) {
        viewModel?.deleteViewModel(event.viewModel)
        adapter.notifyDataSetChanged()
    }

    @Subscribe
    fun onPhotoClicked(event: GeneralPhotoGridItemClickedEvent) {
        viewModel?.let {
            navigator.toFullScreenImageSlideShowActivity(it.getAllImageUrls(), event.position)
        }
    }

    @Subscribe
    fun onRequestError(event: RequestErrorEvent) {
        Toast.makeText(this, event.errorString, Toast.LENGTH_LONG).show()
    }

    @Subscribe
    fun onPhotoUploaded(event: GeneralPhotoGridItemUploadedEvent) {
        if(viewModel?.removePhotoAfterUploaded(event.viewModel) == true) {
            shouldAutoBackAfterUpload()
        }
    }

    //endregion

    private fun finishActivity() {
        navigator.withExtraResult(
            IntentConstant.PRODUCT_PHOTO_RESULT_QAIMAGES_EXTRA_CODE, viewModel?.getAllImageUrls()
                ?: arrayListOf()
        )
            .withExtraResult(
                IntentConstant.PRODUCT_PHOTO_RESULT_PRODUCT_EXTRA_CODE, viewModel?.batchItem?.id
                    .orEmpty()
            )
            .withResultCode(RESULT_OK)
            .finishWithResult()
    }

    // Implementation of PermissionHandlerActivity interface
    override fun getActivity(): AppCompatActivity = this

    override fun onAllPermissionsGranted(requestCode: Int) {
        EdgeToEdgeUtil.preparePhotoPickerActivityEdgeToEdge()
        PhotosPickerActivity.launch(this, RequestCode.REQ_PHOTOS)
    }

    private fun showPhotoGalleryOrTakePictureActivity() {
        // Use the new permission utility to handle permissions
        PermissionUtils.launchPhotoPicker(this, RequestCode.RC_CAMERA_AND_STORAGE)
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        // Let the permission utility handle the result
        PermissionUtils.handlePermissionResult(this, requestCode, permissions, grantResults)
    }
}