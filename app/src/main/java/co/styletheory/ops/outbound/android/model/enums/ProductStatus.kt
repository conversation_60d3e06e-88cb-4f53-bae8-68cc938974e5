package co.styletheory.ops.outbound.android.model.enums

import com.google.gson.annotations.SerializedName

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 14 November 2017.
 * Description
 *
 * <EMAIL>
 */

enum class ProductStatus (val text: String){
    @SerializedName("Picked")
    PICKED("Picked"),
    @SerializedName("Picking")
    PICKING("Picking"),
    @SerializedName("NotFound")
    NOT_FOUND("NotFound"),
    @SerializedName("Rented")
    RENTED("Rented"),
    @SerializedName("Missing")
    MISSING("Missing"),
    @SerializedName("Available")
    AVAILABLE("Available"),
    @SerializedName("InInspection")
    IN_INSPECTION("InInspection"),
    @SerializedName("Washing")
    WASHING("Washing"),
    @SerializedName("OnShippingToCustomer")
    ON_SHIPPING_TO_CUSTOMER("OnShippingToCustomer"),
    @SerializedName("OnShippingHome")
    ON_SHIPPING_HOME("OnShippingHome"),
    @SerializedName("Created")
    CREATED("Created"),
    @SerializedName("Hold")
    HOLD("Hold"),
    @SerializedName("Retired")
    RETIRED("Retired"),
    @SerializedName("QA")
    QA("QA"),
    @SerializedName("QAPassed")
    QA_PASSED("QAPassed"),
    @SerializedName("QAFailedConfirmed")
    QA_FAILED_CONFIRMED("QAFailedConfirmed"),
    @SerializedName("QAFailed")
    QA_FAILED("QAFailed"),
    @SerializedName("PhotoQA")
    PHOTO_QA("PhotoQA"),
    @SerializedName("PhotoQADone")
    PHOTO_QA_DONE("PhotoQADone"),
    @SerializedName("Packing")
    PACKING("Packing"),
    @SerializedName("Packed")
    PACKED("Packed"),
    @SerializedName("ReceivedByCustomer")
    RECEIVED_BY_CUSTOMER("ReceivedByCustomer"),
    @SerializedName("TransitToCustomer")
    TRANSIT_TO_CUSTOMER("TransitToCustomer"),
    @SerializedName("Paid")
    PAID("Paid")
}