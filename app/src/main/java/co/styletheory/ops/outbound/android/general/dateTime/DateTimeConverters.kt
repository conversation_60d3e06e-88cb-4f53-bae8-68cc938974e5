package co.styletheory.ops.outbound.android.general.dateTime

import com.google.gson.*
import org.joda.time.DateTime
import org.joda.time.format.ISODateTimeFormat
import java.lang.reflect.Type

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 9/27/16.
 */
class DateTimeConverters : JsonSerializer<DateTime>, JsonDeserializer<DateTime> {
    override fun serialize(src: DateTime, typeOfSrc: Type, context: JsonSerializationContext): JsonElement {
        //TODO: Read annotation from the src, then adjust the formatter.
        val fmt = ISODateTimeFormat.dateTime()
        return JsonPrimitive(fmt.print(src))
    }

    override fun deserialize(json: JsonElement, typeOfT: Type, context: JsonDeserializationContext): DateTime {
        val dateTimeNoMillisFmt = ISODateTimeFormat.dateTime()
        val dateFmt = ISODateTimeFormat.date()

        return try {
            dateTimeNoMillisFmt.parseDateTime(json.asString)
        } catch (e: IllegalArgumentException) {
            dateFmt.parseDateTime(json.asString)
        }

    }
}
