package co.styletheory.ops.outbound.android.general.test

import android.os.Bundle
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.EmptyActivityBinding
import co.styletheory.ops.outbound.android.general.base.BaseActivity

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 04 December 2017.
 * Description
 *
 * <EMAIL>
 */
class EmptyActivity : BaseActivity<EmptyActivityBinding, EmptyViewModel>(){

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        activityComponent?.inject(this)
        bindContentView(R.layout.empty_activity)
    }
}