package co.styletheory.ops.outbound.android

import android.app.Application
import co.styletheory.android.network.StyleNetworking
import co.styletheory.android.network.interceptors.HeaderInterceptor
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.injection.Dagger2Helper
import co.styletheory.ops.outbound.android.injection.component.AppComponent
import co.styletheory.ops.outbound.android.injection.component.DaggerAppComponent
import co.styletheory.ops.outbound.android.injection.component.DirectInjectComponent
import co.styletheory.ops.outbound.android.injection.module.ApiModule
import co.styletheory.ops.outbound.android.injection.module.AppModule
import co.styletheory.ops.outbound.android.injection.module.DirectInjectModule
import co.styletheory.ops.outbound.android.networking.TokenManager
import co.styletheory.ops.outbound.android.util.AppConstant
import co.styletheory.ops.outbound.android.util.notNull
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.gu.toolargetool.TooLargeTool
import com.orhanobut.hawk.Hawk
import com.prateekj.snooper.AndroidSnooper
import com.prateekj.snooper.okhttp.SnooperInterceptor


/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 18 October 2017.
 * Description
 *
 * <EMAIL>
 */
class StyletheoryOpsApplication : Application() {

    companion object {
        var instance: StyletheoryOpsApplication? = null
        fun directInject(target: Any) {
            instance?.directInjectComponent.notNull {
                Dagger2Helper.inject(DirectInjectComponent::class.java, it, target)
            }
        }
    }

    var appComponent: AppComponent? = null
    var directInjectComponent: DirectInjectComponent? = null

    override fun onCreate() {
        super.onCreate()
        instance = this

        FirebaseCrashlytics.getInstance().setCrashlyticsCollectionEnabled(true)

        StyleNetworking.setup(this, BuildConfig.BASE_URL)
                .setDefaultHeader(object : HeaderInterceptor.DefaultHeader {
                    override fun addHeader(header: MutableMap<String, String>) {
                        header["Region"] = UserStorage().getUserRegion().id.orEmpty()
                        header["Vertical-Type"] = UserStorage().getUserVerticalType().id.orEmpty()
                        header["App-Name"] = AppConstant.OUTBOUND_APP_NAME
                        header["App-Version"] = BuildConfig.VERSION_NAME

                        if(UserStorage().getUserVerticalType().id == "on_demand") {
                            header["Accept-Version"] = "v2"
                        } else {
                            header["Accept-Version"] = "v1"
                        }
                    }
                }).setTokenHandler(TokenManager())

        initializeInjector(AppModule(this), ApiModule(this))

        Hawk.init(this).build()

        if(!BuildConfig.BUILD_TYPE.equals(AppConstant.BUILD_TYPE_RELEASE, true)) {
            AndroidSnooper.init(this)
            TooLargeTool.startLogging(this)
            StyleNetworking.addInterceptors(SnooperInterceptor())
        }
        StyleNetworking.create()
    }

    fun initializeInjector(appModule: AppModule, apiModule: ApiModule) {
        appComponent = DaggerAppComponent.builder()
                .appModule(appModule)
                .apiModule(apiModule)
                .build()
        appComponent.notNull {
            directInjectComponent = Dagger2Helper.buildComponent(DirectInjectComponent::class.java, it, DirectInjectModule())
        }
    }

    fun initializeInjector(appComponent: AppComponent?) {
        appComponent.notNull {
            directInjectComponent = Dagger2Helper.buildComponent(DirectInjectComponent::class.java, it, DirectInjectModule())
        }
    }

}