package co.styletheory.ops.outbound.android.feature.scanBarcodeBottomSheet.view

import android.os.Bundle
import android.os.Parcelable
import android.view.View
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.BottomsheetScanBarcodeBinding
import co.styletheory.ops.outbound.android.feature.scanBarcodeBottomSheet.event.ScanBarcodeBottomSheetUIEvent
import co.styletheory.ops.outbound.android.feature.scanBarcodeBottomSheet.viewModel.ScanBarcodeBottomSheetViewModel
import co.styletheory.ops.outbound.android.general.base.BaseBottomSheetDialog
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.util.IntentConstant
import co.styletheory.ops.outbound.android.util.hideSoftKeyboard
import co.styletheory.ops.outbound.android.util.notNull
import org.greenrobot.eventbus.Subscribe
import org.parceler.Parcels

/**
 * Created by Yoga C. Pranata on 13/05/20.
 * Android Engineer
 */
@Suppress("UNUSED_PARAMETER")
class ScanBarcodeBottomSheetDialog : BaseBottomSheetDialog<BottomsheetScanBarcodeBinding, ScanBarcodeBottomSheetViewModel>() {

    companion object {
        fun newInstance(swapItem: SwapItem): ScanBarcodeBottomSheetDialog {
            val dialog = ScanBarcodeBottomSheetDialog()
            val bundle = Bundle()
            bundle.putParcelable(IntentConstant.SWAP_ITEM, Parcels.wrap(swapItem))
            dialog.arguments = bundle
            return dialog
        }
    }

    override fun getLayoutId(): Int = R.layout.bottomsheet_scan_barcode

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if(viewModel == null) {
            fragmentComponent?.inject(this)
        }
    }

    override fun onLoadBottomSheetDialog(view: View, saveInstance: Bundle?) {
        isCancelable = false
        initArguments()
    }

    private fun initArguments() {
        arguments?.getParcelable<Parcelable>(IntentConstant.SWAP_ITEM).notNull {
            val item = Parcels.unwrap<SwapItem>(it)
            viewModel?.swapItem = item
        }
    }

    private fun hideKeyboardAndClearText() {
        binding?.editTextScannedRfid?.let { context?.hideSoftKeyboard(it) }
        binding?.editTextScannedRfid?.text?.clear()
    }

    @Subscribe
    fun onCloseClicked(event: ScanBarcodeBottomSheetUIEvent.OnCloseClicked) {
        dismiss()
    }

    @Subscribe
    fun onScannedRfid(event: ScanBarcodeBottomSheetUIEvent.OnScannedRfid) {
        hideKeyboardAndClearText()
        dismiss()
    }

}