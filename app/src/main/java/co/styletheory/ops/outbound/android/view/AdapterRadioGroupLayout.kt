package co.styletheory.ops.outbound.android.view

import android.content.Context
import android.database.DataSetObserver
import android.util.AttributeSet
import android.view.View
import android.widget.LinearLayout
import android.widget.ListAdapter
import android.widget.RadioGroup

/**
 * Created by Yoga C. Pranata on 8/21/19.
 * Android Engineer
 */
class AdapterRadioGroupLayout @JvmOverloads constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private var listAdapter: ListAdapter? = null
    private var convertViews: Array<View?>? = null
    private val radioGroup = RadioGroup(context)

    fun setAdapter(listAdapter: ListAdapter) {
        this.listAdapter = listAdapter

        refreshView()

        listAdapter.registerDataSetObserver(object : DataSetObserver() {
            override fun onChanged() {
                super.onChanged()
                refreshView()
            }
        })
    }

    fun refreshView() {
        val listCount = listAdapter?.count ?: 0
        radioGroup.orientation = RadioGroup.VERTICAL
        radioGroup.id = View.generateViewId()

        if(listCount > 0) {
            convertViews = arrayOfNulls(size = listCount)
            removeAllViews()
            radioGroup.clearCheck()
            for(position in 0 until listCount) {
                convertViews?.set(position, listAdapter?.getView(position, convertViews?.get(position), this))
                radioGroup.addView(convertViews?.get(position))
            }
        }

        addView(radioGroup)
    }
}