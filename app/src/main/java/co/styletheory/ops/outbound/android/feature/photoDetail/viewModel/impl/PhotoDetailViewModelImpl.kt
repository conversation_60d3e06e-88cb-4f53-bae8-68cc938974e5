package co.styletheory.ops.outbound.android.feature.photoDetail.viewModel.impl

import androidx.fragment.app.Fragment
import co.styletheory.android.network.core.RequestResult
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.photoDetail.view.PhotoDetailItemFragment
import co.styletheory.ops.outbound.android.feature.photoDetail.viewModel.PhotoDetailViewModel
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.BatchDetailResource
import co.styletheory.ops.outbound.android.resources.CompleteBatchResource
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.util.notNull
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel
import javax.inject.Inject

/**
 * Created by giorgygunawan on 11/27/17.
 *
 */
class PhotoDetailViewModelImpl @Inject constructor() : BaseViewModel<PhotoDetailViewModelImpl>(), PhotoDetailViewModel {
    @Inject
    lateinit var toolbarViewModel: GeneralToolbarViewModel
    @Inject
    lateinit var dataService: DataService
    @Inject
    lateinit var errorResponse: ErrorResponse

    init {
        viewModelClass = PhotoDetailViewModelImpl::class
    }

    override var shipmentId: String = ""
    override var batchItem: BatchItem? = null

    val shipments = mutableListOf<Shipment>()

    private var batchId: String = ""
    private var batch: Batch? = null
    private var batchStatus: BatchStatus? = null

    override fun setBatchId(batchId: String) {
        this.batchId = batchId
    }

    override fun setBatchStatus(batchStatus: BatchStatus) {
        this.batchStatus = batchStatus
    }

    override fun batchName(): String = batch?.name.orEmpty()

    override fun generalToolbarViewModel(): GeneralToolbarViewModel = toolbarViewModel

    //todo: might be fetching a different endpoint according to backend updates
    override fun fetchBatchDetail(callback: Result<Void?, String?>?) {
        dataService.fetchBatchDetail(
            BatchDetailResource(batchId),
            RequestResult {
                onSuccess {
                    it?.data?.result.notNull { dataBatch ->
                        shipments.clear()
                        batch = dataBatch
                        for(data in dataBatch.shipments) {
                            if(data.isPhotoItemsAvailable()) {
                                <EMAIL>(data)
                            }
                        }
                        toolbarViewModel.title.set("${dataBatch.name} - Photo")
                        if(shipments.size > 0) {
                            toolbarViewModel.subtitle.set("1 of ${shipments.size} box")
                        } else {
                            toolbarViewModel.subtitle.set("empty picked box")
                        }
                    }
                    callback?.success(null)
                }
                onError { callback?.failure(errorResponse.getErrorBodyDescription(it)) }
            }
        )
    }

    override fun completeBatch(callback: Result<Void?, String?>?) {
        dataService.completeBatch(
            CompleteBatchResource(batchId, CompleteBatchResource.Type.PHOTO),
            RequestResult {
                onSuccess { callback?.success(null) }
                onError { callback?.failure(errorResponse.getErrorBodyDescription(it)) }
            }
        )
    }

    override fun isAllBatchItemShipmentCompletedPhoto(): Boolean {
        if(shipments.size == 0) return false
        var completeByViewModel = true
        var completeByModel = true
        for(item in shipments) {
            if(!item.isComplete) completeByViewModel = false
            if(!item.isQABoxItemComplete()) completeByModel = false
        }
        return (completeByModel || completeByViewModel)
    }

    override fun changeToolbarSubtitle(subtitle: String?) {
        toolbarViewModel.subtitle.set(subtitle)
    }

    //region PAGER ADAPTER SOURCE
    override val totalViewCount: Int get() = shipments.size

    override fun getItemTypeAtPosition(position: Int): Fragment {
        val shipment = shipments[position]
        val rackSection = if(shipment.items.isNotEmpty()) {
            batch?.getRackSectionFromBatchItemId(shipment.items[0].id)
        } else {
            ""
        }
        return PhotoDetailItemFragment.newInstance(shipment, batch?.rack?.name, rackSection, batchId)
    }

    override fun cacheFragment(): Boolean = true
    override fun getPageTitleAtPosition(position: Int): CharSequence = string(R.string.box_title).format(position + 1)
    //endregion
}