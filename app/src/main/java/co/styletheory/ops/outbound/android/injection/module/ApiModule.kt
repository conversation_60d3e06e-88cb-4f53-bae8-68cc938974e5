package co.styletheory.ops.outbound.android.injection.module

import android.content.Context
import co.styletheory.android.network.StyleNetworking
import co.styletheory.android.network.core.APIManager
import co.styletheory.ops.outbound.android.injection.scope.PerApplication
import co.styletheory.ops.outbound.android.networking.DataService
import dagger.Module
import dagger.Provides

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 18 October 2017.
 * Description
 *
 * <EMAIL>
 */

@Module
class ApiModule(val context: Context) {

    @PerApplication
    @Provides
    fun provideDataService(): DataService {
        return DataService()
    }

    @PerApplication
    @Provides
    fun provideAPIManager(): APIManager {
        return StyleNetworking.apiManager
    }
}