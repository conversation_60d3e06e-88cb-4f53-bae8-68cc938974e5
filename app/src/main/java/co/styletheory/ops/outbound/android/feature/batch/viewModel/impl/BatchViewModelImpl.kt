package co.styletheory.ops.outbound.android.feature.batch.viewModel.impl

import androidx.databinding.ObservableBoolean
import co.styletheory.android.network.core.RequestResult
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.batch.viewModel.BatchListViewModel
import co.styletheory.ops.outbound.android.feature.batch.viewModel.BatchViewModel
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.injection.scope.PerFragment
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.BatchResource
import co.styletheory.ops.outbound.android.util.*
import co.styletheory.ops.outbound.android.viewModelComponent.HeaderTotalBatchViewModel
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 31 October 2017.
 * Description
 *
 * <EMAIL>
 */

@PerFragment
class BatchViewModelImpl @Inject constructor() : BaseViewModel<BatchViewModelImpl>(), BatchViewModel {

    private val TOTAL_BATCH_ITEM_TYPE = 0
    private val BATCH_ITEM_TYPE = 1
    private var batchStatus: BatchStatus? = null

    @Inject
    lateinit var dataService: DataService
    @Inject
    lateinit var headerTotalBatchViewModel: HeaderTotalBatchViewModel
    @Inject
    lateinit var errorResponse: ErrorResponse
    @Inject
    lateinit var userStorage: UserStorage

    init {
        viewModelClass = BatchViewModelImpl::class
    }

    var items: ArrayList<BatchListViewModel> = arrayListOf()

    val showEmptyState = ObservableBoolean(true)


    override fun setBatchStatus(batchStatus: BatchStatus) {
        this.batchStatus = batchStatus
    }

    override fun fetchBatch(callback: Result<Void?, String?>?) {
        fetchBatch(false, callback)
    }

    override fun refreshBatch(callback: Result<Void?, String?>?) {
        fetchBatch(true, callback)
    }

    override fun batchStatus(): BatchStatus = batchStatus ?: BatchStatus.IN_BACKLOG

    override fun fetchBatch(isRefresh: Boolean, callback: Result<Void?, String?>?) {
        val batchDate = DateUtil.createDateStringFrom(userStorage.getBatchDate().time, DateConstant.PATTERN.yyyyMMdd)
        val resource = BatchResource(batchStatus ?: BatchStatus.IN_BACKLOG, batchDate)
        dataService.fetchBatch(
                resource,
                RequestResult {
                    onSuccess {
                        it?.data?.result.notNull { batch ->
                            mapTotalBatch(isRefresh, batch)
                        }
                        callback?.success(null)
                    }
                    onError { callback?.failure(errorResponse.getErrorBodyDescription(it)) }
                }
        )
    }

    fun mapTotalBatch(isRefresh: Boolean = false, batches: List<Batch>) {
        if(isRefresh) items.clear()
        for(batch in batches) {
            val item = BatchListViewModel()
            item.bindViewModel(batch, batchStatus)
            items.add(item)
        }
        headerTotalBatchViewModel.totalBatch.set(items.size.toString())
        showEmptyState.set(shouldShowEmptyState())
    }

    fun shouldShowEmptyState(): Boolean {
        return items.size == 0
    }

    override fun getBatchListItems(): ArrayList<BatchListViewModel> = items

    /**
     * Override method for MultipleTypeRecycleViewSource
     */
    override val listData: MutableList<*> = items
    override val totalItemCount: Int get() = if(items.size == 0) 0 else items.size + 1

    @Suppress("UNCHECKED_CAST")
    override fun <VM : ViewModel> getViewModelAtPosition(position: Int): VM {
        if(getItemTypeAtPosition(position) == TOTAL_BATCH_ITEM_TYPE) {
            return headerTotalBatchViewModel as VM
        }
        return items[position - 1] as VM
    }

    override fun getItemTypeAtPosition(position: Int): Int {
        if(position == 0) {
            return TOTAL_BATCH_ITEM_TYPE
        }
        return BATCH_ITEM_TYPE
    }

    override fun getLayoutIdForItemType(itemType: Int): Int {
        if(itemType == TOTAL_BATCH_ITEM_TYPE) {
            return R.layout.header_total_batch_item_view
        }
        return R.layout.batch_list_item
    }
}