package co.styletheory.ops.outbound.android.feature.qcDetail.view

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.FragmentQcDetailItemBinding
import co.styletheory.ops.outbound.android.feature.qcDetail.event.QcDetailUIEvent
import co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.QcDetailItemViewModel
import co.styletheory.ops.outbound.android.general.base.BaseFragment
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.util.IntentConstant
import co.styletheory.ops.outbound.android.util.notNull
import co.styletheory.ops.outbound.android.util.scrollToChildAtIndex
import org.greenrobot.eventbus.Subscribe
import org.parceler.Parcels


/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 01 November 2017.
 * Description
 *
 * <EMAIL>
 */

class QcDetailItemFragment : BaseFragment<FragmentQcDetailItemBinding, QcDetailItemViewModel>() {

    companion object {
        fun newInstance(shipment: Shipment?, rackName: String?, rackSection: String?, batchId: String?): QcDetailItemFragment {
            val fragment = QcDetailItemFragment()
            val bundle = Bundle()
            bundle.putString(IntentConstant.RACK_NAME, rackName)
            bundle.putString(IntentConstant.RACK_SECTION, rackSection)
            bundle.putString(IntentConstant.BATCH_ID, batchId)
            bundle.putParcelable(IntentConstant.SHIPMENT, Parcels.wrap(shipment))
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if(viewModel == null) {
            fragmentComponent?.inject(this)
            initArguments()
            viewModel?.mapShipment()
        }

    }

    private fun initArguments() {
        arguments?.getString(IntentConstant.BATCH_ID).notNull { viewModel?.batchId = it }
        arguments?.getString(IntentConstant.RACK_NAME).notNull { viewModel?.rackName = it }
        arguments?.getString(IntentConstant.RACK_SECTION).notNull { viewModel?.rackSection = it }
        arguments?.getParcelable<Parcelable>(IntentConstant.SHIPMENT).notNull { viewModel?.shipment = Parcels.unwrap<Shipment>(it) }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? =
            bindContentView(inflater, container, R.layout.fragment_qc_detail_item)

    @Subscribe
    fun resultScannedRfid(event: QcDetailUIEvent.OnResultScannedRfid) {
        viewModel?.checkRfidText(event.scannedText)
    }

    @Subscribe
    fun onScrollToPosition(event: QcDetailUIEvent.OnScrollToPosition) {
        Handler(Looper.getMainLooper()).postDelayed({ binding?.adapterItems?.scrollToChildAtIndex(event.itemPosition) }, IntentConstant.DELAY_TIME_200)
    }
}