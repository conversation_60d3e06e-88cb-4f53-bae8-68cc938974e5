package co.styletheory.ops.outbound.android.util

import co.styletheory.ops.outbound.android.feature.batch.viewModel.BatchStepViewModel
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.model.enums.StepType
import org.joda.time.DateTime
import javax.inject.Inject

/**
 * Created by <PERSON> on 22/12/21.
 */
class GenerateBatchByPerStatus @Inject constructor() {
    fun createStepFromBatch(batch: Batch, batchStatus: BatchStatus?): List<BatchStepViewModel> {
        val stepItems = mutableListOf<BatchStepViewModel>()
        with(batch) {
            when(batchStatus) {
                BatchStatus.IN_BACKLOG -> {
                    stepItems.add(pickerStep(getPickerNames(), getPickerEmails(), pickerStartTime, pickerEndTime, true).setBatch(batch).setBatchStatus(batchStatus))
                }
                BatchStatus.PICKED -> {
                    stepItems.add(pickerStep(getPickerNames(), getPickerEmails(), pickerStartTime, pickerEndTime).setBatch(batch).setBatchStatus(batchStatus))
                    stepItems.add(qaStep(getQANames(), getQAEmails(), qaStartTime, qaEndTime, true).setBatch(batch).setBatchStatus(batchStatus))
                }
                BatchStatus.QA_DONE -> {
                    stepItems.add(pickerStep(getPickerNames(), getPickerEmails(), pickerStartTime, pickerEndTime).setBatch(batch).setBatchStatus(batchStatus))
                    stepItems.add(qaStep(getQANames(), getQAEmails(), qaStartTime, qaEndTime).setBatch(batch).setBatchStatus(batchStatus))
                    stepItems.add(photographStep(getPhotographerNames(), getPhotographerEmails(), photographerStartTime, photographerEndTime, true).setBatch(batch).setBatchStatus(batchStatus))
                }
                BatchStatus.READY_FOR_PACKING -> {
                    stepItems.add(pickerStep(getPickerNames(), getPickerEmails(), pickerStartTime, pickerEndTime).setBatch(batch).setBatchStatus(batchStatus))
                    stepItems.add(qaStep(getQANames(), getQAEmails(), qaStartTime, qaEndTime).setBatch(batch).setBatchStatus(batchStatus))
                    stepItems.add(photographStep(getPhotographerNames(), getPhotographerEmails(), photographerStartTime, photographerEndTime).setBatch(batch).setBatchStatus(batchStatus))
                    stepItems.add(packedStep(getPackerNames(), getPackerEmails(), packerStartTime, packerEndTime, true).setBatch(batch).setBatchStatus(batchStatus))
                }
                else -> {
                    //Not implemented yet
                }
            }
        }
        return stepItems
    }

    private fun pickerStep(names: List<String>, emails: List<String>, startTime: DateTime?, endTime: DateTime?, showAction: Boolean = false): BatchStepViewModel =
            createStep(StepType.PICKER, names, emails, startTime?.toString(DateConstant.PATTERN.ddMMMMMHH_mm), endTime?.toString(DateConstant.PATTERN.ddMMMMMHH_mm), "Take", showAction)

    private fun qaStep(names: List<String>, emails: List<String>, startTime: DateTime?, endTime: DateTime?, showAction: Boolean = false): BatchStepViewModel =
            createStep(StepType.QA, names, emails, startTime?.toString(DateConstant.PATTERN.ddMMMMMHH_mm), endTime?.toString(DateConstant.PATTERN.ddMMMMMHH_mm), "Check", showAction)

    private fun photographStep(names: List<String>, emails: List<String>, startTime: DateTime?, endTime: DateTime?, showAction: Boolean = false): BatchStepViewModel =
            createStep(StepType.PHOTO, names, emails, startTime?.toString(DateConstant.PATTERN.ddMMMMMHH_mm), endTime?.toString(DateConstant.PATTERN.ddMMMMMHH_mm), "Check", showAction)

    private fun packedStep(names: List<String>, emails: List<String>, startTime: DateTime?, endTime: DateTime?, showAction: Boolean = false): BatchStepViewModel =
            createStep(StepType.PACKING, names, emails, startTime?.toString(DateConstant.PATTERN.ddMMMMMHH_mm), endTime?.toString(DateConstant.PATTERN.ddMMMMMHH_mm), "Take", showAction)

    private fun createStep(stepType: StepType, names: List<String>, emails: List<String>, start: String? = "-", end: String? = "-", actionText: String, showAction: Boolean = false): BatchStepViewModel {
        return BatchStepViewModel().apply { bindViewModel(stepType, names, emails, start, end, actionText, showAction) }
    }
}