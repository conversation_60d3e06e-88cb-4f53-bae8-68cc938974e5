@file:Suppress("unused", "UNUSED_PARAMETER")

package co.styletheory.ops.outbound.android.util

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.os.Parcelable
import android.text.Html
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.text.style.*
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.malinskiy.superrecyclerview.SuperRecyclerView
import com.styletheory.android.mvvm.general.binding.ObservableText
import java.io.Serializable
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 31 October 2017.
 * Description
 *
 * <EMAIL>
 */

inline fun <T : Any, R> T.whenNotNull(input: T?, callback: (T) -> R): R? {
    return input?.let(callback)
}

inline fun <T : Any> T?.notNull(f: (it: T) -> Unit) {
    if(this != null) f(this)
}

inline fun String?.notNullOrEmpty(f: (it: String) -> Unit) {
    if(this != null && !this.isEmpty()) f(this)
}

fun SuperRecyclerView.notifyChangedAndClearRecycledPool() {
    recyclerView.recycledViewPool.clear()
    adapter?.notifyDataSetChanged()
}

fun RecyclerView.notifyChangedAndClearRecycledPool() {
    recycledViewPool.clear()
    adapter?.notifyDataSetChanged()
}

@Suppress("DEPRECATION")
fun String.toHtmlText(): Spanned {
    return if(OSUtil().isAboveAndroidN()) {
        Html.fromHtml(this, Html.FROM_HTML_MODE_LEGACY)
    } else {
        Html.fromHtml(this)
    }
}

fun Context.hideSoftKeyboard(view: View) {
    val inputMethodManager = getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
    inputMethodManager.notNull {
        it.hideSoftInputFromWindow(view.windowToken, 0)
    }
}

fun String?.convertDateStringToTimeStamp(): String {
    val dateFormat = SimpleDateFormat(DateConstant.PATTERN.ddMMMyyyyHH_mm, Locale.getDefault())
    val dateTimeStamp = SimpleDateFormat(DateConstant.PATTERN.yyyyMMddTHHmmss, Locale.getDefault())
    return try {
        this?.let {
            val timeGeneration = dateFormat.parse(it) ?: Date()
            dateTimeStamp.format(timeGeneration)
        }.orEmpty()
    } catch(error: ParseException) {
        ""
    }
}

fun TextView.setSpanTexts(spanTexts: List<ObservableText.SpanText>) {
    val spannableString = SpannableStringBuilder(text)
    for(span in spanTexts) {
        if(span.highlight != null) {
            val highlight = if(span.highlight is Int) context.getString(span.highlight) else span.highlight.toString()
            val startIndex = text.indexOf(highlight)
            val lastIndex = startIndex + highlight.length
            if(startIndex > -1 && lastIndex <= text.length) {
                span.click.notNull {
                    val clickableSpan = object : ClickableSpan() {
                        override fun onClick(view: View) {
                            it.invoke()
                        }
                    }
                    spannableString.setSpan(clickableSpan, startIndex, lastIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                    movementMethod = LinkMovementMethod.getInstance()
                }

                if(span.backgroundColor > 0) {
                    val color = ContextCompat.getColor(context, span.backgroundColor)
                    spannableString.setSpan(
                        BackgroundColorSpan(color),
                        startIndex,
                        lastIndex,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }

                if(span.textColor > 0) {
                    val color = ContextCompat.getColor(context, span.textColor)
                    spannableString.setSpan(
                        ForegroundColorSpan(color),
                        startIndex,
                        lastIndex,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }

                if(span.isBold && span.isItalic) {
                    spannableString.setSpan(
                        StyleSpan(Typeface.BOLD_ITALIC),
                        startIndex,
                        lastIndex,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                } else if(span.isItalic) {
                    spannableString.setSpan(
                        StyleSpan(Typeface.ITALIC),
                        startIndex,
                        lastIndex,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                } else if(span.isBold) {
                    spannableString.setSpan(
                        StyleSpan(Typeface.BOLD),
                        startIndex,
                        lastIndex,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
                if(span.isUnderline) {
                    spannableString.setSpan(UnderlineSpan(), startIndex, lastIndex, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
                }
            }
        }
        text = spannableString
    }

}

fun LinearLayout.scrollToChildAtIndex(position: Int) {
    if(childCount > position) {
        val child = getChildAt(position)
        requestChildFocus(child, child)
    }
}

fun Context.getStatusBarHeight(): Int {
    var result = 0
    val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
    if(resourceId > 0) {
        result = resources.getDimensionPixelSize(resourceId)
    }
    return result
}

fun String.capitalize(): String {
    return this.replaceFirstChar { if(it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString() }
}

@Suppress("DEPRECATION")
inline fun <reified T : Serializable> getSerializableExtraProvider(source: Any, identifierParameter: String): T? {
    return when(source) {
        is Intent -> {
            if(OSUtil().isAboveOrEqualsAndroidTIRAMISU()) {
                source.getSerializableExtra(identifierParameter, T::class.java)
            } else {
                source.getSerializableExtra(identifierParameter) as T?
            }
        }

        is Bundle -> {
            if(OSUtil().isAboveOrEqualsAndroidTIRAMISU()) {
                source.getSerializable(identifierParameter, T::class.java)
            } else {
                source.getSerializable(identifierParameter) as T?
            }
        }

        else -> null
    }
}

@Suppress("DEPRECATION")
inline fun <reified T : Parcelable> Intent.getParcelableExtraProvider(identifierParameter: String): T? {
    return if(OSUtil().isAboveOrEqualsAndroidTIRAMISU()) {
        this.getParcelableExtra(identifierParameter, T::class.java)
    } else {
        this.getParcelableExtra(identifierParameter)
    }
}

@Suppress("DEPRECATION")
inline fun <reified T : Parcelable> Intent.getParcelableArrayListExtraProvider(identifierParameter: String): ArrayList<T>? {
    return if(OSUtil().isAboveOrEqualsAndroidTIRAMISU()) {
        this.getParcelableArrayListExtra(identifierParameter, T::class.java)
    } else {
        this.getParcelableArrayListExtra(identifierParameter)
    }
}