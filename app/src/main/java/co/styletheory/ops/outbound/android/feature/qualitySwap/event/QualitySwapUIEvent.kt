package co.styletheory.ops.outbound.android.feature.qualitySwap.event

import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.util.Next

/**
 * Created by Yoga C. P<PERSON> on 18/06/20.
 * Android Engineer
 */
sealed class QualitySwapUIEvent {
    data class TakePictureEvent(val batchId: String, val batchStatus: BatchStatus?, val resultCallback: Next<List<String>>? = null)
}