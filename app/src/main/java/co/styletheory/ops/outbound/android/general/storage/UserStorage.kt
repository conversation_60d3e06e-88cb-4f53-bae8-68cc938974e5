package co.styletheory.ops.outbound.android.general.storage

import co.styletheory.ops.outbound.android.model.Region
import co.styletheory.ops.outbound.android.model.Session
import co.styletheory.ops.outbound.android.model.User
import co.styletheory.ops.outbound.android.model.VerticalType
import co.styletheory.ops.outbound.android.model.enums.Regions
import co.styletheory.ops.outbound.android.model.enums.UserRoles
import co.styletheory.ops.outbound.android.model.enums.VerticalTypes
import com.orhanobut.hawk.Hawk
import java.util.*
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 08 November 2017.
 * Description
 *
 * <EMAIL>
 */
class UserStorage @Inject constructor() {

    fun getUserEmail(): String? = Hawk.get<String>(StorageConstant.USER_EMAIL)
    fun removeUserEmail() = Hawk.delete(StorageConstant.USER_EMAIL)
    fun setUserEmail(email: String) {
        Hawk.put(StorageConstant.USER_EMAIL, email)
    }

    fun getUserName(): String? = Hawk.get<String>(StorageConstant.USER_NAME)
    fun removeUserName() = Hawk.delete(StorageConstant.USER_NAME)
    fun setUserName(name: String) {
        Hawk.put(StorageConstant.USER_NAME, name)
    }

    fun getUserLogin(): User? = Hawk.get<User>(StorageConstant.USER_LOGIN)
    fun removeUserLogin() = Hawk.delete(StorageConstant.USER_LOGIN)
    fun setUserLogin(user: User) {
        Hawk.put(StorageConstant.USER_LOGIN, user)
    }

    fun getUserSession(): Session? = Hawk.get<Session>(StorageConstant.USER_SESSIONS)
    fun removeUserSession() = Hawk.delete(StorageConstant.USER_SESSIONS)
    fun setUserSession(session: Session) {
        Hawk.put(StorageConstant.USER_SESSIONS, session)
    }

    fun getUserRegion(): Region = Hawk.get(StorageConstant.USER_REGION) ?: Region(0,"sg","Singapore")
    fun removeUserRegion() = Hawk.delete(StorageConstant.USER_REGION)
    fun setUserRegion(code: Region?) {
        Hawk.put(StorageConstant.USER_REGION, code)
    }

    fun getUserVerticalType(): VerticalType = Hawk.get(StorageConstant.USER_VERTICAL_TYPE) ?: VerticalType(0,"apparel","ap", "Apparel")
    fun removeUserVerticalType() = Hawk.delete(StorageConstant.USER_VERTICAL_TYPE)
    fun setUserVerticalType(type: VerticalType?) {
        Hawk.put(StorageConstant.USER_VERTICAL_TYPE, type)
    }

    fun isBarcodeApparelFlagOn(): Boolean = Hawk.get(StorageConstant.SETTING_BARCODE_APPAREL) ?: false
    fun removeSettingBarcodeApparel() = Hawk.delete(StorageConstant.SETTING_BARCODE_APPAREL)
    fun setSettingBarcodeApparel(value: Boolean) {
        Hawk.put(StorageConstant.SETTING_BARCODE_APPAREL, value)
    }

    fun isBarcodeBagsFlagOn(): Boolean = Hawk.get(StorageConstant.SETTING_BARCODE_BAGS) ?: false
    fun removeSettingBarcodeBags() = Hawk.delete(StorageConstant.SETTING_BARCODE_BAGS)
    fun setSettingBarcodeBags(value: Boolean) {
        Hawk.put(StorageConstant.SETTING_BARCODE_BAGS, value)
    }

    fun getBatchDate(): Calendar = Hawk.get(StorageConstant.SETTING_BATCH_DATE) ?: Calendar.getInstance()
    fun removeBatchDate() = Hawk.delete(StorageConstant.SETTING_BATCH_DATE)
    fun setBatchDate(value: Calendar?) {
        Hawk.put(StorageConstant.SETTING_BATCH_DATE, value)
    }

    fun isFeatureFlagIsOn(): Boolean = Hawk.get(StorageConstant.FEATURE_FLAG_INITIALIZATION) ?: false
    private fun removeFeatureFlagValue() = Hawk.delete(StorageConstant.FEATURE_FLAG_INITIALIZATION)
    fun setFeatureFlagValue(value: Boolean) {
        Hawk.put(StorageConstant.FEATURE_FLAG_INITIALIZATION, value)
    }

    fun isUserRegionID(): Boolean {
        return (getUserRegion().id == Regions.INDONESIA.code)
    }

    fun isUserRegionSG(): Boolean {
        return (getUserRegion().id == Regions.SINGAPORE.code)
    }

    fun isUserRegionHK(): Boolean {
        return (getUserRegion().id == Regions.HONGKONG.code)
    }

    fun isVerticalTypeBags(): Boolean {
        return getUserVerticalType().code?.contains(VerticalTypes.BAGS.code, ignoreCase = true) ?: false
    }

    fun isVerticalTypeApparel(): Boolean {
        return getUserVerticalType().code?.contains(VerticalTypes.APPAREL.code, ignoreCase = true) ?: false
    }

    fun isVerticalTypeOnDemand(): Boolean {
        return getUserVerticalType().code?.contains(VerticalTypes.ON_DEMAND.code, ignoreCase = true) ?: false
    }

    fun isUserOperationManager(): Boolean {
        return (getUserLogin()?.roles?.contains(UserRoles.OPS_MANAGER.roles) == true)
    }

    fun isUserOnDemandAndRegionID(): Boolean {
        return (isUserRegionID() && isVerticalTypeOnDemand())
    }

    fun clearUserStorage() {
        removeUserEmail()
        removeUserName()
        removeUserLogin()
        removeUserSession()
        removeUserRegion()
        removeUserVerticalType()
        removeSettingBarcodeApparel()
        removeSettingBarcodeBags()
        removeFeatureFlagValue()
        removeBatchDate()
    }
}