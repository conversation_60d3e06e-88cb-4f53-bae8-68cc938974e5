package co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.impl

import android.view.View
import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableInt
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.qcDetail.event.QcDetailUIEvent
import co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.QcDetailItemViewModel
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.injection.scope.PerFragment
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.util.GeneralCallback
import co.styletheory.ops.outbound.android.util.Next
import co.styletheory.ops.outbound.android.util.notNull
import co.styletheory.ops.outbound.android.util.notNullOrEmpty
import co.styletheory.ops.outbound.android.viewModelComponent.LabelBoxViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.ServiceMethodWidgetViewModel
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 01 November 2017.
 * Description
 *
 * <EMAIL>
 */

@PerFragment
class QcDetailItemViewModelImpl @Inject constructor() : BaseViewModel<QcDetailItemViewModelImpl>(), QcDetailItemViewModel {

    @Inject
    lateinit var userStorage: UserStorage

    init {
        viewModelClass = QcDetailItemViewModelImpl::class
    }

    val title = ObservableString()
    val titleVisibility = ObservableInt(View.VISIBLE)
    val completeButtonText = ObservableString()
    val labelBoxItems = ObservableArrayList<LabelBoxViewModel>()
    val qaPickedItems = ObservableArrayList<QcPickedViewModel>()
    val isComplete = ObservableBoolean(false)
    val customerName = ObservableString()
    val businessMethodNameList = ObservableArrayList<ServiceMethodWidgetViewModel>()

    override var shipment: Shipment? = null
    override var rackSection: String = ""
    override var rackName: String = ""
    override var batchId: String = ""

    override fun mapShipment() {
        shipment.notNull {
            setupCustomerLabel(it.customer?.name, rackName, rackSection)

            title.set(it.tracking?.id)
            if(it.tracking?.id.isNullOrEmpty()) {
                titleVisibility.set(View.GONE)
            }
            labelBoxItems.addAll(LabelBoxViewModel.create(it))

            qaPickedItems.clear()

            if(!userStorage.isUserRegionID()) {
                addQaPickedItemsForSG(it.items)
            } else {
                addQaPickedItemsForID(it.items)
            }

            completeButtonRefreshState()
            setupBusinessMethod(it)
        }
    }

    @Suppress("NON_EXHAUSTIVE_WHEN")
    fun addQaPickedItemsForSG(pickedItems: List<BatchItem>) {
        for(item in pickedItems) {
            when(item.status) {
                ProductStatus.QA_PASSED,
                ProductStatus.QA_FAILED,
                ProductStatus.PICKED,
                ProductStatus.QA,
                ProductStatus.PHOTO_QA_DONE -> {
                    val qaItem = QcPickedViewModel()
                    qaItem.bindViewModel(
                        item,
                        rackSection,
                        rackName,
                        shipment?.id.orEmpty(),
                        shipment?.boxId.orEmpty(),
                        shipment?.customer?.email.orEmpty()
                    )
                    qaItem.batchId = batchId
                    qaItem.refreshCompleteState = refreshCompleteState
                    qaItem.removeSwappedItem = removeSwappedItem
                    qaPickedItems.add(qaItem)
                    isHaveRfid(qaItem)
                    setItemWhenVerticalType(qaItem)
                    checkClickedStatus(item.status, qaItem)
                }
                else -> {
                    //Not implemented yet
                }
            }
        }
    }

    @Suppress("NON_EXHAUSTIVE_WHEN")
    fun addQaPickedItemsForID(pickedItems: List<BatchItem>) {
        for(item in pickedItems) {
            when(item.status) {
                ProductStatus.QA_PASSED,
                ProductStatus.QA_FAILED,
                ProductStatus.PICKED,
                ProductStatus.QA -> {
                    val qaItem = QcPickedViewModel()
                    qaItem.bindViewModel(
                        item,
                        rackSection,
                        rackName,
                        shipment?.id.orEmpty(),
                        shipment?.boxId.orEmpty(),
                        shipment?.customer?.email.orEmpty()
                    )
                    qaItem.batchId = batchId
                    qaItem.refreshCompleteState = refreshCompleteState
                    qaItem.removeSwappedItem = removeSwappedItem
                    qaPickedItems.add(qaItem)
                    isHaveRfid(qaItem)
                    setItemWhenVerticalType(qaItem)
                    checkClickedStatus(item.status, qaItem)
                }
                else -> {
                    //Not implemented yet
                }
            }
        }
    }

    fun isHaveRfid(qcItem: QcPickedViewModel) {
        qcItem.batchItem?.rfidFormatted.notNullOrEmpty {
            qcItem.showRfidCode()
        }
    }

    fun setItemWhenVerticalType(qcItem: QcPickedViewModel) {
        when {
            userStorage.isVerticalTypeBags() -> {
                qcItem.showBagsItems()
            }
            userStorage.isUserOnDemandAndRegionID() -> {
                qcItem.showApparelItems()
            }
            else -> {
                qcItem.showApparelItems()
                qcItem.setupResellingSwapVisibility(qcItem.batchItem)
            }
        }
    }

    @Suppress("NON_EXHAUSTIVE_WHEN")
    fun checkClickedStatus(statusPickedItems: ProductStatus, qcItem: QcPickedViewModel) {
        when(statusPickedItems) {
            ProductStatus.QA_PASSED,
            ProductStatus.PHOTO_QA_DONE,
            ProductStatus.QA_FAILED -> qcItem.setupEnableButton(true)
            else -> {
                //Not implemented yet
            }
        }
    }

    override fun checkRfidText(scannedRfidText: String) {
        val position = qaPickedItems.indexOfFirst {
            it.batchItem?.rfid.equals(scannedRfidText, true)
        }

        if(position >= 0) {
            enableButton(position)
            eventBus.post(QcDetailUIEvent.OnScrollToPosition(position))
        }
    }

    override fun enableButton(position: Int?) {
        position?.notNull {
            qaPickedItems[position].setupEnableButton(true)
            qaPickedItems[position].setupEnableAccuracySwapButton(false)
        }
    }

    fun completeButtonRefreshState() {
        if(isQAItemComplete()) {
            isComplete.set(true)
            completeButtonText.set(string(R.string.complete_label))
        } else {
            isComplete.set(false)
            completeButtonText.set(string(R.string.in_complete_label))
        }
        shipment?.isComplete = isQAItemComplete()
    }

    fun isQAItemComplete(): Boolean {
        for(item in qaPickedItems) {
            if(!item.pass.get() && !item.fail.get()) {
                return false
            }
        }
        return true
    }

    fun setupCustomerLabel(name: String?, rackName: String, rackSection: String) {
        if(userStorage.isUserOnDemandAndRegionID()) {
            customerName.set(string(R.string.detail_item_on_demand_title).format(name, rackName, rackSection))
        } else {
            customerName.set(string(R.string.detail_item_title).format(name, rackName, rackSection))
        }
    }

    fun setupBusinessMethod(shipment: Shipment) {
        when {
            shipment.isBusinessMethodReselling() -> {
                businessMethodNameList.add(ServiceMethodWidgetViewModel.create(string(R.string.packing_detail_reselling_title)))
            }
            shipment.isBusinessMethodSurpriseKit() -> {
                businessMethodNameList.add(ServiceMethodWidgetViewModel.create(string(R.string.packing_detail_surprise_kit_title)))
            }
            else -> {
                businessMethodNameList.add(ServiceMethodWidgetViewModel.create(shipment.businessMethod))
            }
        }
    }

    val refreshCompleteState = object : GeneralCallback {
        override fun callback() {
            completeButtonRefreshState()
        }
    }

    val removeSwappedItem = object : Next<QcPickedViewModel?> {
        override fun apply(t: QcPickedViewModel?) {
            t.notNull {
                qaPickedItems.remove(it)
                if(qaPickedItems.size == 0) {
                    eventBus.post(QcDetailUIEvent.RemoveShipmentQaPicked(shipment))
                }
            }
        }
    }

}