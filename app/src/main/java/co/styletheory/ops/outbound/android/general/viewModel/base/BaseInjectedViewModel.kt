package co.styletheory.ops.outbound.android.general.viewModel.base

import android.content.Context
import androidx.annotation.StringRes
import androidx.databinding.BaseObservable
import co.styletheory.ops.outbound.android.StyletheoryOpsApplication
import co.styletheory.ops.outbound.android.general.event.DismissProgressDialogEvent
import co.styletheory.ops.outbound.android.general.event.ShowProgressDialogEvent
import co.styletheory.ops.outbound.android.general.event.ShowToastEvent
import co.styletheory.ops.outbound.android.util.featureflag.FeatureFlagUtil
import org.greenrobot.eventbus.EventBus
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 26 October 2017.
 * Description
 *
 * <EMAIL>
 */
abstract class BaseInjectedViewModel : BaseObservable(), ViewModel {

    @Inject
    lateinit var eventBus: EventBus
    @Inject
    lateinit var context: Context
    @Inject
    lateinit var featureFlagUtil: FeatureFlagUtil

    init {
        inject()
    }

    private fun inject() {
        StyletheoryOpsApplication.directInject(this)
    }

    override fun onDestroy() {
        //Not implemented yet
    }

    override fun afterInject() {
        //Not implemented yet
    }

    fun string(@StringRes resId: Int): String = context.getString(resId)
    fun string(@StringRes resId: Int, vararg formatArgs: Any): String = context.getString(resId, *formatArgs)

    fun showProgressDialog() = eventBus.post(ShowProgressDialogEvent())
    fun dismissProgressDialog() = eventBus.post(DismissProgressDialogEvent())
    fun showToast(message: String?, isLong: Boolean = false) = eventBus.post(ShowToastEvent(message, isLong))
}