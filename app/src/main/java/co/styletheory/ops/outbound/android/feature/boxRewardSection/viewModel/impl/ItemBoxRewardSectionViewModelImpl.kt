package co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.impl

import androidx.databinding.ObservableBoolean
import co.styletheory.ops.outbound.android.feature.boxRewardSection.event.BoxRewardSectionUIEvent
import co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.ItemBoxRewardSectionViewModel
import co.styletheory.ops.outbound.android.general.binding.ObservableChecked
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseItemViewModel
import co.styletheory.ops.outbound.android.model.BoxReward
import com.styletheory.android.mvvm.general.binding.ObservableText
import javax.inject.Inject

/**
 * Created by <PERSON> on 11/20/20.
 */
class ItemBoxRewardSectionViewModelImpl @Inject constructor() : BaseItemViewModel<ItemBoxRewardSectionViewModelImpl>(), ItemBoxRewardSectionViewModel {
    init {
        viewModelClass = ItemBoxRewardSectionViewModelImpl::class
    }

    override val rewardName = ObservableText()
    override val isRewardItemChecked = ObservableChecked(false)
    override val isRewardItemCheckedEnabled = ObservableBoolean(true)
    override lateinit var boxReward: BoxReward

    override fun bindBoxReward(reward: BoxReward): ItemBoxRewardSectionViewModelImpl {
        boxReward = reward
        rewardName.set(reward.title)
        isRewardItemChecked.set(isRewardItemChecked.get())
        isRewardItemCheckedEnabled.set(true)
        return this
    }

    override fun onRewardItemClicked() {
        isRewardItemChecked.set(!isRewardItemChecked.get())
        eventBus.post(BoxRewardSectionUIEvent.OnRewardClicked(this))
    }

    override fun completedBoxReward(reward: BoxReward): ItemBoxRewardSectionViewModelImpl {
        boxReward = reward
        rewardName.set(reward.title)
        isRewardItemChecked.set(true)
        isRewardItemCheckedEnabled.set(false)
        return this
    }
}