package co.styletheory.ops.outbound.android.feature.packing.viewModel.impl

import android.view.View
import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableInt
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.packing.viewModel.PackingItemViewModel
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseItemViewModel
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.util.impl.StringUtil
import co.styletheory.ops.outbound.android.util.notNullOrEmpty
import co.styletheory.ops.outbound.android.viewModelComponent.ColorItemViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.PhotoWithLabelViewModel
import com.styletheory.android.mvvm.general.binding.ObservableText
import javax.inject.Inject

/**
 * Created by Eminarti Sianturi on 2019-09-23.
 */
class PackingItemViewModelImpl @Inject constructor() : BaseItemViewModel<PackingItemViewModelImpl>(), PackingItemViewModel {

    @Inject
    lateinit var userStorage: UserStorage

    @Inject
    lateinit var photoWithLabelVM: PhotoWithLabelViewModel

    private var rackSection: String = ""
    private var rackName: String = ""

    override var batchItem: BatchItem? = null
    override val itemName = ObservableString()
    override val itemSize = ObservableString()
    override val rack = ObservableString()
    override val category = ObservableString()
    override val parts = ObservableString()
    override val notes = ObservableString()
    override val soldTo = ObservableString()
    override val productOrder = ObservableString()
    override val itemStatus = ObservableText()
    override val colorItems = ObservableArrayList<ColorItemViewModel>()
    override val detachable = ObservableString()
    override val partsVisibility = ObservableInt(View.GONE)
    override val detachableVisibility = ObservableInt(View.GONE)
    override val scannerIconVisibility = ObservableInt(View.GONE)
    override val checkMarkRfidVisibility = ObservableInt(View.GONE)
    override val haveScannedRfid = ObservableBoolean(false)
    override val itemStatusVisibility = ObservableInt(View.VISIBLE)
    override val isItemScanned = ObservableBoolean(false)
    override val itemPurchasedVisibility = ObservableInt(View.GONE)

    init {
        viewModelClass = PackingItemViewModelImpl::class
    }

    override fun bindViewModel(batchItem: BatchItem, rackSection: String, rackName: String) {
        this.batchItem = batchItem
        this.rackSection = rackSection
        this.rackName = rackName

        itemName.set(batchItem.style?.designer?.name)
        itemSize.set(batchItem.labelSize)
        productOrder.set("[${batchItem.order}]")
        category.set(batchItem.style?.primaryCategory)
        parts.set(StringUtil.stringArraySeparateWithComma(batchItem.parts))
        detachable.set(batchItem.additionalProperties?.detachableDetail)
        notes.set(batchItem.notes)
        colorItems.clear()

        setupPhotoWithLabel(batchItem)
        setColorItems(batchItem)
        handleStatusItem(batchItem.status)
        setupItemContainRfid()
        setupRackName(rackName, rackSection)
        setupPurchasedItem(batchItem)
    }

    override fun showApparelItems() {
        partsVisibility.set(View.VISIBLE)
    }

    override fun showBagsItems() {
        detachableVisibility.set(View.VISIBLE)
    }

    override fun showScannerIcon() {
        if(featureFlagUtil.isBarcodeSettingIsOn()) {
            checkMarkRfidVisibility.set(View.VISIBLE)
            scannerIconVisibility.set(View.VISIBLE)
        }
    }

    override fun handleStatusItem(productStatus: ProductStatus?) {
        when(productStatus) {
            ProductStatus.RENTED -> itemStatus.set(R.string.warn_ready_item_for_rented)
            ProductStatus.PICKING -> itemStatus.set(R.string.warn_ready_item_for_picking)
            ProductStatus.PICKED -> itemStatus.set(R.string.warn_ready_item_for_picked)
            ProductStatus.PAID -> itemStatus.set(R.string.warn_ready_item_for_paid)
            ProductStatus.NOT_FOUND -> itemStatus.set(R.string.warn_ready_item_for_not_found)
            ProductStatus.QA -> itemStatus.set(R.string.warn_ready_item_for_qa)
            ProductStatus.QA_PASSED -> itemStatus.set(R.string.warn_ready_item_for_qa_passed)
            ProductStatus.QA_FAILED -> itemStatus.set(R.string.warn_ready_item_for_qa_failed)
            ProductStatus.QA_FAILED_CONFIRMED -> itemStatus.set(R.string.warn_ready_item_for_qa_failed_confirmed)
            ProductStatus.PHOTO_QA -> itemStatus.set(R.string.warn_ready_item_for_photo_qa)
            else -> {
                itemStatus.set("")
                itemStatusVisibility.set(View.GONE)
            }
        }
    }

    override fun setupItemContainRfid() {
        batchItem?.rfid.notNullOrEmpty {
            showScannerIcon()
        }
    }

    override fun setupRackName(rackName: String, rackSection: String) {
        if(userStorage.isUserOnDemandAndRegionID()) {
            rack.set(string(R.string.rack_name_on_demand).format(rackName, rackSection))
        } else {
            rack.set(string(R.string.rack_name_regular).format(rackName, rackSection))
        }
    }

    override fun setupPurchasedItem(batchItem: BatchItem) {
        if(!batchItem.label.isNullOrEmpty()) {
            itemPurchasedVisibility.set(View.VISIBLE)
            soldTo.set(batchItem.label)
        } else {
            itemPurchasedVisibility.set(View.GONE)
            soldTo.clear()
        }

    }

    private fun setColorItems(batchItem: BatchItem) {
        for(item in batchItem.style?.colors?.filter { it.colorCode.isNotEmpty() }.orEmpty()) {
            val model = ColorItemViewModel()
            model.colorName.set(item.name)
            model.colorHex.set(item.colorCode)
            colorItems.add(model)
        }
    }

    override fun setPhotoWithLabelVM(): PhotoWithLabelViewModel = photoWithLabelVM
    
    private fun setupPhotoWithLabel(batchItem: BatchItem) {
        photoWithLabelVM.bindPhotoWithLabel(batchItem.style?.imageUrl, batchItem.resellingInventoryType)
    }
}