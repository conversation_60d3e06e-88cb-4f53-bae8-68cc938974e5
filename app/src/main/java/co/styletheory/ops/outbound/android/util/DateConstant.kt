package co.styletheory.ops.outbound.android.util

/**
 * Created by Yoga C. <PERSON> on 2019-11-06.
 * Android Engineer
 */
object DateConstant {

    const val MIN_30_DAYS = -30

    object PATTERN {
        const val ddMMM = "dd MMMM"
        const val ddMMMMHHmm = "dd MMMM HH mm"
        const val ddMMMyyyy = "dd MMM yyyy"
        const val ddMMMMyyyy = "dd MMMM yyyy"
        const val ddMMMyyyyHHmm = "dd MMMM yyyy HH mm"
        const val ddMMMyyyyHH_mm = "dd MMMM yyyy HH.mm"
        const val yyyyMMddTHHmmss = "yyyy-MM-dd'T'HH:mm:ss"
        const val yyyyMMdd = "yyyy-MM-dd"
        const val EEEEMMMMMddyyyy = "EEEE, MMMMM dd yyyy"
        const val ddMMMMMHH_mm = "dd MMMMM HH.mm"
        const val ddMMMMMyyyy = "dd MMMMM yyyy"
        const val HHmm = "HH:mm"
    }
}