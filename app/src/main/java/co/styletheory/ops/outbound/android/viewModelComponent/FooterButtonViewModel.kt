package co.styletheory.ops.outbound.android.viewModelComponent

import androidx.databinding.ObservableBoolean
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 25 October 2017.
 * Description
 *
 * <EMAIL>
 */
class FooterButtonViewModel @Inject constructor() : BaseViewModel<FooterButtonViewModel>() {

    init {
        viewModelClass = FooterButtonViewModel::class
    }

    val buttonTitle: ObservableString = ObservableString()
    val enable = ObservableBoolean(false)

    fun footerButtonClick() {
        eventBus.post(CompleteButtonEvent())
    }

    inner class CompleteButtonEvent
}