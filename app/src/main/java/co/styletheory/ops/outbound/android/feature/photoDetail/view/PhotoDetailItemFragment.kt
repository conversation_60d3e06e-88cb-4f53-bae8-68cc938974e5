package co.styletheory.ops.outbound.android.feature.photoDetail.view

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.FragmentQcDetailItemBinding
import co.styletheory.ops.outbound.android.feature.photoDetail.viewModel.PhotoDetailItemViewModel
import co.styletheory.ops.outbound.android.general.base.BaseFragment
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.util.IntentConstant
import co.styletheory.ops.outbound.android.util.notNull
import org.parceler.Parcels

/**
 * Created by giorgygunawan on 11/27/17.
 *
 */
class PhotoDetailItemFragment : BaseFragment<FragmentQcDetailItemBinding, PhotoDetailItemViewModel>() {

    companion object {
        fun newInstance(shipment: Shipment?, rackName: String?, rackSection: String?, batchId: String?): PhotoDetailItemFragment {
            val fragment = PhotoDetailItemFragment()
            val bundle = Bundle()
            bundle.putString(IntentConstant.BATCH_ID, batchId)
            bundle.putString(IntentConstant.RACK_NAME, rackName)
            bundle.putString(IntentConstant.RACK_SECTION, rackSection)
            bundle.putParcelable(IntentConstant.SHIPMENT, Parcels.wrap(shipment))
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if(viewModel == null) {
            fragmentComponent?.inject(this)
            initArguments()
            viewModel?.mapShipment()
        }
    }

    private fun initArguments() {
        arguments?.getString(IntentConstant.BATCH_ID).notNull { viewModel?.setBatchId(it) }
        arguments?.getString(IntentConstant.RACK_NAME).notNull { viewModel?.setRackName(it) }
        arguments?.getString(IntentConstant.RACK_SECTION).notNull { viewModel?.setRackSection(it) }
        arguments?.getParcelable<Parcelable>(IntentConstant.SHIPMENT).notNull { viewModel?.setShipment(Parcels.unwrap<Shipment>(it)) }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return bindContentView(inflater, container, R.layout.fragment_qa_photo_detail_item)
    }

}