package co.styletheory.ops.outbound.android.general.listener;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import org.jetbrains.annotations.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 11/7/16.
 */

public abstract class BottomScreenOnScrollListener extends RecyclerView.OnScrollListener {

    int visibleItemCount;
    int totalItemCount;
    // The minimum amount of items to have below your current scroll position before loading more.
    private static final int VISIBLE_THRESHOLD_IN_LIST_MODE = 5;
    private RecyclerView.LayoutManager mLayoutManager;

    public void setLayoutManager(RecyclerView.LayoutManager layoutManager) {
        this.mLayoutManager = layoutManager;
    }

    @Override
    public void onScrolled(@NotNull RecyclerView recyclerView, int dx, int dy) {
        super.onScrolled(recyclerView, dx, dy);

        visibleItemCount = recyclerView.getChildCount();

        totalItemCount = mLayoutManager.getItemCount();

        int firstVisibleItemPosition = 0;
        if(mLayoutManager instanceof StaggeredGridLayoutManager) {
            int[] firstVisibleItemPositions = new int[((StaggeredGridLayoutManager) mLayoutManager).getSpanCount()];
            ((StaggeredGridLayoutManager) mLayoutManager).findFirstVisibleItemPositions(firstVisibleItemPositions);
            firstVisibleItemPosition = firstVisibleItemPositions[0];
            if(firstVisibleItemPositions.length > 1) {
                for(int i = 1; i < firstVisibleItemPositions.length; i++) {
                    if(firstVisibleItemPositions[i] < firstVisibleItemPositions[i - 1]) {
                        firstVisibleItemPosition = firstVisibleItemPositions[i];
                    }
                }
            }
        } else if(mLayoutManager instanceof GridLayoutManager) {
            firstVisibleItemPosition = ((GridLayoutManager) mLayoutManager).findFirstVisibleItemPosition();
        } else if(mLayoutManager instanceof LinearLayoutManager) {
            firstVisibleItemPosition = ((LinearLayoutManager) mLayoutManager).findFirstVisibleItemPosition();
        }

        if(firstVisibleItemPosition != RecyclerView.NO_POSITION && (totalItemCount - visibleItemCount) <= (firstVisibleItemPosition + VISIBLE_THRESHOLD_IN_LIST_MODE)) {
            almostReachBottom();
        }
    }

    protected abstract void almostReachBottom();

}
