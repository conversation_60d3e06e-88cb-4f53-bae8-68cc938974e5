package co.styletheory.ops.outbound.android.feature.packing.view

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.FragmentPackingDetailItemBinding
import co.styletheory.ops.outbound.android.feature.boxRewardSection.event.BoxRewardSectionUIEvent
import co.styletheory.ops.outbound.android.feature.packing.event.PackingItemUIEvent
import co.styletheory.ops.outbound.android.feature.packing.viewModel.PackingDetailItemViewModel
import co.styletheory.ops.outbound.android.general.base.BaseFragment
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.LogisticProvider
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.util.IntentConstant
import co.styletheory.ops.outbound.android.util.notNull
import co.styletheory.ops.outbound.android.util.scrollToChildAtIndex
import org.greenrobot.eventbus.Subscribe
import org.parceler.Parcels

/**
 * Created by giorgygunawan on 11/29/17.
 */
class PackingDetailItemFragment : BaseFragment<FragmentPackingDetailItemBinding, PackingDetailItemViewModel>() {
    companion object {
        fun newInstance(batch: Batch?, shipment: Shipment?, rackSection: String?, rackName: String?): PackingDetailItemFragment {
            val fragment = PackingDetailItemFragment()
            val bundle = Bundle()
            bundle.putString(IntentConstant.RACK_SECTION, rackSection)
            bundle.putString(IntentConstant.RACK_NAME, rackName)
            bundle.putString(IntentConstant.BATCH_ID, batch?.id)
            bundle.putParcelable(IntentConstant.SHIPMENT, Parcels.wrap(shipment))
            bundle.putParcelable(IntentConstant.LOGISTIC_PROVIDER, Parcels.wrap(batch?.logisticProviders))
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if(viewModel == null) {
            fragmentComponent?.inject(this)
            initArguments()
            viewModel?.mapShipment()
        }
    }

    private fun initArguments() {
        arguments?.getString(IntentConstant.RACK_NAME).notNull { viewModel?.rackName?.set(it) }
        arguments?.getString(IntentConstant.RACK_SECTION).notNull { viewModel?.rackSection?.set(it) }
        arguments?.getString(IntentConstant.BATCH_ID).notNull { viewModel?.batchId?.set(it) }
        arguments?.getParcelable<Parcelable>(IntentConstant.SHIPMENT).notNull { viewModel?.shipments = Parcels.unwrap<Shipment>(it) }
        arguments?.getParcelable<Parcelable>(IntentConstant.LOGISTIC_PROVIDER).notNull { viewModel?.logisticProviders = Parcels.unwrap<List<LogisticProvider>>(it) }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return bindContentView(inflater, container, R.layout.fragment_packing_detail_item)
    }

    @Subscribe
    fun onScannedRfidTextFetched(event: PackingItemUIEvent.OnScannedRfidTextFetched) {
        viewModel?.checkRfidText(event.rfidText)
    }

    @Subscribe
    fun onScrollToPosition(event: PackingItemUIEvent.OnScrollToPosition) {
        Handler(Looper.getMainLooper()).postDelayed({ binding?.adapterLinearItems?.scrollToChildAtIndex(event.position) }, IntentConstant.DELAY_TIME_200)
    }

    @Subscribe
    fun onRewardClicked(event: BoxRewardSectionUIEvent.OnRewardClicked) {
        viewModel?.setSelectedBoxRewards(event.selectedReward)
    }
}