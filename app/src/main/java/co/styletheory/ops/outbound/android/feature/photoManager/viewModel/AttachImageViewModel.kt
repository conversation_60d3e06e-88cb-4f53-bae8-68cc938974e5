package co.styletheory.ops.outbound.android.feature.photoManager.viewModel

import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.event.PreviewImageEvent
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseInjectedViewModel

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 23 November 2017.
 * Description
 *
 * <EMAIL>
 */
class AttachImageViewModel : BaseInjectedViewModel() {
    val imageUrl = ObservableString()

    fun previewImage() {
        eventBus.post(PreviewImageEvent(imageUrl.get()))
    }
}