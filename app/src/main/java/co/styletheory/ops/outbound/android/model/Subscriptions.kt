package co.styletheory.ops.outbound.android.model

import org.parceler.Parcel

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 05 November 2017.
 * Description
 *
 * <EMAIL>
 */

@Parcel
data class Subscriptions(
        val id: String = "",
        val createdAt: String = "",
        val price: Price? = null
) {
    @Parcel
    data class Price(
            val amount: Long = 0,
            val currency: String = "",
            val symbol: String = ""
    )
}