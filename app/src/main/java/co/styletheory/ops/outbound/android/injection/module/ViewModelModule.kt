package co.styletheory.ops.outbound.android.injection.module

import co.styletheory.ops.outbound.android.feature.accuracySwap.viewModel.AccuracySwapViewModel
import co.styletheory.ops.outbound.android.feature.accuracySwap.viewModel.impl.AccuracySwapViewModelImpl
import co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel.BacklogDetailViewModel
import co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel.impl.BacklogDetailViewModelImpl
import co.styletheory.ops.outbound.android.feature.batch.viewModel.BatchListViewModel
import co.styletheory.ops.outbound.android.feature.batch.viewModel.BatchStepViewModel
import co.styletheory.ops.outbound.android.feature.batch.viewModel.BatchViewModel
import co.styletheory.ops.outbound.android.feature.batch.viewModel.impl.BatchViewModelImpl
import co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.BoxRewardSectionViewModel
import co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.ItemBoxRewardSectionViewModel
import co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.impl.BoxRewardSectionViewModelImpl
import co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.impl.ItemBoxRewardSectionViewModelImpl
import co.styletheory.ops.outbound.android.feature.errorSwapBottomSheet.viewModel.ErrorSwapItemBottomSheetViewModel
import co.styletheory.ops.outbound.android.feature.errorSwapBottomSheet.viewModel.impl.ErrorSwapItemBottomSheetViewModelImpl
import co.styletheory.ops.outbound.android.feature.goSend.viewModel.GoSendViewModel
import co.styletheory.ops.outbound.android.feature.goSend.viewModel.impl.GoSendViewModelImpl
import co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.viewModel.LoadingScanBarcodeBottomSheetViewModel
import co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.viewModel.impl.LoadingScanBarcodeBottomSheetViewModelImpl
import co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel.ItemLogisticProviderNameViewModel
import co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel.LogisticProviderNameViewModel
import co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel.impl.ItemLogisticProviderNameViewModelImpl
import co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel.impl.LogisticProviderNameViewModelImpl
import co.styletheory.ops.outbound.android.feature.packed.viewModel.PackedChildViewModel
import co.styletheory.ops.outbound.android.feature.packed.viewModel.PackedViewModel
import co.styletheory.ops.outbound.android.feature.packed.viewModel.impl.PackedChildViewModelImpl
import co.styletheory.ops.outbound.android.feature.packed.viewModel.impl.PackedViewModelImpl
import co.styletheory.ops.outbound.android.feature.packing.viewModel.PackingDetailItemViewModel
import co.styletheory.ops.outbound.android.feature.packing.viewModel.PackingDetailViewModel
import co.styletheory.ops.outbound.android.feature.packing.viewModel.PackingItemViewModel
import co.styletheory.ops.outbound.android.feature.packing.viewModel.impl.PackingDetailItemViewModelImpl
import co.styletheory.ops.outbound.android.feature.packing.viewModel.impl.PackingDetailViewModelImpl
import co.styletheory.ops.outbound.android.feature.packing.viewModel.impl.PackingItemViewModelImpl
import co.styletheory.ops.outbound.android.feature.photoDetail.viewModel.PhotoDetailItemViewModel
import co.styletheory.ops.outbound.android.feature.photoDetail.viewModel.PhotoDetailViewModel
import co.styletheory.ops.outbound.android.feature.photoDetail.viewModel.impl.PhotoDetailItemViewModelImpl
import co.styletheory.ops.outbound.android.feature.photoDetail.viewModel.impl.PhotoDetailViewModelImpl
import co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.QcDetailItemViewModel
import co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.QcDetailViewModel
import co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.impl.QcDetailItemViewModelImpl
import co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.impl.QcDetailViewModelImpl
import co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.impl.QcPickedViewModel
import co.styletheory.ops.outbound.android.feature.qualitySwap.viewModel.QualitySwapViewModel
import co.styletheory.ops.outbound.android.feature.qualitySwap.viewModel.impl.QualitySwapViewModelImpl
import co.styletheory.ops.outbound.android.feature.scanBarcodeBottomSheet.viewModel.ScanBarcodeBottomSheetViewModel
import co.styletheory.ops.outbound.android.feature.scanBarcodeBottomSheet.viewModel.impl.ScanBarcodeBottomSheetViewModelImpl
import co.styletheory.ops.outbound.android.feature.settings.viewModel.SettingsViewModel
import co.styletheory.ops.outbound.android.feature.settings.viewModel.impl.BatchConfigItemViewModel
import co.styletheory.ops.outbound.android.feature.settings.viewModel.impl.SettingsViewModelImpl
import co.styletheory.ops.outbound.android.feature.signIn.SignInViewModel
import co.styletheory.ops.outbound.android.feature.signIn.SignInViewModelImpl
import co.styletheory.ops.outbound.android.feature.swapConfirmationBottomSheet.viewModel.SwapConfirmationBottomSheetViewModel
import co.styletheory.ops.outbound.android.feature.swapConfirmationBottomSheet.viewModel.impl.SwapConfirmationBottomSheetViewModelImpl
import co.styletheory.ops.outbound.android.general.test.EmptyViewModel
import co.styletheory.ops.outbound.android.general.test.EmptyViewModelImpl
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseInjectedViewModel
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.main.impl.MainViewModel
import co.styletheory.ops.outbound.android.main.viewModel.MainViewModelImpl
import co.styletheory.ops.outbound.android.viewModelComponent.FooterButtonViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.PhotoWithLabelViewModel
import dagger.Binds
import dagger.Module

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 19 October 2017.
 * Description
 *
 * <EMAIL>
 */

@Suppress("unused")
@Module
abstract class ViewModelModule {

    //Activity
    @Binds
    abstract fun bindEmptyViewModel(viewModel: EmptyViewModelImpl): EmptyViewModel

    @Binds
    abstract fun bindSignInViewModel(viewModel: SignInViewModelImpl): SignInViewModel

    @Binds
    abstract fun bindMainViewModel(viewModel: MainViewModelImpl): MainViewModel

    @Binds
    abstract fun bindBacklogDetailViewModel(viewModel: BacklogDetailViewModelImpl): BacklogDetailViewModel

    @Binds
    abstract fun bindQcDetailViewModel(viewModel: QcDetailViewModelImpl): QcDetailViewModel

    @Binds
    abstract fun bindQaPhotoDetailViewModel(viewModel: PhotoDetailViewModelImpl): PhotoDetailViewModel

    @Binds
    abstract fun bindReadyDetailViewModel(viewModel: PackingDetailViewModelImpl): PackingDetailViewModel

    @Binds
    abstract fun bindSettingsViewModel(viewModel: SettingsViewModelImpl): SettingsViewModel

    @Binds
    abstract fun bindAccuracySwapViewModel(viewModel: AccuracySwapViewModelImpl): AccuracySwapViewModel

    @Binds
    abstract fun bindQualitySwapViewModel(viewModel: QualitySwapViewModelImpl): QualitySwapViewModel

    //Fragment
    @Binds
    abstract fun bindBatchViewModel(viewModel: BatchViewModelImpl): BatchViewModel

    @Binds
    abstract fun bindQcDetailItemViewModel(viewModel: QcDetailItemViewModelImpl): QcDetailItemViewModel

    @Binds
    abstract fun bindQaPhotoDetailItemViewModel(viewModel: PhotoDetailItemViewModelImpl): PhotoDetailItemViewModel

    @Binds
    abstract fun bindReadyDetailItemViewModel(viewModel: PackingDetailItemViewModelImpl): PackingDetailItemViewModel

    @Binds
    abstract fun bindPackedViewModel(viewModel: PackedViewModelImpl): PackedViewModel

    @Binds
    abstract fun bindPackedChildViewModel(viewModel: PackedChildViewModelImpl): PackedChildViewModel

    @Binds
    abstract fun bindGoSendViewModel(viewModel: GoSendViewModelImpl): GoSendViewModel

    //BottomSheet
    @Binds
    abstract fun bindScanBarcodeBottomSheetViewModel(viewModel: ScanBarcodeBottomSheetViewModelImpl): ScanBarcodeBottomSheetViewModel

    @Binds
    abstract fun bindSwapConfirmationBottomSheetViewModel(viewModel: SwapConfirmationBottomSheetViewModelImpl): SwapConfirmationBottomSheetViewModel

    @Binds
    abstract fun bindLoadingScanBarcodeBottomSheetViewModel(viewModel: LoadingScanBarcodeBottomSheetViewModelImpl): LoadingScanBarcodeBottomSheetViewModel

    @Binds
    abstract fun bindBoxRewardSectionViewModelImpl(viewModel: BoxRewardSectionViewModelImpl): BoxRewardSectionViewModel

    @Binds
    abstract fun bindItemBoxRewardSectionViewModelImpl(viewModel: ItemBoxRewardSectionViewModelImpl): ItemBoxRewardSectionViewModel

    //View
    @Binds
    abstract fun bindToolbarModel(viewModel: GeneralToolbarViewModel): BaseViewModel<GeneralToolbarViewModel>

    @Binds
    abstract fun bindFooterButtonViewModel(viewModel: FooterButtonViewModel): BaseViewModel<FooterButtonViewModel>

    @Binds
    abstract fun bindPhotoWithLabel(viewModel: PhotoWithLabelViewModel): BaseViewModel<PhotoWithLabelViewModel>

    @Binds
    abstract fun bindErrorSwapItemBottomSheetViewModel(viewModel: ErrorSwapItemBottomSheetViewModelImpl): ErrorSwapItemBottomSheetViewModel


    // View Holder

    @Binds
    abstract fun bindReadyItemViewModel(viewModel: PackingItemViewModelImpl): PackingItemViewModel

    @Binds
    abstract fun bindBatchListViewModel(viewModel: BatchListViewModel): BaseInjectedViewModel

    @Binds
    abstract fun bindBatchStepViewModel(viewModel: BatchStepViewModel): BaseInjectedViewModel

    @Binds
    abstract fun bindQcPickedViewModel(viewModel: QcPickedViewModel): BaseInjectedViewModel

    @Binds
    abstract fun bindBatchConfigItemViewModel(viewModel: BatchConfigItemViewModel): BaseInjectedViewModel

    @Binds
    abstract fun bindLogisticProviderNameViewModel(viewModel: LogisticProviderNameViewModelImpl): LogisticProviderNameViewModel

    @Binds
    abstract fun bindItemLogisticProviderNameViewModel(viewModel: ItemLogisticProviderNameViewModelImpl): ItemLogisticProviderNameViewModel

}