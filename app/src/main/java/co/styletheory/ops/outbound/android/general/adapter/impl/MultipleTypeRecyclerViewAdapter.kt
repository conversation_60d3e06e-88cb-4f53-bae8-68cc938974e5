package co.styletheory.ops.outbound.android.general.adapter.impl

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.RecyclerView
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import javax.inject.Inject


class MultipleTypeRecyclerViewAdapter @Inject constructor() : RecyclerView.Adapter<RecyclerViewHolder>() {

    var source: MultipleTypeRecycleViewSource? = null
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerViewHolder {
        val binding = DataBindingUtil.inflate<ViewDataBinding>(
            LayoutInflater.from(parent.context),
            source?.getLayoutIdForItemType(viewType) ?: 0,
            parent,
            false
        )
        return RecyclerViewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON>cycle<PERSON><PERSON><PERSON>wHolder, position: Int) {
        holder.bindViewModel(source?.getViewModelAtPosition(position))
    }

    override fun getItemCount(): Int {
        return source?.totalItemCount ?: 0
    }

    override fun getItemViewType(position: Int): Int {
        return source?.getItemTypeAtPosition(position) ?: 0
    }

    interface MultipleTypeRecycleViewSource {
        val totalItemCount: Int
        val listData: MutableList<*>
        fun <VM: ViewModel> getViewModelAtPosition(position: Int): VM
        fun getItemTypeAtPosition(position: Int): Int
        fun getLayoutIdForItemType(itemType: Int): Int
    }

}
