package co.styletheory.ops.outbound.android.general.adapter.impl

import android.os.Bundle
import android.os.Parcelable
import android.view.ViewGroup
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import co.styletheory.ops.outbound.android.general.adapter.PagerSource
import co.styletheory.ops.outbound.android.general.base.BaseFragment
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import javax.inject.Inject


/**
 * Created by dennisfarandy on 10/17/16.
 *
 */

class PagerAdapter @Inject constructor(fragmentManager: FragmentManager) : FragmentStatePagerAdapter(fragmentManager, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT) {
    var source: PagerSource? = null
    private val viewModelReference = mutableMapOf<Int, ViewModel?>()
    private val fragmentReference = mutableMapOf<Int, Fragment>()

    override fun getItem(position: Int): Fragment {
        return if(source?.cacheFragment() == true && fragmentReference.containsKey(position)) {
            fragmentReference[position] ?: Fragment()
        } else {
            source?.getItemTypeAtPosition(position) ?: Fragment()
        }
    }

    @Suppress("UNCHECKED_CAST")
    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        val fragment = super.instantiateItem(container, position)
        if(source?.cacheFragment() == true && viewModelReference.containsKey(position)) {
            (fragment as? BaseFragment<ViewDataBinding, ViewModel>)?.viewModel = viewModelReference[position]
        }
        return fragment
    }

    override fun getCount(): Int = source?.totalViewCount ?: 0

    override fun getPageTitle(position: Int): CharSequence = source?.getPageTitleAtPosition(position) ?: ""

    override fun destroyItem(container: ViewGroup, position: Int, fragmentObj: Any) {
        if(source?.cacheFragment() == true) {
            val fragment = fragmentObj as Fragment
            if(fragment is BaseFragment<*, *>) {
                fragmentReference[position] = fragment
                viewModelReference[position] = fragment.viewModel
            }
        }
        super.destroyItem(container, position, fragmentObj)
    }

    override fun saveState(): Parcelable {
        val bundle = super.saveState() as? Bundle
        bundle?.putParcelableArray("states", null) // Never maintain any states from the base class to avoid TransactionTooLargeException
        return bundle ?: Bundle()
    }

    fun clearCacheReference() {
        fragmentReference.clear()
        viewModelReference.clear()
    }

    fun removeReferenceAtPosition(position: Int) {
        fragmentReference.remove(position)
        viewModelReference.remove(position)
        notifyDataSetChanged()
    }
}
