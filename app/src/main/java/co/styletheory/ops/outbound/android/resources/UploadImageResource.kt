package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.AbstractResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.networking.OutboundService
import co.styletheory.ops.outbound.android.util.impl.URLUtilImpl
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.asRequestBody
import retrofit2.Call
import retrofit2.Retrofit
import java.io.File

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 11/21/17.
 */

class UploadImageResource(private val file: File, uploadUrl: String) : AbstractResource<Void>() {

    override var baseUrl: String = URLUtilImpl(uploadUrl).baseURL()

    override var uri: String = URLUtilImpl(uploadUrl).urn()

    override fun shouldUseAuthorize(): Boolean = false

    override fun removeAllHeader(): Boolean = true

    private val mediaType = "image/jpeg"

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<Void>>) {
        val data = file.asRequestBody(mediaType.toMediaTypeOrNull())
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.uploadImageUrl(uri, data))
    }

}
