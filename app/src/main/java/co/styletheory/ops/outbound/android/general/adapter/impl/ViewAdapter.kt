package co.styletheory.ops.outbound.android.general.adapter.impl

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import co.styletheory.ops.outbound.android.BR
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 26 October 2017.
 * Description
 *
 * <EMAIL>
 */
class ViewAdapter @Inject constructor(): BaseAdapter(){
    var items: List<Any> = mutableListOf()
    var layoutId: Int = 0

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        val holder: ViewHolder
        val view : View
        if(convertView == null) {
            val binding = DataBindingUtil.inflate<ViewDataBinding>(LayoutInflater.from(parent?.context), layoutId, parent, false)
            view = binding.root

            holder = ViewHolder(binding)
            view.tag = holder
        }else{
            view = convertView
            holder = view.tag as ViewHolder
        }
        holder.bindViewModel(getItem(position) as ViewModel)
        return view
    }

    override fun getItem(position: Int): Any {
        return items[position]
    }

    override fun getItemId(position: Int): Long {
        return 0
    }

    override fun getCount(): Int {
        return items.size
    }

    inner class ViewHolder (val binding: ViewDataBinding) {

        fun bindViewModel(viewModel: ViewModel) {
            binding.setVariable(BR.viewModel, viewModel)
            binding.executePendingBindings()
        }
    }
}