package co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.viewModel.impl

import co.styletheory.android.network.core.RequestResult
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.event.LoadingScanBarcodeBottomSheetNetworkEvent
import co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.event.LoadingScanBarcodeBottomSheetUIEvent
import co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.viewModel.LoadingScanBarcodeBottomSheetViewModel
import co.styletheory.ops.outbound.android.feature.qcDetail.event.QcDetailUIEvent
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.model.enums.SwapType
import co.styletheory.ops.outbound.android.model.response.CheckSwapAvailabilityResponse
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.BatchItemResource
import co.styletheory.ops.outbound.android.resources.CheckSwapItemAvailabilityResource
import co.styletheory.ops.outbound.android.resources.GetAccuracySwapItemResource
import co.styletheory.ops.outbound.android.util.AppConstant.ERROR_OUTBOUND_ACCURACY_SWAP.ERROR_NOT_FOUND_ITEM
import co.styletheory.ops.outbound.android.util.notNull
import com.styletheory.android.mvvm.general.binding.ObservableText
import javax.inject.Inject

/**
 * Created by Eminarti Sianturi on 22/06/20.
 */
class LoadingScanBarcodeBottomSheetViewModelImpl @Inject constructor() : BaseViewModel<LoadingScanBarcodeBottomSheetViewModelImpl>(), LoadingScanBarcodeBottomSheetViewModel {

    @Inject
    lateinit var dataService: DataService

    @Inject
    lateinit var userStorage: UserStorage

    override var batchId: String = ""
    override var swapItem: SwapItem = SwapItem()
    override var swapType: String = SwapType.QUALITY_SWAP.swapType
    override var newItemUuid: String = ""
    override val bottomSheetContent: ObservableText = ObservableText("")
    override val bottomSheetTitle: ObservableText = ObservableText("")

    val accuracySwapItemCallback = RequestResult<GraphQLData<BatchItem>, String> {
        onSuccess {
            it.notNull { item ->
                eventBus.post(LoadingScanBarcodeBottomSheetNetworkEvent.OnSwapItemFound(swapItem, item.data.result, SwapType.ACCURACY_SWAP))
            }
        }
        onError { eventBus.post(LoadingScanBarcodeBottomSheetUIEvent.OnErrorSwapItem(it.orEmpty())) }
    }

    val qualitySwapItemCallback = RequestResult<GraphQLData<CheckSwapAvailabilityResponse>, String> {
        onSuccess {
            it?.data?.result.notNull { swap ->
                if(swap.items.isNotEmpty()) {
                    eventBus.post(LoadingScanBarcodeBottomSheetNetworkEvent.OnSwapItemFound(swapItem, swap.items[0], SwapType.QUALITY_SWAP))
                } else {
                    val error = ERROR_NOT_FOUND_ITEM
                    eventBus.post(LoadingScanBarcodeBottomSheetUIEvent.OnErrorSwapItem(error))
                }
            }
        }
        onError { eventBus.post(LoadingScanBarcodeBottomSheetUIEvent.OnErrorSwapItem(it.orEmpty())) }
    }

    val updateStatusCallback = RequestResult<GraphQLData<Void>, String> {
        onSuccess { eventBus.post(LoadingScanBarcodeBottomSheetUIEvent.OnUpdateItemQAFailed) }
        onError {
            eventBus.post(LoadingScanBarcodeBottomSheetUIEvent.OnCloseClicked)
            eventBus.post(QcDetailUIEvent.OnErrorSwapItem(it.orEmpty()))
        }
    }

    override fun onClose() {
        eventBus.post(LoadingScanBarcodeBottomSheetUIEvent.OnCloseClicked)
    }

    override fun checkSwapItem() {
        if(isResellingOrBagsItem() || swapItem.boxId.isEmpty()) {
            updateItemToFailed()
        } else {
            when(swapType) {
                SwapType.QUALITY_SWAP.swapType -> {
                    checkQualitySwapItem()
                }
                SwapType.ACCURACY_SWAP.swapType -> {
                    checkAccuracySwapItem()
                }
                else -> {
                    onClose()
                }
            }
        }
    }

    override fun setupBottomSheetContent() {
        if(isResellingOrBagsItem()) {
            bottomSheetTitle.set(string(R.string.quality_swap_bottom_sheet_apply_title))
            bottomSheetContent.set(string(R.string.quality_swap_bottom_sheet_apply_content))
        } else {
            bottomSheetTitle.set(string(R.string.accuracy_swap_this_item))
            bottomSheetContent.set(string(R.string.accuracy_swap_bottom_sheet_scan_loading))
        }
    }

    private fun updateItemToFailed() {
        val resource = BatchItemResource(
                batchId,
                swapItem.shipmentId,
                listOf(swapItem.item?.id.orEmpty()),
                ProductStatus.QA_FAILED,
                swapItem.failCategory,
                swapItem.failReason
        )
        dataService.updateBatchItemStatus(resource, updateStatusCallback)
    }

    private fun checkAccuracySwapItem() {
        val resource = GetAccuracySwapItemResource(newItemUuid, swapItem.item?.id.orEmpty())
        dataService.getAccuracySwapItem(resource, accuracySwapItemCallback)
    }

    private fun checkQualitySwapItem() {
        val resource = CheckSwapItemAvailabilityResource(swapItem.item?.style?.id
               .orEmpty(), swapItem.item?.labelSize.orEmpty(), ProductStatus.AVAILABLE)
        dataService.checkSwapItemAvailability(resource, qualitySwapItemCallback)
    }

    private fun isResellingOrBagsItem(): Boolean {
        return (swapItem.item?.isResellingItem() == true || userStorage.isVerticalTypeBags() || userStorage.isUserOnDemandAndRegionID())
    }
}