package co.styletheory.ops.outbound.android.main.viewModel

import android.view.View
import androidx.databinding.ObservableInt
import co.styletheory.android.network.core.RequestResult
import co.styletheory.ops.outbound.android.general.auth.event.SignOutFailedEvent
import co.styletheory.ops.outbound.android.general.auth.event.SignOutSuccessEvent
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.injection.scope.PerActivity
import co.styletheory.ops.outbound.android.main.impl.MainViewModel
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.SignOutResource
import co.styletheory.ops.outbound.android.util.AppConstant
import co.styletheory.ops.outbound.android.util.ErrorResponse
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 19 October 2017.
 * Description
 *
 * <EMAIL>
 */

@PerActivity
class MainViewModelImpl @Inject constructor() : BaseViewModel<MainViewModelImpl>(), MainViewModel {

    @Inject
    lateinit var userStorage: UserStorage

    @Inject
    lateinit var dataService: DataService

    @Inject
    lateinit var errorResponse: ErrorResponse

    override val snowVisibility = ObservableInt(View.GONE)

    override fun setupSnow() {
        snowVisibility.set(featureFlagUtil.snowfallVisibility.get())
    }

    override fun getTitleTabAtIndex(index: Int): CharSequence {
        val titleTab = if(userStorage.isUserRegionID()) {
            arrayOf(AppConstant.MAIN_TAB.PICKING_TAB,
                    AppConstant.MAIN_TAB.QC_TAB,
                    AppConstant.MAIN_TAB.PHOTO_TAB,
                    AppConstant.MAIN_TAB.PACKING_TAB,
                    AppConstant.MAIN_TAB.PACKED_TAB
            )
        } else {
            arrayOf(AppConstant.MAIN_TAB.PICKING_TAB,
                    AppConstant.MAIN_TAB.QC_TAB,
                    AppConstant.MAIN_TAB.PACKING_TAB,
                    AppConstant.MAIN_TAB.PACKED_TAB
            )
        }
        return titleTab[index]
    }

    override fun doSignOut() {
        dataService.logout(
                SignOutResource(),
                RequestResult {
                    onSuccess {
                        userStorage.clearUserStorage()
                        eventBus.post(SignOutSuccessEvent())
                    }
                    onError { eventBus.post(SignOutFailedEvent(errorResponse.getErrorBodyDescription(it))) }

                }
        )
    }
}