package co.styletheory.ops.outbound.android.injection.component

import android.app.Application
import android.content.Context
import co.styletheory.android.network.core.APIManager
import co.styletheory.android.network.core.APIManagerImpl
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseInjectedViewModel
import co.styletheory.ops.outbound.android.injection.module.ApiModule
import co.styletheory.ops.outbound.android.injection.module.AppModule
import co.styletheory.ops.outbound.android.injection.scope.PerApplication
import co.styletheory.ops.outbound.android.networking.DataService
import dagger.Component
import org.greenrobot.eventbus.EventBus
import javax.inject.Singleton

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 18 October 2017.
 * Description
 *
 * <EMAIL>
 */

@Singleton
@PerApplication
@Component(modules = [AppModule::class, ApiModule::class])
interface AppComponent {

    fun getContext(): Context
    fun getEventBus(): EventBus
    fun getApplication(): Application
    fun getAPIManager(): APIManager
    fun getUserStorage(): UserStorage
    fun getDataService(): DataService

    fun inject(baseInjectedViewModel: BaseInjectedViewModel)
    fun inject(apiManagerImpl: APIManagerImpl)
    fun inject(dataService: DataService)
}