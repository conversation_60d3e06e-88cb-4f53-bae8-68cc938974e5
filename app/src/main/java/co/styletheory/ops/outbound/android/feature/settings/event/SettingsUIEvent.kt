package co.styletheory.ops.outbound.android.feature.settings.event

import co.styletheory.ops.outbound.android.feature.settings.viewModel.impl.BatchConfigItemViewModel

/**
 * Created by Yoga C. Pranata on 16/01/2019.
 * Android Engineer
 */
sealed class SettingsUIEvent {
    data class OnBtnGenerateBatchClicked(val region: String? = "", val verticalType: String? = "", val timeGeneration: String? = "")
    data class OnBatchIsGenerated(val isSuccess: Boolean = false, val message: String? = null)
    data class OnNullGenerationTime(val message: String? = "")
    data class OnRadioButtonClicked(val buttonType: String? = "", val viewModel: BatchConfigItemViewModel)
    object OnBatchDateClicked
    object OnApplyFilterClicked
}