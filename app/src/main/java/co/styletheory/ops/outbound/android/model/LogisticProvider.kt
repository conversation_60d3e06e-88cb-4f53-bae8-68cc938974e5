package co.styletheory.ops.outbound.android.model

import co.styletheory.ops.outbound.android.model.enums.LogisticProviderName
import co.styletheory.ops.outbound.android.util.AppConstant
import org.parceler.Parcel

/**
 * styletheory-ops-outbound-android
 * Created by dwiap<PERSON><PERSON> on 03 November 2017.
 * Description
 *
 * <EMAIL>
 */

@Parcel
data class LogisticProvider(
        val id: String = "",
        val name: String = "",
        val logo: ProviderLogo? = null,
        val region: String = ""
) {


    fun getProvider(): LogisticProviderName? {
        when(id.lowercase()) {
            AppConstant.LOGISTIC_PROVIDER.INDONESIA.GOSEND -> return LogisticProviderName.GOJEK
            AppConstant.LOGISTIC_PROVIDER.SINGAPORE.SINGPOST -> return LogisticProviderName.SINGAPORE_POST
            AppConstant.LOGISTIC_PROVIDER.SINGAPORE.HONESTBEE, AppConstant.LOGISTIC_PROVIDER.SINGAPORE.ZYLLEM -> return LogisticProviderName.HONESTBEE
            AppConstant.LOGISTIC_PROVIDER.SINGAPORE.SELF_COLLECT_SG, AppConstant.LOGISTIC_PROVIDER.INDONESIA.SELF_COLLECT_ID -> return LogisticProviderName.SELF_COLLECT
            AppConstant.LOGISTIC_PROVIDER.SINGAPORE.STYLETHEORY_SG -> return LogisticProviderName.STYLETHEORY_SG
            AppConstant.LOGISTIC_PROVIDER.SINGAPORE.PIING -> return LogisticProviderName.PIING
        }
        return null
    }
    
    fun isDoorToDoorLogisticID(): Boolean {
        return (id.contains(AppConstant.LOGISTIC_PROVIDER.INDONESIA.PAXEL, ignoreCase = true)
                || id.contains(AppConstant.LOGISTIC_PROVIDER.INDONESIA.SICEPAT, ignoreCase = true)
                || id.contains(AppConstant.LOGISTIC_PROVIDER.INDONESIA.NINJAVAN, ignoreCase = true))
    }

    fun isDoorToDoorLogisticSG(): Boolean {
        return (id.contains(AppConstant.LOGISTIC_PROVIDER.SINGAPORE.VERSAFLEET, ignoreCase = true)
                || id.contains(AppConstant.LOGISTIC_PROVIDER.SINGAPORE.SINGPOST, ignoreCase = true)
                || id.contains(AppConstant.LOGISTIC_PROVIDER.SINGAPORE.PICKUPP, ignoreCase = true)
                || id.contains(AppConstant.LOGISTIC_PROVIDER.SINGAPORE.DHL, ignoreCase = true)
                || id.contains(AppConstant.LOGISTIC_PROVIDER.SINGAPORE.PIING, ignoreCase = true))
    }

    @Parcel
    data class ProviderLogo(
            val url: String = "",
            val width: Int = 0,
            val height: Int = 0
    )
}