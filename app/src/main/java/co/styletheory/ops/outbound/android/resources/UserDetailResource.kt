package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.model.User
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import retrofit2.Call
import retrofit2.Retrofit

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 02 March 2018.
 * Description
 *
 * <EMAIL>
 */
class UserDetailResource : GraphQLResource<GraphQLData<User>>() {

    override fun graphQLRequest(): GraphQLRequest = GraphQLRequestList.USER

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<User>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.userDetail(baseUrl, createRequestBody()))
    }
}