package co.styletheory.ops.outbound.android.feature.packing.viewModel.impl

import android.view.View
import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableInt
import co.styletheory.android.network.core.RequestResult
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.impl.BoxRewardSectionViewModelImpl
import co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.impl.ItemBoxRewardSectionViewModelImpl
import co.styletheory.ops.outbound.android.feature.packing.event.PackingItemUIEvent
import co.styletheory.ops.outbound.android.feature.packing.event.PackingNetworkEvent
import co.styletheory.ops.outbound.android.feature.packing.viewModel.PackingDetailItemViewModel
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.BoxReward
import co.styletheory.ops.outbound.android.model.LogisticProvider
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.model.enums.ProcedureType
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.BatchItemResource
import co.styletheory.ops.outbound.android.util.notNull
import co.styletheory.ops.outbound.android.util.notNullOrEmpty
import co.styletheory.ops.outbound.android.viewModelComponent.LabelBoxViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.ServiceMethodWidgetViewModel
import javax.inject.Inject

/**
 * Created by giorgygunawan on 11/29/17.
 *
 */
class PackingDetailItemViewModelImpl @Inject constructor() : BaseViewModel<PackingDetailItemViewModelImpl>(), PackingDetailItemViewModel {

    init {
        viewModelClass = PackingDetailItemViewModelImpl::class
    }

    val title = ObservableString()
    val titleVisibility = ObservableInt(View.VISIBLE)
    val labelItems = ObservableArrayList<LabelBoxViewModel>()
    val itemsComplete = ObservableBoolean(false)
    val enableCompleteButton = ObservableBoolean(false)
    val completeButtonText = ObservableString()
    val customerName = ObservableString()
    val pickupAt = ObservableString()
    val readyItems = ObservableArrayList<PackingItemViewModelImpl>()
    val useToteBag = ObservableBoolean(false)
    val useShippingBox = ObservableBoolean(false)

    override val procedureTypeVisibility = ObservableInt(View.VISIBLE)
    override val businessMethodNameList = ObservableArrayList<ServiceMethodWidgetViewModel>()
    override val logisticNameList = ObservableArrayList<ServiceMethodWidgetViewModel>()
    override val procedureTypeList = ObservableArrayList<ServiceMethodWidgetViewModel>()
    override val rackName = ObservableString()
    override val rackSection = ObservableString()
    override var rfidList = mutableMapOf<String, Boolean>()
    override var shipments: Shipment? = null
    override val batchId = ObservableString()
    override var logisticProviders: List<LogisticProvider>? = null
    override val labelItemVisibility = ObservableInt(View.GONE)
    override val boxRewardsSectionVisibility = ObservableInt(View.GONE)
    override var selectedBoxRewards: ArrayList<BoxReward> = ArrayList()

    @Inject
    lateinit var dataService: DataService

    @Inject
    lateinit var userStorage: UserStorage

    @Inject
    lateinit var boxRewardSectionVM: BoxRewardSectionViewModelImpl

    override fun checkRfidText(rfidText: String) {
        val position = searchRfidItemPosition(rfidText)

        when {
            (position >= 0) -> {
                if(itemShouldInReady(readyItems[position].batchItem) && rfidList[rfidText] == false) {
                    readyItems[position].haveScannedRfid.set(true)
                    readyItems[position].isItemScanned.set(true)
                    rfidList[rfidText] = true
                    setCompleteButtonState()
                }
                eventBus.post(PackingItemUIEvent.OnScrollToPosition(position))
            }
        }
    }

    override fun mapShipment() {
        shipments.notNull { shipment ->
            setupCustomerTitle(shipment.customer?.name, rackName.get(), rackSection.get())

            title.set(shipment.tracking?.id)
            pickupAt.set(shipment.shipmentTime?.getScheduledPickupFormattedText())
            rfidList.clear()
            readyItems.clear()

            for(batchItem in shipment.items) {
                val readyItem = PackingItemViewModelImpl()
                readyItem.bindViewModel(batchItem, rackSection.get(), rackName.get())
                readyItems.add(readyItem)

                checkBatchItemRfid(batchItem)

                if(userStorage.isVerticalTypeBags()) {
                    readyItem.showBagsItems()
                } else {
                    readyItem.showApparelItems()
                }
            }

            setupTitleDivider(shipment)
            setupBusinessMethod(shipment)
            setupLogisticMethod(shipment)
            setupProcedureTypeMethod(shipment)
            setupLabelVisibility(LabelBoxViewModel.create(shipment))
            initBoxReward()
            setCompleteButtonState()
        }
    }

    fun checkBatchItemRfid(batchItem: BatchItem?) {
        batchItem?.rfid.notNullOrEmpty { rfid ->
            val position = searchRfidItemPosition(rfid)
            val itemStatus = itemIsCompleted(batchItem)
            rfidList[rfid] = itemStatus
            readyItems[position].haveScannedRfid.set(itemStatus)
            readyItems[position].isItemScanned.set(itemStatus)
        }
    }

    fun setCompleteButtonState() {
        if(isReadyItemCompleted()) {
            setButtonCompleted()
            setupBoxReward(true)
        } else {
            val enable = shouldEnableCompleteButton()
            enableCompleteButton.set(enable)
            itemsComplete.set(enable)
            completeButtonText.set(string(R.string.complete_order_label))
        }
    }

    fun completeButtonClick() {
        if(shouldEnableCompleteButton() && !isReadyItemCompleted()) {
            completeReadyShipment()
        }
    }

    fun setButtonCompleted() {
        itemsComplete.set(true)
        enableCompleteButton.set(false)
        completeButtonText.set(string(R.string.completed_label))
    }

    fun searchRfidItemPosition(rfidText: String): Int {
        return readyItems.indexOfFirst { (it.batchItem?.rfid.equals(rfidText, true)) }
    }

    fun completeReadyShipment() {
        val ids = shipments?.items?.mapTo(ArrayList()) { it.id }?.toList() ?: emptyList()
        val resource = BatchItemResource(batchId.get(), shipments?.id.orEmpty(), ids, ProductStatus.PACKED)
        showProgressDialog()
        dataService.updateBatchItemStatus(
                resource,
                RequestResult {
                    onSuccess {
                        setupBoxReward(true)
                        setButtonCompleted()
                        dismissProgressDialog()
                    }
                    onError {
                        dismissProgressDialog()
                        eventBus.post(PackingNetworkEvent.OnErrorUpdateItemStatus(it))
                    }
                }
        )
    }

    fun itemShouldInReady(batchItem: BatchItem?): Boolean {
        return (batchItem?.status == ProductStatus.PACKING)
    }

    fun isEnableCompleteButton(): Boolean {
        for(batchItem in shipments?.items.orEmpty()) {
            val isItemNotScanned = (rfidList[batchItem.rfid] == false)
            when {
                !itemShouldInReady(batchItem) -> return false
                featureFlagUtil.isBarcodeSettingIsOn() && isItemNotScanned -> return false
            }
        }
        return true
    }

    private fun shouldEnableCompleteButton(): Boolean {
        return isEnableCompleteButton() && isAllBoxRewardChecked()
    }

    fun isAllBoxRewardChecked(): Boolean {
        shipments?.box?.boxReward?.let { rewards ->
            return rewards.size == selectedBoxRewards.size
        }
        return true
    }

    fun isReadyItemCompleted(): Boolean {
        for(it in shipments?.items.orEmpty()) {
            if(!itemIsCompleted(it)) {
                return false
            }
        }
        return true
    }

    fun itemIsCompleted(batchItem: BatchItem?): Boolean {
        return (batchItem?.status == ProductStatus.PACKED
                || batchItem?.status == ProductStatus.RECEIVED_BY_CUSTOMER
                || batchItem?.status == ProductStatus.TRANSIT_TO_CUSTOMER)
    }

    fun setupCustomerTitle(customer: String?, rackName: String, rackSection: String) {
        if(userStorage.isUserOnDemandAndRegionID()) {
            customerName.set(string(R.string.detail_item_on_demand_title).format(customer, rackName, rackSection))
        } else {
            customerName.set(string(R.string.detail_item_title).format(customer, rackName, rackSection))
        }
    }

    fun setupBusinessMethod(shipment: Shipment) {
        when {
            shipment.isBusinessMethodReselling() -> {
                businessMethodNameList.add(ServiceMethodWidgetViewModel.create(string(R.string.packing_detail_reselling_title)))
            }
            shipment.isBusinessMethodSurpriseKit() -> {
                businessMethodNameList.add(ServiceMethodWidgetViewModel.create(string(R.string.packing_detail_surprise_kit_title)))
            }
            else -> {
                businessMethodNameList.add(ServiceMethodWidgetViewModel.create(shipment.businessMethod))
            }
        }
    }

    fun setupLogisticMethod(shipment: Shipment) {
        logisticNameList.add(ServiceMethodWidgetViewModel.create(shipment.logisticProvider?.name))
    }

    fun setupProcedureTypeMethod(shipment: Shipment) {
        when {
            shipment.isProcedureTypeOneToOne() -> {
                procedureTypeList.add(
                        ServiceMethodWidgetViewModel.create(string(ProcedureType.ONE_TO_ONE.stringRes))
                )
            }
            shipment.isProcedureTypeOneToOneWithLock() -> {
                procedureTypeList.add(
                        ServiceMethodWidgetViewModel.create(string(ProcedureType.ONE_TO_ONE_WITH_LOCK.stringRes))
                )
            }
            shipment.isProcedureTypeRegularWithLock() -> {
                procedureTypeList.add(
                        ServiceMethodWidgetViewModel.create(string(ProcedureType.REGULAR_WITH_LOCK.stringRes))
                )
            }
            shipment.isProcedureTypeRegular() -> {
                procedureTypeVisibility.set(View.GONE)
            }
            else -> {
                procedureTypeList.add(
                        ServiceMethodWidgetViewModel.create(shipment.procedureType)
                )
            }
        }
    }

    override fun setupLabelVisibility(list: List<LabelBoxViewModel>) {
        if(list.isNotEmpty()) {
            labelItemVisibility.set(View.VISIBLE)
            labelItems.addAll(list)
        } else {
            labelItemVisibility.set(View.GONE)
            labelItems.clear()
        }
    }

    fun setupTitleDivider(shipment: Shipment) {
        if(shipment.tracking?.id.isNullOrEmpty()) {
            titleVisibility.set(View.INVISIBLE)
        }
    }

    override fun setBoxRewardViewModel(): BoxRewardSectionViewModelImpl = boxRewardSectionVM
    override fun setupBoxReward(isComplete: Boolean) {
        if(shipments?.box?.boxReward?.isEmpty() == true) {
            boxRewardsSectionVisibility.set(View.GONE)
        } else {
            shipments?.box?.boxReward?.let { rewards ->
                boxRewardsSectionVisibility.set(View.VISIBLE)
                boxRewardSectionVM.setBoxRewards(rewards, isComplete)
            }
        }
    }

    private fun initBoxReward() {
        selectedBoxRewards.clear()
        setupBoxReward(false)
    }

    override fun setSelectedBoxRewards(modelReward: ItemBoxRewardSectionViewModelImpl) {
        if(boxRewardSectionVM.rewardItems.contains(modelReward))
            if(modelReward.isRewardItemChecked.get()) {
                selectedBoxRewards.add(modelReward.boxReward)
            } else {
                selectedBoxRewards.remove(modelReward.boxReward)
            }
        setCompleteButtonState()
    }
}