package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import retrofit2.Call
import retrofit2.Retrofit

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 22/06/20.
 */
class GetAccuracySwapItemResource constructor(private val rfid: String,
                                              private val oldItemUuid: String) : GraphQLResource<GraphQLData<BatchItem>>() {

    override fun graphQLRequest(): GraphQLRequest = GraphQLRequestList.GET_ACCURACY_SWAP_ITEM

    override fun bodyParameter(): MutableMap<String, Any> {
        bodyParameter["rfid"] = rfid
        bodyParameter["oldItemId"] = oldItemUuid
        return bodyParameter
    }

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<BatchItem>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.getAccuracySwapItem(uri, createRequestBody()))
    }
}