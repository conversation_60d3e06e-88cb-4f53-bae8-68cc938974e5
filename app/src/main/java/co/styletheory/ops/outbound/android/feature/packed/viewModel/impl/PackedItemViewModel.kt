package co.styletheory.ops.outbound.android.feature.packed.viewModel.impl

import android.view.View
import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableInt
import co.styletheory.ops.outbound.android.feature.packed.event.PackedItemUIEvent
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.event.CallEvent
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseInjectedViewModel
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.model.enums.DeliveryType
import co.styletheory.ops.outbound.android.model.enums.ShipmentStatus
import co.styletheory.ops.outbound.android.util.Next
import co.styletheory.ops.outbound.android.util.notNull
import co.styletheory.ops.outbound.android.util.notNullOrEmpty
import co.styletheory.ops.outbound.android.viewModelComponent.LabelBoxViewModel

/**
 * Created by giorgygunawan on 11/22/17.
 */
class PackedItemViewModel : BaseInjectedViewModel() {

    var shipment: Shipment? = null
    var showTrackingInfo: Boolean? = null
    val packageName = ObservableString()
    val packageAuthor = ObservableString()
    val pickupAt = ObservableString()
    val labelItems = ObservableArrayList<LabelBoxViewModel>()
    val isSelected = ObservableBoolean()
    val trackingStatus = ObservableString()
    val courierName = ObservableString()
    val courierImage = ObservableString()
    val courierPhone = ObservableString()
    val checkedBoxVisibility = ObservableInt(View.GONE)
    val courierInfoVisibility = ObservableInt(View.GONE)
    val trackingStatusVisibility = ObservableInt(View.GONE)
    val trackingVisibility = ObservableInt(View.GONE)
    var onItemCheckedCallback: Next<PackedItemViewModel>? = null

    fun bindViewModel(shipment: Shipment) {
        this.shipment = shipment
        packageName.set(shipment.tracking?.id)
        packageAuthor.set(shipment.customer?.name)
        pickupAt.set(shipment.shipmentTime?.getScheduledPickupFormattedText())
        labelItems.addAll(LabelBoxViewModel.create(shipment))
        trackingVisibility.set(if(showTrackingInfo == true) View.VISIBLE else View.GONE)

        goSendCheckboxHandle()

        if(shipment.courier?.name != null) {
            courierInfoVisibility.set(View.VISIBLE)
            trackingStatusVisibility.set(View.GONE)
            courierName.set(shipment.courier.name)
            courierPhone.set(shipment.courier.phone)
            courierImage.set(shipment.courier.imageUrl)
        } else {
            trackingStatusVisibility.set(View.VISIBLE)
            trackingStatus.set(shipment.tracking?.status)
            courierInfoVisibility.set(View.GONE)
        }
    }

    fun goSendCheckboxHandle() {
        showTrackingInfo.notNull {
            if(!it)
                checkedBoxVisibility.set(View.VISIBLE)
        }
    }

    fun onPhoneButtonClicked() {
        eventBus.post(CallEvent(courierPhone.get()))
    }

    fun onCheckButtonClicked() {
        isSelected.set(!isSelected.get())
        onItemCheckedCallback?.apply(this)
    }

    fun hideCheckbox() {
        checkedBoxVisibility.set(View.GONE)
    }

    fun onBoxClicked() {
        if(needsWarehouseReceival()) {
            shipment?.id?.notNullOrEmpty { shipmentId -> eventBus.post(PackedItemUIEvent.OnShowMarkShipmentDialogPopup(shipmentId)) }
        }
    }

    private fun needsWarehouseReceival() = shipment?.deliveryType == DeliveryType.DELIVERY.toString() && shipment?.status == ShipmentStatus.FAILED.toString()
}