package co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.impl

import androidx.databinding.ObservableArrayList
import androidx.recyclerview.widget.LinearLayoutManager
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.BoxRewardSectionViewModel
import co.styletheory.ops.outbound.android.general.recyclerView.RecyclerViewSetting
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.model.BoxReward
import javax.inject.Inject

/**
 * Created by <PERSON> on 11/20/20.
 */
class BoxRewardSectionViewModelImpl @Inject constructor() : BaseViewModel<BoxRewardSectionViewModelImpl>(), BoxRewardSectionViewModel {

    init {
        viewModelClass = BoxRewardSectionViewModelImpl::class
    }

    override val rewardItems = ObservableArrayList<ItemBoxRewardSectionViewModelImpl>()

    override val recyclerViewSetting = RecyclerViewSetting()

    private fun setUpRecycleView() {
        recyclerViewSetting.apply {
            layoutId = R.layout.box_reward_section_item
            orientation = LinearLayoutManager.VERTICAL
            layoutManagerType = RecyclerViewSetting.LayoutManagerType.LINEAR
        }
    }

    override fun setBoxRewards(rewards: List<BoxReward>, isCompleted: Boolean) {
        setUpRecycleView()
        rewardItems.clear()
        for(it in rewards) {
            if(isCompleted) {
                rewardItems.add(ItemBoxRewardSectionViewModelImpl().completedBoxReward(it))
            } else {
                rewardItems.add(ItemBoxRewardSectionViewModelImpl().bindBoxReward(it))
            }
        }
    }
}