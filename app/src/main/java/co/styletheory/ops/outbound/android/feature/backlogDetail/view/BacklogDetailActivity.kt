package co.styletheory.ops.outbound.android.feature.backlogDetail.view

import android.content.DialogInterface
import android.os.Bundle
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.widget.Toolbar
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.BacklogDetailActivityBinding
import co.styletheory.ops.outbound.android.feature.backlogDetail.event.BacklogItemSwapClickEvent
import co.styletheory.ops.outbound.android.feature.backlogDetail.event.BacklogItemUIEvent
import co.styletheory.ops.outbound.android.feature.backlogDetail.event.RefreshCompleteButtonEvent
import co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel.BacklogDetailViewModel
import co.styletheory.ops.outbound.android.feature.swap.SwapItemDialogViewModel
import co.styletheory.ops.outbound.android.general.adapter.WrapContentLinearLayoutManager
import co.styletheory.ops.outbound.android.general.adapter.impl.MultipleTypeRecyclerViewAdapter
import co.styletheory.ops.outbound.android.general.base.BaseActivity
import co.styletheory.ops.outbound.android.general.base.BaseDialogFragment
import co.styletheory.ops.outbound.android.general.event.DismissDialogEvent
import co.styletheory.ops.outbound.android.general.event.PreviewImageEvent
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.util.*
import co.styletheory.ops.outbound.android.viewModelComponent.AlertDialogViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.FooterButtonViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.ProcessDialogViewModel
import org.greenrobot.eventbus.Subscribe
import javax.inject.Inject


/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 24 October 2017.
 * Description
 *
 * <EMAIL>
 */

@Suppress("UNUSED_PARAMETER", "unused")
class BacklogDetailActivity : BaseActivity<BacklogDetailActivityBinding, BacklogDetailViewModel>() {

    @Inject
    lateinit var adapter: MultipleTypeRecyclerViewAdapter

    override fun onResume() {
        super.onResume()
        refreshBatchDetail()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        activityComponent?.inject(this)
        bindContentView(R.layout.backlog_detail_activity)
        initExtras()
        setSupportActionBar(binding?.toolbarWrapper?.toolbar as Toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        setupToolbarViewModel()
        setupRecyclerView()

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                navigator.finishWithResult()
            }
        })
    }

    private val fetchBatchDetailCallback = object : Result<Void?, String?> {
        override fun success(success: Void?) {
            binding?.swipeRefreshBacklog?.isRefreshing = false
            dismissProgressDialog()
            binding?.recyclerView?.notifyChangedAndClearRecycledPool()
            viewModel?.refreshCompleteStatusButton()
        }

        override fun failure(error: String?) {
            binding?.swipeRefreshBacklog?.isRefreshing = false
            dismissProgressDialog()
            showShortToast(error ?: getString(R.string.err_something_when_wrong))
        }
    }

    private fun initExtras() {
        intent?.getStringExtra(IntentConstant.BATCH_ID).notNull { viewModel?.batchId?.set(it) }
        intent.getSerializableExtra(IntentConstant.BATCH_STATUS).notNull {
            viewModel?.batchStatus = it as BatchStatus
        }
    }

    private fun fetchBatchDetail() {
        showProgressDialog()
        viewModel?.fetchBatchDetail(fetchBatchDetailCallback)
    }

    private fun refreshBatchDetail() {
        binding?.swipeRefreshBacklog?.isRefreshing = true
        viewModel?.refreshBatchDetail(fetchBatchDetailCallback)
    }

    private fun setupRecyclerView() {
        adapter.source = viewModel
        binding?.recyclerView?.layoutManager = WrapContentLinearLayoutManager(this)
        binding?.recyclerView?.adapter = adapter
        binding?.swipeRefreshBacklog?.setOnRefreshListener { refreshBatchDetail() }
    }

    private fun setupToolbarViewModel() {
        binding?.toolbarVM = viewModel?.toolbarViewModel()
    }

    private fun hideKeyboardAndClearText() {
        binding?.editTextScannedRfid?.let { hideSoftKeyboard(it) }
        binding?.editTextScannedRfid?.text?.clear()
    }

    //region EVENT BUS
    @Subscribe
    fun swapItemClick(event: BacklogItemSwapClickEvent) {
        val model = SwapItemDialogViewModel()
        model.bindViewModel(event.shipmentId, event.boxId, event.customerEmail, event.batchItem, event.callback)
        BaseDialogFragment.setViewModelAndLayoutId(model, R.layout.swap_item_dialog).show(supportFragmentManager)
    }


    @Subscribe
    fun refreshCompleteButtonEvent(event: RefreshCompleteButtonEvent) {
        viewModel?.refreshCompleteStatusButton()
    }

    @Subscribe
    fun completeBacklogEvent(event: FooterButtonViewModel.CompleteButtonEvent) {
        showProgressDialog()
        viewModel?.completeBacklog(object : Result<Void?, String?> {
            override fun success(success: Void?) {
                dismissProgressDialog()

                val dialog = BaseDialogFragment.setViewModelAndLayoutId(
                    ProcessDialogViewModel.success(
                        viewModel?.batchName?.get()
                            .orEmpty(), this@BacklogDetailActivity
                    ),
                    R.layout.process_dialog
                )

                dialog.onDialogDismiss(object : DialogInterface {
                    override fun cancel() {
                        //Not implemented yet
                    }

                    override fun dismiss() {
                        navigator.finishWithResult()
                    }
                })

                dialog.setDismissAfter(1500)
                dialog.show(supportFragmentManager)
            }

            override fun failure(error: String?) {
                dismissProgressDialog()
                showShortToast(error ?: string(R.string.err_something_when_wrong))
            }

        })
    }

    @Subscribe
    fun previewImageEvent(event: PreviewImageEvent) {
        navigator.toFullScreenImageSlideShowActivity(event.imageUrls)
    }

    @Subscribe
    fun onScannedRfidFoundPosition(event: BacklogItemUIEvent.OnScannedRfidFoundPosition) {
        binding?.recyclerView?.scrollToPosition(event.itemPosition)
        hideKeyboardAndClearText()
    }

    @Subscribe
    fun onScannedRfidTextNotFound(event: BacklogItemUIEvent.OnScannedRfidTextNotFound) {
        hideKeyboardAndClearText()
        val dialogViewModel = AlertDialogViewModel()
        dialogViewModel.setTitle(event.title)
        dialogViewModel.setBody(event.message)
        dialogViewModel.setRightButtonText(string(R.string.ok_button))
        dialogViewModel.setRightButtonClickListener(object : AlertDialogViewModel.OnClickListener {
            override fun onClick() {
                eventBus.post(DismissDialogEvent(AlertDialogViewModel.TAG))
            }
        })

        val dialog = BaseDialogFragment.setViewModelAndLayoutId(dialogViewModel, R.layout.alert_dialog)
        dialog.show(supportFragmentManager, AlertDialogViewModel.TAG)
    }
    //endregion
}