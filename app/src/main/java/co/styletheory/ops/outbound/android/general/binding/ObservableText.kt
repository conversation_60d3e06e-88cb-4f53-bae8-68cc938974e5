package com.styletheory.android.mvvm.general.binding

import androidx.annotation.ColorRes
import androidx.annotation.PluralsRes
import androidx.databinding.BaseObservable

/**
 * styletheory-android
 * Created by dwiaprianto on 28 June 2018.
 * Description
 *
 * Add by j<PERSON><PERSON> on 31/01/2019
 */
class ObservableText constructor() : BaseObservable() {
    private var textValue: String = ""
    private var textRes: Int = 0
    val textArgs: MutableList<Any> = mutableListOf()
    var isHtml: Boolean = false
    var isWithLinkMovementMethod: Boolean = false
    var quantity: Int? = null

    val spanTexts = mutableListOf<SpanText>()

    data class SpanText(
            val highlight: Any? = null,
            val click: (() -> Unit)? = null,
            val backgroundColor: Int = 0,
            @ColorRes val textColor: Int = 0,
            val isUnderline: Boolean = false,
            val isBold: Boolean = false,
            val isItalic: Boolean = false
    )

    fun setSpanText(vararg spanText: SpanText) {
        for(it in spanText.asSequence()) { spanTexts.add(it) }
        notifyChange()
    }

    constructor(textRes: Int?) : this() {
        this.textRes = textRes ?: 0
    }

    constructor(text: String?) : this() {
        this.textValue = text.orEmpty()
    }

    constructor(isHtml: Boolean, isWithLinkMovementMethod: Boolean = false) : this() {
        this.isHtml = isHtml
        this.isWithLinkMovementMethod = isWithLinkMovementMethod
    }

    fun get(): Any {
        return if(textRes != 0) {
            this.textRes
        } else {
            textValue
        }
    }

    fun set(value: String?) {
        clear()
        this.textValue = value.orEmpty()
        notifyChange()
    }

    fun setPlurals(@PluralsRes value: Int, quantity: Int, vararg args: Any?) {
        clear()
        this.textRes = value
        for(it in args) { if(it != null) textArgs.add(it) }
        this.quantity = quantity
        notifyChange()
    }

    fun set(value: String?, vararg args: Any?) {
        clear()
        this.textValue = value.orEmpty()
        for(it in args) { if(it != null) textArgs.add(it) }
        notifyChange()
    }

    fun set(value: Int?) {
        clear()
        this.textRes = value ?: 0
        notifyChange()
    }

    fun set(value: Int?, vararg args: Any?) {
        clear()
        this.textRes = value ?: 0
        for(it in args) { if(it != null) textArgs.add(it) }
        notifyChange()
    }

    fun clear() {
        spanTexts.clear()
        textArgs.clear()
        textValue = ""
        textRes = 0
    }
}
