package co.styletheory.ops.outbound.android.feature.photoManager.viewModel

import androidx.databinding.ObservableBoolean
import co.styletheory.android.network.core.RequestResult
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.injection.scope.PerActivity
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.enums.AttachPhotoType
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.AttachPhotoToItemResource
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.util.notNull
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralPhotoGridItemViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel
import javax.inject.Inject

/**
 * Created by giorgygunawan on 11/7/17.
 *
 */
@PerActivity
class PhotoManagerViewModel @Inject constructor() : BaseViewModel<PhotoManagerViewModel>() {

    @Inject
    lateinit var toolbarViewModel: GeneralToolbarViewModel
    @Inject
    lateinit var dataService: DataService
    @Inject
    lateinit var errorResponse: ErrorResponse

    init {
        viewModelClass = PhotoManagerViewModel::class
    }

    var shipmentId: String? = ""
    var batchItem: BatchItem? = null

    var customListener: Listener? = null
    var existingImages: List<String> = emptyList()
    var photoViewModels: ArrayList<GeneralPhotoGridItemViewModel> = ArrayList()
    var attachmentViewModel: ArrayList<GeneralPhotoGridItemViewModel> = ArrayList()

    val productFeaturedImageUrl = ObservableString()
    val designerName = ObservableString()
    val itemName = ObservableString()
    val isEditingMode: ObservableBoolean = ObservableBoolean(false)

    override fun afterInject() {
        super.afterInject()
        toolbarViewModel.title.set("Photo Gallery Manager")
    }


    fun addNewPhotoUrls(photoUrls: List<String>) {
        for(it in photoUrls.map { ObservableString(it) }) {
            val photoGridItem = GeneralPhotoGridItemViewModel()
            photoGridItem.bindPhoto(photoViewModels.size, it, shipmentId, true, isEditingMode)
            photoViewModels.add(photoGridItem)
        }

        attachmentViewModel.addAll(photoViewModels)
    }

    fun getAllImageUrls(): ArrayList<String> = photoViewModels.mapTo(ArrayList()) { if(it.imageUrl.get().isEmpty()) it.localImageUrl.get() else it.imageUrl.get() }

    fun fillNecessaryFields(orderId: String, batchItem: BatchItem, isEditMode: Boolean, listener: Listener) {
        this.shipmentId = orderId
        this.batchItem = batchItem
        this.customListener = listener
        this.isEditingMode.set(isEditMode)
        bindModel()
    }

    private fun bindModel() {
        batchItem?.style.notNull {
            designerName.set(it.designer?.name)
            itemName.set(it.name)
            if(it.gallery.isNotEmpty()) {
                productFeaturedImageUrl.set(it.gallery.first())
            }
        }

        for(it in existingImages.map { ObservableString(it) }) {
            val photoGridItem = GeneralPhotoGridItemViewModel()
            photoGridItem.bindPhoto(photoViewModels.size, it, shipmentId, false, isEditingMode)
            photoViewModels.add(photoGridItem)
        }

    }

    fun deleteViewModel(viewModel: GeneralPhotoGridItemViewModel) {
        for(vm in photoViewModels) {
            if(vm.imageUrl.get().equals(viewModel.imageUrl.get(), ignoreCase = true)) {
                photoViewModels.remove(vm)
                break
            }
        }
    }

    fun removePhotoAfterUploaded(viewModel: GeneralPhotoGridItemViewModel): Boolean {
        for(vm in attachmentViewModel) {
            if(vm.imageUrl.get().equals(viewModel.imageUrl.get(), ignoreCase = true)) {
                attachmentViewModel.remove(vm)
                break
            }
        }

        return attachmentViewModel.size == 0
    }


    fun attachImageToItem(attachPhotoType: AttachPhotoType?, callback: Result<Void?, String?>) {
        dataService.attachImageToItem(
                AttachPhotoToItemResource(batchItem?.id.orEmpty(), getAllImageUrls(), attachPhotoType),
                RequestResult {
                    onSuccess { callback.success(null) }
                    onError { callback.failure(errorResponse.getErrorBodyDescription(it)) }
                }
        )
    }

    interface Listener {
        fun onSelectChooseOrTakePhotoCallback()
    }

}