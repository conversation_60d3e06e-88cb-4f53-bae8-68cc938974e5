package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.model.Session
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import retrofit2.Call
import retrofit2.Retrofit

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 26 February 2018.
 * Description
 *
 * <EMAIL>
 */
class CreatePasswordResource constructor(
        val name: String,
        val email: String,
        val password: String,
        private val newPassword: String
) : GraphQLResource<GraphQLData<Session>>() {

    override fun graphQLRequest(): GraphQLRequest = GraphQLRequestList.CREATE_PASSWORD

    override fun bodyParameter(): MutableMap<String, Any> {
        bodyParameter["name"] = name
        bodyParameter["email"] = email
        bodyParameter["password"] = password
        bodyParameter["newPassword"] = newPassword
        return super.bodyParameter()
    }

    override fun shouldUseAuthorize(): Boolean = false

    override fun headerParameter(): MutableMap<String, String> {
        headerParameter["x-madame-cognito-client-id"] = "64vqep0aourgc8p99rdqmfhu44"
        return super.headerParameter()
    }

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<Session>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.createPassword(baseUrl, createRequestBody()))
    }
}