package com.styletheory.android.helper

import android.content.Context
import org.json.JSONObject
import java.io.InputStream
import javax.inject.Inject

/**
 * Created by <PERSON> on 22/02/23.
 */
class FeatureFlagHelper @Inject constructor() {

    @Inject
    lateinit var context: Context
    private val jsonObj: JSONObject by lazy {
        with(context) {
            val assets = this.resources.assets
            val fileName = "feature-flag-config.json"
            val inputStream: InputStream = assets.open(fileName)
            val size: Int = inputStream.available()
            val buffer = ByteArray(size)
            inputStream.read(buffer)
            inputStream.close()
            JSONObject(String(buffer))
        }
    }

    fun getConfigBoolean(key: String): Boolean {
        return jsonObj.getBoolean(key)
    }

    fun getConfigString(key: String): String {
        return jsonObj.getString(key)
    }

    fun getConfigInt(key: String): Int {
        return jsonObj.getInt(key)
    }
}