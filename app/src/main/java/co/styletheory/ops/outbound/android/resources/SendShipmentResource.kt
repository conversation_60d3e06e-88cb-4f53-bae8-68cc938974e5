package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.model.response.SendShipmentResponse
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import org.joda.time.DateTime
import retrofit2.Call
import retrofit2.Retrofit

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 25 January 2018.
 * Description
 *
 * <EMAIL>
 */
class SendShipmentResource(private val shipmentIds: List<String>, val shipmentDate: DateTime) : GraphQLResource<GraphQLData<SendShipmentResponse>>() {
    override fun graphQLRequest(): GraphQLRequest = GraphQLRequestList.SEND_SHIPMENT

    override fun bodyParameter(): MutableMap<String, Any> {
        bodyParameter["shipmentIds"] = shipmentIds
        bodyParameter["date"] = shipmentDate.toString("yyyy-MM-dd")
        return super.bodyParameter()
    }

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<SendShipmentResponse>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.sendShipment(uri, createRequestBody()))
    }
}