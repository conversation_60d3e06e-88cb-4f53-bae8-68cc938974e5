package co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.event

import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.model.enums.SwapType

sealed class LoadingScanBarcodeBottomSheetNetworkEvent {
    data class OnSwapItemFound(val oldItem: SwapItem, val newItem: BatchItem, val swapType: SwapType)
}