package co.styletheory.ops.outbound.android.feature.photoDetail.view

import android.app.Activity
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.viewpager.widget.ViewPager
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.QaPhotoDetailActivityBinding
import co.styletheory.ops.outbound.android.feature.photoDetail.viewModel.PhotoDetailViewModel
import co.styletheory.ops.outbound.android.feature.photoManager.event.TakePictureEvent
import co.styletheory.ops.outbound.android.feature.photoManager.view.PhotoManagerActivity
import co.styletheory.ops.outbound.android.general.adapter.impl.PagerAdapter
import co.styletheory.ops.outbound.android.general.base.BaseActivity
import co.styletheory.ops.outbound.android.general.base.BaseDialogFragment
import co.styletheory.ops.outbound.android.general.event.PreviewImageEvent
import co.styletheory.ops.outbound.android.model.enums.AttachPhotoType
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.util.EdgeToEdgeUtil
import co.styletheory.ops.outbound.android.util.IntentConstant
import co.styletheory.ops.outbound.android.util.Next
import co.styletheory.ops.outbound.android.util.PermissionHandlerActivity
import co.styletheory.ops.outbound.android.util.PermissionUtils
import co.styletheory.ops.outbound.android.util.RequestCode
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.util.notNull
import co.styletheory.ops.outbound.android.viewModelComponent.ProcessDialogViewModel
import com.styletheory.camera.screens.home.PhotosPickerActivity
import com.styletheory.camera.shared.models.Photo
import org.greenrobot.eventbus.Subscribe
import javax.inject.Inject

/**
 * Created by giorgygunawan on 11/27/17.
 *
 */
class PhotoDetailActivity : BaseActivity<QaPhotoDetailActivityBinding, PhotoDetailViewModel>(), PermissionHandlerActivity {
    @Inject
    lateinit var pagerAdapter: PagerAdapter
    private var takePhotoCallback: Next<List<String>>? = null
    private var currentPage = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        activityComponent?.inject(this)
        bindContentView(R.layout.qa_photo_detail_activity)
        initExtras()
        setupToolbar()
        setupViewPager()
        fetchBatchDetail()

        // Handle back button press with the new API
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                navigator.finishWithResult()
            }
        })
    }

    private fun initExtras() {
        intent.getStringExtra(IntentConstant.BATCH_ID).notNull { viewModel?.setBatchId(it) }
        intent.getSerializableExtra(IntentConstant.BATCH_STATUS).notNull { viewModel?.setBatchStatus(it as BatchStatus) }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if(resultCode == Activity.RESULT_OK && requestCode == RequestCode.PHOTO_MANAGER) {
            data.notNull {
                if(it.hasExtra(IntentConstant.PRODUCT_PHOTO_RESULT_QAIMAGES_EXTRA_CODE)) {
                    takePhotoCallback?.apply(it.getStringArrayListExtra(IntentConstant.PRODUCT_PHOTO_RESULT_QAIMAGES_EXTRA_CODE)?.toList() ?: listOf())
                }
            }
        } else if(requestCode == RequestCode.REQ_PHOTOS) {
            val photos = data?.getParcelableArrayListExtra<Photo>(PhotosPickerActivity.EXTRA_PHOTOS)
                ?: return
            processData(photos)
        }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding?.toolbarWrapper?.toolbar as Toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        binding?.toolbarVM = viewModel?.generalToolbarViewModel()
    }

    private fun setupViewPager() {
        binding.notNull {
            it.viewPager.setPadding(50, 0, 50, 0)
            it.viewPager.clipToPadding = false
            it.viewPager.pageMargin = 25
            it.tabs.setupWithViewPager(it.viewPager)
            pagerAdapter.source = viewModel
            it.viewPager.adapter = pagerAdapter

            it.viewPager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
                override fun onPageScrollStateChanged(state: Int) {
                    //Not implemented yet
                }

                override fun onPageScrolled(
                    position: Int,
                    positionOffset: Float,
                    positionOffsetPixels: Int
                ) {
                    //Not implemented yet
                }

                override fun onPageSelected(position: Int) {
                    val subtitle = "${position + 1} of ${viewModel?.totalViewCount} Box"
                    viewModel?.changeToolbarSubtitle(subtitle)
                }
            })
        }
    }

    private fun fetchBatchDetail() {
        showProgressDialog()
        currentPage = binding?.viewPager?.currentItem ?: 0
        viewModel?.fetchBatchDetail(object : Result<Void?, String?> {
            override fun success(success: Void?) {
                rebindPagerAdapter()
                dismissProgressDialog()
            }

            override fun failure(error: String?) {
                dismissProgressDialog()
                showShortToast(error ?: string(R.string.err_something_when_wrong))
            }
        })
    }


    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.menu_complete, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if(item.itemId == R.id.action_complete) {
            doCompleteBatch()
            return true
        } else if(item.itemId == R.id.action_refresh) {
            fetchBatchDetail()
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    private fun doCompleteBatch() {
        viewModel.notNull {
            if(it.totalViewCount > 0) {
                if(viewModel?.isAllBatchItemShipmentCompletedPhoto() == true) {
                    completeBatch()
                } else {
                    BaseDialogFragment.setViewModelAndLayoutId(ProcessDialogViewModel.failed(this), R.layout.process_dialog).show(supportFragmentManager)
                }
            } else {
                showShortToast("Shipment not found")
            }
        }

    }

    private fun completeBatch() {
        showProgressDialog()
        viewModel?.completeBatch(object : Result<Void?, String?> {
            override fun success(success: Void?) {
                dismissProgressDialog()
                BaseDialogFragment.setViewModelAndLayoutId(
                    ProcessDialogViewModel.success(
                        viewModel?.batchName()
                            .orEmpty(), this@PhotoDetailActivity
                    ), R.layout.process_dialog
                )
                    .onDialogDismiss(object : DialogInterface {
                        override fun dismiss() {
                            finish()
                        }

                        override fun cancel() {
                            //Not implemented yet
                        }
                    }).setDismissAfter(1500).show(supportFragmentManager)
            }

            override fun failure(error: String?) {
                dismissProgressDialog()
                showShortToast(error ?: string(R.string.err_something_when_wrong))
            }
        })
    }

    private fun rebindPagerAdapter() {
        pagerAdapter = PagerAdapter(supportFragmentManager)
        pagerAdapter.source = viewModel
        binding?.viewPager?.adapter = pagerAdapter
        binding?.tabs?.setupWithViewPager(binding?.viewPager)
        binding?.viewPager?.adapter?.notifyDataSetChanged()
        binding?.viewPager?.currentItem = currentPage
    }

    @Subscribe
    fun takePictureEvent(event: TakePictureEvent) {
        takePhotoCallback = event.resultCallback
        viewModel?.shipmentId = event.shipmentId
        viewModel?.batchItem = event.batchItem

        showPhotoGalleryOrTakePictureActivity()
    }

    @Subscribe
    fun previewImage(event: PreviewImageEvent) {
        navigator.toFullScreenImageSlideShowActivity(event.imageUrls)
    }

    // Implementation of PermissionHandlerActivity interface
    override fun getActivity(): AppCompatActivity = this

    override fun onAllPermissionsGranted(requestCode: Int) {
        EdgeToEdgeUtil.preparePhotoPickerActivityEdgeToEdge()
        PhotosPickerActivity.launch(this, RequestCode.REQ_PHOTOS)
    }

    private fun showPhotoGalleryOrTakePictureActivity() {
        // Use the new permission utility to handle permissions
        PermissionUtils.launchPhotoPicker(this, RequestCode.RC_CAMERA_AND_STORAGE)
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        // Let the permission utility handle the result
        PermissionUtils.handlePermissionResult(this, requestCode, permissions, grantResults)
    }

    private fun processData(photos: ArrayList<Photo>) {
        navigator.withExtras(
            PhotoManagerActivity.createExtras(
                viewModel?.shipmentId
                    .orEmpty(), viewModel?.batchItem, AttachPhotoType.QA_PHOTO, photos
            )
        )
            .startForResult(RequestCode.PHOTO_MANAGER)
            .toPhotoManager()
    }

}