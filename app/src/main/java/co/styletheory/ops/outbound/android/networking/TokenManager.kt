package co.styletheory.ops.outbound.android.networking

import co.styletheory.android.network.auth.AuthType
import co.styletheory.android.network.auth.TokenHandler
import co.styletheory.android.network.core.APIManager
import co.styletheory.android.network.core.RequestResult
import co.styletheory.android.network.event.NotAuthorizeEvent
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.model.Session
import co.styletheory.ops.outbound.android.resources.RefreshSessionResource
import co.styletheory.ops.outbound.android.util.notNull
import org.greenrobot.eventbus.EventBus

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 24 August 2018.
 * Description
 *
 * <EMAIL>
 */
class TokenManager : TokenHandler {

    val userStorage = UserStorage()
    val eventBus = EventBus.getDefault()

    override fun accessToken(): String {
        userStorage.getUserSession().notNull {
            return it.accessToken
        }
        return ""
    }

    override fun refreshToken(): String {
        userStorage.getUserSession().notNull {
            return it.refreshToken
        }
        return ""
    }

    override var authType: AuthType = AuthType.BEARER

    override fun isTokenExpired(): Boolean {
        userStorage.getUserSession().notNull {
            return it.isTokenExpired()
        }
        return false
    }

    override fun onTokenExpired(apiManager: APIManager) {
        apiManager.refreshToken(
                RefreshSessionResource(userStorage.getUserSession()?.refreshToken.orEmpty()),
                RequestResult<GraphQLData<Session>, String?> {
                    onSuccess {
                        it?.data?.result.notNull { session ->
                            userStorage.setUserSession(session)
                        }
                    }
                    onError { eventBus.post(NotAuthorizeEvent()) }
                }
        )
    }
}