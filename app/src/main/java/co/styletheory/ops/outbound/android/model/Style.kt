package co.styletheory.ops.outbound.android.model

import org.parceler.Parcel

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 05 November 2017.
 * Description
 *
 * <EMAIL>
 */


@Parcel
data class Style(
        val id: String = "",
        val name: String = "",
        val primaryCategory: String = "",
        val galleries: List<Gallery> = emptyList(),
        val designer: Designer? = null,
        val colors: List<Color> = emptyList()
) {
    val gallery get() = galleries.firstOrNull { it.images.isNotEmpty() }?.images ?: listOf()
    val imageUrl get() = gallery.getOrElse(0) { "" }
}