package co.styletheory.ops.outbound.android.feature.qcDetail.viewModel

import co.styletheory.ops.outbound.android.general.adapter.PagerSource
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.listener.OnKeyListener
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.model.enums.SwapType
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 31 October 2017.
 * Description
 *
 * <EMAIL>
 */
interface QcDetailViewModel : ViewModel, PagerSource {
    var batchId: String
    var batchStatus: BatchStatus?
    var shipmentId: String
    var batchItem: BatchItem?
    val scannedRfidText: ObservableString
    val onEditTextKeyListener: OnKeyListener
    var swapItem: SwapItem
    var swapType: SwapType
    var rfid: String
    var failCategory: String
    var failReason: String
    var customerEmail: String

    fun toolbarViewModel(): GeneralToolbarViewModel
    fun batchName(): String
    fun fetchBatchDetail(callback: Result<Void?, String>?)
    fun changeToolbarTitle(title: String)
    fun changeToolbarSubtitle(subtitle: String)
    fun isAllBatchItemShipmentCompletedQA(): Boolean
    fun completeBatch(callback: Result<Void?, String>?)
    fun updateItemToFailed()
}
