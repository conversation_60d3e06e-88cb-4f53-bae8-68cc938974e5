package co.styletheory.ops.outbound.android.util.featureflag

import co.styletheory.ops.outbound.android.general.storage.UserStorage
import com.styletheory.android.helper.FeatureFlagHelper
import org.greenrobot.eventbus.EventBus
import javax.inject.Inject

/**
 * Created by Yoga C. Pranata on 04/04/19.
 * Android Engineer
 */
class FeatureFlag @Inject constructor(val eventBus: EventBus, val userStorage: UserStorage) {

    @Inject
    lateinit var featureFlagHelper: FeatureFlagHelper

    fun isToggleOn(key: String, featureFlagToggleListener: FeatureFlagToggleListener, defaultValue: Boolean = false): Boolean {
        // Todo: Implement toggle change listener from 3rd party here to invoke featureFlagToggleListener
        return featureFlagHelper.getConfigBoolean(key)
    }

    fun turnOffToggleListener(key: String) {
        // Todo: Implement remove toggle listener from 3rd party here
    }
}