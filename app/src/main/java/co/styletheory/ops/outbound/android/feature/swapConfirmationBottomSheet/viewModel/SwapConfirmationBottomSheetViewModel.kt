package co.styletheory.ops.outbound.android.feature.swapConfirmationBottomSheet.viewModel

import androidx.databinding.ObservableInt
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.SwapItem
import com.styletheory.android.mvvm.general.binding.ObservableText

interface SwapConfirmationBottomSheetViewModel : ViewModel {

    var swapType: String
    var swapItem: SwapItem
    var batchId: String

    val itemFoundVisibility: ObservableInt
    val loadingStateVisibility: ObservableInt
    val oldItemImage: ObservableString
    val oldItemName: ObservableString
    val oldItemDesignerName: ObservableString
    val oldItemSizeNote: ObservableString
    val oldItemStyleCategory: ObservableString
    val newItemImage: ObservableString
    val newItemName: ObservableString
    val newItemDesignerName: ObservableString
    val newItemSizeNote: ObservableString
    val newItemStyleCategory: ObservableString
    val swapInformation: ObservableText

    fun onConfirmButtonClick()
    fun onCloseClick()
    fun updateItemToFailed(oldItem: BatchItem)
    fun setItems(swapItem: SwapItem, newItem: BatchItem, swapType: String, batchId: String)
}