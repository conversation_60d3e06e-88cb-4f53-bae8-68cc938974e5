package co.styletheory.ops.outbound.android.feature.packing.viewModel

import co.styletheory.ops.outbound.android.general.adapter.PagerSource
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.listener.OnKeyListener
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 19 December 2017.
 * Description
 *
 * <EMAIL>
 */
interface PackingDetailViewModel : ViewModel, PagerSource {
    val onEditTextKeyListener: OnKeyListener
    val scannedRfidText: ObservableString
    val shipments: List<Shipment>
    var batchId: String
    var batch: Batch?
    var batchStatus: BatchStatus?

    fun generalToolbarViewModel(): GeneralToolbarViewModel
    fun changeToolbarSubtitle(subtitle: String?)
    fun fetchBatchDetail(callback: Result<Void?, String?>?)
    fun isAllBatchItemShipmentCompletedPacking(): Boolean
    fun completeBatch(callback: Result<Void?, String?>?)
    fun checkScannedText()
    fun checkBatchItemStatus(position: Int, rfidText: String)
}
