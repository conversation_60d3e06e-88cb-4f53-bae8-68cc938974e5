package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import retrofit2.Call
import retrofit2.Retrofit

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 02 March 2018.
 * Description
 *
 * <EMAIL>
 */
class SignOutResource : GraphQLResource<GraphQLData<Boolean>>() {

    override fun graphQLRequest(): GraphQLRequest = GraphQLRequestList.LOGOUT

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<Boolean>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.logout(baseUrl, createRequestBody()))
    }
}