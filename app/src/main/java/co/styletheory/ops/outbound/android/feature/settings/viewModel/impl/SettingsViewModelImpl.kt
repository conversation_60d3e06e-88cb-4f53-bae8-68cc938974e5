package co.styletheory.ops.outbound.android.feature.settings.viewModel.impl

import android.view.View
import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableInt
import co.styletheory.android.network.core.RequestResult
import co.styletheory.ops.outbound.android.BuildConfig
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.settings.event.SettingsNetworkEvent
import co.styletheory.ops.outbound.android.feature.settings.event.SettingsUIEvent
import co.styletheory.ops.outbound.android.feature.settings.viewModel.SettingsViewModel
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.model.BatchConfig
import co.styletheory.ops.outbound.android.model.BatchGenerationTime
import co.styletheory.ops.outbound.android.model.Region
import co.styletheory.ops.outbound.android.model.VerticalType
import co.styletheory.ops.outbound.android.model.enums.Barcode
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.BatchConfigsResource
import co.styletheory.ops.outbound.android.resources.GenerateBatchListResource
import co.styletheory.ops.outbound.android.util.*
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel
import java.util.*
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 05 June 2018.
 * Description
 *
 * <EMAIL>
 */
class SettingsViewModelImpl @Inject constructor() : BaseViewModel<SettingsViewModelImpl>(), SettingsViewModel {

    @Inject
    lateinit var toolbarViewModel: GeneralToolbarViewModel
    @Inject
    lateinit var userStorage: UserStorage
    @Inject
    lateinit var dataService: DataService
    @Inject
    lateinit var errorResponse: ErrorResponse

    override val shimmerVisibility = ObservableInt(View.VISIBLE)
    override val sectionVisibility = ObservableInt(View.GONE)

    init {
        viewModelClass = SettingsViewModelImpl::class
    }

    override val isGeneratePage: ObservableBoolean = ObservableBoolean(false)
    override val selectedCalendar: Calendar = Calendar.getInstance()
    override val barcodeApparelRadioButton = ObservableInt(R.id.radioBarcodeApparelOff)
    override val barcodeBagsRadioButton = ObservableInt(R.id.radioBarcodeBagsOff)
    override val btnGenerateBatchVisibility = ObservableInt(View.GONE)
    override val barcodeApparelVisibility = ObservableInt(View.GONE)
    override val barcodeBagsVisibility = ObservableInt(View.GONE)
    override val batchDate: ObservableString = ObservableString()
    override val regionList: ArrayList<Region> = arrayListOf()
    override val verticalTypeList: ArrayList<VerticalType> = arrayListOf()
    override val batchGenerationTimeList: ArrayList<BatchGenerationTime> = arrayListOf()
    override val regionConfigViewModelList = ObservableArrayList<BatchConfigItemViewModel>()
    override val verticalTypeConfigViewModelList = ObservableArrayList<BatchConfigItemViewModel>()
    override val settingsPageVisibility: ObservableInt = ObservableInt(View.VISIBLE)
    override val outboundAppVersion = ObservableString()

    override fun toolbarViewModel(): GeneralToolbarViewModel = toolbarViewModel

    override fun initializeViewModel() {
        barcodeApparelRadioButton.set(getBarcodeApparelStatus())
        barcodeBagsRadioButton.set(getBarcodeBagsStatus())
        batchDate.set(DateUtil.createDateStringFrom(userStorage.getBatchDate().time, DateConstant.PATTERN.ddMMMyyyy))
        setupVersionAppTitle(!BuildConfig.BUILD_TYPE.equals(AppConstant.BUILD_TYPE_RELEASE, true))
    }

    override fun onRadioButtonChecked(inputString: String) {
        when(inputString) {
            Barcode.APPAREL_ON.value -> {
                userStorage.setSettingBarcodeApparel(true)
                barcodeApparelRadioButton.set(getBarcodeApparelStatus())
            }
            Barcode.APPAREL_OFF.value -> {
                userStorage.setSettingBarcodeApparel(false)
                barcodeApparelRadioButton.set(getBarcodeApparelStatus())
            }
            Barcode.BAGS_ON.value -> {
                userStorage.setSettingBarcodeBags(true)
                barcodeBagsRadioButton.set(getBarcodeBagsStatus())
            }
            Barcode.BAGS_OFF.value -> {
                userStorage.setSettingBarcodeBags(false)
                barcodeBagsRadioButton.set(getBarcodeBagsStatus())
            }
        }
    }

    override fun onBtnGenerateBatchClicked() {
        findNearestHourGenerationTime()
    }

    override fun batchDateClicked() {
        eventBus.post(SettingsUIEvent.OnBatchDateClicked)
    }

    override fun setupBtnGenerateBatchVisibility() {
        if(userStorage.isUserOperationManager()) {
            btnGenerateBatchVisibility.set(View.VISIBLE)
        } else {
            btnGenerateBatchVisibility.set(View.GONE)
        }
    }

    override fun generateBatch(timeGeneration: String) {
        val batchTimeGeneration = timeGeneration.convertDateStringToTimeStamp()
        dataService.generateBatchList(
                GenerateBatchListResource(batchTimeGeneration),
                RequestResult {
                    onSuccess { eventBus.post(SettingsUIEvent.OnBatchIsGenerated(true)) }
                    onError {
                        eventBus.post(SettingsUIEvent.OnBatchIsGenerated(false, errorResponse.getErrorBodyDescription(it)))
                    }
                }
        )
    }

    override fun fetchBatchConfigs() {
        dataService.fetchBatchConfigs(
                BatchConfigsResource(),
                RequestResult {
                    onSuccess {
                        if(it?.data?.result?.isNotEmpty() == true) {
                            processBatchConfigs(it.data.result)
                            eventBus.post(SettingsNetworkEvent.OnSuccessFetchBatchConfigs())
                        } else {
                            eventBus.post(SettingsNetworkEvent.OnErrorFetchBatchConfigs(string(R.string.settings_failed_fetch_batch_config_content)))
                        }
                    }
                    onError { eventBus.post(SettingsNetworkEvent.OnErrorFetchBatchConfigs(string(R.string.settings_failed_fetch_batch_config_content))) }
                }
        )
    }

    override fun processBatchConfigs(configList: List<BatchConfig.Result>) {
        val regions = configList.sortedBy { sortBy -> sortBy.regionOutboundOrder }
        val verticals = configList.sortedBy { sortBy -> sortBy.verticalTypeOutboundOrder }

        for(configs in regions) {
            configs.convertToRegionModel(regionList, userStorage.getUserLogin()?.roles)
            regionConfigViewModelList.bindRegionToViewModel(regionList)
        }

        for(configs in verticals) {
            configs.convertToBatchGenerationTimeModel(batchGenerationTimeList)
            configs.convertToVerticalModel(verticalTypeList)
            verticalTypeConfigViewModelList.bindVerticalToViewModel(verticalTypeList)
        }
    }

    override fun saveCheckedRegionByName(region: String) {
        val regionData = regionList.firstOrNull { it.name?.contains(region, ignoreCase = true) == true }
        regionData.notNull { userStorage.setUserRegion(it) }
    }

    override fun saveCheckedVerticalTypeByName(verticalType: String) {
        val verticalData = verticalTypeList.firstOrNull { it.name?.contains(verticalType, ignoreCase = true) == true }
        verticalData.notNull { userStorage.setUserVerticalType(it) }
    }

    override fun getBarcodeApparelStatus(): Int {
        return if(userStorage.isBarcodeApparelFlagOn()) {
            R.id.radioBarcodeApparelOn
        } else {
            R.id.radioBarcodeApparelOff
        }
    }

    override fun getBarcodeBagsStatus(): Int {
        return if(userStorage.isBarcodeBagsFlagOn()) {
            R.id.radioBarcodeBagsOn
        } else {
            R.id.radioBarcodeBagsOff
        }
    }

    override fun showAllSection() {
        shimmerVisibility.set(View.GONE)
        sectionVisibility.set(View.VISIBLE)
        setupBtnGenerateBatchVisibility()
    }

    override fun findNearestHourGenerationTime() {
        val sortedGenerationTime = batchGenerationTimeList.sortBatchGenerationTime(
                userStorage.getUserVerticalType().code, userStorage.getUserRegion().id
        )
        val batchGenerationTime = sortedGenerationTime.getBatchGenerationTime()
        if(batchGenerationTime != null) {
            val generationTime = batchGenerationTime.convertToFormattedGenerationTime()
            eventBus.post(SettingsUIEvent.OnBtnGenerateBatchClicked(userStorage.getUserRegion().name, userStorage.getUserVerticalType().name, generationTime))
        } else {
            eventBus.post(SettingsUIEvent.OnNullGenerationTime(
                    context.getString(R.string.settings_batch_config_not_active).format("${userStorage.getUserRegion().name} - ${userStorage.getUserVerticalType().name}")
            ))
        }
    }

    override fun clearRegionSelection() {
        for(it in regionConfigViewModelList) {
            it.isChecked.set(false)
        }
    }

    override fun clearVerticalSelection() {
        for(it in verticalTypeConfigViewModelList) {
            it.isChecked.set(false)
        }
    }

    override fun setSelectedRegion(selectedRegion: String) {
        clearRegionSelection()
        val regionConfigs = regionConfigViewModelList.find { it.name.get() == selectedRegion }

        if(regionConfigs != null) {
            regionConfigs.isChecked.set(true)
            saveCheckedRegionByName(regionConfigs.name.get())
        } else {
            regionConfigViewModelList.first().apply {
                isChecked.set(true)
                saveCheckedRegionByName(name.get())
            }
        }
    }

    override fun setSelectedVertical(selectedVertical: String) {
        clearVerticalSelection()
        val verticalConfigs = verticalTypeConfigViewModelList.find { it.name.get() == selectedVertical }

        if(verticalConfigs != null) {
            verticalConfigs.isChecked.set(true)
            saveCheckedVerticalTypeByName(verticalConfigs.name.get())
        } else {
            verticalTypeConfigViewModelList.first().apply {
                isChecked.set(true)
                saveCheckedVerticalTypeByName(name.get())
            }
        }
    }

    override fun setBatchDate(year: Int, monthOfYear: Int, dayOfMonth: Int) {
        selectedCalendar.set(Calendar.YEAR, year)
        selectedCalendar.set(Calendar.MONTH, monthOfYear)
        selectedCalendar.set(Calendar.DAY_OF_MONTH, dayOfMonth)
        batchDate.set(DateUtil.createDateStringFrom(selectedCalendar.time, DateConstant.PATTERN.ddMMMMyyyy))
        userStorage.setBatchDate(selectedCalendar)
    }

    override fun onBtnApplyClicked() {
        eventBus.post(SettingsUIEvent.OnApplyFilterClicked)
    }

    override fun showGeneratePage() {
        isGeneratePage.set(true)
        barcodeApparelVisibility.set(View.GONE)
        barcodeBagsVisibility.set(View.GONE)
        settingsPageVisibility.set(View.GONE)
        setupBtnGenerateBatchVisibility()
    }

    override fun showSettingsPage() {
        isGeneratePage.set(false)
        settingsPageVisibility.set(View.VISIBLE)
        btnGenerateBatchVisibility.set(View.GONE)
        barcodeApparelVisibility.set(featureFlagUtil.barcodeApparelVisibility.get())
        barcodeBagsVisibility.set(featureFlagUtil.barcodeBagsVisibility.get())
    }

    fun setupVersionAppTitle(isDebugVersion: Boolean) {
        if(isDebugVersion) {
            outboundAppVersion.set(string(R.string.settings_app_version_debug).format(BuildConfig.VERSION_NAME, BuildConfig.BUILD_TYPE))
        } else {
            outboundAppVersion.set(string(R.string.settings_app_version_release).format(BuildConfig.VERSION_NAME))
        }
    }
}
