package co.styletheory.ops.outbound.android.general.binding

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.os.Handler
import android.os.Looper
import android.text.method.LinkMovementMethod
import android.util.Patterns
import android.view.KeyEvent
import android.view.View
import android.widget.*
import androidx.core.content.ContextCompat
import androidx.databinding.*
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.general.StartSnapHelper
import co.styletheory.ops.outbound.android.general.adapter.impl.RecyclerViewAdapterImpl
import co.styletheory.ops.outbound.android.general.adapter.impl.RecyclerViewHolder
import co.styletheory.ops.outbound.android.general.adapter.impl.TextWatcherAdapter
import co.styletheory.ops.outbound.android.general.adapter.impl.ViewAdapter
import co.styletheory.ops.outbound.android.general.listener.AlmostReachBottomListener
import co.styletheory.ops.outbound.android.general.listener.BottomScreenOnScrollListener
import co.styletheory.ops.outbound.android.general.listener.OnKeyListener
import co.styletheory.ops.outbound.android.general.listener.RecyclerViewInterceptorListener
import co.styletheory.ops.outbound.android.general.recyclerView.RecyclerViewSetting
import co.styletheory.ops.outbound.android.util.setSpanTexts
import co.styletheory.ops.outbound.android.util.toHtmlText
import co.styletheory.ops.outbound.android.view.AdapterFlowLayout
import co.styletheory.ops.outbound.android.view.AdapterLinearLayout
import co.styletheory.ops.outbound.android.view.AdapterRadioGroupLayout
import co.styletheory.ops.outbound.android.view.ScrollableLinearLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.CenterInside
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.styletheory.android.mvvm.general.binding.ObservableText
import com.viven.imagezoom.ImageZoomHelper
import java.io.File


/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 21 October 2017.
 * Description
 *
 * <EMAIL>
 */

object CustomBinding {

    @JvmStatic
    @BindingAdapter("android:imageUrl")
    fun imageUrl(view: ImageView, url: String) {
        Glide.with(view)
            .load(url)
            .fitCenter()
            .placeholder(android.R.drawable.gallery_thumb)
            .listener(object : RequestListener<Drawable> {
                override fun onResourceReady(
                    resource: Drawable,
                    model: Any,
                    target: Target<Drawable>,
                    dataSource: DataSource,
                    isFirstResource: Boolean
                ): Boolean {
                    view.setImageDrawable(resource)
                    return false
                }

                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Drawable>,
                    isFirstResource: Boolean
                ): Boolean {
                    view.setImageResource(android.R.drawable.gallery_thumb)
                    return false
                }
            })
            .into(view)
    }

    @JvmStatic
    @BindingAdapter("android:imageUrlRoundCorner")
    fun imageUrlRoundCorner(view: ImageView, url: String?) {
        Glide.with(view)
            .load(url)
            .placeholder(android.R.drawable.gallery_thumb)
            .transform(CenterInside(), RoundedCorners(8))
            .listener(object : RequestListener<Drawable> {
                override fun onResourceReady(
                    resource: Drawable,
                    model: Any,
                    target: Target<Drawable>,
                    dataSource: DataSource,
                    isFirstResource: Boolean
                ): Boolean {
                    view.setImageDrawable(resource)
                    return false
                }

                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Drawable>,
                    isFirstResource: Boolean
                ): Boolean {
                    if (!url.isNullOrEmpty()) {
                        Handler(Looper.getMainLooper()).post { localImageUrl(view, url) }
                    } else {
                        view.setImageResource(android.R.drawable.gallery_thumb)
                    }
                    return false
                }
            })
            .into(view)
    }

    @JvmStatic
    @BindingAdapter("android:srcBinding")
    fun imageViewBinding(view: ImageView, resource: ObservableInt?) {
        resource?.let {
            view.setImageResource(it.get())
        }
    }

    @JvmStatic
    @BindingAdapter("android:srcDrawable")
    fun imageViewDrawableBinding(view: ImageView, resource: Int?) {
        resource?.let {
            view.setImageResource(it)
        }
    }

    @JvmStatic
    @BindingAdapter("enableZoomImage")
    fun enableZoomImage(view: ImageView, zoom: Boolean) {
        if(zoom) {
            ImageZoomHelper.setViewZoomable(view)
        }
    }

    @JvmStatic
    @BindingAdapter("android:renderColor")
    fun renderColor(view: ImageView, color: ObservableString) {
        if(Patterns.WEB_URL.matcher(color.get()).matches())
            Glide.with(view).load(color).fitCenter().into(view)
        else
            view.setImageDrawable(ColorDrawable(Color.parseColor(color.get())))
    }

    @JvmStatic
    @BindingAdapter("android:centerImage")
    fun centerImage(view: ImageView, url: String) {
        Glide.with(view).load(url).centerCrop().into(view)
    }

    @JvmStatic
    @BindingAdapter("android:localImageUrl")
    fun localImageUrl(view: ImageView, url: String) {
        Glide.with(view).load(File("file:" + url)).fitCenter().centerCrop().into(view)
    }

    @JvmStatic
    @BindingAdapter("android:noFitSmallImageUrl")
    fun noFitSmallImageUrl(view: ImageView, url: String) {
        Glide.with(view).load(url)
                .fitCenter()
                .centerCrop()
                .placeholder(android.R.drawable.gallery_thumb)
                .override(400, 200)
                .into(view) //TODO: Think how to change the resize number dimens in dp.
    }

    @JvmStatic
    @BindingAdapter(value = ["adapterFlowLayoutItems", "adapterFlowLayoutId"], requireAll = true)
    fun flowLayoutAdapter(view: AdapterFlowLayout, items: ObservableArrayList<*>, layoutId: Int) {
        val adapter = ViewAdapter()
        adapter.items = items
        adapter.layoutId = layoutId
        view.setAdapter(adapter)
    }

    @JvmStatic
    @BindingAdapter(value = ["adapterLinearLayoutItems", "adapterLinearLayoutId"], requireAll = true)
    fun linearLayoutAdapter(view: AdapterLinearLayout, items: ObservableArrayList<*>, layoutId: Int) {
        val adapter = ViewAdapter()
        adapter.items = items
        adapter.layoutId = layoutId
        view.setAdapter(adapter)
    }

    @Suppress("UNCHECKED_CAST")
    @JvmStatic
    @BindingAdapter("editTextBinding")
    fun bindEditText(view: EditText, observableField: ObservableString) {
        val pair = view.getTag(R.id.bound_observable) as? Pair<ObservableString, TextWatcherAdapter>
        if(pair == null || pair.first != observableField) {
            if(pair != null) {
                view.removeTextChangedListener(pair.second)
            }
            val watcher = object : TextWatcherAdapter() {
                override fun onTextChanged(char: CharSequence, start: Int, before: Int, count: Int) {
                    observableField.set(char.toString())
                }
            }
            view.setTag(R.id.bound_observable, Pair(observableField, watcher))
            view.addTextChangedListener(watcher)
        }
        val newValue = observableField.get()
        if(view.text.toString() != newValue) {
            view.setText(newValue)
        }
    }

    @Suppress("unused", "RedundantVisibilityModifier")
    @JvmStatic
    @BindingConversion
    public fun convertObservableToString(observableString: ObservableString?): String {
        return observableString?.get().orEmpty()
    }

    @JvmStatic
    @BindingAdapter("android:textStringResource")
    fun textStringResource(view: TextView, stringRes: ObservableInt) {
        view.setText(stringRes.get())
    }

    @JvmStatic
    @BindingAdapter("android:onEditTextKeyListener")
    fun onEditTextKeyListener(view: EditText, listener: OnKeyListener) {
        view.setOnKeyListener { _, keyCode, keyEvent ->
            if(keyEvent.action == KeyEvent.ACTION_UP) {
                when(keyCode) {
                    KeyEvent.KEYCODE_ENTER -> {
                        listener.onKeyTapped()
                        return@setOnKeyListener true
                    }
                }
            }
            return@setOnKeyListener false
        }
    }


    @JvmStatic
    @androidx.databinding.BindingAdapter("android:textBinding")
    fun textBinding(view: TextView, resource: ObservableText) {
        val formatArgs = mutableListOf<String>()
        with(resource) {
            for(it in textArgs) {
                if(it is Int) {
                    formatArgs.add(view.context.getString(it))
                } else {
                    formatArgs.add(it.toString())
                }
            }

            val value = get()
            var stringText = ""
            if(value is Int) {
                stringText = if(quantity != null) {
                    view.context.resources.getQuantityString(value, quantity ?: 0)
                } else {
                    view.context.getString(value)
                }
            } else if(value is String) {
                stringText = value
            }
            if(formatArgs.isNotEmpty()) stringText = String.format(stringText, *formatArgs.toTypedArray())
            view.text = if(isHtml) stringText.toHtmlText() else stringText
            if(spanTexts.isNotEmpty()) view.setSpanTexts(spanTexts)
            if(isWithLinkMovementMethod) view.movementMethod = LinkMovementMethod.getInstance()
        }
    }

    @JvmStatic
    @androidx.databinding.BindingAdapter("android:checkedBinding")
    fun radioGroup(view: RadioGroup, checkedState: ObservableInt) {
        view.setOnCheckedChangeListener { group, _ ->
            val radioButton = group.findViewById<RadioButton>(group.checkedRadioButtonId)
            checkedState.set(radioButton.id)
        }
        view.check(checkedState.get())
    }

    @JvmStatic
    @BindingAdapter("android:bindChecked")
    fun bindChecked(view: CompoundButton, checked: ObservableChecked) {
        view.isChecked = checked.get()
        view.setOnCheckedChangeListener { _, isChecked ->
            checked.set(isChecked)
            checked.listener?.onCheckedChange(checked, isChecked)
        }
    }

    @JvmStatic
    @BindingAdapter(value = ["android:radioGroupItems", "android:radioGroupLayoutId"], requireAll = true)
    fun radioGroupAdapter(view: AdapterRadioGroupLayout, items: ObservableArrayList<*>, layoutId: Int) {
        val adapter = ViewAdapter()
        adapter.items = items
        adapter.layoutId = layoutId
        view.setAdapter(adapter)
    }

    @JvmStatic
    @BindingAdapter("android:backgroundResource")
    fun backgroundResource(view: View, resource: ObservableInt) {
        view.background = ContextCompat.getDrawable(view.context, resource.get())
    }

    @Suppress("UNCHECKED_CAST", "ComplexMethod")
    @JvmStatic
    @BindingAdapter(value = ["android:viewSetting", "android:items", "android:addLoadMore"], requireAll = false)
    fun setViewSetting(view: RecyclerView, setting: RecyclerViewSetting, items: ObservableArrayList<*>, listener: AlmostReachBottomListener?) {
        if(setting.isAttachToSnap) StartSnapHelper().attachToRecyclerView(view)

        val layoutManager: LinearLayoutManager = when(setting.layoutManagerType) {
            RecyclerViewSetting.LayoutManagerType.SCROLLABLE -> ScrollableLinearLayoutManager(view.context, setting.orientation, false)
            RecyclerViewSetting.LayoutManagerType.LINEAR -> LinearLayoutManager(view.context, setting.orientation, false)
            RecyclerViewSetting.LayoutManagerType.GRID -> GridLayoutManager(view.context, setting.column)
            else -> LinearLayoutManager(view.context, setting.orientation, false)
        }

        if(setting.isItemPrefetchCountActive) layoutManager.initialPrefetchItemCount = items.size + 1

        val adapter = RecyclerViewAdapterImpl()
        adapter.layoutId = setting.layoutId
        adapter.items = items as MutableList<Any>

        setting.interceptor?.let {
            adapter.interceptor = object : RecyclerViewInterceptorListener {
                override fun onBindView(holder: RecyclerViewHolder) {
                    it.setHolder(holder)
                }
            }
        }

        setting.isNestedScrolling?.let {
            view.isNestedScrollingEnabled = it
        }

        setting.isAttachToFastScroller?.let {
            adapter.isAttachToFastScroller = it
        }

        view.layoutManager = layoutManager
        view.itemAnimator = DefaultItemAnimator()
        view.adapter = adapter

        if(listener != null) {
            val onScrollListener = object : BottomScreenOnScrollListener() {
                override fun almostReachBottom() {
                    listener.almostReachBottom()
                }
            }
            onScrollListener.setLayoutManager(layoutManager)
            view.addOnScrollListener(onScrollListener)
        }
    }

    @JvmStatic
    @BindingAdapter("android:selected")
    fun setSelected(view: View, selected: ObservableBoolean) {
        view.isSelected = selected.get()
    }

    @JvmStatic
    @BindingAdapter("android:textColorResource")
    fun textColorResource(view: TextView, resource: ObservableInt) {
        view.setTextColor(ContextCompat.getColor(view.context, resource.get()))
        view.hintTextColors
    }

}