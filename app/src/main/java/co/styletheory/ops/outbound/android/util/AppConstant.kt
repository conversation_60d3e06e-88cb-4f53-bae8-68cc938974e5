package co.styletheory.ops.outbound.android.util

import co.styletheory.ops.outbound.android.BuildConfig

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 07 November 2017.
 * Description
 *
 * <EMAIL>
 */
object AppConstant {
    var BASE_URL = BuildConfig.BASE_URL
    const val REGION = "sg"
    const val BUILD_TYPE_RELEASE = "release"
    const val GENERATE_BATCH = "generateBatch"
    const val OUTBOUND_APP_NAME = "outbound"

    object CONFIG_TYPE {
        const val REGION = "region"
        const val REGION_BUTTON_ID = 10
        const val VERTICAL = "vertical"
        const val VERTICAL_BUTTON_ID = 20
    }

    object MAIN_TAB {
        const val PICKING_TAB = "Picking"
        const val QC_TAB = "QC"
        const val PHOTO_TAB = "Photo"
        const val PACKING_TAB = "Packing"
        const val PACKED_TAB = "Packed"
    }

    object DIALOG_TAG {
        const val BATCH_DATE_DIALOG = "BatchDatePicker"
    }

    object LOGISTIC_PROVIDER {

        object INDONESIA {
            const val GOJEK = "Gojek"
            const val GOSEND = "go-send"
            const val PAXEL = "paxel"
            const val SICEPAT = "sicepat"
            const val NINJAVAN = "ninjavan"
            const val SELF_COLLECT_ID = "self_collect_id"
        }

        object SINGAPORE {
            const val SINGPOST = "singpost"
            const val HONESTBEE = "honestbee"
            const val ZYLLEM = "zyllem"
            const val VERSAFLEET = "versafleet"
            const val PICKUPP = "pickupp"
            const val DHL = "dhl"
            const val SELF_COLLECT_SG = "self_collect_sg"
            const val STYLETHEORY_SG = "styletheory_sg"
            const val PIING = "piing"
        }

    }

    object ERROR_OUTBOUND_ACCURACY_SWAP {
        const val ITEM_STATUS_NOT_ALLOWED = "OUT-001"
        const val DIFFERENT_STYLE_AND_SIZE = "OUT-1002"
        const val CANNOT_SCAN_PART_ITEM = "OUT-1003"
        const val ITEM_BYRFID_NOT_FOUND = "OUT-1004"
        const val NO_NETWORK = "No network available, please check your WiFi or Data connection"
        const val ERROR_NOT_FOUND_ITEM = "Unexpected error value: { status: 400, code: \"OUT-1004\", description: \"Oops, no other available items found. Please contact Customer Experience to arrange a new item for the customer.\" }"
    }
}