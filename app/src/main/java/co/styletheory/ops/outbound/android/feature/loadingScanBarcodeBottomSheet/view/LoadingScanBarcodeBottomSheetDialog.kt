package co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.view

import android.os.Bundle
import android.os.Parcelable
import android.view.View
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.BottomsheetScanBarcodeBinding
import co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.event.LoadingScanBarcodeBottomSheetNetworkEvent
import co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.event.LoadingScanBarcodeBottomSheetUIEvent
import co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.viewModel.LoadingScanBarcodeBottomSheetViewModel
import co.styletheory.ops.outbound.android.general.base.BaseBottomSheetDialog
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.model.enums.SwapType
import co.styletheory.ops.outbound.android.util.IntentConstant
import co.styletheory.ops.outbound.android.util.notNull
import org.greenrobot.eventbus.Subscribe
import org.parceler.Parcels

/**
 * Created by Eminarti Sianturi on 22/06/20.
 */
@Suppress("UNUSED_PARAMETER", "unused")
class LoadingScanBarcodeBottomSheetDialog : BaseBottomSheetDialog<BottomsheetScanBarcodeBinding, LoadingScanBarcodeBottomSheetViewModel>() {

    companion object {
        fun newInstance(swapItem: SwapItem, newItemUuid: String?, swapType: SwapType): LoadingScanBarcodeBottomSheetDialog {
            val dialog = LoadingScanBarcodeBottomSheetDialog()
            val bundle = Bundle()
            bundle.putParcelable(IntentConstant.SWAP_ITEM, Parcels.wrap(swapItem))
            bundle.putString(IntentConstant.SWAP_TYPE, swapType.swapType)
            bundle.putString(IntentConstant.NEW_ITEM_UUID, newItemUuid)
            dialog.arguments = bundle
            return dialog
        }

        fun newInstance(batchId: String?, swapItem: SwapItem?, swapType: SwapType): LoadingScanBarcodeBottomSheetDialog {
            val dialog = LoadingScanBarcodeBottomSheetDialog()
            val bundle = Bundle()
            bundle.putString(IntentConstant.BATCH_ID, batchId)
            bundle.putParcelable(IntentConstant.SWAP_ITEM, Parcels.wrap(swapItem))
            bundle.putString(IntentConstant.SWAP_TYPE, swapType.swapType)
            dialog.arguments = bundle
            return dialog
        }
    }

    override fun getLayoutId(): Int = R.layout.bottomsheet_loading_swap_item

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if(viewModel == null) {
            fragmentComponent?.inject(this)
        }
    }

    override fun onLoadBottomSheetDialog(view: View, saveInstance: Bundle?) {
        isCancelable = false
        initArguments()
    }

    private fun initArguments() {
        arguments?.getParcelable<Parcelable>(IntentConstant.SWAP_ITEM).notNull {
            val item = Parcels.unwrap<SwapItem>(it)
            viewModel?.swapItem = item
        }

        if(arguments?.containsKey(IntentConstant.NEW_ITEM_UUID) == true) {
            viewModel?.newItemUuid = arguments?.getString(IntentConstant.NEW_ITEM_UUID).orEmpty()
        }

        if(arguments?.containsKey(IntentConstant.SWAP_TYPE) == true) {
            viewModel?.swapType = arguments?.getString(IntentConstant.SWAP_TYPE) ?: SwapType.QUALITY_SWAP.swapType
        }

        if(arguments?.containsKey(IntentConstant.BATCH_ID) == true) {
            viewModel?.batchId = arguments?.getString(IntentConstant.BATCH_ID).orEmpty()
        }

        viewModel?.setupBottomSheetContent()
        viewModel?.checkSwapItem()
    }

    @Subscribe
    fun onCloseClicked(event: LoadingScanBarcodeBottomSheetUIEvent.OnCloseClicked) {
        dismiss()
    }

    @Subscribe
    fun onUpdateItemQAFailed(event: LoadingScanBarcodeBottomSheetUIEvent.OnUpdateItemQAFailed) {
        dismiss()
    }

    @Subscribe
    fun onErrorSwapItem(event: LoadingScanBarcodeBottomSheetUIEvent.OnErrorSwapItem) {
        dismiss()
    }

    @Subscribe
    fun onSwapItemFound(event: LoadingScanBarcodeBottomSheetNetworkEvent.OnSwapItemFound) {
        dismiss()
    }

}