package co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.viewModel

import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.SwapItem
import com.styletheory.android.mvvm.general.binding.ObservableText

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 22/06/20.
 */
interface LoadingScanBarcodeBottomSheetViewModel : ViewModel {

    var batchId: String
    var swapItem: SwapItem
    var newItemUuid: String
    var swapType: String
    val bottomSheetTitle: ObservableText
    val bottomSheetContent: ObservableText

    fun onClose()
    fun checkSwapItem()
    fun setupBottomSheetContent()
}