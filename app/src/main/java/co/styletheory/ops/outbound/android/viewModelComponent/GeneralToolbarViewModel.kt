package co.styletheory.ops.outbound.android.viewModelComponent

import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import javax.inject.Inject


class GeneralToolbarViewModel @Inject constructor() : BaseViewModel<GeneralToolbarViewModel>() {
    init {
        viewModelClass = GeneralToolbarViewModel::class
    }
    val title = ObservableString()
    val subtitle = ObservableString()
}
