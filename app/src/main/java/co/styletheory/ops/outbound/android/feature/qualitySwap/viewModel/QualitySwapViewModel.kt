package co.styletheory.ops.outbound.android.feature.qualitySwap.viewModel

import androidx.databinding.ObservableInt
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.viewModelComponent.FooterButtonViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel
import com.styletheory.android.mvvm.general.binding.ObservableText

/**
 * Created by Yoga C. Pranata on 18/06/20.
 * Android Engineer
 */
interface QualitySwapViewModel : ViewModel {
    var batchId: String
    var shipmentId: String
    var swapItem: SwapItem
    var batchStatus: BatchStatus?
    val selectedFailReason: ObservableString
    var photos: List<String>
    val inputNotes: ObservableString
    val infoVisibility: ObservableInt

    fun toolbarViewModel(): GeneralToolbarViewModel
    fun footerButtonViewModel(): FooterButtonViewModel
    fun selectedSwapReasonPosition(selectedPosition: Int)
    fun uploadPhotoClick()
    fun checkRequiredFields()
    fun getFailReasonList(): Array<String>
    fun clearAllFields()
    fun setupInfoVisibility()
}