package co.styletheory.ops.outbound.android.model

import com.google.gson.annotations.SerializedName
import org.joda.time.DateTime
import org.parceler.Parcel

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 09 November 2017.
 * Description
 *
 * <EMAIL>
 */

@Parcel
data class EstimateTime(
        @SerializedName("start")
        val startDateTime: DateTime? = null,
        @SerializedName("end")
        val endDateTime: DateTime? = null
)