package co.styletheory.ops.outbound.android.injection.component

import co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel.impl.BacklogDetailItemViewModelImpl
import co.styletheory.ops.outbound.android.feature.batch.viewModel.BatchListViewModel
import co.styletheory.ops.outbound.android.feature.batch.viewModel.BatchStepViewModel
import co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.impl.ItemBoxRewardSectionViewModelImpl
import co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel.impl.ItemLogisticProviderNameViewModelImpl
import co.styletheory.ops.outbound.android.feature.packed.viewModel.impl.PackedItemViewModel
import co.styletheory.ops.outbound.android.feature.packing.viewModel.impl.PackingItemViewModelImpl
import co.styletheory.ops.outbound.android.feature.photoDetail.viewModel.impl.PhotoItemViewModel
import co.styletheory.ops.outbound.android.feature.photoManager.viewModel.AttachImageViewModel
import co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.impl.FailNotesDialogViewModel
import co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.impl.QcPickedViewModel
import co.styletheory.ops.outbound.android.feature.settings.viewModel.impl.BatchConfigItemViewModel
import co.styletheory.ops.outbound.android.feature.swap.SwapItemDialogViewModel
import co.styletheory.ops.outbound.android.general.binding.ObservableFeatureFlagBoolean
import co.styletheory.ops.outbound.android.general.binding.ObservableFeatureFlagVisibility
import co.styletheory.ops.outbound.android.injection.module.DirectInjectModule
import co.styletheory.ops.outbound.android.injection.scope.PerInjectedViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.*
import dagger.Component

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 29 October 2017.
 * Description
 *
 * <EMAIL>
 */

@PerInjectedViewModel
@Component(dependencies = [AppComponent::class], modules = [DirectInjectModule::class])
interface DirectInjectComponent {

    fun inject(viewModel: ColorItemViewModel)
    fun inject(viewModel: AttachImageViewModel)
    fun inject(viewModel: LabelBoxViewModel)
    fun inject(viewModel: BatchStepViewModel)
    fun inject(viewModel: BatchListViewModel)
    fun inject(viewModel: SwapItemDialogViewModel)
    fun inject(viewModel: QcPickedViewModel)
    fun inject(viewModel: ProcessDialogViewModel)
    fun inject(viewModel: FailNotesDialogViewModel)
    fun inject(viewModel: GeneralPhotoGridItemViewModel)
    fun inject(viewModel: PhotoItemViewModel)
    fun inject(viewModel: PackedItemViewModel)
    fun inject(viewModel: AlertDialogViewModel)
    fun inject(viewModel: BatchConfigItemViewModel)
    fun inject(viewModel: BacklogDetailItemViewModelImpl)
    fun inject(viewModel: PackingItemViewModelImpl)
    fun inject(viewModel: ServiceMethodWidgetViewModel)
    fun inject(viewModel: ItemBoxRewardSectionViewModelImpl)
    fun inject(viewModel: ItemLogisticProviderNameViewModelImpl)
    fun inject(observableFeatureFlagBoolean: ObservableFeatureFlagBoolean)
    fun inject(observableFeatureFlagVisibility: ObservableFeatureFlagVisibility)
}