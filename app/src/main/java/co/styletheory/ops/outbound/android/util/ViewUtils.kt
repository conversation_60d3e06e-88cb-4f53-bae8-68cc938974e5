package co.styletheory.ops.outbound.android.util

import android.app.Activity
import android.content.res.Resources
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.util.DisplayMetrics
import android.util.TypedValue

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 24 October 2017.
 * Description
 *
 * <EMAIL>
 */
class ViewUtils {
    companion object {
        @JvmStatic
        fun createShapeDrawable(backgroundColor: Int = Color.WHITE, borderColor: Int = Color.WHITE, shapeRadius: Float = 8f): Drawable {
            val shape = GradientDrawable()
            shape.shape = GradientDrawable.RECTANGLE
            shape.cornerRadii = floatArrayOf(shapeRadius, shapeRadius, shapeRadius, shapeRadius, shapeRadius, shapeRadius, shapeRadius, shapeRadius)
            shape.setColor(backgroundColor)
            shape.setStroke(3, borderColor)
            return shape
        }

        @JvmStatic
        fun dpToPixel(resource: Resources, dp: Float): Int {
            return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, resource.displayMetrics).toInt()
        }

        @Suppress("DEPRECATION")
        fun getDisplay(activity: Activity) = if(OSUtil().isAboveAndroidR()) {
            activity.display
        } else {
            activity.windowManager.defaultDisplay
        }

        @Suppress("DEPRECATION")
        private fun getDisplayMetrics(activity: Activity) = DisplayMetrics().apply {
            if(OSUtil().isAboveAndroidR())
                getDisplay(activity)?.getRealMetrics(this)
            else
                getDisplay(activity)?.getMetrics(this)
        }

        fun getDisplayHeight(activity: Activity): Int = getDisplayMetrics(activity).heightPixels

        fun getDisplayWidht(activity: Activity): Int = getDisplayMetrics(activity).widthPixels
    }
}