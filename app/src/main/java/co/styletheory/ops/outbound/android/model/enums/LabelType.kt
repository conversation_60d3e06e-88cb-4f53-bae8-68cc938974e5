package co.styletheory.ops.outbound.android.model.enums

import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import co.styletheory.ops.outbound.android.R

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 24 October 2017.
 * Description
 *
 * <EMAIL>
 */
enum class LabelType constructor(@ColorRes val colorRes: Int, @StringRes val stringRes: Int, @DrawableRes val drawableRes: Int) {
    VIP(R.color.gold, R.string.vip_label, R.drawable.ic_vip),
    NO_PAPER(R.color.blue_cff, R.string.no_paper_label, R.drawable.ic_nopaper),
    FIRST_BOX(R.color.light_green, R.string.first_box_label, R.drawable.ic_first_box),
    NO_TOTE_BAG(R.color.gray_64, R.string.no_tote_bag_label, R.drawable.ic_no_tote_bag)
}