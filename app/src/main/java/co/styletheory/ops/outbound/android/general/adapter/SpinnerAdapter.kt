package co.styletheory.ops.outbound.android.general.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.TextView
import co.styletheory.ops.outbound.android.R

/**
 * Created by Yoga C. <PERSON> on 11/05/20.
 * Android Engineer
 */
class SpinnerAdapter(val context: Context, private val itemList: Array<String>, private val hideFirstItem: Boolean = false) : BaseAdapter() {

    val inflater: LayoutInflater = LayoutInflater.from(context)

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        val view: View
        val itemHolder: ItemViewHolder

        if(convertView == null) {
            view = inflater.inflate(R.layout.spinner_item, parent, false)
            itemHolder = ItemViewHolder(view)
            view?.tag = itemHolder
        } else {
            view = convertView
            itemHolder = view.tag as ItemViewHolder
        }

        itemHolder.label.text = itemList[position]
        return view
    }

    override fun getItem(position: Int): Any {
        return itemList[position]
    }

    override fun getItemId(p0: Int): Long {
        return 0
    }

    override fun getCount(): Int {
        return itemList.size
    }

    private class ItemViewHolder(view: View?) {
        val label: TextView = view?.findViewById(android.R.id.text1) as TextView
    }

}