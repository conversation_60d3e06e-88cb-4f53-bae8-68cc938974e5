package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import retrofit2.Call
import retrofit2.Retrofit

/**
 * Created by Yoga C<PERSON> on 16/04/19.
 * Android Engineer
 */
class SwapBoxItemResource constructor(private val boxId: String,
                                      private val oldItemUUID: String,
                                      private val newItemUUID: String,
                                      private val email: String) : GraphQLResource<GraphQLData<Void>>() {

    override fun graphQLRequest(): GraphQLRequest = GraphQLRequestList.SWAP_BOX_ITEM

    override fun bodyParameter(): MutableMap<String, Any> {
        bodyParameter["boxId"] = boxId
        bodyParameter["oldItemId"] = oldItemUUID
        bodyParameter["newItemId"] = newItemUUID
        bodyParameter["email"] = email
        return bodyParameter
    }

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<Void>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.swapItem(uri, createRequestBody()))
    }
}