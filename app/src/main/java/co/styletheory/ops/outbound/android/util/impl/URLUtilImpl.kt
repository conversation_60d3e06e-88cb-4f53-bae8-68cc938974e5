package co.styletheory.ops.outbound.android.util.impl

import co.styletheory.ops.outbound.android.util.URLUtil
import java.net.MalformedURLException
import java.net.URL


/**
 * Created by dennisfarandy on 10/4/16.
 */

class URLUtilImpl(url: String) : URLUtil {
    override fun baseURL(): String {
        return url!!.protocol + "://" + url.host
    }

    override fun url(): String {
        return url!!.path
    }

    override fun urn(): String {
        return url!!.file
    }

    override fun queryString(): String {
        return url!!.query
    }

    override fun completeURL(): String {
        return getURL() + queryString()
    }

    private val url: URL?

    init {
        var url1: URL?
        try {
            url1 = URL(url)
        } catch (e: MalformedURLException) {
            e.printStackTrace()
            url1 = null
        }

        this.url = url1
    }

    fun getURL(): String {
        return baseURL() + url!!.path
    }

}
