package co.styletheory.ops.outbound.android.util

import android.Manifest
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import co.styletheory.ops.outbound.android.R
import pub.devrel.easypermissions.AfterPermissionGranted
import pub.devrel.easypermissions.EasyPermissions

/**
 * Interface for activities that need to handle camera and storage permissions
 */
interface PermissionHandlerActivity {
    /**
     * Called when all required permissions are granted
     * Implement this to launch your photo picker or camera
     */
    fun onAllPermissionsGranted(requestCode: Int)

    fun getActivity(): AppCompatActivity
}

/**
 * Utility class for handling runtime permissions across different Android versions.
 * Manages camera and storage permissions with version-specific handling for Android 10, 11-12, and 13+.
 */
object PermissionUtils {

    // Permission request codes
    const val RC_CAMERA_PERMISSION = 100
    const val RC_STORAGE_PERMISSION = 101
    const val RC_MEDIA_PERMISSION = 102
    const val RC_ALL_PERMISSIONS = 103

    /**
     * Get required permissions based on Android SDK version
     */
    fun getRequiredPermissions(): Array<String> {
        return if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) { // Android 13+
            arrayOf(
                Manifest.permission.CAMERA,
                Manifest.permission.READ_MEDIA_IMAGES,
                Manifest.permission.READ_MEDIA_VIDEO
            )
        } else if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) { // Android 11-12
            arrayOf(
                Manifest.permission.CAMERA,
                Manifest.permission.READ_EXTERNAL_STORAGE
            )
        } else { // Android 10 and below
            arrayOf(
                Manifest.permission.CAMERA,
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
        }
    }

    /**
     * Check if all required permissions are granted
     */
    fun hasAllRequiredPermissions(context: Context): Boolean {
        val permissions = getRequiredPermissions()
        return EasyPermissions.hasPermissions(context, *permissions)
    }

    /**
     * Request all required permissions at once
     */
    fun requestAllPermissions(activity: AppCompatActivity, rationale: String) {
        val permissions = getRequiredPermissions()
        EasyPermissions.requestPermissions(
            activity,
            rationale,
            RC_ALL_PERMISSIONS,
            *permissions
        )
    }

    /**
     * Request camera permission
     */
    fun requestCameraPermission(activity: AppCompatActivity, rationale: String) {
        EasyPermissions.requestPermissions(
            activity,
            rationale,
            RC_CAMERA_PERMISSION,
            Manifest.permission.CAMERA
        )
    }

    /**
     * Request storage permissions based on SDK version
     */
    fun requestStoragePermissions(activity: AppCompatActivity, rationale: String) {
        val storagePermissions = if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arrayOf(
                Manifest.permission.READ_MEDIA_IMAGES,
                Manifest.permission.READ_MEDIA_VIDEO
            )
        } else if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
        } else {
            arrayOf(
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
        }

        EasyPermissions.requestPermissions(
            activity,
            rationale,
            RC_STORAGE_PERMISSION,
            *storagePermissions
        )
    }

    /**
     * Check if camera permission is granted
     */
    fun hasCameraPermission(context: Context): Boolean {
        return EasyPermissions.hasPermissions(context, Manifest.permission.CAMERA)
    }

    /**
     * Check if storage permissions are granted
     */
    fun hasStoragePermissions(context: Context): Boolean {
        return if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            EasyPermissions.hasPermissions(
                context,
                Manifest.permission.READ_MEDIA_IMAGES,
                Manifest.permission.READ_MEDIA_VIDEO
            )
        } else if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            EasyPermissions.hasPermissions(context, Manifest.permission.READ_EXTERNAL_STORAGE)
        } else {
            EasyPermissions.hasPermissions(
                context,
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
        }
    }

    /**
     * Show settings dialog when permissions are permanently denied
     */
    fun showSettingsDialog(context: Context, message: String) {
        AlertDialog.Builder(context)
            .setTitle(context.getString(R.string.permission_required))
            .setMessage(message)
            .setPositiveButton(context.getString(R.string.go_to_settings)) { _, _ ->
                // Open app settings
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                val uri = Uri.fromParts("package", context.packageName, null)
                intent.data = uri
                context.startActivity(intent)
            }
            .setNegativeButton(context.getString(R.string.cancel), null)
            .create()
            .show()
    }

    /**
     * Sequential permission request strategy
     * First requests camera permission, then storage permissions
     */
    fun requestPermissionsSequentially(activity: AppCompatActivity) {
        // First check camera permission
        if(!hasCameraPermission(activity)) {
            requestCameraPermission(
                activity,
                activity.getString(R.string.camera_permission_rationale)
            )
        } else if(!hasStoragePermissions(activity)) {
            // Camera permission is granted, now request storage permissions
            requestStoragePermissions(
                activity,
                activity.getString(R.string.storage_permission_rationale)
            )
        }
    }

    /**
     * Handle permission requests for activities implementing PermissionHandlerActivity
     * This is a more general approach that can be used across different activities
     */
    fun handlePermissionRequest(handler: PermissionHandlerActivity, requestCode: Int) {
        val activity = handler.getActivity()

        if(hasAllRequiredPermissions(activity)) {
            handler.onAllPermissionsGranted(requestCode)
        } else {
            requestPermissionsSequentially(activity)
        }
    }

    /**
     * Handle permission result for activities implementing PermissionHandlerActivity
     * Call this from onRequestPermissionsResult in your activity
     */
    fun handlePermissionResult(handler: PermissionHandlerActivity, requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
        val activity = handler.getActivity()

        // Let EasyPermissions handle the result first
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, activity)
        if(requestCode == RC_CAMERA_PERMISSION &&
            !EasyPermissions.hasPermissions(activity, Manifest.permission.CAMERA) &&
            !EasyPermissions.somePermissionPermanentlyDenied(activity, listOf(Manifest.permission.CAMERA))
        ) {
            requestPermissionsSequentially(activity)
        } else if(requestCode == RC_STORAGE_PERMISSION &&
            !hasStoragePermissions(activity) &&
            EasyPermissions.somePermissionPermanentlyDenied(activity, listOf(Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE))
        ) {
            showSettingsDialog(activity, activity.getString(R.string.storage_permission_rationale))
        } else if(hasAllRequiredPermissions(activity)) {
            handler.onAllPermissionsGranted(requestCode)
        }
    }

    /**
     * Launch the photo picker with proper permission handling
     * This is a convenience method that can be used across different activities
     */
    @AfterPermissionGranted(RC_ALL_PERMISSIONS)
    fun launchPhotoPicker(handler: PermissionHandlerActivity, requestCode: Int) {
        handlePermissionRequest(handler, requestCode)
    }
}
