package co.styletheory.ops.outbound.android.feature.photoDetail.viewModel.impl

import android.view.View
import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableInt
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.photoDetail.viewModel.PhotoDetailItemViewModel
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.util.GeneralCallback
import co.styletheory.ops.outbound.android.util.notNull
import co.styletheory.ops.outbound.android.viewModelComponent.LabelBoxViewModel
import javax.inject.Inject

/**
 * Created by g<PERSON>gygunawan on 11/27/17.
 *
 */
class PhotoDetailItemViewModelImpl @Inject constructor() : BaseViewModel<PhotoDetailItemViewModelImpl>(), PhotoDetailItemViewModel {

    @Inject
    lateinit var userStorage: UserStorage

    init {
        viewModelClass = PhotoDetailItemViewModelImpl::class
    }

    val title = ObservableString()
    val titleVisibility = ObservableInt(View.VISIBLE)
    val labelItems = ObservableArrayList<LabelBoxViewModel>()
    val isComplete = ObservableBoolean(false)
    val completeButtonText = ObservableString()
    val customerName = ObservableString()
    val qaPhotoItems = ObservableArrayList<PhotoItemViewModel>()

    private var shipment: Shipment? = null
    private var rackSection: String = ""
    private var rackName: String = ""
    private var batchId: String = ""

    override fun setRackSection(rackSection: String?) {
        this.rackSection = rackSection.orEmpty()
    }

    override fun setRackName(rackName: String?) {
        this.rackName = rackName.orEmpty()
    }

    override fun setShipment(shipment: Shipment?) {
        this.shipment = shipment
    }

    override fun setBatchId(batchId: String?) {
        this.batchId = batchId.orEmpty()
    }

    override fun mapShipment() {
        shipment.notNull {
            customerName.set(string(R.string.rack_name_photo_detail).format(it.customer?.name, getRackName()))
            title.set(it.tracking?.id)
            if(it.tracking?.id.isNullOrEmpty()) {
                titleVisibility.set(View.GONE)
            }
            labelItems.addAll(LabelBoxViewModel.create(it))

            qaPhotoItems.clear()
            for(item in it.items.filter { item ->
                item.status == ProductStatus.PHOTO_QA_DONE ||
                        item.status == ProductStatus.PHOTO_QA ||
                        item.status == ProductStatus.QA_PASSED
            }) {
                val qaItem = PhotoItemViewModel()
                qaItem.bindViewModel(item, rackSection, rackName, shipment?.id.orEmpty())
                qaItem.refreshCompleteState = refreshCompleteState
                qaItem.setBatchId(batchId)
                qaPhotoItems.add(qaItem)
            }
            completeButtonRefreshState()
        }
    }

    override fun getRackName(): String {
        return if(userStorage.isUserOnDemandAndRegionID()) {
            string(R.string.rack_name_on_demand).format(rackName, rackSection)
        } else {
            string(R.string.rack_name_regular).format(rackName, rackSection)
        }
    }

    private fun completeButtonRefreshState() {
        if(isQAItemComplete()) {
            isComplete.set(true)
            completeButtonText.set(string(R.string.complete_label))
        } else {
            isComplete.set(false)
            completeButtonText.set(string(R.string.in_complete_label))
        }
        shipment?.isComplete = isQAItemComplete()
    }

    private fun isQAItemComplete(): Boolean {
        for(it in qaPhotoItems) {
            if(!it.photoDone.get()) {
                return false
            }
        }
        return true
    }

    private val refreshCompleteState = object : GeneralCallback {
        override fun callback() {
            completeButtonRefreshState()
        }
    }
}