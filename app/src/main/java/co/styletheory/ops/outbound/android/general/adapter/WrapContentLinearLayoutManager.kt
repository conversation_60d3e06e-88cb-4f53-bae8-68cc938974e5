package co.styletheory.ops.outbound.android.general.adapter

import android.content.Context
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import timber.log.Timber

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 22 May 2018.
 * Description
 *
 * <EMAIL>
 */
class WrapContentLinearLayoutManager(val context: Context?) : LinearLayoutManager(context) {
    override fun supportsPredictiveItemAnimations(): <PERSON><PERSON><PERSON> {
        return false
    }

    override fun onLayoutChildren(recycler: RecyclerView.Recycler?, state: RecyclerView.State?) {
        try {
            super.onLayoutChildren(recycler, state)
        } catch(e: IndexOutOfBoundsException) {
            Timber.e("Catch index out of bounds because holder item position more than adapter item count")
        }
    }
}