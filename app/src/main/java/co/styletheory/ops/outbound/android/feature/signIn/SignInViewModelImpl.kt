package co.styletheory.ops.outbound.android.feature.signIn

import androidx.databinding.ObservableBoolean
import co.styletheory.android.network.core.RequestResult
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.general.auth.event.AuthFailedEvent
import co.styletheory.ops.outbound.android.general.auth.event.AuthSuccessEvent
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.injection.scope.PerActivity
import co.styletheory.ops.outbound.android.model.Region
import co.styletheory.ops.outbound.android.model.Session
import co.styletheory.ops.outbound.android.model.User
import co.styletheory.ops.outbound.android.model.enums.Regions
import co.styletheory.ops.outbound.android.model.enums.UserRoles
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.CreatePasswordResource
import co.styletheory.ops.outbound.android.resources.SignInResource
import co.styletheory.ops.outbound.android.resources.UserDetailResource
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.util.notNull
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 07 November 2017.
 * Description
 *
 * <EMAIL>
 */

@PerActivity
class SignInViewModelImpl @Inject constructor() : BaseViewModel<SignInViewModelImpl>(), SignInViewModel {

    init {
        viewModelClass = SignInViewModelImpl::class
    }

    val enableSignInButton = ObservableBoolean(true)
    val emailText = ObservableString()
    val passwordText = ObservableString()
    val nameText = ObservableString()
    val newPasswordText = ObservableString()
    val newConfirmPasswordText = ObservableString()
    val showAdditionalField = ObservableBoolean(false)

    @Inject
    lateinit var userStorage: UserStorage

    @Inject
    lateinit var dataService: DataService

    @Inject
    lateinit var errorResponse: ErrorResponse

    override fun doSignIn() {
        if(userStorage.getUserLogin() == null) {
            if(showAdditionalField.get()) {
                dataService.createPassword(CreatePasswordResource(nameText.get(), emailText.get(), passwordText.get(), newPasswordText.get()), sessionCallback)
            } else {
                dataService.login(SignInResource(emailText.get(), passwordText.get()), sessionCallback)
            }
        } else {
            fetchUserDetail()
        }
    }

    val sessionCallback = RequestResult<GraphQLData<Session>, String> {
        onSuccess {
            it?.data?.result?.notNull { session ->
                if(session.isNewUser) {
                    firstTimeSignIn()
                } else {
                    userStorage.setUserSession(session)
                    fetchUserDetail()
                }
            }
        }
        onError { eventBus.post(AuthFailedEvent(errorResponse.getErrorBodyDescription(it))) }
    }

    fun firstTimeSignIn() {
        showAdditionalField.set(true)
        eventBus.post(AuthFailedEvent(context.getString(R.string.err_update_user_data)))
    }

    override fun fetchUserDetail() {
        dataService.userDetail(UserDetailResource(), userDetailCallback)
    }

    val userDetailCallback = RequestResult<GraphQLData<User>, String> {
        onSuccess {
            it?.data?.result?.notNull { user ->
                userStorage.setUserLogin(user)
                userStorage.setUserRegion(getUserRegionByRole(user.roles))
                eventBus.post(AuthSuccessEvent())
            }
        }
        onError { eventBus.post(AuthFailedEvent(errorResponse.getErrorBodyDescription(it))) }
    }

    fun getUserRegionByRole(userRoles: List<String>?): Region {
        return when {
            userRoles?.contains(UserRoles.SG_STAFF.roles) == true -> Region(0, Regions.SINGAPORE.code, Regions.SINGAPORE.country)
            userRoles?.contains(UserRoles.HK_STAFF.roles) == true -> Region(0, Regions.HONGKONG.code, Regions.HONGKONG.country)
            userRoles?.contains(UserRoles.ID_STAFF.roles) == true -> Region(0, Regions.INDONESIA.code, Regions.INDONESIA.country)
            else -> Region(0, Regions.SINGAPORE.code, Regions.SINGAPORE.country)
        }
    }

    override fun isAllFieldValid(): Boolean {
        var isValid: Boolean
        isValid = emailText.get().isNotEmpty() && passwordText.get().isNotEmpty()
        if(showAdditionalField.get()) {
            when {
                nameText.isEmpty() -> isValid = false
                newPasswordText.isEmpty() -> isValid = false
                newConfirmPasswordText.isEmpty() -> isValid = false
                newPasswordText.get() != newConfirmPasswordText.get() -> isValid = false
            }
        }
        return isValid
    }
}