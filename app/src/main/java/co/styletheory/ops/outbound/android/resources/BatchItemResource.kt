package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import co.styletheory.ops.outbound.android.util.notNullOrEmpty
import retrofit2.Call
import retrofit2.Retrofit

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 15 November 2017.
 * Description
 *
 * <EMAIL>
 */
class BatchItemResource constructor(
        val batchId: String,
        val shipmentId: String,
        val itemIds: List<String>,
        val status: ProductStatus,
        val qaFailedCategory: String = "",
        val qaFailedReason: String = ""
) : GraphQLResource<GraphQLData<Void>>() {

    override fun graphQLRequest(): GraphQLRequest = GraphQLRequestList.UPDATE_BATCH_ITEM_STATUS
    override fun bodyParameter(): MutableMap<String, Any> {
        bodyParameter["batchId"] = batchId
        bodyParameter["shipmentId"] = shipmentId
        bodyParameter["ids"] = itemIds
        bodyParameter["status"] = status.text
        qaFailedCategory.notNullOrEmpty { bodyParameter["qaFailedCategory"] = it }
        qaFailedReason.notNullOrEmpty { bodyParameter["qaFailedReason"] = it }
        return bodyParameter
    }

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<Void>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.updateBatchItemStatus(uri, createRequestBody()))
    }

}