package co.styletheory.ops.outbound.android.feature.settings.viewModel.impl

import androidx.databinding.ObservableBoolean
import co.styletheory.ops.outbound.android.feature.settings.event.SettingsUIEvent
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseInjectedViewModel
import co.styletheory.ops.outbound.android.injection.scope.PerInjectedViewModel
import co.styletheory.ops.outbound.android.model.Region
import co.styletheory.ops.outbound.android.model.VerticalType
import co.styletheory.ops.outbound.android.util.AppConstant
import javax.inject.Inject

/**
 * Created by Yoga C. Pranata on 8/20/19.
 * Android Engineer
 */
@PerInjectedViewModel
class BatchConfigItemViewModel @Inject constructor() : BaseInjectedViewModel() {

    val name = ObservableString()
    val type = ObservableString()
    val isChecked = ObservableBoolean(false)
    var region = Region()
    var vertical = VerticalType()
    private val listOfRegion: MutableMap<String, Boolean> = mutableMapOf()
    private val listOfVertical: MutableMap<String, Boolean> = mutableMapOf()

    fun bindRegionView(region: Region) {
        this.region = region
        this.name.set(region.name)
        this.type.set(AppConstant.CONFIG_TYPE.REGION)
        listOfRegion[name.get()] = false
    }

    fun bindVerticalView(verticalType: VerticalType) {
        this.vertical = verticalType
        this.name.set(verticalType.name)
        this.type.set(AppConstant.CONFIG_TYPE.VERTICAL)
        listOfVertical[name.get()] = false
    }

    fun onRadioButtonClicked() {
        eventBus.post(SettingsUIEvent.OnRadioButtonClicked(type.get(), this))
    }
}