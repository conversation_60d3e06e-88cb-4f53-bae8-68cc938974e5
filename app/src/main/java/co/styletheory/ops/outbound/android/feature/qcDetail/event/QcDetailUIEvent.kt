package co.styletheory.ops.outbound.android.feature.qcDetail.event

import android.view.View
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.util.Result

/**
 * Created by <PERSON> on 13/03/19.
 */
sealed class QcDetailUIEvent {
    data class FailReasonButtonClick(
            val batchId: String,
            val batchItemId: String?,
            val shipmentId: String,
            val addNotesResult: Result<List<String>, String?>,
            val anchorView: View,
            val swapItem: SwapItem
    )

    data class RemoveShipmentQaPicked(val shipment: Shipment?)
    data class OnScannedRfidText(val position: Int, val scannedText: String)
    data class OnScannedRfidTextNotFound(val title: String, val message: String)
    data class OnResultScannedRfid(val scannedText: String)
    data class OnScrollToPosition(val itemPosition: Int)
    data class OnAccuracySwapButtonClicked(val swapItem: SwapItem)
    data class OnErrorSwapItem(val errorMessage: String)
    object OnUpdateItemSuccess
}