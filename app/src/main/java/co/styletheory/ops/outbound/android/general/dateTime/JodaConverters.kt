package co.styletheory.ops.outbound.android.general.dateTime

import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import org.joda.time.DateTime

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 9/27/16.
 */
object JodaConverters {
    fun registerAll(builder: GsonBuilder?): GsonBuilder {
        if (builder == null) {
            throw NullPointerException("builder cannot be null")
        }

        registerDateTime(builder)

        return builder
    }

    fun registerDateTime(builder: GsonBuilder?): GsonBuilder {
        if (builder == null) {
            throw NullPointerException("builder cannot be null")
        }

        val type = object : TypeToken<DateTime>() {

        }.type
        builder.registerTypeAdapter(type, DateTimeConverters())

        return builder
    }
}

