package co.styletheory.ops.outbound.android.feature.accuracySwap.viewModel.impl

import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.accuracySwap.viewModel.AccuracySwapViewModel
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.viewModelComponent.FooterButtonViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel
import javax.inject.Inject

/**
 * Created by Yoga C. Pranata on 05/05/20.
 * Android Engineer
 */
class AccuracySwapViewModelImpl @Inject constructor() : BaseViewModel<AccuracySwapViewModelImpl>(), AccuracySwapViewModel {

    @Inject
    lateinit var toolbarViewModel: GeneralToolbarViewModel
    @Inject
    lateinit var footerButtonViewModel: FooterButtonViewModel

    init {
        viewModelClass = AccuracySwapViewModelImpl::class
    }

    override var swapItem: SwapItem = SwapItem()

    override fun toolbarViewModel(): GeneralToolbarViewModel = toolbarViewModel
    override fun footerButtonViewModel(): FooterButtonViewModel = footerButtonViewModel

    override fun afterInject() {
        footerButtonViewModel.buttonTitle.set(string(R.string.accuracy_swap_now_title).uppercase())
        footerButtonViewModel.enable.set(false)
    }

    override fun selectedSwapReasonPosition(selectedPosition: Int) {
        if(selectedPosition > 0) {
            footerButtonViewModel.enable.set(true)
        } else {
            footerButtonViewModel.enable.set(false)
        }
    }
}