package co.styletheory.ops.outbound.android.injection.component

import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentManager
import co.styletheory.ops.outbound.android.feature.accuracySwap.view.AccuracySwapActivity
import co.styletheory.ops.outbound.android.feature.backlogDetail.view.BacklogDetailActivity
import co.styletheory.ops.outbound.android.feature.packing.view.PackingDetailActivity
import co.styletheory.ops.outbound.android.feature.photoDetail.view.PhotoDetailActivity
import co.styletheory.ops.outbound.android.feature.photoManager.view.PhotoManagerActivity
import co.styletheory.ops.outbound.android.feature.qcDetail.view.QcDetailActivity
import co.styletheory.ops.outbound.android.feature.qualitySwap.view.QualitySwapActivity
import co.styletheory.ops.outbound.android.feature.settings.view.SettingsActivity
import co.styletheory.ops.outbound.android.feature.signIn.SignInActivity
import co.styletheory.ops.outbound.android.general.test.EmptyActivity
import co.styletheory.ops.outbound.android.injection.module.ActivityModule
import co.styletheory.ops.outbound.android.injection.module.ViewModelModule
import co.styletheory.ops.outbound.android.injection.scope.PerActivity
import co.styletheory.ops.outbound.android.main.view.MainActivity
import dagger.Component

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 19 October 2017.
 * Description
 *
 * <EMAIL>
 */


@PerActivity
@Component(dependencies = [AppComponent::class], modules = [ActivityModule::class, ViewModelModule::class])
interface ActivityComponent : AppComponent {

    fun getActivity(): AppCompatActivity
    fun getFragmentManager(): FragmentManager

    // Create Inject Methods Here
    fun inject(activity: EmptyActivity)

    fun inject(activity: SignInActivity)
    fun inject(activity: MainActivity)
    fun inject(activity: BacklogDetailActivity)
    fun inject(activity: QcDetailActivity)
    fun inject(activity: PhotoManagerActivity)
    fun inject(activity: PhotoDetailActivity)
    fun inject(activity: PackingDetailActivity)
    fun inject(activity: SettingsActivity)
    fun inject(activity: AccuracySwapActivity)
    fun inject(activity: QualitySwapActivity)
}