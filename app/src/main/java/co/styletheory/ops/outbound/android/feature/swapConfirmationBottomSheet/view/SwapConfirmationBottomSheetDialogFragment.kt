package co.styletheory.ops.outbound.android.feature.swapConfirmationBottomSheet.view

import android.os.Bundle
import android.view.View
import android.widget.Toast
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.BottomsheetSwapConfirmationBinding
import co.styletheory.ops.outbound.android.feature.swapConfirmationBottomSheet.event.SwapConfirmationBottomDialogNetworkEvent
import co.styletheory.ops.outbound.android.feature.swapConfirmationBottomSheet.event.SwapConfirmationBottomDialogUIEvent
import co.styletheory.ops.outbound.android.feature.swapConfirmationBottomSheet.viewModel.SwapConfirmationBottomSheetViewModel
import co.styletheory.ops.outbound.android.general.base.BaseBottomSheetDialog
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.model.enums.SwapType
import co.styletheory.ops.outbound.android.util.IntentConstant
import co.styletheory.ops.outbound.android.util.notNull
import org.greenrobot.eventbus.Subscribe
import org.parceler.Parcels


@Suppress("UNUSED_PARAMETER", "unused")
class SwapConfirmationBottomSheetDialogFragment : BaseBottomSheetDialog<BottomsheetSwapConfirmationBinding, SwapConfirmationBottomSheetViewModel>() {

    companion object {
        fun newInstance(oldItem: SwapItem, newItem: BatchItem, type: SwapType, batchId: String): SwapConfirmationBottomSheetDialogFragment {
            val args = Bundle()
            val fragment = SwapConfirmationBottomSheetDialogFragment()
            args.putParcelable(IntentConstant.OLD_SWAP_ITEM, Parcels.wrap(oldItem))
            args.putParcelable(IntentConstant.NEW_SWAP_ITEM, Parcels.wrap(newItem))
            args.putString(IntentConstant.SWAP_TYPE, type.swapType)
            args.putString(IntentConstant.BATCH_ID, batchId)
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if(viewModel == null) {
            fragmentComponent?.inject(this)
        }
    }

    override fun getLayoutId() = R.layout.bottomsheet_swap_confirmation

    override fun onLoadBottomSheetDialog(view: View, saveInstance: Bundle?) {
        isCancelable = false
        arguments.notNull {
            val oldItem = Parcels.unwrap<SwapItem>(it.getParcelable(IntentConstant.OLD_SWAP_ITEM))
            val newItem = Parcels.unwrap<BatchItem>(it.getParcelable(IntentConstant.NEW_SWAP_ITEM))
            val swapType = it.getString(IntentConstant.SWAP_TYPE).orEmpty()
            val batchId = it.getString(IntentConstant.BATCH_ID).orEmpty()
            viewModel?.setItems(oldItem, newItem, swapType, batchId)
        }
    }

    @Subscribe
    fun onSwapSuccessEvent(event: SwapConfirmationBottomDialogNetworkEvent.OnSwapSuccess) {
        dismiss()
    }

    @Subscribe
    fun onSwapFailedEvent(event: SwapConfirmationBottomDialogNetworkEvent.OnSwapFailed) {
        Toast.makeText(context, event.errorMessage, Toast.LENGTH_SHORT).show()
    }

    @Subscribe
    fun onCloseDialogEvent(event: SwapConfirmationBottomDialogUIEvent.OnCloseClick) {
        dismiss()
    }
}