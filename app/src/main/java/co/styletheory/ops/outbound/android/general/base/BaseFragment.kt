package co.styletheory.ops.outbound.android.general.base

import android.content.Intent
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.annotation.*
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import co.styletheory.ops.outbound.android.BR
import co.styletheory.ops.outbound.android.general.Navigator
import co.styletheory.ops.outbound.android.general.dialog.CustomProgressDialog
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.injection.component.DaggerFragmentComponent
import co.styletheory.ops.outbound.android.injection.component.FragmentComponent
import co.styletheory.ops.outbound.android.injection.module.FragmentModule
import co.styletheory.ops.outbound.android.util.notNull
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import javax.inject.Inject


/**
 * Created by elroysetiabudi on 9/13/16.
 */
abstract class BaseFragment<VB : ViewDataBinding, VM : ViewModel> : Fragment() {

    @Inject
    lateinit var eventBus: EventBus
    @Inject
    lateinit var navigator: Navigator
    @Inject
    lateinit var progressDialog: CustomProgressDialog

    var fragmentComponent: FragmentComponent? = null
        get() {
            if(field == null) {
                field = DaggerFragmentComponent.builder()
                        .activityComponent((activity as? BaseActivity<*, *>)?.activityComponent)
                        .fragmentModule(FragmentModule(this))
                        .build()
            }
            return field
        }

    var binding: VB? = null

    @Inject
    @JvmField
    var viewModel: VM? = null

    override fun onStart() {
        super.onStart()
        viewModel?.afterInject()
        registerEventBus()
    }

    override fun onStop() {
        unregisterEventBus()
        super.onStop()
    }

    override fun onDestroy() {
        binding = null
        viewModel = null
        fragmentComponent = null
        super.onDestroy()
    }

    override fun onPause() {
        super.onPause()
        unregisterEventBus()
    }

    override fun onResume() {
        super.onResume()
        registerEventBus()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        registerEventBus()

        val fragments = childFragmentManager.fragments
        for(fragment in fragments) {
            fragment.onActivityResult(requestCode, resultCode, data)
        }
    }

    private fun registerEventBus() {
        if(!eventBus.isRegistered(this)) {
            eventBus.register(this)
        }
    }

    private fun unregisterEventBus() {
        if(eventBus.isRegistered(this)) {
            eventBus.unregister(this)
        }
    }

    fun showProgressDialog(withText: String = "") {
        progressDialog.showWithText(withText, childFragmentManager)
    }


    fun dismissProgressDialog() {
        activity.notNull { progressDialog.dismissProgressDialog() }
    }

    @Suppress("UNUSED_PARAMETER")
    @Subscribe
    fun onEventMainThread(event: BaseEvent) {
        //Not implemented yet
    }

    fun <T> pushArgument(arg: Any) {
        eventBus.postSticky(arg)
    }

    fun <T> popArgumentsAs(tClass: Class<T>): T? {
        val stickyArgument = eventBus.getStickyEvent(tClass)
        eventBus.removeStickyEvent(tClass)

        return stickyArgument
    }

    /* Sets the content view, creates the binding and attaches the view to the view model */
    protected fun bindContentView(inflater: LayoutInflater, container: ViewGroup?, @LayoutRes layoutResID: Int): View? {
        if(viewModel == null) {
            throw IllegalStateException("viewModel must already be set via injection")
        }
        binding = DataBindingUtil.inflate(inflater, layoutResID, container, false)
        binding?.setVariable(BR.viewModel, viewModel)
        return binding?.root
    }

    fun showShortToast(message: String?) {
        if(activity != null && activity?.isFinishing == false) {
            Toast.makeText(activity?.applicationContext, message, Toast.LENGTH_SHORT).show()
        }
    }

    fun showLongToast(message: String) {
        if(activity != null && activity?.isFinishing == false) {
            Toast.makeText(activity?.applicationContext, message, Toast.LENGTH_LONG).show()
        }
    }

    fun dimen(@DimenRes resId: Int): Int = resources.getDimensionPixelSize(resId)

    fun color(@ColorRes resId: Int): Int = ContextCompat.getColor(requireContext(), resId)

    fun drawable(@DrawableRes resId: Int): Drawable? = ContextCompat.getDrawable(requireContext(), resId)

    fun integer(@IntegerRes resId: Int): Int = resources.getInteger(resId)

    fun string(@StringRes resId: Int): String = resources.getString(resId)

}
