package co.styletheory.ops.outbound.android.view

import android.content.Context
import android.database.DataSetObserver
import android.util.AttributeSet
import android.view.View
import android.widget.ListAdapter
import com.nex3z.flowlayout.FlowLayout

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 31 October 2017.
 * Description
 *
 * <EMAIL>
 */
class AdapterFlowLayout @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null)
    : FlowLayout(context, attrs) {

    private var listAdapter: ListAdapter? = null
    private var convertViews: Array<View?>? = null

    fun setAdapter(listAdapter: ListAdapter) {
        this.listAdapter = listAdapter

        refreshView()

        listAdapter.registerDataSetObserver(object : DataSetObserver() {
            override fun onChanged() {
                super.onChanged()
                refreshView()
            }
        })
    }

    fun refreshView() {
        convertViews = arrayOfNulls(size = listAdapter!!.count)
        removeAllViews()
        for (i in 0 until listAdapter!!.count) {
            convertViews?.set(i, listAdapter!!.getView(i, convertViews?.get(i), this))
            addView(convertViews?.get(i))
        }
    }
}