package co.styletheory.ops.outbound.android.feature.signIn

import android.os.Bundle
import android.view.View
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.SignInActivityBinding
import co.styletheory.ops.outbound.android.general.auth.event.AuthFailedEvent
import co.styletheory.ops.outbound.android.general.auth.event.AuthSuccessEvent
import co.styletheory.ops.outbound.android.general.base.BaseActivity
import org.greenrobot.eventbus.Subscribe

@Suppress("UNUSED_PARAMETER", "unused")
/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 07 November 2017.
 * Description
 *
 * <EMAIL>
 */

class SignInActivity : BaseActivity<SignInActivityBinding, SignInViewModel>() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        activityComponent?.inject(this)
        bindContentView(R.layout.sign_in_activity)
        afterCreate()
    }

    fun afterCreate() {
        if(userStorage.getUserSession() == null) {
            userStorage.clearUserStorage()
        }else {
            if(userStorage.getUserLogin() != null && userStorage.getUserSession() != null) {
                navigator.clearStack().toMainActivity()
                finish()
            } else if(userStorage.getUserLogin() == null && userStorage.getUserSession() != null) {
                showProgressDialog()
                viewModel?.fetchUserDetail()
            }
        }
    }

    fun forgotPasswordClick(view: View) {
        //Not implemented yet
    }

    fun signInClick(view: View) {
        if(viewModel?.isAllFieldValid() == true) {
            showProgressDialog()
            viewModel?.doSignIn()
        } else {
            showShortToast(getString(R.string.error_fill_required_field))
        }
    }

    @Subscribe
    fun onAuthSuccess(event: AuthSuccessEvent) {
        dismissProgressDialog()
        showShortToast(getString(R.string.login_success_label))
        navigator.clearStack().toMainActivity()
        finish()
    }

    @Subscribe
    fun onAuthFailed(event: AuthFailedEvent) {
        dismissProgressDialog()
        showShortToast(event.error)
    }
}