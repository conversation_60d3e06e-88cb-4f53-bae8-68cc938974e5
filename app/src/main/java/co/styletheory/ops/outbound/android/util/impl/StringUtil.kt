package co.styletheory.ops.outbound.android.util.impl

import android.content.Context
import co.styletheory.ops.outbound.android.util.notNull


/**
 * styletheory-ops-outbound-android
 * Created by dwiap<PERSON><PERSON> on 20 November 2017.
 * Description
 *
 * <EMAIL>
 */
object StringUtil {
    fun stringArraySeparateWithComma(datas: List<String>?, spaceAfterData: Boolean = false): String{
        datas.notNull {
            return stringArraySeparateWithComma(it.toTypedArray(), spaceAfterData)
        }
        return ""
    }
    fun stringArraySeparateWithComma(datas: Array<String> = arrayOf(), spaceAfterData: Boolean = false): String{
        var data = ""
        for(d in datas){
            d.notNull {
                data += if(spaceAfterData){
                    "$d, "
                } else {
                    "$d,"
                }
            }
        }
        if(data.isEmpty()) return ""
        return data.substring(0, data.length - if(spaceAfterData) 2 else 1)
    }

    fun getStringFromAsset(context: Context?, fileName:String): String?{
        return context?.assets?.open("graphql/$fileName.graphql")?.bufferedReader().use { it?.readText() }
    }
}