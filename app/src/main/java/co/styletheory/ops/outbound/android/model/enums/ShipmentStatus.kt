package co.styletheory.ops.outbound.android.model.enums

import android.os.Parcelable
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonValue
import kotlinx.parcelize.Parcelize

@Parcelize
enum class ShipmentStatus(private val shippingStatus: String) : Parcelable {
    DELIVERED("Delivered"),
    STAGING("Staging"),
    SENDING("Sending"),
    FAILED("Failed"),
    RECEIVED_BY_WAREHOUSE("ReceivedByWarehouse");

    @JsonValue
    override fun toString(): String {
        return this.shippingStatus
    }

    companion object {
        private val types = values().associateBy(ShipmentStatus::shippingStatus)

        @JvmStatic
        @JsonCreator
        fun findValue(status: String): ShipmentStatus? = types[status]

        fun getMissingItemShippingStatus(): List<ShipmentStatus> {
            return listOf(DELIVERED, STAGING, SENDING, FAILED)
        }
    }
}