package co.styletheory.ops.outbound.android.feature.photoDetail.viewModel.impl

import android.view.View
import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableInt
import co.styletheory.android.network.core.RequestResult
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.photoManager.event.TakePictureEvent
import co.styletheory.ops.outbound.android.feature.photoManager.viewModel.AttachImageViewModel
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.event.PreviewImageEvent
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseInjectedViewModel
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.BatchItemResource
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.util.GeneralCallback
import co.styletheory.ops.outbound.android.util.Next
import co.styletheory.ops.outbound.android.util.impl.StringUtil
import co.styletheory.ops.outbound.android.viewModelComponent.ColorItemViewModel
import javax.inject.Inject

/**
 * Created by giorgygunawan on 11/28/17.
 *
 */
class PhotoItemViewModel @Inject constructor() : BaseInjectedViewModel() {

    @Inject
    lateinit var userStorage: UserStorage
    @Inject
    lateinit var errorResponse: ErrorResponse
    @Inject
    lateinit var dataService: DataService

    private var batchItem: BatchItem? = null
    private var rackSection: String = ""
    private var rackName: String = ""
    private var shipmentId: String = ""
    private var batchId: String = ""
    var refreshCompleteState: GeneralCallback? = null

    val imageUrl = ObservableString()
    val itemName = ObservableString()
    val itemSize = ObservableString()
    val rack = ObservableString()
    val category = ObservableString()
    val parts = ObservableString()
    val notes = ObservableString()
    val soldTo = ObservableString()
    val productOrder = ObservableString()
    val qaItemImages = ObservableArrayList<AttachImageViewModel>()
    val colorItems = ObservableArrayList<ColorItemViewModel>()
    val itemPurchasedVisibility = ObservableInt(View.GONE)

    val errorMessage = ObservableString()
    val photoDone = ObservableBoolean(false)
    val showOverlayLoading = ObservableBoolean(false)

    fun setBatchId(batchId: String) {
        this.batchId = batchId
    }

    fun bindViewModel(batchItem: BatchItem, rackSection: String, rackName: String, shipmentId: String) {
        this.batchItem = batchItem
        this.rackSection = rackSection
        this.rackName = rackName
        this.shipmentId = shipmentId

        imageUrl.set(batchItem.style?.gallery?.getOrElse(0) { "" })
        itemName.set(batchItem.style?.designer?.name)
        itemSize.set(batchItem.labelSize)
        productOrder.set("[${batchItem.order}]")
        category.set(batchItem.style?.primaryCategory)
        parts.set(StringUtil.stringArraySeparateWithComma(batchItem.parts))

        notes.set(batchItem.notes)
        colorItems.clear()
        for(item in batchItem.style?.colors?.filter { it.colorCode.isNotEmpty() }.orEmpty()) {
            val model = ColorItemViewModel()
            model.colorName.set(item.name)
            model.colorHex.set(item.colorCode)
            colorItems.add(model)
        }

        qaItemImages.clear()
        setupRackName(rackName, rackSection)
        setupPurchasedItem(batchItem)

        when(batchItem.status) {
            ProductStatus.PHOTO_QA_DONE -> {
                photoDone.set(true)
            }
            else -> {
                photoDone.set(false)
            }
        }
    }

    fun photoDoneClick() {
        if(canDoPhotoDone()) {
            val resource = BatchItemResource(batchId, shipmentId, listOf(batchItem?.id.orEmpty()), ProductStatus.PHOTO_QA_DONE)
            showOverlayLoading.set(true)
            dataService.updateBatchItemStatus(
                    resource,
                    RequestResult {
                        onSuccess {
                            photoDone.set(true)
                            errorMessage.clear()
                            showOverlayLoading.set(false)
                            refreshCompleteState?.callback()
                        }
                        onError {
                            errorMessage.set(errorResponse.getErrorBodyDescription(it))
                            showOverlayLoading.set(false)
                        }
                    }
            )
        } else {
            if(!photoDone.get()) {
                errorMessage.set(string(R.string.err_empty_attachment_picture))
            }
        }
    }

    fun canDoPhotoDone(): Boolean {
        return !photoDone.get() && qaItemImages.size > 0
    }

    fun imageClick() {
        eventBus.post(PreviewImageEvent(imageUrl.get()))
    }

    fun cameraClick() {
        eventBus.post(TakePictureEvent(shipmentId, batchItem, takePictureCallback))
    }

    fun addQAImages(imageUrls: List<String>) {
        for(url in imageUrls) {
            val model = AttachImageViewModel()
            model.imageUrl.set(url)
            qaItemImages.add(model)
        }
    }

    fun setupRackName(rackName: String?, rackSection: String) {
        if(userStorage.isUserOnDemandAndRegionID()) {
            rack.set(string(R.string.rack_name_on_demand).format(rackName, rackSection))
        } else {
            rack.set(string(R.string.rack_name_regular).format(rackName, rackSection))
        }
    }

    private val takePictureCallback = object : Next<List<String>> {
        override fun apply(t: List<String>) {
            addQAImages(t)
            photoDoneClick()
        }
    }

    fun setupPurchasedItem(batchItem: BatchItem) {
        if(!batchItem.label.isNullOrEmpty()) {
            itemPurchasedVisibility.set(View.VISIBLE)
            soldTo.set(batchItem.label)
        } else {
            itemPurchasedVisibility.set(View.GONE)
            soldTo.clear()
        }
    }
}