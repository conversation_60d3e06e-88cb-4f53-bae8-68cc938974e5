package co.styletheory.ops.outbound.android.util.featureflag

import co.styletheory.ops.outbound.android.general.binding.ObservableFeatureFlagBoolean
import co.styletheory.ops.outbound.android.general.binding.ObservableFeatureFlagVisibility
import co.styletheory.ops.outbound.android.general.event.GeneralUIEvent
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.model.BatchItem
import org.greenrobot.eventbus.EventBus
import javax.inject.Inject

/**
 * Created by Yoga C. Pranata on 08/04/19.
 * Android Engineer
 */
class FeatureFlagUtil @Inject constructor() : FeatureFlagBooleanListener {

    @Inject
    lateinit var userStorage: UserStorage

    @Inject
    lateinit var featureFlag: FeatureFlag

    @Inject
    lateinit var eventBus: EventBus

    private val isBarcodeApparelFlagOn = ObservableFeatureFlagBoolean(FeatureFlagConstant.OUTBOUND_BARCODE_APPAREL, false, this)
    private val isBarcodeBagsFlagOn = ObservableFeatureFlagBoolean(FeatureFlagConstant.OUTBOUND_BARCODE_BAGS, false, this)
    private val isAccuracySwapOn = ObservableFeatureFlagBoolean(FeatureFlagConstant.OUTBOUND_ACCURACY_SWAP_ITEM, false, this)
    private val isOldQualityCheckSwapOn = ObservableFeatureFlagBoolean(FeatureFlagConstant.OUTBOUND_OLD_QUALITY_CHECK_SWAP_ITEM, false, this)
    private val isQualitySwapResellingDisabled = ObservableFeatureFlagBoolean(FeatureFlagConstant.DISABLE_QUALITY_SWAP_RESELLING, false, this)
    private val isPickingSwapButtonEnabled = ObservableFeatureFlagBoolean(FeatureFlagConstant.ENABLE_OUTBOUND_PICKING_SWAP, false, this)
    val barcodeApparelVisibility = ObservableFeatureFlagVisibility(FeatureFlagConstant.OUTBOUND_BARCODE_APPAREL, false)
    val barcodeBagsVisibility = ObservableFeatureFlagVisibility(FeatureFlagConstant.OUTBOUND_BARCODE_BAGS, false)
    val snowfallVisibility = ObservableFeatureFlagVisibility(FeatureFlagConstant.INTERNAL_APP_SNOWFALL, false)

    override fun onToggleChange(key: String, toggleState: Boolean) {
        when(key) {
            FeatureFlagConstant.OUTBOUND_BARCODE_APPAREL,
            FeatureFlagConstant.OUTBOUND_BARCODE_BAGS -> {
                if(userStorage.isFeatureFlagIsOn() != toggleState) {
                    userStorage.setFeatureFlagValue(toggleState)
                    eventBus.post(GeneralUIEvent.FeatureFlagNeedToRefreshPage)
                }
            }
        }
    }

    private fun isBarcodeApparelSettingIsOn(): Boolean {
        return (isBarcodeApparelFlagOn.get() && userStorage.isBarcodeApparelFlagOn())
    }

    private fun isBarcodeBagsSettingIsOn(): Boolean {
        return (isBarcodeBagsFlagOn.get() && userStorage.isBarcodeBagsFlagOn())
    }

    fun isAccuracySwapIsOn() = isAccuracySwapOn.get()
    fun isOldQualityCheckSwapOn() = isOldQualityCheckSwapOn.get()
    fun isPickingSwapButtonEnabled() = isPickingSwapButtonEnabled.get()

    fun isBarcodeSettingIsOn(): Boolean {
        return when {
            (userStorage.isVerticalTypeApparel()
                    || userStorage.isVerticalTypeOnDemand()) -> (isBarcodeApparelSettingIsOn() || !isBarcodeApparelFlagOn.get())
            userStorage.isVerticalTypeBags() -> (isBarcodeBagsSettingIsOn() || !isBarcodeBagsFlagOn.get())
            else -> false
        }
    }

    fun isQualitySwapResellingDisabled(batchItem: BatchItem?) : Boolean {
        return (batchItem?.isResellingItem() == true && isQualitySwapResellingDisabled.get())
    }
}