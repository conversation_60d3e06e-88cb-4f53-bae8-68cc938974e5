package co.styletheory.ops.outbound.android.networking

import co.styletheory.android.network.resource.graphql.GraphQLRequest

/**
 * styletheory-ops-outbound-android
 * Created by dwiap<PERSON>to on 24 August 2018.
 * Description
 *
 * <EMAIL>
 */
object GraphQLRequestList {

    val LOGIN = GraphQLRequest("auth", "login")
    val CREATE_PASSWORD = GraphQLRequest("auth", "createPassword")
    val REFRESH_SESSION = GraphQLRequest("auth", "refreshSession")
    val LOGOUT = GraphQLRequest("auth", "logout")
    val BATCH_LIST = GraphQLRequest("batch-list", "batchList")
    val ATTACH_QA_IMAGES = GraphQLRequest("attach-photo-item", "attachQaImages")
    val ATTACH_PHOTO_IMAGES = GraphQLRequest("attach-photo-item", "attachPhotoImages")
    val BATCH_DETAIL = GraphQLRequest("batch-detail", "batchDetail")
    val UPDATE_BATCH_PROCESS_STATUS = GraphQLRequest("update-batch-process-status", "updateBatchProcessStatus")
    val UPDATE_BATCH_ITEM_STATUS = GraphQLRequest("update-batch-item-status", "updateBatchItemStatus")
    val CHECK_SWAP_ITEM_AVAILABILITY = GraphQLRequest("check-swap-availability", "checkSwapAvailability")
    val SWAP_SHIPMENT_ITEM = GraphQLRequest("swap-shipment-item", "swapShipmentItem")
    val SWAP_BOX_ITEM = GraphQLRequest("swap-box-item", "swapBoxItem")
    val CREATE_UPLOAD_PRESIGNED_URL = GraphQLRequest("create-upload-presigned-url", "createUploadPresignedUrl")
    val LOGISTIC_PROVIDER_LIST = GraphQLRequest("logistic-provider-list", "logisticProviders")
    val SHIPMENT_LIST_BY_PROVIDER = GraphQLRequest("shipment-list-by-provider", "shipmentListByProvider")
    val SEND_SHIPMENT = GraphQLRequest("send-shipment", "sendShipment")
    val USER = GraphQLRequest("user", "fetchUser")
    val CANCEL_GOJEK_SHIPMENTS = GraphQLRequest("cancel-gojek-shipments", "cancelGojekShipments")
    val ORDER_GOJEK_SHIPMENTS = GraphQLRequest("order-gojek-shipments", "orderGojekShipments")
    val GENERATE_BATCH_LIST = GraphQLRequest("generate-batch-list", "createBatchList")
    val BATCH_CONFIGS = GraphQLRequest("batch-config", "getBacthConfig")
    val GET_ACCURACY_SWAP_ITEM = GraphQLRequest("accuracy-swap-item", "getAccuracySwapItem")
    val ACCURACY_SWAP_ITEM = GraphQLRequest("accuracy-swap-item", "outboundAccuracySwap")
    val MARK_SHIPMENT_AS_RECEIVED = GraphQLRequest("mark-shipment-as-received", "markShipmentAsReceived")
}