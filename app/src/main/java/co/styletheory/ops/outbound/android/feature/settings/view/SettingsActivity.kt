package co.styletheory.ops.outbound.android.feature.settings.view

import android.app.DatePickerDialog
import android.os.Bundle
import android.widget.DatePicker
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import co.styletheory.ops.outbound.android.util.EdgeToEdgeUtil
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.ActivitySettingsBinding
import co.styletheory.ops.outbound.android.feature.settings.event.SettingsNetworkEvent
import co.styletheory.ops.outbound.android.feature.settings.event.SettingsUIEvent
import co.styletheory.ops.outbound.android.feature.settings.viewModel.SettingsViewModel
import co.styletheory.ops.outbound.android.general.base.BaseActivity
import co.styletheory.ops.outbound.android.general.base.BaseDialogFragment
import co.styletheory.ops.outbound.android.general.dialog.MyDatePickerDialog
import co.styletheory.ops.outbound.android.general.event.DismissDialogEvent
import co.styletheory.ops.outbound.android.util.*
import co.styletheory.ops.outbound.android.viewModelComponent.AlertDialogViewModel
import org.greenrobot.eventbus.Subscribe
import java.util.*

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 05 June 2018.
 * Description
 *
 * <EMAIL>
 */
@Suppress("UNUSED_PARAMETER", "unused")
class SettingsActivity : BaseActivity<ActivitySettingsBinding, SettingsViewModel>(), DatePickerDialog.OnDateSetListener {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        activityComponent?.inject(this)
        bindContentView(R.layout.activity_settings)
        startRegionAndVerticalTypeShimmer()
        initExtras()
        viewModel?.initializeViewModel()

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                navigator.clearStack().toMainActivity()
                finish()
            }
        })
    }

    /**
     * Override the base implementation to apply custom insets handling for SettingsActivity
     */
    override fun setupEdgeToEdgeInsets() {
        // Apply insets to the toolbar directly
        binding?.toolbarWrapper?.toolbar?.let { toolbar ->
            EdgeToEdgeUtil.fixToolbarInsets(this, toolbar)
        }

        // Apply insets to the content
        binding?.root?.let { rootView ->
            ViewCompat.setOnApplyWindowInsetsListener(rootView) { _, windowInsets ->
                val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())

                // Apply bottom insets to the content
                binding?.btnGenerateBatch?.updatePadding(bottom = insets.bottom)
                binding?.btnApplyFilter?.updatePadding(bottom = insets.bottom)

                // Return the insets so they can be consumed by child views
                windowInsets
            }
        }
    }

    private fun setupToolbar(title: String) {
        setSupportActionBar(binding?.toolbarWrapper?.toolbar as Toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        binding?.toolbarVM = viewModel?.toolbarViewModel()
        binding?.toolbarVM?.title?.set(title)

        // Apply window insets to the toolbar for proper edge-to-edge display
        binding?.toolbarWrapper?.toolbar?.let { toolbar ->
            // Use our specialized method to fix toolbar insets
            EdgeToEdgeUtil.fixToolbarInsets(this, toolbar)
        }
    }

    private fun startRegionAndVerticalTypeShimmer() {
        binding?.shimmerPlaceholder?.shimmerSettings?.startShimmer()
        viewModel?.fetchBatchConfigs()
    }

    private fun stopRegionAndVerticalTypeShimmer() {
        binding?.shimmerPlaceholder?.shimmerSettings?.stopShimmer()
    }

    private fun checkSelectedRegion() {
        userStorage.getUserRegion().name.notNullOrEmpty {
            viewModel?.setSelectedRegion(it)
        }
    }

    private fun checkSelectedVertical() {
        userStorage.getUserVerticalType().name.notNullOrEmpty {
            viewModel?.setSelectedVertical(it)
        }
    }

    private fun openDatePickedDialog() {
        val maxDate = System.currentTimeMillis()
        val minimumCalendar = Calendar.getInstance()
        minimumCalendar.add(Calendar.DATE, DateConstant.MIN_30_DAYS)
        val minDate = minimumCalendar.timeInMillis

        val datePicker = MyDatePickerDialog.newInstance(this, userStorage.getBatchDate(), minDate, maxDate,this)
        datePicker.show(supportFragmentManager, AppConstant.DIALOG_TAG.BATCH_DATE_DIALOG)
    }

    private fun initExtras() {
        val bundleData: String? = intent.getStringExtra(IntentConstant.SETTINGS_GENERATE)

        if(bundleData.equals(AppConstant.GENERATE_BATCH)) {
            setupToolbar(string(R.string.generate_title))
            viewModel?.showGeneratePage()
        } else {
            setupToolbar(string(R.string.settings_title))
            viewModel?.showSettingsPage()
        }
    }

    override fun onDateSet(view: DatePicker?, year: Int, monthOfYear: Int, dayOfMonth: Int) {
        viewModel?.setBatchDate(year, monthOfYear, dayOfMonth)
    }

    @Subscribe
    fun onBtnGenerateBatchClicked(event: SettingsUIEvent.OnBtnGenerateBatchClicked) {
        val model = AlertDialogViewModel()
                .setTitle(string(R.string.settings_dialog_generate_batch_title))
                .setBody(string(R.string.settings_dialog_generate_batch_content))
                .setBatchGeneration(event.region, event.verticalType, event.timeGeneration)
                .setLeftButtonText(string(R.string.cancel_label))
                .setRightButtonText(string(R.string.settings_button_generate_batch))
                .setRightButtonClickListener(object : AlertDialogViewModel.OnClickListener {
                    override fun onClick() {
                        viewModel?.generateBatch(event.timeGeneration.orEmpty())
                        eventBus.post(DismissDialogEvent(AlertDialogViewModel.TAG))
                    }
                })
        BaseDialogFragment.setViewModelAndLayoutId(model, R.layout.alert_dialog_batch_generation).show(supportFragmentManager, AlertDialogViewModel.TAG)
    }

    @Subscribe
    fun onBatchIsGenerated(event: SettingsUIEvent.OnBatchIsGenerated) {
        event.message.notNullOrEmpty { showShortToast(it) }
        // Use onBackPressedDispatcher instead of onBackPressed()
        onBackPressedDispatcher.onBackPressed()
    }

    @Subscribe
    fun onSuccessFetchBatchConfigs(event: SettingsNetworkEvent.OnSuccessFetchBatchConfigs) {
        stopRegionAndVerticalTypeShimmer()
        checkSelectedRegion()
        checkSelectedVertical()
        viewModel?.showAllSection()
    }

    @Subscribe
    fun onNullGenerationTime(event: SettingsUIEvent.OnNullGenerationTime) {
        event.message?.let { message ->
            val model = AlertDialogViewModel()
                    .setTitle(string(R.string.settings_failed_fetch_batch_config_title))
                    .setBody(message)
                    .setRightButtonText(string(R.string.ok_button))
                    .setRightButtonClickListener(object : AlertDialogViewModel.OnClickListener {
                        override fun onClick() {
                            eventBus.post(DismissDialogEvent(AlertDialogViewModel.TAG))
                        }
                    })

            BaseDialogFragment
                    .setViewModelAndLayoutId(model, R.layout.alert_dialog)
                    .setIsCancelable(false)
                    .show(supportFragmentManager, AlertDialogViewModel.TAG)
        }
    }

    @Subscribe
    fun onErrorFetchBatchConfigs(event: SettingsNetworkEvent.OnErrorFetchBatchConfigs) {
        event.message?.let {
            val model = AlertDialogViewModel()
                    .setTitle(string(R.string.settings_failed_fetch_batch_config_title))
                    .setBody(it)
                    .setRightButtonText(string(R.string.ok_button))
                    .setRightButtonClickListener(object : AlertDialogViewModel.OnClickListener {
                        override fun onClick() {
                            eventBus.post(DismissDialogEvent(AlertDialogViewModel.TAG))
                            // Use onBackPressedDispatcher instead of onBackPressed()
                            onBackPressedDispatcher.onBackPressed()
                        }
                    })

            BaseDialogFragment
                    .setViewModelAndLayoutId(model, R.layout.alert_dialog)
                    .setIsCancelable(false)
                    .show(supportFragmentManager, AlertDialogViewModel.TAG)
        }
    }

    @Subscribe
    fun onRadioButtonBatchConfigClicked(event: SettingsUIEvent.OnRadioButtonClicked) {
        event.viewModel.notNull {
            when(event.buttonType) {
                AppConstant.CONFIG_TYPE.REGION -> viewModel?.setSelectedRegion(it.name.get())
                AppConstant.CONFIG_TYPE.VERTICAL -> viewModel?.setSelectedVertical(it.name.get())
            }
        }
    }

    @Subscribe
    fun onBatchDateClicked(event: SettingsUIEvent.OnBatchDateClicked) {
        openDatePickedDialog()
    }

    @Subscribe
    fun onApplyFilterClicked(event: SettingsUIEvent.OnApplyFilterClicked) {
        onBackPressedDispatcher.onBackPressed()
    }
}