package co.styletheory.ops.outbound.android.model.request

import com.google.gson.annotations.SerializedName

/**
 * styletheory-ops-outbound-android
 * Created by dwiap<PERSON>to on 29 November 2017.
 * Description
 *
 * <EMAIL>
 */

class AttachPhotosBody {
    @SerializedName("data")
    val data = DataBody()
    inner class DataBody{
        @SerializedName("type")
        val type: String = "image"
        @SerializedName("domain")
        val domain: String = "Warehouse"
        @SerializedName("context")
        val context: String = "items"
        @SerializedName("attributes")
        val attribute = Attribute()
    }
    inner class Attribute{
        @SerializedName("urls")
        val urls = mutableListOf<String>()
    }
}