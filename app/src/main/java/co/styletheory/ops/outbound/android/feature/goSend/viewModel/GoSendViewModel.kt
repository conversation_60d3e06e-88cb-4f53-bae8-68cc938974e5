package co.styletheory.ops.outbound.android.feature.goSend.viewModel

import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.LogisticProvider
import org.joda.time.DateTime

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 11 June 2018.
 * Description
 *
 * <EMAIL>
 */
interface GoSendViewModel : ViewModel {

    var logisticProvider: LogisticProvider?
    var selectedDate: DateTime?

    fun onRadioButtonSelected(position: Int)
}