package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.model.response.CheckSwapAvailabilityResponse
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import retrofit2.Call
import retrofit2.Retrofit

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 20 November 2017.
 * Description
 *
 * <EMAIL>
 */
class CheckSwapItemAvailabilityResource constructor(
        private val styleId: String,
        private val size: String,
        private val status: ProductStatus
) : GraphQLResource<GraphQLData<CheckSwapAvailabilityResponse>>() {

    override fun graphQLRequest(): GraphQLRequest = GraphQLRequestList.CHECK_SWAP_ITEM_AVAILABILITY

    override fun bodyParameter(): MutableMap<String, Any> {
        bodyParameter["styleId"] = styleId
        bodyParameter["labelSize"] = size
        bodyParameter["status"] = status.text
        return bodyParameter
    }

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<CheckSwapAvailabilityResponse>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.checkSwapItemAvailability(uri, createRequestBody()))
    }
}