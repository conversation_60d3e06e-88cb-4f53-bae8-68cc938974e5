package co.styletheory.ops.outbound.android.feature.scanBarcodeBottomSheet.viewModel.impl

import co.styletheory.ops.outbound.android.feature.scanBarcodeBottomSheet.event.ScanBarcodeBottomSheetUIEvent
import co.styletheory.ops.outbound.android.feature.scanBarcodeBottomSheet.viewModel.ScanBarcodeBottomSheetViewModel
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.listener.OnKeyListener
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.model.SwapItem
import javax.inject.Inject

/**
 * Created by Yoga C. Pranata on 13/05/20.
 * Android Engineer
 */
class ScanBarcodeBottomSheetViewModelImpl @Inject constructor() : BaseViewModel<ScanBarcodeBottomSheetViewModelImpl>(), ScanBarcodeBottomSheetViewModel, OnKeyListener {

    init {
        viewModelClass = ScanBarcodeBottomSheetViewModelImpl::class
    }

    override val scannedRfidText = ObservableString()
    override val onEditTextKeyListener: OnKeyListener = this

    override var swapItem: SwapItem = SwapItem()

    override fun onKeyTapped() {
        if(featureFlagUtil.isBarcodeSettingIsOn()) {
            eventBus.post(ScanBarcodeBottomSheetUIEvent.OnScannedRfid(swapItem, scannedRfidText.get()))
        }
    }

    override fun onClose() {
        eventBus.post(ScanBarcodeBottomSheetUIEvent.OnCloseClicked)
    }

}