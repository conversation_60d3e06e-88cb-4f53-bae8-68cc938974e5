package co.styletheory.ops.outbound.android.feature.batch.viewModel

import co.styletheory.ops.outbound.android.general.adapter.impl.MultipleTypeRecyclerViewAdapter
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.util.Result

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 31 October 2017.
 * Description
 *
 * <EMAIL>
 */
interface BatchViewModel : ViewModel, MultipleTypeRecyclerViewAdapter.MultipleTypeRecycleViewSource{
    fun getBatchListItems() : ArrayList<BatchListViewModel>
    fun setBatchStatus(batchStatus: BatchStatus)
    fun batchStatus() : BatchStatus
    fun fetchBatch(callback: Result<Void?, String?>?)
    fun fetchBatch(isRefresh: <PERSON><PERSON><PERSON>, callback: Result<Void?, String?>?)
    fun refreshBatch(callback: Result<Void?, String?>?)
}