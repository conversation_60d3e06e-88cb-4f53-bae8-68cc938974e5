package co.styletheory.ops.outbound.android.feature.errorSwapBottomSheet.viewModel

import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import com.styletheory.android.mvvm.general.binding.ObservableText

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 23/06/20.
 */
interface ErrorSwapItemBottomSheetViewModel : ViewModel {

    val errorMessage: ObservableText
    val errorButtonLabel: ObservableText
    var swapType: String

    fun bindError(error: String)
    fun onClose()
    fun onClickErrorButton()
}