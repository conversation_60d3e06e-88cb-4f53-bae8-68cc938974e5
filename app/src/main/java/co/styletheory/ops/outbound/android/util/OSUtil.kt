package co.styletheory.ops.outbound.android.util

import android.os.Build
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 10/11/17.
 * add by j<PERSON><PERSON><PERSON> on 31/01/2019
 */
class OSUtil @Inject constructor() {

    fun isAboveOrEqualsAndroidL(): <PERSON><PERSON><PERSON> {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP
    }

    fun isAboveAndroidM(): <PERSON><PERSON><PERSON> {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.M
    }

    fun isAboveAndroidN(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.N
    }

    fun isAboveOrEqualsAndroidO(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.O
    }

    fun isAboveAndroidR(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.R
    }

    fun isAboveOrEqualsAndroidTIRAMISU(): Bo<PERSON>an {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU
    }

    fun isAboveOrEqualsAndroid15(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE
    }

    /**
     * Checks if the device is running Android 15 (SDK 35) or higher
     * This is used for edge-to-edge display compatibility
     *
     * @return true if the device is running Android 15 or higher
     */
    fun isAndroid15OrHigher(): Boolean {
        // Android 15 is SDK 35 (VANILLA_ICE_CREAM)
        return Build.VERSION.SDK_INT >= 35
    }
}