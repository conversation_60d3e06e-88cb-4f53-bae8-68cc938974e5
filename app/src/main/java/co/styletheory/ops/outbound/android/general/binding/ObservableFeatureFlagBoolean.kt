package co.styletheory.ops.outbound.android.general.binding

import androidx.databinding.ObservableBoolean
import co.styletheory.ops.outbound.android.StyletheoryOpsApplication
import co.styletheory.ops.outbound.android.util.featureflag.FeatureFlag
import co.styletheory.ops.outbound.android.util.featureflag.FeatureFlagBooleanListener
import co.styletheory.ops.outbound.android.util.featureflag.FeatureFlagToggleListener
import javax.inject.Inject

/**
 * Created by Yoga C. Pranata on 02/04/19.
 * Android Engineer
 */
class ObservableFeatureFlagBoolean constructor(val key: String, defaultValue: Boolean = false, var callback: FeatureFlagBooleanListener? = null) : ObservableBoolean() {
    @Inject
    @JvmField
    var featureFlag: FeatureFlag? = null

    init {
        StyletheoryOpsApplication.directInject(this)

        val toggleListener = object : FeatureFlagToggleListener {
            override fun onToggleChange(toggleState: Boolean) {
                if(get() != toggleState) {
                    set(toggleState)
                    callback?.onToggleChange(key, toggleState)
                }
            }
        }

        set(featureFlag?.isToggleOn(key, toggleListener, defaultValue) == true)
    }

    fun unregisterToggleListener() {
        featureFlag?.turnOffToggleListener(key)
    }

}