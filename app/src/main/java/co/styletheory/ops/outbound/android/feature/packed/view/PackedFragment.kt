package co.styletheory.ops.outbound.android.feature.packed.view

import android.app.DatePickerDialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.DatePicker
import androidx.fragment.app.Fragment
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.PackedFragmentBinding
import co.styletheory.ops.outbound.android.feature.goSend.event.GojekSelectedTabChangeEvent
import co.styletheory.ops.outbound.android.feature.goSend.view.GoSendFragment
import co.styletheory.ops.outbound.android.feature.packed.event.PackedItemNetworkEvent
import co.styletheory.ops.outbound.android.feature.packed.event.PackedItemUIEvent
import co.styletheory.ops.outbound.android.feature.packed.event.RefreshShipmentListEvent
import co.styletheory.ops.outbound.android.feature.packed.event.ShipmentDateClickEvent
import co.styletheory.ops.outbound.android.feature.packed.event.UpdateTotalSelectedShipmentEvent
import co.styletheory.ops.outbound.android.feature.packed.viewModel.PackedViewModel
import co.styletheory.ops.outbound.android.general.adapter.PagerSource
import co.styletheory.ops.outbound.android.general.adapter.impl.PagerAdapter
import co.styletheory.ops.outbound.android.general.base.BaseFragment
import co.styletheory.ops.outbound.android.general.dialog.MyDatePickerDialog
import co.styletheory.ops.outbound.android.general.event.CallEvent
import co.styletheory.ops.outbound.android.util.DateUtil
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.util.notNull
import com.google.android.material.tabs.TabLayout
import org.greenrobot.eventbus.Subscribe
import org.joda.time.DateTime
import java.util.Calendar
import javax.inject.Inject

/**
 * Created by giorgygunawan on 11/22/17.
 */
class PackedFragment : BaseFragment<PackedFragmentBinding, PackedViewModel>(), DatePickerDialog.OnDateSetListener, PagerSource {

    @Inject
    lateinit var pagerAdapter: PagerAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if(viewModel == null) {
            fragmentComponent?.inject(this)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return bindContentView(inflater, container, R.layout.packed_fragment)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupViewPager()
        setupEnterKeyListener()
        if(viewModel?.totalLogisticProvider == 0) {
            showProgressDialog()
            viewModel?.fetchLogisticProviders(object : Result<Void?, String?> {
                override fun success(success: Void?) {
                    dismissProgressDialog()
                    pagerAdapter.notifyDataSetChanged()
                }

                override fun failure(error: String?) {
                    dismissProgressDialog()
                    showShortToast(error ?: string(R.string.err_something_when_wrong))
                }

            })
        }
    }

    private fun setupEnterKeyListener() {
        binding?.editTextSearch?.setOnEditorActionListener { _, actionId, _ ->
            if(actionId == EditorInfo.IME_ACTION_DONE) {
                sendEventRefreshShipmentList()
            }
            false
        }
    }

    private fun setupViewPager() {
        pagerAdapter = PagerAdapter(childFragmentManager)
        pagerAdapter.source = this
        binding?.notNull {
            it.viewPager.adapter = pagerAdapter
            it.tabs.setupWithViewPager(binding?.viewPager)
            it.tabs.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabUnselected(tab: TabLayout.Tab) {
                    //Do Nothing
                }

                override fun onTabReselected(tab: TabLayout.Tab) {
                    //Do Nothing
                }

                override fun onTabSelected(tab: TabLayout.Tab) {
                    viewModel?.tabPosition = tab.position
                    viewModel?.showGojekActionButtons(viewModel?.isGoJek(tab.position) == true, false)
                    eventBus.post(RefreshShipmentListEvent(DateTime(viewModel?.selectedCalendar?.time), viewModel?.searchQuery?.get()))
                }
            })
        }

    }

    private fun openDatePickedDialog() {
        context?.let { ctx ->
            val calendar = viewModel?.selectedCalendar ?: Calendar.getInstance()
            val datePicker = MyDatePickerDialog.newInstance(ctx, calendar, this)
            datePicker.show(childFragmentManager, "dpd")
        }
    }

    override fun onDateSet(view: DatePicker?, year: Int, monthOfYear: Int, dayOfMonth: Int) {
        viewModel?.selectedCalendar?.set(Calendar.YEAR, year)
        viewModel?.selectedCalendar?.set(Calendar.MONTH, monthOfYear)
        viewModel?.selectedCalendar?.set(Calendar.DAY_OF_MONTH, dayOfMonth)
        viewModel?.shipmentDate?.set(DateUtil.createDateStringFrom(viewModel?.selectedCalendar?.time, "dd MMM yyyy"))
        sendEventRefreshShipmentList()
    }

    private fun sendEventRefreshShipmentList() {
        val dateTime = DateTime(viewModel?.selectedCalendar?.time)
        eventBus.post(RefreshShipmentListEvent(dateTime, viewModel?.searchQuery?.get().orEmpty()))
    }

    //region EVENT BUS
    @Suppress("UNUSED_PARAMETER")
    @Subscribe
    fun onShipmentDateClicked(event: ShipmentDateClickEvent) {
        openDatePickedDialog()
    }

    @Subscribe
    fun onTotalSelectedPackedShipmentEvent(event: UpdateTotalSelectedShipmentEvent) {
        viewModel?.updateTotalSelectedPackedShipment(event.totalSelected)
    }

    @Subscribe
    fun onGojekSelectedTabChangeEvent(event: GojekSelectedTabChangeEvent) {
        viewModel?.showGojekActionButtons(true, event.isOrdered)
    }

    @Subscribe
    fun onCallEvent(event: CallEvent) {
        navigator.phoneCall(event.phoneNumber)
    }

    @Subscribe
    fun onFailedMarkShipmentAsReceived(event: PackedItemNetworkEvent.OnFailedMarkShipmentAsReceived) {
        showShortToast(event.errorMessage)
    }
    //endregion

    //region Pager Source
    override val totalViewCount: Int get() = viewModel?.totalLogisticProvider ?: 0

    override fun getItemTypeAtPosition(position: Int): Fragment {
        val logisticProvider = viewModel?.logisticItems?.get(position)
        val selectedDate = DateTime(viewModel?.selectedCalendar?.time)
        return if(viewModel?.isGoJek(position) == true)
            GoSendFragment.newInstance(logisticProvider, selectedDate)
        else
            PackedChildFragment.newInstance(logisticProvider, selectedDate)
    }

    override fun cacheFragment(): Boolean = true
    override fun getPageTitleAtPosition(position: Int): CharSequence {
        return viewModel?.getTitleTabAtIndex(position) ?: ""
    }
    //endregion
}