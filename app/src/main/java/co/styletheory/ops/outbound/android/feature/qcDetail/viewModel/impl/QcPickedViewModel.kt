package co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.impl

import android.view.View
import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableInt
import co.styletheory.android.network.core.RequestResult
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.backlogDetail.event.BacklogItemSwapClickEvent
import co.styletheory.ops.outbound.android.feature.photoManager.event.TakePictureEvent
import co.styletheory.ops.outbound.android.feature.photoManager.viewModel.AttachImageViewModel
import co.styletheory.ops.outbound.android.feature.qcDetail.event.QcDetailUIEvent
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseInjectedViewModel
import co.styletheory.ops.outbound.android.injection.scope.PerInjectedViewModel
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.Color
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.model.enums.ResellingInventoryType
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.BatchItemResource
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.util.GeneralCallback
import co.styletheory.ops.outbound.android.util.Next
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.util.impl.StringUtil
import co.styletheory.ops.outbound.android.viewModelComponent.ColorItemViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.PhotoWithLabelViewModel
import com.styletheory.android.mvvm.general.binding.ObservableText
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 01 November 2017.
 * Description
 *
 * <EMAIL>
 */

@PerInjectedViewModel
class QcPickedViewModel @Inject constructor() : BaseInjectedViewModel() {
    var batchItem: BatchItem? = null
    var batchId: String = ""
    var rackSection: String = ""
    var rackName: String = ""
    var shipmentId: String = ""
    var boxId: String = ""
    var customerEmail: String = ""
    var refreshCompleteState: GeneralCallback? = null
    var removeSwappedItem: Next<QcPickedViewModel?>? = null

    val itemName = ObservableString()
    val itemSize = ObservableString()
    val errorMessage = ObservableString()
    val errorVisibility = ObservableInt(View.GONE)
    val attachementVisibility = ObservableInt(View.VISIBLE)
    val qaItemImages = ObservableArrayList<AttachImageViewModel>()
    val failCategory = ObservableString()
    val failReason = ObservableString()
    val failReasonVisibility = ObservableInt(View.GONE)
    val rack = ObservableString()
    val soldTo = ObservableString()
    val soldToVisibility = ObservableInt(View.GONE)
    val category = ObservableString()
    val parts = ObservableString()
    val pass = ObservableBoolean()
    val fail = ObservableBoolean()
    val showOverlayLoading = ObservableInt(View.GONE)
    val notPickedUp = ObservableInt(View.GONE)
    val notes = ObservableString()
    val productOrder = ObservableString()
    val colorItems = ObservableArrayList<ColorItemViewModel>()
    val detachable = ObservableString()
    val rfidCode: ObservableText = ObservableText().apply {
        isHtml = true
    }
    val isButtonEnabled = ObservableBoolean(true)
    val scannerIconVisibility = ObservableInt(View.GONE)
    val rfidCodeVisibility = ObservableInt(View.GONE)
    val partsVisibility = ObservableInt(View.GONE)
    val detachableVisibility = ObservableInt(View.GONE)
    val swapVisibility = ObservableInt(View.GONE)
    val isAccuracySwapEnabled = ObservableBoolean(true)
    val accuracySwapButtonVisibility = ObservableInt(View.VISIBLE)
    val oldSwapSectionVisibility = ObservableInt(View.GONE)

    @Inject
    lateinit var dataService: DataService

    @Inject
    lateinit var userStorage: UserStorage

    @Inject
    lateinit var errorResponse: ErrorResponse

    @Inject
    lateinit var photoWithLabelVM: PhotoWithLabelViewModel

    fun bindViewModel(batchItem: BatchItem, rackSection: String, rackName: String, shipmentId: String, boxId: String, customerEmail: String) {
        this.batchItem = batchItem
        this.rackSection = rackSection
        this.rackName = rackName
        this.shipmentId = shipmentId
        this.boxId = boxId
        this.customerEmail = customerEmail

        itemName.set(batchItem.style?.designer?.name)
        itemSize.set(batchItem.labelSize)
        soldTo.set(batchItem.label)
        productOrder.set("[${batchItem.order}]")
        category.set(batchItem.style?.primaryCategory)
        parts.set(StringUtil.stringArraySeparateWithComma(batchItem.parts))
        failReason.set(batchItem.qaFailedReason)
        failCategory.set(if(batchItem.qaFailedCategory.isNullOrEmpty()) string(R.string.fail_label) else batchItem.qaFailedCategory)
        detachable.set(batchItem.additionalProperties?.detachableDetail)
        rfidCode.set(batchItem.rfidFormatted)
        notes.set(batchItem.notes)
        colorItems.clear()
        for(item in batchItem.style?.colors?.filter { it.colorCode.isNotEmpty() }.orEmpty()) {
            addColorItem(item)
        }

        setupPhotoWithLabel(batchItem)
        setQAPickedState(batchItem.status)
        setupRackName(rackName, rackSection)
        setupAccuracySwapButton()
        setupOldSwapVisibility()
        setupResellingSwapVisibility(batchItem)
        updateView()
    }

    fun showApparelItems() {
        partsVisibility.set(View.VISIBLE)
    }

    fun showBagsItems() {
        detachableVisibility.set(View.VISIBLE)
    }

    fun showRfidCode() {
        rfidCodeVisibility.set(View.VISIBLE)

        if(featureFlagUtil.isBarcodeSettingIsOn()) {
            setupEnableButton(false)
            scannerIconVisibility.set(View.VISIBLE)
        }
    }

    fun setupEnableButton(isEnabled: Boolean) {
        isButtonEnabled.set(isEnabled)
    }

    fun setupEnableAccuracySwapButton(isEnabled: Boolean) {
        isAccuracySwapEnabled.set(isEnabled)
    }

    fun showSwapButton() {
        swapVisibility.set(View.VISIBLE)
    }

    fun addColorItem(color: Color) {
        val model = ColorItemViewModel()
        model.colorName.set(color.name)
        model.colorHex.set(color.colorCode)
        colorItems.add(model)
    }

    fun setQAPickedState(status: ProductStatus?) {
        when(status) {
            ProductStatus.QA_PASSED -> {
                pass.set(true)
                fail.set(false)
            }

            ProductStatus.QA_FAILED -> {
                pass.set(false)
                fail.set(true)
            }

            else -> {
                if(!userStorage.isUserRegionID() && status == ProductStatus.PHOTO_QA_DONE) {
                    pass.set(true)
                    fail.set(false)
                } else {
                    pass.set(false)
                    fail.set(false)
                }
            }
        }
    }

    private val addNotesResult = object : Result<List<String>, String?> {
        override fun success(success: List<String>) {
            addNotes(success[0], success[1])
        }

        override fun failure(error: String?) {
            errorMessage.set(error ?: string(R.string.err_failed_to_update_status))
            errorVisibility.set(View.VISIBLE)
        }
    }

    fun addNotes(category: String, reason: String) {
        fail.set(!fail.get())
        pass.set(false)

        failCategory.set(if(category.isNullOrEmpty()) string(R.string.fail_reason_label) else category)

        failReason.set(reason)
        failReasonVisibility.set(View.VISIBLE)
        refreshCompleteState?.callback()
    }

    fun passClick() {
        if(!pass.get()) {

            val status = if(!userStorage.isUserRegionID()) {
                ProductStatus.PHOTO_QA_DONE
            } else {
                ProductStatus.QA_PASSED
            }

            val resource = BatchItemResource(batchId, shipmentId, listOf(batchItem?.id
                   .orEmpty()), status)
            showOverlayLoading.set(View.VISIBLE)
            dataService.updateBatchItemStatus(resource, updateBatchCallback)
        }
    }

    fun updateBatchSuccess() {
        pass.set(!pass.get())
        fail.set(false)

        errorMessage.set("")
        errorVisibility.set(View.GONE)
        showOverlayLoading.set(View.GONE)
        refreshCompleteState?.callback()
    }

    val updateBatchCallback = RequestResult<GraphQLData<Void>, String> {
        onSuccess { updateBatchSuccess() }
        onError {
            errorMessage.set(errorResponse.getErrorBodyDescription(it))
            showOverlayLoading.set(View.GONE)
            errorVisibility.set(View.VISIBLE)
        }
    }

    fun failReasonClick(view: View) {
        if(!fail.get()) {
            val swapItem = SwapItem(batchItem, boxId, shipmentId, customerEmail)
            eventBus.post(QcDetailUIEvent.FailReasonButtonClick(batchId, batchItem?.id, shipmentId, addNotesResult, view, swapItem))
        }
    }

    fun swapClick() {
        eventBus.post(BacklogItemSwapClickEvent(shipmentId, boxId, customerEmail, batchItem, swapCallback))
    }

    val swapCallback = object : Next<BatchItem> {
        override fun apply(t: BatchItem) {
            removeSwappedItem?.apply(this@QcPickedViewModel)
            refreshCompleteState?.callback()
        }
    }

    fun cameraClick() {
        eventBus.post(TakePictureEvent(shipmentId, batchItem))
    }

    fun setupRackName(rackName: String, rackSection: String) {
        if(userStorage.isUserOnDemandAndRegionID()) {
            rack.set(string(R.string.rack_name_on_demand).format(rackName, rackSection))
        } else {
            rack.set(string(R.string.rack_name_regular).format(rackName, rackSection))
        }
    }

    fun setupAccuracySwapButton() {
        if(!userStorage.isVerticalTypeApparel()
                || !featureFlagUtil.isAccuracySwapIsOn()
                || batchItem?.isResellingItem() == true) {
            isAccuracySwapEnabled.set(false)
            accuracySwapButtonVisibility.set(View.GONE)
        }
    }

    fun setupOldSwapVisibility() {
        if(featureFlagUtil.isOldQualityCheckSwapOn()) {
            oldSwapSectionVisibility.set(View.VISIBLE)
        }
    }

    fun setupResellingSwapVisibility(batchItem: BatchItem?) {
        when {
            featureFlagUtil.isQualitySwapResellingDisabled(batchItem) || batchItem?.resellingInventoryType == ResellingInventoryType.WAREHOUSE.type -> {
                swapVisibility.set(View.GONE)
            }
            else -> {
                showSwapButton()
            }
        }
    }

    fun accuracySwapClick() {
        val swapItem = SwapItem(batchItem, boxId, shipmentId)
        eventBus.post(QcDetailUIEvent.OnAccuracySwapButtonClicked(swapItem))
    }

    fun updateView() {
        if(soldTo.isEmpty()) {
            soldToVisibility.set(View.GONE)
        } else {
            soldToVisibility.set(View.VISIBLE)
        }

        if(qaItemImages.size > 0) {
            attachementVisibility.set(View.GONE)
        } else {
            attachementVisibility.set(View.VISIBLE)
        }

        if(failReason.isEmpty()) {
            failReasonVisibility.set(View.GONE)
        } else {
            failReasonVisibility.set(View.VISIBLE)
        }
    }

    fun setPhotoWithLabelVM(): PhotoWithLabelViewModel = photoWithLabelVM
    private fun setupPhotoWithLabel(batchItem: BatchItem) {
        photoWithLabelVM.bindPhotoWithLabel(batchItem.style?.imageUrl, batchItem.resellingInventoryType)
    }

}