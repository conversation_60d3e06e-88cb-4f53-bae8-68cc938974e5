package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import retrofit2.Call
import retrofit2.Retrofit

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 11/15/17.
 *
 */
class CreateImageUrlResource(private val shipmentId: String) : GraphQLResource<GraphQLData<String>>() {

    override fun graphQLRequest(): GraphQLRequest = GraphQLRequestList.CREATE_UPLOAD_PRESIGNED_URL

    override fun bodyParameter(): MutableMap<String, Any> {
        bodyParameter["shipmentId"] = shipmentId
        return bodyParameter
    }

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<String>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.fetchImageUploadUrl(uri, createRequestBody()))

    }
}