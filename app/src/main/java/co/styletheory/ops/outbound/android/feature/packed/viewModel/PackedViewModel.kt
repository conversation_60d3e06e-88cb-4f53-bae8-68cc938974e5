package co.styletheory.ops.outbound.android.feature.packed.viewModel

import androidx.databinding.ObservableInt
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.LogisticProvider
import co.styletheory.ops.outbound.android.util.Result
import java.util.*

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 11/22/17.
 *
 */
interface PackedViewModel : ViewModel {

    var tabPosition: Int
    val totalLogisticProvider: Int
    val leftButtonText: ObservableString
    val rightButtonText: ObservableString
    val shipmentDate: ObservableString
    val searchQuery: ObservableString
    val gojekToBeOrderedActionVisibility: ObservableInt
    val selectedCalendar: Calendar
    val logisticItems: ArrayList<LogisticProvider>

    fun getTitleTabAtIndex(index: Int): CharSequence?
    fun isGoJek(position: Int): Boolean
    fun showGojekActionButtons(show: <PERSON><PERSON><PERSON>, ordered: <PERSON>ole<PERSON>)

    fun addLogisticProvider(logisticProviders: List<LogisticProvider>)
    fun fetchLogisticProviders(callback: Result<Void?, String?>?)
    fun updateTotalSelectedPackedShipment(totalSelected: Int)

    fun onSelectAllClicked()
    fun shipmentDateClicked()
    fun onOrderGosendClicked()
}