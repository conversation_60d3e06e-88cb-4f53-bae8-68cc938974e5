package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import retrofit2.Call
import retrofit2.Retrofit

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 20 November 2017.
 * Description
 *
 * <EMAIL>
 */
class BeginBatchResource constructor(
        private val batchId: String,
        private val type: Type
) : GraphQLResource<GraphQLData<Void>>() {

    enum class Type constructor(val text: String) {
        PICK("Picking"), QA("QA"), PHOTO("Photo"), PACKING("Packing")
    }

    override fun bodyParameter(): MutableMap<String, Any> {
        bodyParameter.put("id", batchId)
        bodyParameter.put("processStatus", type.text)
        return bodyParameter
    }

    override fun graphQLRequest(): GraphQLRequest = GraphQLRequestList.UPDATE_BATCH_PROCESS_STATUS

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<Void>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.beginBatch(uri, createRequestBody()))
    }
}