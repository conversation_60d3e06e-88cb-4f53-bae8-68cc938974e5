package co.styletheory.ops.outbound.android.feature.packed.viewModel.impl

import androidx.databinding.ObservableArrayList
import co.styletheory.android.network.core.RequestResult
import co.styletheory.ops.outbound.android.feature.packed.event.PackedItemNetworkEvent
import co.styletheory.ops.outbound.android.feature.packed.event.PackedItemUIEvent
import co.styletheory.ops.outbound.android.feature.packed.event.UpdateTotalSelectedShipmentEvent
import co.styletheory.ops.outbound.android.feature.packed.viewModel.PackedChildViewModel
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.model.LogisticProvider
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.CancelGojekResource
import co.styletheory.ops.outbound.android.resources.OrderGojekResource
import co.styletheory.ops.outbound.android.resources.SendShipmentResource
import co.styletheory.ops.outbound.android.resources.ShipmentListResource
import co.styletheory.ops.outbound.android.resources.ShipmentReceivedResource
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.util.Next
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.util.notNull
import org.joda.time.DateTime
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 16 January 2018.
 * Description
 *
 * <EMAIL>
 */
class PackedChildViewModelImpl @Inject constructor() : BaseViewModel<PackedChildViewModelImpl>(), PackedChildViewModel {

    init {
        viewModelClass = PackedChildViewModelImpl::class
    }

    @Inject
    lateinit var dataService: DataService

    @Inject
    lateinit var errorResponse: ErrorResponse

    override val packedItems: ObservableArrayList<PackedItemViewModel> = ObservableArrayList()
    var isAllSelected = false
    val shipmentIds = mutableSetOf<String>()
    val selectedItemIds = mutableSetOf<String>()

    override var logisticProvider: LogisticProvider? = null
    override var shipmentDate: DateTime = DateTime.now()
    override var searchQuery: String = ""
    override var logisticOrdered: Boolean? = null

    override fun totalSelectedShipment(): Int = packedItems.count { it.isSelected.get() }

    override fun selectAllItem() {
        isAllSelected = true
        selectedItemIds.clear()
        for(it in packedItems) {
            if(!it.isSelected.get()) {
                isAllSelected = false
            }
        }
        if(isAllSelected) {
            for(it in packedItems) {
                it.isSelected.set(false)
            }
        } else {
            for(it in packedItems) {
                if(!it.isSelected.get()) {
                    it.isSelected.set(true)
                }
            }
            selectedItemIds.addAll(shipmentIds)
        }
        eventBus.post(UpdateTotalSelectedShipmentEvent(selectedItemIds.size))
    }

    override fun fetchFirstPage(callback: Result<Void?, String?>?) {
        fetchShipment(callback, 1, true)
    }

    override fun fetchNextPage(callback: Result<Void?, String?>?, page: Int) {
        fetchShipment(callback, page)
    }

    override fun sendShipmentBox(callback: Result<String?, String?>?) {
        dataService.sendShipment(
            SendShipmentResource(selectedItemIds.toList(), shipmentDate),
            RequestResult {
                onSuccess {
                    callback?.success(it?.data?.result?.meta?.get("message")?.toString())
                }
                onError { callback?.failure(errorResponse.getErrorBodyDescription(it)) }
            }
        )
    }

    override fun orderGojek(callback: Result<String?, String?>?) {
        dataService.orderGojekShipments(
            OrderGojekResource(shipmentDate, selectedItemIds.toList()),
            RequestResult {
                onSuccess { callback?.success(it?.data?.result?.meta?.get("message")?.toString()) }
                onError { callback?.failure(errorResponse.getErrorBodyDescription(it)) }
            }
        )
    }

    override fun cancelGojek(callback: Result<Void?, String?>?) {
        dataService.cancelGojekShipments(
            CancelGojekResource(DateTime.now(), selectedItemIds.toList()),
            RequestResult {
                onSuccess { callback?.success(null) }
                onError { callback?.failure(errorResponse.getErrorBodyDescription(it)) }
            }
        )
    }

    private fun fetchShipment(callback: Result<Void?, String?>?, page: Int, isRefresh: Boolean = false) {
        val resource = ShipmentListResource(
            logisticProvider?.id
                .orEmpty(), shipmentDate, page, searchQuery, logisticOrdered
        )
        dataService.fetchShipmentByLogisticProvider(
            resource,
            RequestResult {
                onSuccess {
                    refreshData(isRefresh)
                    it?.data?.result.notNull { shipment ->
                        mapPackedItems(shipment.shipments)
                    }
                    callback?.success(null)
                }
                onError { callback?.failure(errorResponse.getErrorBodyDescription(it)) }
            }
        )
    }

    fun refreshData(refresh: Boolean) {
        if(refresh) {
            packedItems.clear()
            selectedItemIds.clear()
            shipmentIds.clear()
        }
    }

    fun mapPackedItems(shipments: List<Shipment>) {
        for(it in shipments) {
            val viewModel = PackedItemViewModel()
            viewModel.showTrackingInfo = logisticOrdered
            viewModel.bindViewModel(it)
            viewModel.onItemCheckedCallback = onItemCheckedCallback
            viewModel.isSelected.set(itsContainOnSelectedShipment(it.id))

            if(logisticProvider?.isDoorToDoorLogisticSG() == true
                || logisticProvider?.isDoorToDoorLogisticID() == true
            ) {
                viewModel.hideCheckbox()
            }

            packedItems.add(viewModel)
        }
    }

    fun itsContainOnSelectedShipment(shipmentId: String): Boolean {
        return selectedItemIds.contains(shipmentId)
    }

    private val onItemCheckedCallback = object : Next<PackedItemViewModel> {
        override fun apply(t: PackedItemViewModel) {
            onCheckedItemChange(t.isSelected.get(), t.shipment?.id.orEmpty())
        }
    }

    fun onCheckedItemChange(selected: Boolean, shipmentId: String) {
        if(selected) {
            selectedItemIds.add(shipmentId)
        } else {
            selectedItemIds.remove(shipmentId)
        }
        eventBus.post(UpdateTotalSelectedShipmentEvent(selectedItemIds.size))
    }

    override fun isSameLogisticProvider(providerId: String?): Boolean {
        return providerId == logisticProvider?.id
    }

    override fun markShipmentAsReceived(shipmentId: String) {
        dataService.markShipmentAsReceivedByWarehouse(
            ShipmentReceivedResource(shipmentId),
            RequestResult {
                onSuccess {
                    eventBus.post(PackedItemUIEvent.OnUpdateShipmentAsReceived(shipmentId))
                }
                onError { error ->
                    eventBus.post(PackedItemNetworkEvent.OnFailedMarkShipmentAsReceived(errorResponse.getErrorBodyDescription(error))) }
            })
    }
}