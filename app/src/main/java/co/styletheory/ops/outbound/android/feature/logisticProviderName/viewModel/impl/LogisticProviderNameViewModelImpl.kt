package co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel.impl

import androidx.databinding.ObservableArrayList
import androidx.recyclerview.widget.LinearLayoutManager
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel.LogisticProviderNameViewModel
import co.styletheory.ops.outbound.android.general.recyclerView.RecyclerViewSetting
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.model.LogisticProvider
import javax.inject.Inject

/**
 * Created by <PERSON> on 05/01/22.
 */
class LogisticProviderNameViewModelImpl @Inject constructor() : BaseViewModel<LogisticProviderNameViewModelImpl>(), LogisticProviderNameViewModel {
    override val recyclerViewLogisticName = RecyclerViewSetting()
    override val logisticNameItems = ObservableArrayList<ItemLogisticProviderNameViewModelImpl>()

    override fun bindViewModel(providers: List<LogisticProvider>) {
        setLogisticProviderName(providers)
    }

    override fun bindViewModelProvider(provider: LogisticProvider?) {
        setUpRecycleView()
        logisticNameItems.add(ItemLogisticProviderNameViewModelImpl().bindViewModel(provider))
    }

    private fun setUpRecycleView() {
        recyclerViewLogisticName.layoutId = R.layout.item_logistic_provider_name
        recyclerViewLogisticName.orientation = LinearLayoutManager.HORIZONTAL
        recyclerViewLogisticName.layoutManagerType = RecyclerViewSetting.LayoutManagerType.LINEAR
    }

    private fun setLogisticProviderName(providers: List<LogisticProvider>) {
        setUpRecycleView()
        logisticNameItems.clear()
        for(provider in providers){
            logisticNameItems.add(ItemLogisticProviderNameViewModelImpl().bindViewModel(provider))
        }
    }

}