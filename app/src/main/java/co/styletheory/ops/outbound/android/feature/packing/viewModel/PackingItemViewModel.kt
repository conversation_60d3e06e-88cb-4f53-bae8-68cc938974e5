package co.styletheory.ops.outbound.android.feature.packing.viewModel

import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableInt
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.viewModelComponent.ColorItemViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.PhotoWithLabelViewModel
import com.styletheory.android.mvvm.general.binding.ObservableText

/**
 * Created by <PERSON>inart<PERSON> on 2019-09-23.
 */
interface PackingItemViewModel : ViewModel {
    var batchItem: BatchItem?
    val itemName: ObservableString
    val itemSize: ObservableString
    val rack: ObservableString
    val category: ObservableString
    val parts: ObservableString
    val notes: ObservableString
    val soldTo: ObservableString
    val productOrder: ObservableString
    val itemStatus: ObservableText
    val colorItems: ObservableArrayList<ColorItemViewModel>
    val detachable: ObservableString
    val partsVisibility: ObservableInt
    val detachableVisibility: ObservableInt
    val scannerIconVisibility: ObservableInt
    val checkMarkRfidVisibility: ObservableInt
    val haveScannedRfid: ObservableBoolean
    val itemStatusVisibility: ObservableInt
    val isItemScanned: ObservableBoolean
    val itemPurchasedVisibility: ObservableInt

    fun setPhotoWithLabelVM(): PhotoWithLabelViewModel
    fun bindViewModel(batchItem: BatchItem, rackSection: String, rackName: String)
    fun showApparelItems()
    fun showScannerIcon()
    fun showBagsItems()
    fun handleStatusItem(productStatus: ProductStatus?)
    fun setupItemContainRfid()
    fun setupRackName(rackName: String, rackSection: String)
    fun setupPurchasedItem(batchItem: BatchItem)
}