package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import retrofit2.Call
import retrofit2.Retrofit

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 09 November 2017.
 * Description
 *
 * <EMAIL>
 */

class BatchResource(
        private val batchStatus: BatchStatus,
        private val batchDate: String
) : GraphQLResource<GraphQLData<List<Batch>>>() {

    override fun graphQLRequest(): GraphQLRequest = GraphQLRequestList.BATCH_LIST

    override fun bodyParameter(): MutableMap<String, Any> {
        bodyParameter["batchStatus"] = batchStatus.textName
        bodyParameter["batchCreatedAt"] = batchDate
        return bodyParameter
    }

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<List<Batch>>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.fetchBatch(baseUrl, createRequestBody()))
    }
}