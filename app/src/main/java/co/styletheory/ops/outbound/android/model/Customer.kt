package co.styletheory.ops.outbound.android.model

import org.parceler.Parcel

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 03 November 2017.
 * Description
 *
 * <EMAIL>
 */


@Parcel
data class Customer(
        val id: String = "",
        val name: String = "",
        val email: String = "",
        val isVip: Boolean = false,
        val noPaper: <PERSON>olean = false,
        val noToteBag: Boolean = false,
        val subscriptions: Subscriptions? = null
)