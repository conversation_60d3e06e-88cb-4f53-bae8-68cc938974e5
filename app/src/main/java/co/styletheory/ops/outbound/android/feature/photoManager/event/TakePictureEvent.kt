package co.styletheory.ops.outbound.android.feature.photoManager.event

import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.util.Next

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 23 November 2017.
 * Description
 *
 * <EMAIL>
 */
class TakePictureEvent (val shipmentId: String, val batchItem: BatchItem?, val resultCallback: Next<List<String>>? = null)