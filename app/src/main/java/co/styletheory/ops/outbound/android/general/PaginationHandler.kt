package co.styletheory.ops.outbound.android.general

import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import javax.inject.Inject

@Suppress("UNUSED_PARAMETER")
/**
 * styletheory-ops-outbound-android
 * Created by dwi<PERSON>rian<PERSON> on 18 January 2018.
 * Description
 *
 * <EMAIL>
 */
class PaginationHandler @Inject constructor() : RecyclerView.OnScrollListener() {

    private val VISIBLE_THRESHOLD = 1
    private val incrementBy = 1
    private var onLoadMore: Boolean = true
    var page: Int = 1
    var listener: Listener? = null

    private var lastScrollPositions: IntArray? = null

    fun reset() {
        onLoadMore = true
        this.page = 1
    }

    fun finishLoad() {
        this.onLoadMore = false
    }

    fun failedLoad() {
        this.onLoadMore = false
        if(page > 1) this.page -= incrementBy
    }


    fun incrementPage(value: Int = 1) {
        this.page = page + incrementBy
    }

    fun decrementPage(value: Int = 1) {
        this.page = page - incrementBy
    }

    override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
        super.onScrolled(recyclerView, dx, dy)
        if (dy > 0) {
            val layoutManager = recyclerView.layoutManager
            val lastVisibleItemPosition = getLastVisibleItemPosition(layoutManager)
            val visibleItemCount = layoutManager?.childCount ?: 0
            val totalItemCount = layoutManager?.itemCount ?: 0

            if((totalItemCount - lastVisibleItemPosition <= VISIBLE_THRESHOLD ||
                        totalItemCount - lastVisibleItemPosition == 0 &&
                        totalItemCount > visibleItemCount) && !onLoadMore
            ) {
                this.onLoadMore = true
                incrementPage(incrementBy)
                listener?.onMoreAsked()
            }
        }
    }

    private fun getLastVisibleItemPosition(layoutManager: RecyclerView.LayoutManager?): Int {
        return when (layoutManager) {
            is GridLayoutManager -> layoutManager.findLastVisibleItemPosition()
            is LinearLayoutManager -> layoutManager.findLastVisibleItemPosition()
            is StaggeredGridLayoutManager -> caseStaggeredGrid(layoutManager)
            else -> throw RuntimeException("Unsupported LayoutManager used. Valid ones are LinearLayoutManager, GridLayoutManager and StaggeredGridLayoutManager")
        }
    }

    private fun caseStaggeredGrid(layoutManager: RecyclerView.LayoutManager): Int {
        val staggeredGridLayoutManager = layoutManager as StaggeredGridLayoutManager
        if (lastScrollPositions == null)
            lastScrollPositions = IntArray(staggeredGridLayoutManager.spanCount)

        staggeredGridLayoutManager.findLastVisibleItemPositions(lastScrollPositions)
        return findMax(lastScrollPositions)
    }


    private fun findMax(lastPositions: IntArray?): Int {
        return lastPositions?.maxOrNull() ?: Integer.MIN_VALUE
    }


    interface Listener {
        fun onMoreAsked()
    }
}