package co.styletheory.ops.outbound.android.general

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.coordinatorlayout.widget.CoordinatorLayout
import com.google.android.material.appbar.AppBarLayout

// Workaround for https://code.google.com/p/android/issues/detail?id=177195
// Based off of solution originally found here: http://stackoverflow.com/a/31140112/1317564
class CustomScrollingViewBehavior(context: Context, attrs: AttributeSet) : AppBarLayout.ScrollingViewBehavior(context, attrs) {
    private var appBarLayout: AppBarLayout? = null
    private var onAnimationRunnablePosted = false

    override fun onStartNestedScroll(coordinatorLayout: CoordinatorLayout, child: View, directTargetChild: View, target: View, axes: Int, type: Int): Boolean {
        if(appBarLayout != null) {
            // We need to check from when a scroll is started, as we may not have had the chance to update the layout at
            // the start of a scroll or fling event.
            startAnimationRunnable(child, appBarLayout!!)
        }
        return super.onStartNestedScroll(coordinatorLayout, child, directTargetChild, target, axes, type)
    }

    override fun onMeasureChild(parent: CoordinatorLayout, child: View, parentWidthMeasureSpec: Int, widthUsed: Int,
                                parentHeightMeasureSpec: Int, heightUsed: Int): Boolean {
        if(appBarLayout != null) {
            val bottomPadding = calculateBottomPadding(appBarLayout!!)
            //            final int bottomPadding = 0;
            if(bottomPadding != child.paddingBottom) {
                // We need to update the padding in onMeasureChild as otherwise we won't have the correct padding in
                // place when the view is flung, and the changes done in onDependentViewChanged will only take effect on
                // the next animation frame, which means it will be out of sync with the new scroll offset. This is only
                // needed when the view is flung -- when dragged with a finger, things work fine with just
                // implementing onDependentViewChanged().
                child.setPadding(child.paddingLeft, child.paddingTop, child.paddingRight, bottomPadding)
            }
        }

        return super.onMeasureChild(parent, child, parentWidthMeasureSpec, widthUsed, parentHeightMeasureSpec, heightUsed)
    }

    override fun onDependentViewChanged(parent: CoordinatorLayout, child: View, dependency: View): Boolean {
        if(appBarLayout == null) {
            appBarLayout = dependency as AppBarLayout
        }

        val result = super.onDependentViewChanged(parent, child, dependency)
        val bottomPadding = calculateBottomPadding(appBarLayout!!)
        val paddingChanged = bottomPadding != child.paddingBottom
        if(paddingChanged) {
            // If we've changed the padding, then update the child and make sure a layout is requested.
            child.setPadding(child.paddingLeft,
                    child.paddingTop,
                    child.paddingRight,
                    bottomPadding)
            child.requestLayout()
        }

        // Even if we didn't change the padding, if onDependentViewChanged was called then that means that the app bar
        // layout was changed or was flung. In that case, we want to check for these changes over the next few animation
        // frames so that we can ensure that we capture all the changes and update the view pager padding to match.
        startAnimationRunnable(child, dependency)
        return paddingChanged || result
    }

    // Calculate the padding needed to keep the bottom of the view pager's content at the same location on the screen.
    private fun calculateBottomPadding(dependency: AppBarLayout): Int {
        val totalScrollRange = dependency.totalScrollRange
        return totalScrollRange + dependency.top - getStatusBarHeight(dependency.context)
    }

    private fun startAnimationRunnable(child: View, dependency: View) {
        if(onAnimationRunnablePosted) {
            return
        }

        val onPostChildTop = child.top
        val onPostDependencyTop = dependency.top
        onAnimationRunnablePosted = true
        // Start looking for changes at the beginning of each animation frame. If there are any changes, we have to
        // ensure that layout is run again so that we can update the padding to take the changes into account.
        child.postOnAnimation(object : Runnable {
            private val MAX_COUNT_OF_FRAMES_WITH_NO_CHANGES = 5
            private var previousChildTop = onPostChildTop
            private var previousDependencyTop = onPostDependencyTop
            private var countOfFramesWithNoChanges: Int = 0

            override fun run() {
                // Make sure we request a layout at the beginning of each animation frame, until we notice a few
                // frames where nothing changed.
                val currentChildTop = child.top
                val currentDependencyTop = dependency.top
                var hasChanged = false

                if(currentChildTop != previousChildTop) {
                    previousChildTop = currentChildTop
                    hasChanged = true
                    countOfFramesWithNoChanges = 0
                }
                if(currentDependencyTop != previousDependencyTop) {
                    previousDependencyTop = currentDependencyTop
                    hasChanged = true
                    countOfFramesWithNoChanges = 0
                }
                if(!hasChanged) {
                    countOfFramesWithNoChanges++
                }
                if(countOfFramesWithNoChanges <= MAX_COUNT_OF_FRAMES_WITH_NO_CHANGES) {
                    // We can still look for changes on subsequent frames.
                    child.requestLayout()
                    child.postOnAnimation(this)
                } else {
                    // We've encountered enough frames with no changes. Do a final layout request, and don't repost.
                    child.requestLayout()
                    onAnimationRunnablePosted = false
                }
            }
        })
    }

    private fun getStatusBarHeight(context: Context): Int {
        var result = 0
        val resourceId = context.resources.getIdentifier("status_bar_height", "dimen", "android")
        if(resourceId > 0) {
            result = context.resources.getDimensionPixelSize(resourceId)
        }
        return result
    }
}