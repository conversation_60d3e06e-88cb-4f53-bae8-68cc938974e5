package co.styletheory.ops.outbound.android.util

import co.styletheory.ops.outbound.android.feature.settings.viewModel.impl.BatchConfigItemViewModel
import co.styletheory.ops.outbound.android.model.BatchConfig
import co.styletheory.ops.outbound.android.model.BatchGenerationTime
import co.styletheory.ops.outbound.android.model.Region
import co.styletheory.ops.outbound.android.model.VerticalType
import co.styletheory.ops.outbound.android.model.enums.Regions
import co.styletheory.ops.outbound.android.model.enums.UserRoles
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*

/**
 * Created by Yoga C. Pranata on 2019-08-14.
 * Android Engineer
 */
fun BatchConfig.Result.convertToRegionModel(list: ArrayList<Region>, userRoles: List<String>?): ArrayList<Region> {
    val resource = this
    val regionData = Region()

    with(regionData) {
        id = resource.region?.id
        orderId = resource.regionOutboundOrder
        name = resource.region?.name
    }


    val attribute = resource.region.takeIf {
        list.firstOrNull { it.name?.contains(resource.region?.name.toString(), ignoreCase = true) == true }?.name.isNullOrEmpty()
    }

    attribute.notNull {
        if (userRoles == null) {
            if (isRegionSg(regionData)) list.add(regionData)
            return@notNull
        }

        userRoles.forEach { role ->
            val hasRegionalAccess = when {
                isHkStaff(role) && isRegionHk(regionData) -> true
                isIdStaff(role) && isRegionId(regionData) -> true
                isSgStaff(role) && isRegionSg(regionData) -> true
                else -> false
            }

            val isNonRegionalStaff = !userRoles.contains(UserRoles.HK_STAFF.roles) &&
                                    !userRoles.contains(UserRoles.ID_STAFF.roles) &&
                                    !userRoles.contains(UserRoles.SG_STAFF.roles)

            when {
                hasRegionalAccess -> list.add(regionData)
                isNonRegionalStaff && isRegionSg(regionData) -> list.add(regionData)
            }
        }
    }
    return list
}

private fun isRegionHk(region: Region) = region.id.equals(Regions.HONGKONG.code, ignoreCase = true)
private fun isRegionId(region: Region) = region.id.equals(Regions.INDONESIA.code, ignoreCase = true)
private fun isRegionSg(region: Region) = region.id.equals(Regions.SINGAPORE.code, ignoreCase = true)

private fun isHkStaff(role: String) = role.equals(UserRoles.HK_STAFF.roles, ignoreCase = true)
private fun isIdStaff(role: String) = role.equals(UserRoles.ID_STAFF.roles, ignoreCase = true)
private fun isSgStaff(role: String) = role.equals(UserRoles.SG_STAFF.roles, ignoreCase = true)

fun BatchConfig.Result.convertToVerticalModel(list: ArrayList<VerticalType>): ArrayList<VerticalType> {
    val resource = this
    val verticalData = VerticalType()

    with(verticalData) {
        id = resource.vertical?.id
        orderId = resource.verticalTypeOutboundOrder
        name = resource.vertical?.name
        code = resource.verticalTypeCode
    }

    val attribute = resource.vertical?.takeIf {
        list.firstOrNull { it.name?.contains(resource.vertical.name, ignoreCase = true) == true }?.name.isNullOrEmpty()
    }

    attribute.notNull {
        list.add(verticalData)
    }
    return list
}

fun BatchConfig.Result.convertToBatchGenerationTimeModel(list: ArrayList<BatchGenerationTime>): ArrayList<BatchGenerationTime> {
    val resource = this
    val batchGenerationTime = BatchGenerationTime()

    with(batchGenerationTime) {
        generationTimeId = resource.id
        regionId = resource.regionId
        verticalTypeCode = resource.verticalTypeCode
        hourGenerationTime = resource.batchGenerationTime?.hour ?: 0
        minutesGenerationTime = resource.batchGenerationTime?.minute ?: 0
    }

    list.add(batchGenerationTime)
    return list
}

fun ArrayList<BatchConfigItemViewModel>.bindRegionToViewModel(list: ArrayList<Region>): ArrayList<BatchConfigItemViewModel> {
    val item = BatchConfigItemViewModel()
    val resources = this

    for(region in list) {
        val attribute = region.takeIf { reg ->
            val regionName = reg.name.orEmpty()
            resources.firstOrNull {
                it.name.get().contains(regionName, ignoreCase = true)
            }?.name?.get().isNullOrEmpty()
        }

        attribute.notNull {
            item.bindRegionView(it)
            resources.add(item)
        }
    }

    return resources
}

fun ArrayList<BatchConfigItemViewModel>.bindVerticalToViewModel(list: ArrayList<VerticalType>): ArrayList<BatchConfigItemViewModel> {
    val item = BatchConfigItemViewModel()
    val resources = this

    for(vertical in list) {
        val attribute = vertical.takeIf { ver ->
            val verticalName = ver.name.orEmpty()
            resources.firstOrNull {
                it.name.get().contains(verticalName)
            }?.name?.get().isNullOrEmpty()
        }

        attribute.notNull {
            item.bindVerticalView(it)
            resources.add(item)
        }
    }

    return resources
}

fun Iterable<BatchGenerationTime?>.sortBatchGenerationTime(verticalType: String?, userRegion: String?): List<BatchGenerationTime?> {
    return this.filter {
        it?.verticalTypeCode == verticalType && it?.regionId == userRegion
    }.sortedWith(compareBy({ it?.hourGenerationTime }, { it?.minutesGenerationTime }))
}

fun List<BatchGenerationTime?>.getBatchGenerationTime(): BatchGenerationTime? {
    val calendar = Calendar.getInstance()
    val currentHour = calendar.get(Calendar.HOUR_OF_DAY)
    val currentMinutes = calendar.get(Calendar.MINUTE)

    val timeGeneration = arrayListOf<BatchGenerationTime?>()

    for(batchGenerationTime in this) {
        val hourGeneration = batchGenerationTime?.hourGenerationTime ?: 0
        val minuteGeneration = batchGenerationTime?.minutesGenerationTime ?: 0
        val isCurrentHourMoreThanGenerationHour = (currentHour > hourGeneration
                || (currentHour >= hourGeneration && currentMinutes > minuteGeneration))

        if(isCurrentHourMoreThanGenerationHour) {
            timeGeneration.add(batchGenerationTime)
        }
    }

    when {
        timeGeneration.isNotEmpty() -> {
            //Sort item
            timeGeneration.sortWith(
                    compareBy(
                            { it?.hourGenerationTime },
                            { it?.minutesGenerationTime }
                    )
            )

            //Reverse the sorted item
            timeGeneration.reverse()
        }
        this.isEmpty() -> return null
        else -> timeGeneration.add(this.last())
    }

    return timeGeneration.firstOrNull()
}

@Suppress("IMPLICIT_CAST_TO_ANY")
fun BatchGenerationTime?.convertToFormattedGenerationTime(): String? {
    val calendar = Calendar.getInstance()
    val currentHour = calendar.get(Calendar.HOUR_OF_DAY)
    val currentMinutes = calendar.get(Calendar.MINUTE)

    val dateMonth = SimpleDateFormat(DateConstant.PATTERN.ddMMMMyyyy, Locale.getDefault())
    val dateMonthTime = SimpleDateFormat(DateConstant.PATTERN.ddMMMyyyyHHmm, Locale.getDefault())
    val dateMonthTimeFormatted = SimpleDateFormat(DateConstant.PATTERN.ddMMMyyyyHH_mm, Locale.getDefault())
    val generateHour = this?.hourGenerationTime ?: 0
    val generateMinute = this?.minutesGenerationTime ?: 0

    val isCurrentHourMoreThanGenerationHour = (currentHour > generateHour
            || (currentHour >= generateHour && currentMinutes > generateMinute))

    if(!isCurrentHourMoreThanGenerationHour) {
        calendar.add(Calendar.DATE, -1)
    }

    val currentDateMonth = dateMonth.format(calendar.time)

    val minutes = if(generateMinute <= 0) "00" else generateMinute
    val stringTime = "$currentDateMonth $generateHour $minutes"

    return try {
        val timeGeneration = dateMonthTime.parse(stringTime) ?: Date()
        dateMonthTimeFormatted.format(timeGeneration)
    } catch(error: ParseException) {
        ""
    }
}
