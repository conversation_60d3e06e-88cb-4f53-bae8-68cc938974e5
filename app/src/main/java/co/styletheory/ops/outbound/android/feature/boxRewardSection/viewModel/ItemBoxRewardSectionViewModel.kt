package co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel

import androidx.databinding.ObservableBoolean
import co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.impl.ItemBoxRewardSectionViewModelImpl
import co.styletheory.ops.outbound.android.general.binding.ObservableChecked
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.BoxReward
import com.styletheory.android.mvvm.general.binding.ObservableText

/**
 * Created by <PERSON> on 11/20/20.
 */
interface ItemBoxRewardSectionViewModel : ViewModel {
    val rewardName: ObservableText
    val isRewardItemChecked: ObservableChecked
    val isRewardItemCheckedEnabled: ObservableBoolean
    val boxReward: BoxReward

    fun bindBoxReward(reward: BoxReward): ItemBoxRewardSectionViewModelImpl
    fun onRewardItemClicked()
    fun completedBoxReward(reward: BoxReward): ItemBoxRewardSectionViewModelImpl
}