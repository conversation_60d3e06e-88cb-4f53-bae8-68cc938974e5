package co.styletheory.ops.outbound.android.feature.packed.viewModel.impl

import android.annotation.SuppressLint
import android.view.View
import androidx.databinding.ObservableInt
import co.styletheory.android.network.core.RequestResult
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.goSend.event.GojekOrderClickedEvent
import co.styletheory.ops.outbound.android.feature.packed.event.SelectAllShipmetEvent
import co.styletheory.ops.outbound.android.feature.packed.event.ShipmentDateClickEvent
import co.styletheory.ops.outbound.android.feature.packed.viewModel.PackedViewModel
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.model.LogisticProvider
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.LogisticProviderResource
import co.styletheory.ops.outbound.android.util.*
import java.util.*
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 16 January 2018.
 * Description
 *
 * <EMAIL>
 */

class PackedViewModelImpl @Inject constructor() : BaseViewModel<PackedViewModelImpl>(), PackedViewModel {

    init {
        viewModelClass = PackedViewModelImpl::class
    }

    override var tabPosition: Int = 0
    override val logisticItems = arrayListOf<LogisticProvider>()

    override val selectedCalendar: Calendar = Calendar.getInstance()
    override val rightButtonText: ObservableString = ObservableString()
    override val leftButtonText: ObservableString = ObservableString()
    override val shipmentDate: ObservableString = ObservableString()
    override val searchQuery: ObservableString = ObservableString()
    override val gojekToBeOrderedActionVisibility: ObservableInt = ObservableInt(View.GONE)

    @Inject
    lateinit var dataService: DataService

    @Inject
    lateinit var userStorage: UserStorage

    @Inject
    lateinit var errorResponse: ErrorResponse

    override val totalLogisticProvider: Int get() = logisticItems.size
    override fun afterInject() {
        super.afterInject()
        shipmentDate.set(DateUtil.createDateStringFrom(selectedCalendar.time, "dd MMM yyyy"))
        leftButtonText.set(string(R.string.select_all))
        rightButtonText.set(string(R.string.send))
    }

    @SuppressLint("DefaultLocale")
    override fun getTitleTabAtIndex(index: Int): CharSequence {
        return logisticItems[index].name.capitalize()
    }

    override fun fetchLogisticProviders(callback: Result<Void?, String?>?) {
        dataService.fetchLogisticProvider(LogisticProviderResource(), RequestResult {
            onSuccess {
                it?.data?.result.notNull { providers ->
                    addLogisticProvider(providers)
                }
                callback?.success(null)
            }
            onError { callback?.failure(errorResponse.getErrorBodyDescription(it)) }
        })
    }

    override fun showGojekActionButtons(show: Boolean, ordered: Boolean) {
        if(ordered || !show) {
            gojekToBeOrderedActionVisibility.set(View.GONE)
        } else {
            gojekToBeOrderedActionVisibility.set(View.VISIBLE)
        }
    }

    override fun addLogisticProvider(logisticProviders: List<LogisticProvider>) {
        logisticItems.addAll(logisticProviders)
    }

    override fun updateTotalSelectedPackedShipment(totalSelected: Int) {
        rightButtonText.set(if(totalSelected == 0) string(R.string.send) else "${string(R.string.send)} ($totalSelected)")
    }

    override fun shipmentDateClicked() {
        eventBus.post(ShipmentDateClickEvent())
    }

    override fun onSelectAllClicked() {
        if(tabPosition < logisticItems.size) {
            eventBus.post(SelectAllShipmetEvent(logisticItems[tabPosition].id))
        }
    }

    override fun onOrderGosendClicked() {
        if(tabPosition < logisticItems.size) {
            eventBus.post(GojekOrderClickedEvent(logisticItems[tabPosition]))
        }
    }

    override fun isGoJek(position: Int): Boolean {
        return logisticItems[position].id.equals(AppConstant.LOGISTIC_PROVIDER.INDONESIA.GOJEK, ignoreCase = true)
    }

}