package co.styletheory.ops.outbound.android.general

import android.app.Activity
import android.content.Intent
import co.styletheory.ops.outbound.android.feature.accuracySwap.view.AccuracySwapActivity
import co.styletheory.ops.outbound.android.feature.backlogDetail.view.BacklogDetailActivity
import co.styletheory.ops.outbound.android.feature.packing.view.PackingDetailActivity
import co.styletheory.ops.outbound.android.feature.photoDetail.view.PhotoDetailActivity
import co.styletheory.ops.outbound.android.feature.photoManager.view.PhotoManagerActivity
import co.styletheory.ops.outbound.android.feature.qcDetail.view.QcDetailActivity
import co.styletheory.ops.outbound.android.feature.qualitySwap.view.QualitySwapActivity
import co.styletheory.ops.outbound.android.feature.settings.view.SettingsActivity
import co.styletheory.ops.outbound.android.feature.signIn.SignInActivity
import co.styletheory.ops.outbound.android.general.base.BaseNavigator
import co.styletheory.ops.outbound.android.general.view.FullScreenImageSlideShowActivity
import co.styletheory.ops.outbound.android.main.view.MainActivity
import co.styletheory.ops.outbound.android.util.IntentConstant
import co.styletheory.ops.outbound.android.util.notNullOrEmpty
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 18 October 2017.
 * Description
 *
 * <EMAIL>
 */
class Navigator @Inject constructor(activity: Activity) : BaseNavigator<Navigator>(activity) {

    fun toSignIn() {
        startActivity(SignInActivity::class.java)
    }

    fun toMainActivity(clearStack: Boolean = false) {
        val intent = Intent(activity, MainActivity::class.java)
        if(clearStack) intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
        activity.startActivity(intent)
    }

    fun toSettings(param: String = "") {
        val intent = Intent(activity, SettingsActivity::class.java)
        param.notNullOrEmpty {
            intent.putExtra(IntentConstant.SETTINGS_GENERATE, param)
        }
        activity.startActivity(intent)
    }

    fun toAccuracySwap() {
        startActivity(AccuracySwapActivity::class.java)
    }

    fun toQualitySwap() {
        startActivity(QualitySwapActivity::class.java)
    }

    fun toBacklogDetail() {
        startActivity(BacklogDetailActivity::class.java)
    }

    fun toPickedDetail() {
        startActivity(QcDetailActivity::class.java)
    }

    fun toPhotoManager() {
        startActivity(PhotoManagerActivity::class.java)
    }

    fun toQaPhotoDetails() {
        startActivity(PhotoDetailActivity::class.java)
    }

    fun toReadyDetails() {
        startActivity(PackingDetailActivity::class.java)
    }

    fun toFullScreenImageSlideShowActivity(allImageUrls: ArrayList<String>, position: Int = 0) {
        withArrayListStringExtra(IntentConstant.IMAGE_SOURCE_URLS_EXTRA_KEY, ArrayList(allImageUrls))
        withExtra(IntentConstant.SELECTED_IMAGE_POSITION_EXTRA_KEY, position)
        startActivity(FullScreenImageSlideShowActivity::class.java)
    }
}