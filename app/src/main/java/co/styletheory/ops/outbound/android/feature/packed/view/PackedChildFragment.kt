package co.styletheory.ops.outbound.android.feature.packed.view

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.PackedChildFragmentBinding
import co.styletheory.ops.outbound.android.feature.goSend.event.GojekCancelClickedEvent
import co.styletheory.ops.outbound.android.feature.goSend.event.GojekOrderClickedEvent
import co.styletheory.ops.outbound.android.feature.packed.event.PackedItemUIEvent
import co.styletheory.ops.outbound.android.feature.packed.event.RefreshGojekShipmentEvent
import co.styletheory.ops.outbound.android.feature.packed.event.RefreshShipmentListEvent
import co.styletheory.ops.outbound.android.feature.packed.event.SelectAllShipmetEvent
import co.styletheory.ops.outbound.android.feature.packed.event.SendShipmentEvent
import co.styletheory.ops.outbound.android.feature.packed.event.UpdateTotalSelectedShipmentEvent
import co.styletheory.ops.outbound.android.feature.packed.viewModel.PackedChildViewModel
import co.styletheory.ops.outbound.android.general.PaginationHandler
import co.styletheory.ops.outbound.android.general.adapter.WrapContentLinearLayoutManager
import co.styletheory.ops.outbound.android.general.adapter.impl.RecyclerViewAdapterImpl
import co.styletheory.ops.outbound.android.general.base.BaseDialogFragment
import co.styletheory.ops.outbound.android.general.base.BaseFragment
import co.styletheory.ops.outbound.android.general.event.DismissDialogEvent
import co.styletheory.ops.outbound.android.model.LogisticProvider
import co.styletheory.ops.outbound.android.util.IntentConstant
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.util.notNull
import co.styletheory.ops.outbound.android.util.notNullOrEmpty
import co.styletheory.ops.outbound.android.util.notifyChangedAndClearRecycledPool
import co.styletheory.ops.outbound.android.viewModelComponent.AlertDialogViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.ProcessDialogViewModel
import org.greenrobot.eventbus.Subscribe
import org.joda.time.DateTime
import org.parceler.Parcels
import javax.inject.Inject

/**
 * Created by giorgygunawan on 11/22/17.
 *
 */
@Suppress("unused")
class PackedChildFragment : BaseFragment<PackedChildFragmentBinding, PackedChildViewModel>(), SwipeRefreshLayout.OnRefreshListener,
    PaginationHandler.Listener {

    companion object {
        fun newInstance(logisticProvider: LogisticProvider?, dateTime: DateTime?, isOrdered: Boolean? = null): PackedChildFragment {
            val fragment = PackedChildFragment()
            val bundle = Bundle()
            bundle.putParcelable(IntentConstant.LOGISTIC_PROVIDER, Parcels.wrap(logisticProvider))
            bundle.putSerializable(IntentConstant.SELECTED_DATE, dateTime)
            isOrdered.notNull { bundle.putBoolean(IntentConstant.LOGISTIC_ORDERED, it) }
            fragment.arguments = bundle
            return fragment
        }
    }

    @Inject
    lateinit var adapter: RecyclerViewAdapterImpl

    @Inject
    lateinit var paginationHandler: PaginationHandler
    private val processDialogViewModel = ProcessDialogViewModel.sendShipment()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if(viewModel == null) {
            fragmentComponent?.inject(this)
            initArgument()
        }
    }

    private fun initArgument() {
        arguments?.getParcelable<Parcelable>(IntentConstant.LOGISTIC_PROVIDER).notNull { viewModel?.logisticProvider = Parcels.unwrap(it) }
        arguments?.getSerializable(IntentConstant.SELECTED_DATE).notNull { viewModel?.shipmentDate = it as DateTime }
        arguments.notNull {
            viewModel?.logisticOrdered = it[IntentConstant.LOGISTIC_ORDERED] as? Boolean
            onRefresh()
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? =
        bindContentView(inflater, container, R.layout.packed_child_fragment)

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if(!eventBus.isRegistered(this)) {
            eventBus.register(this)
        }
        setupRecyclerView()
    }

    override fun onResume() {
        super.onResume()
        if(viewModel?.packedItems?.size == 0) {
            fetchNewData()
        }
    }

    @Suppress("UNCHECKED_CAST")
    private fun setupRecyclerView() {
        binding?.recyclerView?.notNull {
            paginationHandler.listener = this
            it.setLayoutManager(WrapContentLinearLayoutManager(context))

            adapter.layoutId = R.layout.packed_list_item
            adapter.items = viewModel?.packedItems as MutableList<Any>
            it.adapter = adapter
            it.setRefreshListener(this)
            it.setOnScrollListener(paginationHandler)
        }
    }

    override fun onRefresh() {
        fetchNewData()
    }

    override fun onMoreAsked() {
        adapter.showFooterLoading()
        viewModel?.fetchNextPage(resultCallback, paginationHandler.page)
    }

    private val resultCallback = object : Result<Void?, String?> {
        override fun success(success: Void?) {
            hideProgressBar()
            dismissProgressDialog()
            adapter.hideFooterLoading()
            paginationHandler.finishLoad()
        }

        override fun failure(error: String?) {
            hideProgressBar()
            dismissProgressDialog()
            adapter.hideFooterLoading()
            paginationHandler.failedLoad()
            showShortToast(error ?: string(R.string.err_something_when_wrong))
        }
    }

    private val sendShipmentCallback = object : Result<String?, String?> {
        override fun success(success: String?) {
            processDialogViewModel.dialogTitle.set(string(R.string.box_already_sent_label))
            processDialogViewModel.showSuccess()
            Handler(Looper.getMainLooper()).postDelayed({ eventBus.post(DismissDialogEvent(ProcessDialogViewModel.TAG)) }, 2000)
            eventBus.post(DismissDialogEvent(AlertDialogViewModel.TAG))
            success.notNullOrEmpty { showLongToast(it) }
            viewModel?.fetchFirstPage(resultCallback)
        }

        override fun failure(error: String?) {
            processDialogViewModel.showFailed()
            showShortToast(error ?: string(R.string.err_something_when_wrong))
        }
    }

    override fun setMenuVisibility(menuVisible: Boolean) {
        super.setMenuVisibility(menuVisible)
        if(menuVisible && viewModel != null) {
            eventBus.post(UpdateTotalSelectedShipmentEvent(viewModel?.totalSelectedShipment() ?: 0))
        }
    }

    private fun cancelGojekShipments(logisticProvider: LogisticProvider?) {
        if(viewModel?.isSameLogisticProvider(logisticProvider?.id) == true) {
            showProgressDialog()
            viewModel?.cancelGojek(object : Result<Void?, String?> {
                override fun success(success: Void?) {
                    dismissProgressDialog()
                    showShortToast(getString(R.string.packed_fragment_shipment_canceled))

                    eventBus.post(RefreshGojekShipmentEvent(viewModel?.logisticProvider, false))
                }

                override fun failure(error: String?) {
                    dismissProgressDialog()
                    showShortToast(error)
                }
            })
        }
    }

    fun sendBox() {
        processDialogViewModel.resetSendShipment()
        processDialogViewModel.showLoading()
        BaseDialogFragment.setViewModelAndLayoutId(processDialogViewModel, R.layout.process_dialog)
            .show(childFragmentManager, ProcessDialogViewModel.TAG)
        viewModel?.sendShipmentBox(sendShipmentCallback)
    }

    private fun isShipmentSelected(): Boolean {
        val totalSelected = viewModel?.totalSelectedShipment() ?: 0
        if(totalSelected > 0) {
            return true
        }
        showShortToast(string(R.string.please_select_the_shipment_label))
        return false
    }

    private fun showProgressBar() {
        binding?.recyclerView?.swipeToRefresh?.isRefreshing = true
    }

    private fun hideProgressBar() {
        binding?.recyclerView?.swipeToRefresh?.isRefreshing = false
    }

    private fun fetchNewData() {
        showProgressBar()
        refreshNewData()
        viewModel?.fetchFirstPage(resultCallback)
    }

    private fun refreshNewData() {
        viewModel?.packedItems?.clear()
        binding?.recyclerView?.notifyChangedAndClearRecycledPool()
        paginationHandler.reset()
    }

    //region EVENT BUS
    @Subscribe
    fun onUpdateShipmentAsReceived(event: PackedItemUIEvent.OnUpdateShipmentAsReceived) {
        fetchNewData()
    }

    @Subscribe
    fun onRefreshShipmentListEvent(event: RefreshShipmentListEvent) {
        if(viewModel?.shipmentDate?.toLocalDate()?.isEqual(event.shipmentDate?.toLocalDate()) == false || !viewModel?.searchQuery.equals(
                event.query,
                ignoreCase = false
            )
        ) {
            viewModel?.shipmentDate = event.shipmentDate ?: DateTime.now()
            viewModel?.searchQuery = event.query.orEmpty()
            fetchNewData()
        }
    }

    @Subscribe
    fun onSelectAllClicked(event: SelectAllShipmetEvent) {
        if(viewModel?.isSameLogisticProvider(event.logisticProvider) == true && (viewModel?.logisticOrdered == null || viewModel?.logisticOrdered == false)) {
            viewModel?.selectAllItem()
        }
    }

    @Subscribe
    fun onSendShipmentClicked(event: SendShipmentEvent) {
        if(viewModel?.isSameLogisticProvider(event.logisticProvider) == true &&
            (viewModel?.logisticOrdered == null || viewModel?.logisticOrdered == true) &&
            isShipmentSelected()
        ) {
            val model = AlertDialogViewModel()
            model.bindViewModel(
                viewModel?.totalSelectedShipment() ?: 0,
                viewModel?.logisticProvider
            )
            model.rightButtonOnClickListener = object : AlertDialogViewModel.OnClickListener {
                override fun onClick() {
                    sendBox()
                }
            }
            BaseDialogFragment.setViewModelAndLayoutId(model, R.layout.alert_dialog)
                .show(childFragmentManager, AlertDialogViewModel.TAG)
        }
    }


    @Subscribe
    fun onGojekOrderClickedEvent(event: GojekOrderClickedEvent) {
        if(viewModel?.isSameLogisticProvider(event.logisticProvider?.id) == true &&
            viewModel?.logisticOrdered != null &&
            viewModel?.logisticOrdered == false &&
            isShipmentSelected()
        ) {
            processDialogViewModel.resetSendShipment()
            processDialogViewModel.showLoading()
            BaseDialogFragment.setViewModelAndLayoutId(
                processDialogViewModel,
                R.layout.process_dialog
            ).show(childFragmentManager, ProcessDialogViewModel.TAG)
            viewModel?.orderGojek(object : Result<String?, String?> {
                override fun success(success: String?) {
                    processDialogViewModel.dialogTitle.set(getString(R.string.packed_fragment_gojek_ordered))
                    processDialogViewModel.showSuccess()
                    Handler(Looper.getMainLooper()).postDelayed(
                        { eventBus.post(DismissDialogEvent(ProcessDialogViewModel.TAG)) },
                        2000
                    )
                    showShortToast(success)
                    eventBus.post(RefreshGojekShipmentEvent(viewModel?.logisticProvider, false))
                }

                override fun failure(error: String?) {
                    processDialogViewModel.showFailed()
                    showShortToast(error)
                }
            })
        }
    }

    @Subscribe
    fun onGojekCancelClickedEvent(event: GojekCancelClickedEvent) {
        if(viewModel?.isSameLogisticProvider(event.logisticProvider?.id) == true &&
            viewModel?.logisticOrdered != null &&
            viewModel?.logisticOrdered == true &&
            isShipmentSelected()
        ) {
            val model = AlertDialogViewModel()
                .setTitle(string(R.string.packed_fragment_cancel_gojek_dialog_title))
                .setBody(string(R.string.packed_fragment_cancel_gojek_dialog_message))
                .setRightButtonText(string(R.string.packed_fragment_cancel_shipment))
                .setLeftButtonText(string(R.string.close_label))
                .setRightButtonClickListener(object : AlertDialogViewModel.OnClickListener {
                    override fun onClick() {
                        cancelGojekShipments(viewModel?.logisticProvider)
                        eventBus.post(DismissDialogEvent(AlertDialogViewModel.TAG))
                    }
                })
            BaseDialogFragment.setViewModelAndLayoutId(model, R.layout.alert_dialog)
                .show(childFragmentManager, AlertDialogViewModel.TAG)
        }
    }

    @Subscribe
    fun onRefreshGojekShipmentEvent(event: RefreshGojekShipmentEvent) {
        if(viewModel?.isSameLogisticProvider(event.logisticProvider?.id) == true && viewModel?.logisticOrdered != null) {
            fetchNewData()
        }
    }

    @Subscribe
    fun onShowMarkShipmentDialogPopup(event: PackedItemUIEvent.OnShowMarkShipmentDialogPopup) {
        val model = AlertDialogViewModel()
            .setTitle(string(R.string.packed_fragment_received_by_warehouse_dialog_title))
            .setBody(string(R.string.packed_fragment_received_by_warehouse_dialog_message))
            .setRightButtonText(string(R.string.confirm_button_text))
            .setLeftButtonText(string(R.string.cancel_label))
            .setRightButtonClickListener(object : AlertDialogViewModel.OnClickListener {
                override fun onClick() {
                    viewModel?.markShipmentAsReceived(event.shipmentId)
                    eventBus.post(DismissDialogEvent(AlertDialogViewModel.TAG))
                }
            })
        BaseDialogFragment.setViewModelAndLayoutId(model, R.layout.alert_dialog)
            .show(childFragmentManager, AlertDialogViewModel.TAG)
    }
    //endregion
}
