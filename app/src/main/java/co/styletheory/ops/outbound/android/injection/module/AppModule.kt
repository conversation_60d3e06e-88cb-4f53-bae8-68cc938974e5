package co.styletheory.ops.outbound.android.injection.module

import android.app.Application
import android.content.Context
import co.styletheory.ops.outbound.android.StyletheoryOpsApplication
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.injection.scope.PerApplication
import dagger.Module
import dagger.Provides
import org.greenrobot.eventbus.EventBus

/**
 * styletheory-ops-outbound-android
 * Created by dwi<PERSON><PERSON><PERSON> on 18 October 2017.
 * Description
 *
 * <EMAIL>
 */

@Module
open class AppModule(private val application: StyletheoryOpsApplication) {

    @Provides
    @PerApplication
    fun provideAppContext(): Context = application

    @Provides
    @PerApplication
    fun provideApplication(): Application = application

    @Provides
    @PerApplication
    fun provideEventBus(): EventBus = EventBus.getDefault()

    @Provides
    @PerApplication
    open fun provideUserStorage(): UserStorage = UserStorage()

}