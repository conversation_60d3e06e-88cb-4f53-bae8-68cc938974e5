package co.styletheory.ops.outbound.android.networking

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.BatchConfig
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.GenerateBatchList
import co.styletheory.ops.outbound.android.model.LogisticProvider
import co.styletheory.ops.outbound.android.model.Session
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.model.User
import co.styletheory.ops.outbound.android.model.response.CheckSwapAvailabilityResponse
import co.styletheory.ops.outbound.android.model.response.OrderGojekResponse
import co.styletheory.ops.outbound.android.model.response.SendShipmentResponse
import co.styletheory.ops.outbound.android.model.response.ShipmentResponse
import okhttp3.RequestBody
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Url

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 28 October 2017.
 * Description
 *
 * <EMAIL>
 */
interface OutboundService {

    @POST
    fun login(@Url url: String, @Body body: RequestBody): Call<GraphQLData<Session>>

    @POST
    fun logout(@Url url: String, @Body body: RequestBody): Call<GraphQLData<Boolean>>

    @POST
    fun createPassword(@Url url: String, @Body body: RequestBody): Call<GraphQLData<Session>>

    @POST
    fun refreshSession(@Url url: String, @Body body: RequestBody): Call<GraphQLData<Session>>

    @POST
    fun userDetail(@Url url: String, @Body body: RequestBody): Call<GraphQLData<User>>

    @POST
    fun fetchBatch(@Url url: String, @Body body: RequestBody): Call<GraphQLData<List<Batch>>>

    @POST
    fun fetchBatchDetail(@Url url: String, @Body body: RequestBody): Call<GraphQLData<Batch>>

    @POST
    fun updateBatchItemStatus(@Url url: String, @Body body: RequestBody): Call<GraphQLData<Void>>

    @POST
    fun completeBatch(@Url url: String, @Body body: RequestBody): Call<GraphQLData<Void>>

    @POST
    fun beginBatch(@Url url: String, @Body body: RequestBody): Call<GraphQLData<Void>>

    @POST
    fun swapItem(@Url url: String, @Body body: RequestBody): Call<GraphQLData<Void>>

    @POST
    fun checkSwapItemAvailability(@Url url: String, @Body body: RequestBody): Call<GraphQLData<CheckSwapAvailabilityResponse>>

    @POST
    fun fetchImageUploadUrl(@Url url: String, @Body body: RequestBody): Call<GraphQLData<String>>

    @POST
    fun attachImageToItem(@Url url: String, @Body body: RequestBody): Call<GraphQLData<Void>>

    @PUT
    fun uploadImageUrl(@Url uri: String, @Body imageBody: RequestBody): Call<Void>

    @POST
    fun fetchLogisticProvider(@Url url: String, @Body body: RequestBody): Call<GraphQLData<List<LogisticProvider>>>

    @POST
    fun fetchShipmentByLogisticProvider(@Url url: String, @Body body: RequestBody): Call<GraphQLData<ShipmentResponse>>

    @POST
    fun sendShipment(@Url url: String, @Body body: RequestBody): Call<GraphQLData<SendShipmentResponse>>

    @POST
    fun orderGojekShipments(@Url url: String, @Body body: RequestBody): Call<GraphQLData<OrderGojekResponse>>

    @POST
    fun cancelGojekShipments(@Url url: String, @Body body: RequestBody): Call<GraphQLData<List<Batch>>>

    @POST
    fun generateBatchList(@Url url: String, @Body body: RequestBody): Call<GraphQLData<GenerateBatchList>>

    @POST
    fun fetchBatchConfigs(@Url url: String, @Body body: RequestBody): Call<GraphQLData<List<BatchConfig.Result>>>

    @POST
    fun getAccuracySwapItem(@Url url: String, @Body body: RequestBody): Call<GraphQLData<BatchItem>>

    @POST
    fun accuracySwapItem(@Url url: String, @Body body: RequestBody): Call<GraphQLData<Void>>

    @POST
    fun markShipmentAsReceivedByWarehouse(@Url url: String, @Body body: RequestBody):  Call<GraphQLData<Void>>
}