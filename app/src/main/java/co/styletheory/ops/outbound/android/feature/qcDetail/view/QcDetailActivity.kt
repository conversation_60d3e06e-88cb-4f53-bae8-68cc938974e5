package co.styletheory.ops.outbound.android.feature.qcDetail.view

import android.app.Activity
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Parcelable
import android.view.Gravity
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.ArrayAdapter
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.ListPopupWindow
import androidx.appcompat.widget.Toolbar
import androidx.viewpager.widget.ViewPager
import co.styletheory.android.network.event.NoInternetConnectionEvent
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.ActivityQcDetailBinding
import co.styletheory.ops.outbound.android.feature.backlogDetail.event.BacklogItemSwapClickEvent
import co.styletheory.ops.outbound.android.feature.errorSwapBottomSheet.event.ErrorSwapItemBottomSheetUIEvent
import co.styletheory.ops.outbound.android.feature.errorSwapBottomSheet.view.ErrorSwapItemBottomSheetDialog
import co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.event.LoadingScanBarcodeBottomSheetNetworkEvent
import co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.event.LoadingScanBarcodeBottomSheetUIEvent
import co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.view.LoadingScanBarcodeBottomSheetDialog
import co.styletheory.ops.outbound.android.feature.photoManager.event.TakePictureEvent
import co.styletheory.ops.outbound.android.feature.photoManager.view.PhotoManagerActivity
import co.styletheory.ops.outbound.android.feature.qcDetail.event.QcDetailUIEvent
import co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.QcDetailViewModel
import co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.impl.FailNotesDialogViewModel
import co.styletheory.ops.outbound.android.feature.scanBarcodeBottomSheet.event.ScanBarcodeBottomSheetUIEvent
import co.styletheory.ops.outbound.android.feature.scanBarcodeBottomSheet.view.ScanBarcodeBottomSheetDialog
import co.styletheory.ops.outbound.android.feature.swap.SwapItemDialogViewModel
import co.styletheory.ops.outbound.android.feature.swapConfirmationBottomSheet.event.SwapConfirmationBottomDialogNetworkEvent
import co.styletheory.ops.outbound.android.feature.swapConfirmationBottomSheet.view.SwapConfirmationBottomSheetDialogFragment
import co.styletheory.ops.outbound.android.general.adapter.impl.PagerAdapter
import co.styletheory.ops.outbound.android.general.base.BaseActivity
import co.styletheory.ops.outbound.android.general.base.BaseDialogFragment
import co.styletheory.ops.outbound.android.general.event.DismissDialogEvent
import co.styletheory.ops.outbound.android.general.event.PreviewImageEvent
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.model.enums.AttachPhotoType
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.model.enums.MainMenuTab
import co.styletheory.ops.outbound.android.model.enums.SwapType
import co.styletheory.ops.outbound.android.util.EdgeToEdgeUtil
import co.styletheory.ops.outbound.android.util.IntentConstant
import co.styletheory.ops.outbound.android.util.Next
import co.styletheory.ops.outbound.android.util.PermissionHandlerActivity
import co.styletheory.ops.outbound.android.util.PermissionUtils
import co.styletheory.ops.outbound.android.util.RequestCode
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.util.getSerializableExtraProvider
import co.styletheory.ops.outbound.android.util.hideSoftKeyboard
import co.styletheory.ops.outbound.android.util.notNull
import co.styletheory.ops.outbound.android.viewModelComponent.AlertDialogViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.ProcessDialogViewModel
import com.styletheory.camera.screens.home.PhotosPickerActivity
import com.styletheory.camera.shared.models.Photo
import org.greenrobot.eventbus.Subscribe
import org.parceler.Parcels
import javax.inject.Inject


@Suppress("UNUSED_PARAMETER", "unused")
/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 31 October 2017.
 * Description
 *
 * <EMAIL>
 */
class QcDetailActivity : BaseActivity<ActivityQcDetailBinding, QcDetailViewModel>(), PermissionHandlerActivity {
    private val ACCURACY_SWAP = 300
    private val QUALITY_SWAP = 400

    @Inject
    lateinit var pagerAdapter: PagerAdapter
    var takePhotoCallback: Next<List<String>>? = null
    private var currentPage = 0

    private var arrayQAFailedReasonForApparel = emptyArray<String>()

    private var arrayQAFailedReasonForBags = emptyArray<String>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        activityComponent?.inject(this)
        bindContentView(R.layout.activity_qc_detail)
        initExtras()
        enableZoomImageView()
        initFailedReason()

        setSupportActionBar(binding?.toolbarWrapper?.toolbar as Toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        setupToolbarViewModel()

        setupViewPager()
        fetchBatchDetail()
        checkIsBarcodeSettingOn()

        // Handle back button press with the new API
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                navigator.withExtraResult(IntentConstant.MAIN_TAB_POSITION, MainMenuTab.MAIN.PICKED.index.toString())
                    .finishWithResult()
            }
        })
    }

    private fun initFailedReason() {
        arrayQAFailedReasonForApparel = arrayOf(
            string(R.string.sewing_label),
            string(R.string.stain_label),
            string(R.string.spoilt_zipper_label),
            string(R.string.fabric_defect_label),
            string(R.string.hardware_damage_label),
            string(R.string.bad_odour_label),
            string(R.string.missing_belt_label),
            string(R.string.wrong_item_label),
            string(R.string.other_label)
        )

        arrayQAFailedReasonForBags = arrayOf(
            string(R.string.missing_detachables_label),
            string(R.string.missing_hardware_label),
            string(R.string.stained_bag_label),
            string(R.string.bags_with_defected_parts_label),
            string(R.string.bags_with_defected_hardware_label),
            string(R.string.hardware_defect_label),
            string(R.string.smelly_bag_label),
            string(R.string.edging_label)
        )
    }

    private fun initExtras() {
        intent.getStringExtra(IntentConstant.BATCH_ID).notNull { viewModel?.batchId = it }
        getSerializableExtraProvider<BatchStatus>(intent, IntentConstant.BATCH_STATUS).notNull { viewModel?.batchStatus = it }
    }

    private fun setupToolbarViewModel() {
        binding?.toolbarVM = viewModel?.toolbarViewModel()
    }

    private fun setupViewPager() {
        binding.notNull {
            it.viewPager.clipToPadding = false
            it.tabs.setupWithViewPager(it.viewPager)
            it.viewPager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
                override fun onPageScrollStateChanged(state: Int) {
                    // not implemented yet
                }

                override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                    // not implemented yet
                }

                override fun onPageSelected(position: Int) {
                    val subtitle = "${position + 1} of ${viewModel?.totalViewCount} Box"
                    viewModel?.changeToolbarSubtitle(subtitle)
                }
            })
        }
    }

    private fun fetchBatchDetail() {
        showProgressDialog()
        currentPage = binding?.viewPager?.currentItem ?: 0
        viewModel?.fetchBatchDetail(object : Result<Void?, String> {
            override fun success(success: Void?) {
                dismissProgressDialog()
                rebindPagerAdapter()
            }

            override fun failure(error: String) {
                dismissProgressDialog()
                showShortToast(error)
            }
        })
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.menu_complete, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if(item.itemId == R.id.action_complete) {
            doCompleteBatch()
            return true
        } else if(item.itemId == R.id.action_refresh) {
            checkIsBarcodeSettingOn()
            fetchBatchDetail()
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    private fun doCompleteBatch() {
        viewModel.notNull {
            if(it.totalViewCount > 0) {
                if(viewModel?.isAllBatchItemShipmentCompletedQA() == true) {
                    showProgressDialog()
                    it.completeBatch(object : Result<Void?, String> {
                        override fun success(success: Void?) {
                            dismissProgressDialog()
                            BaseDialogFragment.setViewModelAndLayoutId(
                                ProcessDialogViewModel.success(
                                    viewModel?.batchName()
                                        .orEmpty(), this@QcDetailActivity
                                ), R.layout.process_dialog
                            )
                                .onDialogDismiss(object : DialogInterface {
                                    override fun dismiss() {
                                        finish()
                                    }

                                    override fun cancel() {
                                        // not implemented yet
                                    }
                                }).setDismissAfter(1500).show(supportFragmentManager)
                        }

                        override fun failure(error: String) {
                            dismissProgressDialog()
                            showShortToast(error)
                        }
                    })
                } else {
                    BaseDialogFragment.setViewModelAndLayoutId(ProcessDialogViewModel.failed(this), R.layout.process_dialog).show(supportFragmentManager)
                }
            } else {
                showShortToast("Shipment not found")
            }
        }

    }

    private fun rebindPagerAdapter() {
        pagerAdapter = PagerAdapter(supportFragmentManager)
        pagerAdapter.source = viewModel
        binding?.viewPager?.adapter = pagerAdapter
        binding?.tabs?.setupWithViewPager(binding?.viewPager)
        binding?.viewPager?.adapter?.notifyDataSetChanged()
        binding?.viewPager?.currentItem = currentPage
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if(resultCode == Activity.RESULT_OK && requestCode == RequestCode.PHOTO_MANAGER) {
            data.notNull {
                if(it.hasExtra(IntentConstant.PRODUCT_PHOTO_RESULT_QAIMAGES_EXTRA_CODE)) {
                    takePhotoCallback?.apply(
                        it.getStringArrayListExtra(IntentConstant.PRODUCT_PHOTO_RESULT_QAIMAGES_EXTRA_CODE)?.toList()
                            ?: listOf()
                    )
                }
            }
        } else if(requestCode == RequestCode.REQ_PHOTOS) {
            val photos = data?.getParcelableArrayListExtra<Photo>(PhotosPickerActivity.EXTRA_PHOTOS)
                ?: return
            processData(photos)
        } else if(requestCode == ACCURACY_SWAP) {
            if(data?.hasExtra(IntentConstant.BOTTOM_SHEET_ARGS) == true) {
                data.getParcelableExtra<Parcelable>(IntentConstant.SWAP_ITEM).notNull {
                    val item = Parcels.unwrap<SwapItem>(it)
                    viewModel?.swapItem = item
                    showScanBarcodeBottomSheet(item)
                }
            } else {
                fetchBatchDetail()
            }
        } else if(requestCode == QUALITY_SWAP) {
            if(data?.hasExtra(IntentConstant.BOTTOM_SHEET_ARGS) == true) {
                data.getParcelableExtra<Parcelable>(IntentConstant.SWAP_ITEM).notNull {
                    val item = Parcels.unwrap<SwapItem>(it)
                    viewModel?.swapItem = item
                    viewModel?.swapType = SwapType.QUALITY_SWAP
                }

                showLoadingQualitySwapBottomSheet(viewModel?.batchId, viewModel?.swapItem)
            } else {
                fetchBatchDetail()
            }
        }
    }

    private fun hideKeyboardAndClearText() {
        binding?.editTextScannedRfid?.let { hideSoftKeyboard(it) }
        binding?.editTextScannedRfid?.text?.clear()
    }

    private fun showScanBarcodeBottomSheet(swapItem: SwapItem) {
        val scanBottomSheetDialog = ScanBarcodeBottomSheetDialog.newInstance(swapItem)
        scanBottomSheetDialog.show(supportFragmentManager, IntentConstant.SHOW_BOTTOMSHEET)
    }

    private fun showLoadingScanBarcodeBottomSheet(swapItem: SwapItem, newItemUuid: String, swapType: SwapType) {
        val loadingScanBottomSheetDialog = LoadingScanBarcodeBottomSheetDialog.newInstance(swapItem, newItemUuid, swapType)
        loadingScanBottomSheetDialog.show(supportFragmentManager, IntentConstant.SHOW_BOTTOMSHEET)
    }

    private fun showLoadingQualitySwapBottomSheet(batchId: String?, swapItem: SwapItem?) {
        val loadingScanBottomSheetDialog = LoadingScanBarcodeBottomSheetDialog.newInstance(batchId, swapItem, SwapType.QUALITY_SWAP)
        loadingScanBottomSheetDialog.show(supportFragmentManager, IntentConstant.SHOW_BOTTOMSHEET)
    }

    private fun showErrorSwapItemBottomSheet(errorMessage: String) {
        val swapType = viewModel?.swapType ?: SwapType.QUALITY_SWAP
        val errorSwapItemBottomSheetDialog = ErrorSwapItemBottomSheetDialog.newInstance(errorMessage, swapType)
        errorSwapItemBottomSheetDialog.show(supportFragmentManager, IntentConstant.SHOW_BOTTOMSHEET)
    }


    //region EVENT BUS
    @Subscribe
    fun swapItemClick(event: BacklogItemSwapClickEvent) {
        val model = SwapItemDialogViewModel()
        model.bindViewModel(event.shipmentId, event.boxId, event.customerEmail, event.batchItem, event.callback)
        BaseDialogFragment.setViewModelAndLayoutId(model, R.layout.swap_item_dialog).show(supportFragmentManager)
    }

    @Subscribe
    fun onFailReasonClickEvent(event: QcDetailUIEvent.FailReasonButtonClick) {
        if(featureFlagUtil.isOldQualityCheckSwapOn()) {
            createFailReasonPopUpList(event.anchorView, event.batchId, event.batchItemId, event.shipmentId, event.addNotesResult)
        } else {
            navigator.withExtra(IntentConstant.SHIPMENT_ID, event.shipmentId)
                .withExtra(IntentConstant.BATCH_ID, event.batchId)
                .withExtra(IntentConstant.SWAP_ITEM, Parcels.wrap(event.swapItem))
                .startForResult(QUALITY_SWAP)
                .toQualitySwap()
        }
    }

    @Subscribe
    fun onScannedRfid(event: ScanBarcodeBottomSheetUIEvent.OnScannedRfid) {
        viewModel?.rfid = event.rfid
        viewModel?.swapType = SwapType.ACCURACY_SWAP

        showLoadingScanBarcodeBottomSheet(event.swapItem, event.rfid, SwapType.ACCURACY_SWAP)
    }

    @Subscribe
    fun onErrorSwapItem(event: LoadingScanBarcodeBottomSheetUIEvent.OnErrorSwapItem) {
        showErrorSwapItemBottomSheet(event.errorMessage)
    }

    @Subscribe
    fun onUpdateItemQAFailed(event: LoadingScanBarcodeBottomSheetUIEvent.OnUpdateItemQAFailed) {
        fetchBatchDetail()
    }

    @Subscribe
    fun onUpdateItemSuccess(event: QcDetailUIEvent.OnUpdateItemSuccess) {
        fetchBatchDetail()
    }

    @Subscribe
    fun onErrorSwapItem(event: QcDetailUIEvent.OnErrorSwapItem) {
        showLongToast(event.errorMessage)
    }

    @Subscribe
    fun onRetryClick(event: ErrorSwapItemBottomSheetUIEvent.OnRetryClick) {
        showLoadingScanBarcodeBottomSheet(
            viewModel?.swapItem ?: SwapItem(),
            viewModel?.rfid.orEmpty(), viewModel?.swapType ?: SwapType.QUALITY_SWAP
        )
    }

    @Subscribe
    fun onScanAgainClick(event: ErrorSwapItemBottomSheetUIEvent.OnScanAgainClick) {
        showScanBarcodeBottomSheet(viewModel?.swapItem ?: SwapItem())
    }

    @Subscribe
    fun onConfirmQualitySwapClick(event: ErrorSwapItemBottomSheetUIEvent.OnConfirmQualitySwapClick) {
        viewModel?.updateItemToFailed()
    }

    @Subscribe
    override fun noInternetConnectionEvent(event: NoInternetConnectionEvent) {
        // Do nothing
    }

    private fun createFailReasonPopUpList(anchorView: View, batchId: String, batchItemId: String?, shipmentId: String, resultCallback: Result<List<String>, String?>) {
        val popup = ListPopupWindow(this)

        val adapter = if(userStorage.isVerticalTypeBags()) {
            ArrayAdapter<String>(this, R.layout.simple_spinner_dropdown_item, arrayQAFailedReasonForBags)
        } else {
            ArrayAdapter<String>(this, R.layout.simple_spinner_dropdown_item, arrayQAFailedReasonForApparel)
        }

        popup.setAdapter(adapter)
        popup.anchorView = anchorView
        popup.width = 250
        popup.setDropDownGravity(Gravity.BOTTOM and Gravity.END)
        popup.setOnItemClickListener { _, _, index, _ ->
            val viewModel = FailNotesDialogViewModel()
            viewModel.failCategory.set(adapter.getItem(index))
            viewModel.batchId = batchId
            viewModel.batchItemId = batchItemId
            viewModel.shipmentId = shipmentId
            viewModel.addNotesResult = resultCallback
            BaseDialogFragment.setViewModelAndLayoutId(viewModel, R.layout.fail_notes_dialog).show(supportFragmentManager)
            popup.dismiss()
        }
        popup.show()
    }

    @Subscribe
    fun takePictureEvent(event: TakePictureEvent) {
        takePhotoCallback = event.resultCallback
        viewModel?.shipmentId = event.shipmentId
        viewModel?.batchItem = event.batchItem

        showPhotoGalleryOrTakePictureActivity()
    }

    @Subscribe
    fun previewImage(event: PreviewImageEvent) {
        navigator.toFullScreenImageSlideShowActivity(event.imageUrls)
    }

    @Subscribe
    fun removeShipmentQaPickedEvent(event: QcDetailUIEvent.RemoveShipmentQaPicked) {
        fetchBatchDetail()
    }

    @Subscribe
    fun scannedResultEvent(event: QcDetailUIEvent.OnScannedRfidText) {
        hideKeyboardAndClearText()
        binding?.viewPager?.currentItem = event.position
        eventBus.post(QcDetailUIEvent.OnResultScannedRfid(event.scannedText))
        Handler(Looper.getMainLooper()).postDelayed({ binding?.editTextScannedRfid?.requestFocus() }, IntentConstant.DELAY_TIME_500)
    }

    @Subscribe
    fun onScannedRfidTextNotFound(event: QcDetailUIEvent.OnScannedRfidTextNotFound) {
        hideKeyboardAndClearText()
        val model = AlertDialogViewModel()
        model.setTitle(event.title)
        model.setBody(event.message)
        model.setRightButtonText(string(R.string.ok_button))
            .setRightButtonClickListener(object : AlertDialogViewModel.OnClickListener {
                override fun onClick() {
                    eventBus.post(DismissDialogEvent(AlertDialogViewModel.TAG))
                }
            })
        BaseDialogFragment.setViewModelAndLayoutId(model, R.layout.alert_dialog).show(supportFragmentManager, AlertDialogViewModel.TAG)
    }

    @Subscribe
    fun onAccuracySwapButtonClicked(event: QcDetailUIEvent.OnAccuracySwapButtonClicked) {
        viewModel?.batchStatus?.notNull {
            navigator.withExtra(IntentConstant.SWAP_ITEM, Parcels.wrap(event.swapItem))
                .startForResult(ACCURACY_SWAP).toAccuracySwap()
        }
    }

    @Subscribe
    fun onSwapItemSuccess(event: SwapConfirmationBottomDialogNetworkEvent.OnSwapSuccess) {
        fetchBatchDetail()
    }

    @Subscribe
    fun onSwapItemByBarcodeFound(event: LoadingScanBarcodeBottomSheetNetworkEvent.OnSwapItemFound) {
        SwapConfirmationBottomSheetDialogFragment.newInstance(
            event.oldItem,
            event.newItem,
            event.swapType,
            viewModel?.batchId.orEmpty()
        ).show(supportFragmentManager, IntentConstant.SHOW_BOTTOMSHEET)
    }

    // Implementation of PermissionHandlerActivity interface
    override fun getActivity(): AppCompatActivity = this

    override fun onAllPermissionsGranted(requestCode: Int) {
        EdgeToEdgeUtil.preparePhotoPickerActivityEdgeToEdge()
        PhotosPickerActivity.launch(this, RequestCode.REQ_PHOTOS)
    }

    private fun showPhotoGalleryOrTakePictureActivity() {
        // Use the new permission utility to handle permissions
        PermissionUtils.launchPhotoPicker(this, RequestCode.RC_CAMERA_AND_STORAGE)
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        // Let the permission utility handle the result
        PermissionUtils.handlePermissionResult(this, requestCode, permissions, grantResults)
    }

    private fun processData(photos: ArrayList<Photo>) {
        navigator.withExtras(
            PhotoManagerActivity.createExtras(
                viewModel?.shipmentId
                    .orEmpty(), viewModel?.batchItem, AttachPhotoType.PICKED_PHOTO, photos
            )
        )
            .startForResult(RequestCode.PHOTO_MANAGER)
            .toPhotoManager()
    }

    private fun checkIsBarcodeSettingOn() {
        if(!featureFlagUtil.isBarcodeSettingIsOn()
            && featureFlagUtil.isAccuracySwapIsOn()
            && userStorage.isVerticalTypeApparel()
        ) {
            showBarcodeSettingOffDialog()
        }
    }

    private fun showBarcodeSettingOffDialog() {
        val model = AlertDialogViewModel()
        model.setTitle(string(R.string.qc_info_barcode_setting_off_title))
        model.setBody(string(R.string.qc_info_barcode_setting_off_content))

        model.setLeftButtonText(string(R.string.cancel_label))
            .setLeftButtonClickListener(object : AlertDialogViewModel.OnClickListener {
                override fun onClick() {
                    onBackPressed()
                }
            })

        model.setRightButtonText(string(R.string.go_to_settings_title))
            .setRightButtonClickListener(object : AlertDialogViewModel.OnClickListener {
                override fun onClick() {
                    navigator.toSettings()
                    finish()
                }
            })

        BaseDialogFragment
            .setViewModelAndLayoutId(model, R.layout.alert_dialog)
            .setIsCancelable(false)
            .show(supportFragmentManager, AlertDialogViewModel.TAG)
    }

    //endregion
}