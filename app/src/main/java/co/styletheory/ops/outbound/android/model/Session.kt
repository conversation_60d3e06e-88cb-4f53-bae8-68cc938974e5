package co.styletheory.ops.outbound.android.model

import org.joda.time.DateTime
import org.parceler.Parcel

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 02 March 2018.
 * Description
 *
 * <EMAIL>
 */
@Parcel
data class Session(val accessToken: String = "", val refreshToken: String = "", val exp: Long = 0, val isNewUser: Boolean = false) {
    fun getExpiredDate(): DateTime = DateTime(exp * 1000)
    fun isTokenExpired(): Boolean = getExpiredDate().isBeforeNow
}