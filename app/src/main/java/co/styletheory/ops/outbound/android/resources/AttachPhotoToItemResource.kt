package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.model.enums.AttachPhotoType
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import retrofit2.Call
import retrofit2.Retrofit

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 20 December 2017.
 * Description
 *
 * <EMAIL>
 */
class AttachPhotoToItemResource(
        private val itemId: String,
        private val photoUrls: List<String>,
        val type: AttachPhotoType?
) : GraphQLResource<GraphQLData<Void>>() {
    override fun graphQLRequest(): GraphQLRequest {
        return if(type == AttachPhotoType.PICKED_PHOTO) GraphQLRequestList.ATTACH_QA_IMAGES else GraphQLRequestList.ATTACH_PHOTO_IMAGES
    }

    override fun bodyParameter(): MutableMap<String, Any> {
        bodyParameter["id"] = itemId
        bodyParameter["images"] = photoUrls
        return bodyParameter
    }

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<Void>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.attachImageToItem(uri, createRequestBody()))
    }

}