package co.styletheory.ops.outbound.android.feature.photoDetail.viewModel

import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.Shipment

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 15 December 2017.
 * Description
 *
 * <EMAIL>
 */
interface PhotoDetailItemViewModel : ViewModel {

    fun setRackSection(rackSection: String?)
    fun setRackName(rackName: String?)
    fun setBatchId(batchId: String?)
    fun setShipment(shipment: Shipment?)
    fun mapShipment()
    fun getRackName(): String
}