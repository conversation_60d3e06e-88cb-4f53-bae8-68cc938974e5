package co.styletheory.ops.outbound.android.feature.qcDetail.viewModel

import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.Shipment

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 01 November 2017.
 * Description
 *
 * <EMAIL>
 */
interface QcDetailItemViewModel : ViewModel {

    var shipment: Shipment?
    var rackSection: String
    var rackName: String
    var batchId: String

    fun mapShipment()
    fun checkRfidText(scannedRfidText: String)
    fun enableButton(position: Int?)
}