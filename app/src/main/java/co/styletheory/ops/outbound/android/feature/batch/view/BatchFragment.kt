package co.styletheory.ops.outbound.android.feature.batch.view

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import co.styletheory.ops.android.viewModelComponent.recyclerItemDecoration.DividerDecorationFactory
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.BatchFragmentBinding
import co.styletheory.ops.outbound.android.feature.batch.event.BatchStepActionClickEvent
import co.styletheory.ops.outbound.android.feature.batch.viewModel.BatchViewModel
import co.styletheory.ops.outbound.android.general.adapter.WrapContentLinearLayoutManager
import co.styletheory.ops.outbound.android.general.adapter.impl.MultipleTypeRecyclerViewAdapter
import co.styletheory.ops.outbound.android.general.base.BaseFragment
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.util.IntentConstant
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.util.getSerializableExtraProvider
import co.styletheory.ops.outbound.android.util.notifyChangedAndClearRecycledPool
import org.greenrobot.eventbus.Subscribe
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 31 October 2017.
 * Description
 *
 * <EMAIL>
 */

class BatchFragment : BaseFragment<BatchFragmentBinding, BatchViewModel>() {

    companion object {
        fun newInstance(batchStatus: BatchStatus): BatchFragment {
            val fragment = BatchFragment()
            val bundle = Bundle()
            bundle.putSerializable(IntentConstant.BATCH_STATUS, batchStatus)
            fragment.arguments = bundle
            return fragment
        }
    }

    @Inject
    lateinit var adapter: MultipleTypeRecyclerViewAdapter

    @Inject
    lateinit var errorResponse: ErrorResponse

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if(viewModel == null) {
            fragmentComponent?.inject(this)
            initArgument()
        }
    }

    private fun initArgument() {
        arguments?.let { args ->
            if (args.containsKey(IntentConstant.BATCH_STATUS)) {
                getSerializableExtraProvider<BatchStatus>(args, IntentConstant.BATCH_STATUS)?.let { status ->
                    viewModel?.setBatchStatus(status)
                }
            }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? =
        bindContentView(inflater, container, R.layout.batch_fragment)


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupRecyclerView()

        if(viewModel?.totalItemCount == 0) {
            fetchBatch()
        }
    }

    private val fetchBatchCallback = object : Result<Void?, String?> {
        override fun success(success: Void?) {
            stopSwipeRefresh()
            binding?.recyclerView?.notifyChangedAndClearRecycledPool()
        }

        override fun failure(error: String?) {
            stopSwipeRefresh()
            val errorMessage = errorResponse.getErrorBodyDescription(error)
            showShortToast(errorMessage)
        }

    }

    private fun stopSwipeRefresh() {
        binding?.recyclerView?.swipeToRefresh?.isRefreshing = false
        binding?.emptyContent?.visibility = if(viewModel?.totalItemCount == 0) View.VISIBLE else View.GONE
    }

    private fun fetchBatch() {
        binding?.recyclerView?.swipeToRefresh?.isRefreshing = true
        viewModel?.fetchBatch(fetchBatchCallback)
    }

    private fun setupRecyclerView() {
        binding?.recyclerView?.setLayoutManager(WrapContentLinearLayoutManager(context))
        context?.let { binding?.recyclerView?.addItemDecoration(DividerDecorationFactory.createHorizontalDivider(it)) }
        if(viewModel != null) {
            adapter.source = viewModel
            binding?.recyclerView?.adapter = adapter
        }

        binding?.recyclerView?.setRefreshListener {
            viewModel?.refreshBatch(fetchBatchCallback)
        }
    }

    @SuppressLint("RestrictedApi")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if(isMenuVisible) {
            binding?.recyclerView?.swipeToRefresh?.isRefreshing = true
            viewModel?.refreshBatch(fetchBatchCallback)
        }
    }

    //region EVENT BUS
    @Suppress("unused", "UNUSED_PARAMETER")
    @Subscribe
    fun batchStepActionClickEvent(event: BatchStepActionClickEvent) {
        if(event.batchStatus == viewModel?.batchStatus()) {
            when(event.batchStatus) {
                BatchStatus.IN_BACKLOG,
                BatchStatus.PICKING ->
                    navigator.withExtra(IntentConstant.BATCH_ID, event.batchId.orEmpty())
                        .withExtra(IntentConstant.BATCH_STATUS, event.batchStatus)
                        .startForResult(3).toBacklogDetail()

                BatchStatus.PICKED,
                BatchStatus.QA_PROCESSING ->
                    navigator.withExtra(IntentConstant.BATCH_ID, event.batchId.orEmpty())
                        .withExtra(IntentConstant.BATCH_STATUS, event.batchStatus)
                        .startForResult(3).toPickedDetail()

                BatchStatus.QA_DONE,
                BatchStatus.PHOTO ->
                    navigator.withExtra(IntentConstant.BATCH_ID, event.batchId.orEmpty())
                        .withExtra(IntentConstant.BATCH_STATUS, event.batchStatus)
                        .startForResult(3).toQaPhotoDetails()

                BatchStatus.READY_FOR_PACKING ->
                    navigator.withExtra(IntentConstant.BATCH_ID, event.batchId.orEmpty())
                        .withExtra(IntentConstant.BATCH_STATUS, event.batchStatus)
                        .startForResult(3).toReadyDetails()

                else -> {
                    //Not implemented yet
                }
            }
        }
    }
    //endregion
}