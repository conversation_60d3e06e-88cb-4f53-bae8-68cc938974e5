package co.styletheory.ops.outbound.android.general.storage

/**
 * styletheory-ops-outbound-android
 * Created by dwi<PERSON><PERSON><PERSON> on 08 November 2017.
 * Description
 *
 * <EMAIL>
 */
object StorageConstant {

    const val USER_LOGIN = "user_login"
    const val USER_SESSIONS = "user_sessions"
    const val USER_EMAIL = "user_email"
    const val USER_NAME = "user_name"
    const val USER_REGION = "user_region"
    const val USER_VERTICAL_TYPE = "vertical_type"
    const val USER_VERTICAL_CODE = "vertical_code"
    const val SETTING_BARCODE_APPAREL = "setting_barcode_apparel"
    const val SETTING_BARCODE_BAGS = "setting_barcode_bags"
    const val SETTING_BATCH_DATE = "setting_batch_date"
    const val FEATURE_FLAG_INITIALIZATION = "feature_flag_initialization"
}