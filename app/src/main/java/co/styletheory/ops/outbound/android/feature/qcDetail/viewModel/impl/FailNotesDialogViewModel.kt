package co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.impl

import co.styletheory.android.network.core.RequestResult
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.event.DismissDialogEvent
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseInjectedViewModel
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.resources.BatchItemResource
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.util.Result
import javax.inject.Inject

@Suppress("UNUSED_PARAMETER")
/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 02 November 2017.
 * Description
 *
 * <EMAIL>
 */
class FailNotesDialogViewModel : BaseInjectedViewModel() {

    val failCategory = ObservableString()
    val failReason = ObservableString()
    var addNotesResult: Result<List<String>, String?>? = null
    var batchItemId: String? = ""
    var shipmentId: String? = ""
    var batchId: String? = ""

    @Inject
    lateinit var dataService: DataService
    @Inject
    lateinit var errorResponse: ErrorResponse

    fun cancelClick() {
        eventBus.post(DismissDialogEvent())
    }

    fun addNotesClick() {
        showProgressDialog()
        val resource = BatchItemResource(
                batchId.orEmpty(),
                shipmentId.orEmpty(),
                listOf(batchItemId.orEmpty()),
                ProductStatus.QA_FAILED,
                failCategory.get(),
                failReason.get()
        )
        dataService.updateBatchItemStatus(resource, updateStatusCallback)
    }

    val updateStatusCallback = RequestResult<GraphQLData<Void>, String> {
        onSuccess {
            addNotesResult?.success(listOf(failCategory.get(), failReason.get()))
            dismissProgressDialog()
            eventBus.post(DismissDialogEvent())
        }
        onError {
            addNotesResult?.failure(errorResponse.getErrorBodyDescription(it))
            dismissProgressDialog()
            eventBus.post(DismissDialogEvent())
        }
    }
}