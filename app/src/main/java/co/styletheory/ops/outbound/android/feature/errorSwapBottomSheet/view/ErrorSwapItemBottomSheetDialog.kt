package co.styletheory.ops.outbound.android.feature.errorSwapBottomSheet.view

import android.os.Bundle
import android.view.View
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.BottomsheetScanBarcodeBinding
import co.styletheory.ops.outbound.android.feature.errorSwapBottomSheet.event.ErrorSwapItemBottomSheetUIEvent
import co.styletheory.ops.outbound.android.feature.errorSwapBottomSheet.viewModel.ErrorSwapItemBottomSheetViewModel
import co.styletheory.ops.outbound.android.general.base.BaseBottomSheetDialog
import co.styletheory.ops.outbound.android.model.enums.SwapType
import co.styletheory.ops.outbound.android.util.IntentConstant
import org.greenrobot.eventbus.Subscribe

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 23/06/20.
 */
@Suppress("UNUSED_PARAMETER", "unused")
class ErrorSwapItemBottomSheetDialog : BaseBottomSheetDialog<BottomsheetScanBarcodeBinding, ErrorSwapItemBottomSheetViewModel>() {

    companion object {
        fun newInstance(errorResponse: String, swapType: SwapType): ErrorSwapItemBottomSheetDialog {
            val dialog = ErrorSwapItemBottomSheetDialog()
            val bundle = Bundle()
            bundle.putString(IntentConstant.ERROR_SWAP_ITEM, errorResponse)
            bundle.putString(IntentConstant.SWAP_TYPE, swapType.swapType)
            dialog.arguments = bundle
            return dialog
        }
    }

    override fun getLayoutId(): Int = R.layout.bottomsheet_error_swap_item

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if(viewModel == null) {
            fragmentComponent?.inject(this)
        }
    }

    override fun onLoadBottomSheetDialog(view: View, saveInstance: Bundle?) {
        isCancelable = false
        initArguments()
    }

    private fun initArguments() {
        if(arguments?.containsKey(IntentConstant.ERROR_SWAP_ITEM) == true) {
            val error = arguments?.getString(IntentConstant.ERROR_SWAP_ITEM).orEmpty()
            viewModel?.bindError(error)
        }

        if(arguments?.containsKey(IntentConstant.SWAP_TYPE) == true) {
            val swapType = arguments?.getString(IntentConstant.SWAP_TYPE).orEmpty()
            viewModel?.swapType = swapType
        }
    }

    @Subscribe
    fun onCloseClicked(event: ErrorSwapItemBottomSheetUIEvent.OnCloseClicked) {
        dismiss()
    }

    @Subscribe
    fun onConfirmClick(event: ErrorSwapItemBottomSheetUIEvent.OnConfirmClick) {
        dismiss()
    }

    @Subscribe
    fun onConfirmQualitySwapClick(event: ErrorSwapItemBottomSheetUIEvent.OnConfirmQualitySwapClick) {
        dismiss()
    }

    @Subscribe
    fun onRetryClick(event: ErrorSwapItemBottomSheetUIEvent.OnRetryClick) {
        dismiss()
    }

    @Subscribe
    fun onScanAgainClick(event: ErrorSwapItemBottomSheetUIEvent.OnScanAgainClick) {
        dismiss()
    }

}