package co.styletheory.ops.outbound.android.general.binding

import androidx.databinding.ObservableBoolean

class ObservableChecked constructor() : ObservableBoolean() {

    var listener: CheckedChangeListener? = null

    constructor(isChecked: <PERSON><PERSON><PERSON>, listener: CheckedChangeListener? = null) : this() {
        set(isChecked)
        this.listener = listener
    }

    interface CheckedChangeListener {
        fun onCheckedChange(observable: ObservableChecked, isChecked: <PERSON><PERSON>an)
    }
}