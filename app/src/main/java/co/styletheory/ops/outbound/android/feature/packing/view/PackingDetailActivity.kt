package co.styletheory.ops.outbound.android.feature.packing.view

import android.content.DialogInterface
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.Menu
import android.view.MenuItem
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.widget.Toolbar
import androidx.viewpager.widget.ViewPager
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.ActivityPackingDetailBinding
import co.styletheory.ops.outbound.android.feature.packing.event.PackingItemUIEvent
import co.styletheory.ops.outbound.android.feature.packing.event.PackingNetworkEvent
import co.styletheory.ops.outbound.android.feature.packing.viewModel.PackingDetailViewModel
import co.styletheory.ops.outbound.android.general.adapter.impl.PagerAdapter
import co.styletheory.ops.outbound.android.general.base.BaseActivity
import co.styletheory.ops.outbound.android.general.base.BaseDialogFragment
import co.styletheory.ops.outbound.android.general.event.DismissDialogEvent
import co.styletheory.ops.outbound.android.general.event.PreviewImageEvent
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.util.IntentConstant
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.util.hideSoftKeyboard
import co.styletheory.ops.outbound.android.util.notNull
import co.styletheory.ops.outbound.android.viewModelComponent.AlertDialogViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.ProcessDialogViewModel
import org.greenrobot.eventbus.Subscribe
import javax.inject.Inject

/**
 * Created by giorgygunawan on 11/29/17.
 *
 */
class PackingDetailActivity : BaseActivity<ActivityPackingDetailBinding, PackingDetailViewModel>() {

    @Inject
    lateinit var pagerAdapter: PagerAdapter
    private var currentPage = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        activityComponent?.inject(this)
        bindContentView(R.layout.activity_packing_detail)
        initExtras()
        enableZoomImageView()
        setupToolbar()
        setupViewPager()
        fetchBatchDetail()

        // Handle back button press with the new API
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                navigator.finishWithResult()
            }
        })
    }

    private fun initExtras() {
        intent.getStringExtra(IntentConstant.BATCH_ID).notNull { viewModel?.batchId = it }
        intent.getSerializableExtra(IntentConstant.BATCH_STATUS)
            .notNull { viewModel?.batchStatus = it as BatchStatus }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding?.toolbarWrapper?.toolbar as Toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        binding?.toolbarVM = viewModel?.generalToolbarViewModel()
    }

    private fun setupViewPager() {
        binding.notNull {
            it.viewPager.clipToPadding = false
            it.viewPager.pageMargin = 25
            pagerAdapter.source = viewModel
            it.viewPager.adapter = pagerAdapter

            it.viewPager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
                override fun onPageScrollStateChanged(state: Int) {
                    // not implemented yet
                }

                override fun onPageScrolled(
                    position: Int,
                    positionOffset: Float,
                    positionOffsetPixels: Int
                ) {
                    // not implemented yet
                }

                override fun onPageSelected(position: Int) {
                    val subtitle = "${position + 1} of ${viewModel?.totalViewCount} Box"
                    viewModel?.changeToolbarSubtitle(subtitle)
                }
            })
        }
    }

    private fun fetchBatchDetail() {
        showProgressDialog()
        currentPage = binding?.viewPager?.currentItem ?: 0
        viewModel?.fetchBatchDetail(object : Result<Void?, String?> {
            override fun success(success: Void?) {
                rebindPagerAdapter()
                dismissProgressDialog()
            }

            override fun failure(error: String?) {
                dismissProgressDialog()
                showShortToast(error ?: string(R.string.err_something_when_wrong))
            }
        })
    }


    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.menu_complete, menu)
        return true
    }

    override fun onPrepareOptionsMenu(menu: Menu?): Boolean {
        menu?.findItem(R.id.action_complete)?.isVisible = false
        return super.onPrepareOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if(item.itemId == R.id.action_complete) {
            doCompleteBatch()
            return true
        } else if(item.itemId == R.id.action_refresh) {
            fetchBatchDetail()
            return true
        }
        return super.onOptionsItemSelected(item)
    }


    private fun doCompleteBatch() {
        viewModel.notNull {
            if(it.totalViewCount > 0) {
                if(viewModel?.isAllBatchItemShipmentCompletedPacking() == true) {
                    completeBatch()
                } else {
                    BaseDialogFragment.setViewModelAndLayoutId(
                        ProcessDialogViewModel.failed(this),
                        R.layout.process_dialog
                    ).show(supportFragmentManager)
                }
            } else {
                showShortToast("Shipment not found")
            }
        }

    }

    private fun completeBatch() {
        showProgressDialog()
        viewModel?.completeBatch(object : Result<Void?, String?> {
            override fun success(success: Void?) {
                dismissProgressDialog()
                BaseDialogFragment.setViewModelAndLayoutId(
                    ProcessDialogViewModel.success(
                        viewModel?.batch?.name
                           .orEmpty(), this@PackingDetailActivity
                    ), R.layout.process_dialog
                )
                    .onDialogDismiss(object : DialogInterface {
                        override fun dismiss() {
                            finish()
                        }

                        override fun cancel() {
                            // not implemented yet
                        }
                    }).setDismissAfter(1500).show(supportFragmentManager)
            }

            override fun failure(error: String?) {
                dismissProgressDialog()
                showShortToast(error ?: string(R.string.err_something_when_wrong))
            }
        })
    }

    private fun rebindPagerAdapter() {
        pagerAdapter = PagerAdapter(supportFragmentManager)
        pagerAdapter.source = viewModel
        binding?.viewPager?.adapter = pagerAdapter
        binding?.viewPager?.adapter?.notifyDataSetChanged()
        binding?.viewPager?.currentItem = currentPage
    }

    private fun hideKeyboardAndClearText() {
        binding?.editTextScannedRfid?.let { hideSoftKeyboard(it) }
        binding?.editTextScannedRfid?.text?.clear()
    }

    @Subscribe
    fun scannedResultEvent(event: PackingItemUIEvent.OnScannedRfidFoundPosition) {
        hideKeyboardAndClearText()
        binding?.viewPager?.currentItem = event.itemPosition
        eventBus.post(PackingItemUIEvent.OnScannedRfidTextFetched(event.rfidText))
        Handler(Looper.getMainLooper()).postDelayed(
            { binding?.editTextScannedRfid?.requestFocus() },
            IntentConstant.DELAY_TIME_500
        )
    }

    @Subscribe
    fun onScannedRfidTextNotFound(event: PackingItemUIEvent.OnScannedRfidTextNotFound) {
        hideKeyboardAndClearText()
        val dialogViewModel = AlertDialogViewModel()
        dialogViewModel.setTitle(event.title)
        dialogViewModel.setBody(event.message)
        dialogViewModel.setRightButtonText(string(R.string.ok_button))
        dialogViewModel.setRightButtonClickListener(object : AlertDialogViewModel.OnClickListener {
            override fun onClick() {
                eventBus.post(DismissDialogEvent(AlertDialogViewModel.TAG))
            }
        })

        val dialog =
            BaseDialogFragment.setViewModelAndLayoutId(dialogViewModel, R.layout.alert_dialog)
        dialog.show(supportFragmentManager, AlertDialogViewModel.TAG)
    }

    @Subscribe
    fun previewImage(event: PreviewImageEvent) {
        navigator.toFullScreenImageSlideShowActivity(event.imageUrls)
    }

    @Subscribe
    fun onErrorUpdateItemStatus(event: PackingNetworkEvent.OnErrorUpdateItemStatus) {
        showShortToast(getString(R.string.packing_detail_error_update_item_status).format(event.message))
    }
}