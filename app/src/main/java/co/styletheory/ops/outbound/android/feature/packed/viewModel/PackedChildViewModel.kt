package co.styletheory.ops.outbound.android.feature.packed.viewModel

import androidx.databinding.ObservableArrayList
import co.styletheory.ops.outbound.android.feature.packed.viewModel.impl.PackedItemViewModel
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.LogisticProvider
import co.styletheory.ops.outbound.android.util.Result
import org.joda.time.DateTime

/**
 * Created by g<PERSON><PERSON><PERSON><PERSON><PERSON> on 11/22/17.
 *
 */

interface PackedChildViewModel : ViewModel {
    val packedItems: ObservableArrayList<PackedItemViewModel>
    var logisticProvider: LogisticProvider?
    var shipmentDate: DateTime
    var searchQuery: String
    var logisticOrdered: Boolean?

    fun selectAllItem()
    fun totalSelectedShipment(): Int
    fun isSameLogisticProvider(providerId: String?): Boolean

    fun fetchFirstPage(callback: Result<Void?, String?>?)
    fun fetchNextPage(callback: Result<Void?, String?>?, page: Int)
    fun sendShipmentBox(callback: Result<String?, String?>?)
    fun orderGojek(callback: Result<String?, String?>?)
    fun cancelGojek(callback: Result<Void?, String?>?)
    fun markShipmentAsReceived(shipmentId: String)
}