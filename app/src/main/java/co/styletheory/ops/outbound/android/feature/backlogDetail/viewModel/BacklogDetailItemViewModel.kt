package co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel

import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableInt
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.util.Next
import co.styletheory.ops.outbound.android.viewModelComponent.ColorItemViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.PhotoWithLabelViewModel

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019-09-24.
 */
interface BacklogDetailItemViewModel : ViewModel {

    var isHeaderSection: Boolean
    var batch: Batch?
    var boxId: String
    var customerEmail: String
    var batchItem: BatchItem?

    val itemName: ObservableString
    val itemSize: ObservableString
    val location: ObservableString
    val brand: ObservableString
    val category: ObservableString
    val parts: ObservableString
    val rack: ObservableString
    val soldTo: ObservableString
    val errorMessage: ObservableString
    val swap: ObservableBoolean
    val picked: ObservableBoolean
    val missing: ObservableBoolean
    val showOverlayLoading: ObservableBoolean
    val notes: ObservableString
    val productOrder: ObservableString
    val colorItems: ObservableArrayList<ColorItemViewModel>
    val detachable: ObservableString
    val partsVisibility: ObservableInt
    val detachableVisibility: ObservableInt
    val scannerIconVisibility: ObservableInt
    val isPickedButtonEnabled: ObservableBoolean
    val swapButtonVisibility: ObservableInt
    val swapCallback: Next<BatchItem>
    val itemSectionVisibility: ObservableInt
    val itemPurchasedVisibility: ObservableInt
    val isShowLocation: ObservableInt

    fun setPhotoWithLabelViewModel(): PhotoWithLabelViewModel
    fun setRackSection(rackSection: String?)
    fun setShipmentId(shipmentId: String)
    fun showApparelItems()
    fun showBagsItems()
    fun setupPickedButton(isEnabled: Boolean)
    fun bindViewModel(batch: Batch?, batchItem: BatchItem)
    fun setColorItems(batchItem: BatchItem)
    fun setMissingAndPickedButton(batchItem: BatchItem)
    fun swapClick()
    fun pickedClick()
    fun missingClick()
    fun updateBatchStatus(status: ProductStatus)
    fun updateViewModelStateWith(status: ProductStatus)
    fun showHideError(error: String?)
    fun showScannerIcon()
    fun isComplete(): Boolean
    fun setupItemContainRfid()
    fun setupSwapButtonVisibility(batchItem: BatchItem)
    fun setupRackName(batch: Batch?)
    fun setupPurchasedItem(batchItem: BatchItem)
}