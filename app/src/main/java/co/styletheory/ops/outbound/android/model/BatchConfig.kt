package co.styletheory.ops.outbound.android.model

/**
 * Created by Yoga C. <PERSON> on 2019-08-14.
 * Android Engineer
 */
sealed class BatchConfig {

    data class Result(
            val id: String = "",
            val regionId: String = "",
            val regionOutboundOrder: Int = 0,
            val verticalTypeId: String = "",
            val verticalTypeCode: String = "",
            val verticalTypeOutboundOrder: Int = 0,
            val batchGenerationTime: BatchGenerationTime? = null,
            val region: BatchConfigAttribute? = null,
            val vertical: BatchConfigAttribute? = null
    )

    data class BatchGenerationTime(
            val hour: Int = 0,
            val minute: Int = 0
    )

    data class BatchConfigAttribute(
            val id: String = "",
            val name: String = ""
    )
}