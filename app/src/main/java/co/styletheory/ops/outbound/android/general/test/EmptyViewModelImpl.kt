package co.styletheory.ops.outbound.android.general.test

import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import javax.inject.Inject

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 05 December 2017.
 * Description
 *
 * <EMAIL>
 */
class EmptyViewModelImpl @Inject constructor(): BaseViewModel<EmptyViewModelImpl>(), EmptyViewModel {
    init {
        viewModelClass = EmptyViewModelImpl::class
    }
}