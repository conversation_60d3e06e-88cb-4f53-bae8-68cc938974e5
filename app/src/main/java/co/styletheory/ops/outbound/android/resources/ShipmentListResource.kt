package co.styletheory.ops.outbound.android.resources

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLRequest
import co.styletheory.android.network.resource.graphql.GraphQLResource
import co.styletheory.android.network.util.Func
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.model.response.ShipmentResponse
import co.styletheory.ops.outbound.android.networking.GraphQLRequestList
import co.styletheory.ops.outbound.android.networking.OutboundService
import org.joda.time.DateTime
import retrofit2.Call
import retrofit2.Retrofit

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 25 January 2018.
 * Description
 *
 * <EMAIL>
 */
class ShipmentListResource(
        private val logisticProviderId: String,
        private val dateTime: DateTime,
        private val page: Int,
        private val keyword: String,
        private val logisticOrdered: Boolean? = null
) : GraphQLResource<GraphQLData<ShipmentResponse>>() {
    override fun graphQLRequest(): GraphQLRequest = GraphQLRequestList.SHIPMENT_LIST_BY_PROVIDER

    override fun bodyParameter(): MutableMap<String, Any> {
        bodyParameter["page"] = page
        bodyParameter["date"] = dateTime.toString("yyyy-MM-dd")
        bodyParameter["batchStatus"] = BatchStatus.PACKED.textName
        bodyParameter["logisticProvider"] = logisticProviderId
        if(keyword.isNotEmpty()) bodyParameter["keyword"] = keyword
        if(logisticProviderId.equals("gojek", ignoreCase = true) && logisticOrdered != null) {
            bodyParameter["logisticOrdered"] = logisticOrdered
        }
        return bodyParameter
    }

    override fun retrofitService(retrofit: Retrofit, callback: Func<Call<GraphQLData<ShipmentResponse>>>) {
        val service = retrofit.create(OutboundService::class.java)
        callback.onNext(service.fetchShipmentByLogisticProvider(uri, createRequestBody()))
    }
}