<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- Storage permissions for Android 12 and below -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />

    <!-- Media permissions for Android 13+ -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

    <application
        android:name=".StyletheoryOpsApplication"
        android:allowBackup="true"
        android:icon="@drawable/ic_launcher"
        android:roundIcon="@drawable/ic_launcher"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme.NoActionBar"
        tools:ignore="GoogleAppIndexingWarning"
        tools:replace="android:theme">

        <activity
            android:name=".general.test.EmptyActivity"
            android:exported="false"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".main.view.MainActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".feature.signIn.SignInActivity"
            android:exported="true"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".feature.backlogDetail.view.BacklogDetailActivity"
            android:exported="false"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateHidden" />

        <activity
            android:name=".feature.qcDetail.view.QcDetailActivity"
            android:exported="false"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="stateHidden" />

        <activity
            android:name=".feature.photoManager.view.PhotoManagerActivity"
            android:exported="false"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".general.view.FullScreenImageSlideShowActivity"
            android:exported="false"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".feature.photoDetail.view.PhotoDetailActivity"
            android:exported="false"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".feature.packing.view.PackingDetailActivity"
            android:exported="false"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".feature.settings.view.SettingsActivity"
            android:exported="false"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".feature.accuracySwap.view.AccuracySwapActivity"
            android:exported="false"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".feature.qualitySwap.view.QualitySwapActivity"
            android:exported="false"
            android:theme="@style/AppTheme.NoActionBar"
            android:windowSoftInputMode="adjustResize" />

    </application>

</manifest>