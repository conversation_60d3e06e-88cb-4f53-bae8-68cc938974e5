<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/color_primary</item>
        <item name="colorPrimaryDark">@color/color_primary_dark</item>
        <item name="colorAccent">@color/light_green</item>
        <item name="android:windowBackground">@color/gray_e5</item>
        <item name="android:buttonStyle">@style/Widget.Button</item>
        <item name="android:textViewStyle">@style/Widget.TextView</item>
        <item name="popupTheme">@style/AppTheme.PopupOverlay</item>
        <item name="dropDownListViewStyle">@style/spinnerDivider</item>
    </style>
    <style name="AppTheme.NoActionBar" parent="AppTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />
    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="Widget"/>
    <style name="Widget.NestedTabLayout" parent="Widget.Design.TabLayout">
        <item name="tabIndicatorColor">@color/light_blue</item>
        <item name="tabIndicatorHeight">2dp</item>
        <item name="tabTextAppearance">@style/TabTextAppearance</item>
        <item name="tabTextColor">@color/color_primary</item>
        <item name="tabMode">fixed</item>
        <item name="android:elevation">2dp</item>
        <item name="android:background">@color/white</item>
    </style>
    <style name="TabTextAppearance" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">12sp</item>
        <item name="android:fontFamily">@font/opensans_semibold</item>
        <item name="textAllCaps">false</item>
    </style>
    <style name="SubTabTextAppearance" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">12sp</item>
        <item name="android:fontFamily">@font/opensans_regular</item>
        <item name="textAllCaps">false</item>
    </style>
    <style name="Widget.Button">
        <!--<item name="android:background">@drawable/button_primary</item>-->
        <item name="android:foreground">?attr/selectableItemBackground</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:fontFamily">@font/opensans_regular</item>
        <item name="android:gravity">center_vertical|center_horizontal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:textSize">12sp</item>
        <item name="android:paddingLeft">@dimen/button_padding_side</item>
        <item name="android:paddingRight">@dimen/button_padding_side</item>
        <item name="android:paddingTop">@dimen/button_padding_top_bottom</item>
        <item name="android:paddingBottom">@dimen/button_padding_top_bottom</item>
    </style>

    <style name="BaseDialogStyle" parent="Base.Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:textColorPrimary">@color/gray_18</item>
    </style>
    <style name="DialogStyle90" parent="BaseDialogStyle">
        <item name="android:windowMinWidthMajor">90%</item>
        <item name="android:windowMinWidthMinor">90%</item>
    </style>

    <style name="Widget.Button.Dialog">
        <item name="android:fontFamily">@font/opensans_semibold</item>
        <item name="android:background">@color/white</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:paddingStart">@dimen/space_medium</item>
        <item name="android:paddingEnd">@dimen/space_medium</item>
        <item name="android:paddingTop">@dimen/space_small</item>
        <item name="android:paddingBottom">@dimen/space_small</item>
    </style>

    <style name="Widget.Button.Big">
        <item name="android:fontFamily">@font/opensans_regular</item>
        <item name="android:textSize">18sp</item>
        <item name="android:padding">0dp</item>
    </style>
    <style name="Widget.Button.Big.SemiBold">
        <item name="android:fontFamily">@font/opensans_semibold</item>
        <item name="android:textSize">18sp</item>
        <item name="android:padding">0dp</item>
    </style>
    <style name="Widget.Button.Big.Bold">
        <item name="android:fontFamily">@font/opensans_bold</item>
        <item name="android:textSize">18sp</item>
        <item name="android:padding">0dp</item>
    </style>

    <style name="Widget.Button.Medium">
        <item name="android:fontFamily">@font/opensans_regular</item>
        <item name="android:textSize">18sp</item>
        <item name="android:padding">0dp</item>
    </style>
    <style name="Widget.Button.Medium.SemiBold">
        <item name="android:fontFamily">@font/opensans_semibold</item>
        <item name="android:textSize">16sp</item>
        <item name="android:padding">0dp</item>
    </style>
    <style name="Widget.Button.Medium.Bold">
        <item name="android:fontFamily">@font/opensans_bold</item>
        <item name="android:textSize">16sp</item>
        <item name="android:padding">0dp</item>
    </style>

    <style name="Widget.Button.Small">
        <item name="android:fontFamily">@font/opensans_regular</item>
        <item name="android:textSize">14sp</item>
        <item name="android:padding">0dp</item>
    </style>
    <style name="Widget.Button.Small.SemiBold">
        <item name="android:fontFamily">@font/opensans_semibold</item>
        <item name="android:textSize">14sp</item>
        <item name="android:padding">0dp</item>
    </style>
    <style name="Widget.Button.Small.Bold">
        <item name="android:fontFamily">@font/opensans_bold</item>
        <item name="android:textSize">14sp</item>
        <item name="android:padding">0dp</item>
    </style>

    <style name="Widget.Button.ButtonPrimary">
        <item name="android:textColor">@color/white</item>
    </style>
    <style name="Widget.Button.ButtonPrimary.Small">
        <item name="android:paddingTop">@dimen/button_padding_top_bottom_small</item>
        <item name="android:paddingBottom">@dimen/button_padding_top_bottom_small</item>
    </style>
    <style name="Widget.Button.ButtonSecondary">
        <item name="android:textColor">@drawable/selector_text_color_accent</item>
    </style>
    <style name="Widget.Button.ButtonSecondary.Small">
        <item name="android:paddingTop">@dimen/button_padding_top_bottom_small</item>
        <item name="android:paddingBottom">@dimen/button_padding_top_bottom_small</item>
    </style>
    <style name="Widget.Button.ButtonTertiary">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:textColor">@drawable/selector_text_color_accent</item>
    </style>
    <style name="Widget.Button.ButtonTertiary.Small">
        <item name="android:paddingTop">@dimen/button_padding_top_bottom_small</item>
        <item name="android:paddingBottom">@dimen/button_padding_top_bottom_small</item>
    </style>

    <style name="Widget.TextView" >
        <item name="android:textSize">17sp</item>
        <item name="android:fontFamily">@font/opensans_semibold</item>
    </style>

    <style name="Widget.TextView.Header1">
        <item name="android:textSize">20sp</item>
        <item name="android:fontFamily">@font/opensans_bold</item>
    </style>
    <style name="Widget.TextView.Header1.Medium">
        <item name="android:textSize">20sp</item>
        <item name="android:fontFamily">@font/opensans_semibold</item>
    </style>

    <style name="Widget.TextView.Header2">
        <item name="android:textSize">18sp</item>
        <item name="android:fontFamily">@font/opensans_bold</item>
    </style>
    <style name="Widget.TextView.Header2.Medium">
        <item name="android:textSize">18sp</item>
        <item name="android:fontFamily">@font/opensans_semibold</item>
    </style>

    <style name="Widget.TextView.Header3">
        <item name="android:textSize">17sp</item>
        <item name="android:fontFamily">@font/opensans_bold</item>
    </style>
    <style name="Widget.TextView.Header3.Medium">
        <item name="android:textSize">17sp</item>
        <item name="android:fontFamily">@font/opensans_semibold</item>
    </style>


    <style name="Widget.TextView.SubHeader">
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">@font/opensans_regular</item>
    </style>
    <style name="Widget.TextView.SubHeader.Medium">
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">@font/opensans_semibold</item>
    </style>
    <style name="Widget.TextView.SubHeader.Bold">
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">@font/opensans_bold</item>
    </style>

    <style name="Widget.TextView.SubHeader1">
        <item name="android:textSize">15sp</item>
        <item name="android:fontFamily">@font/opensans_regular</item>
    </style>
    <style name="Widget.TextView.SubHeader1.Medium">
        <item name="android:textSize">15sp</item>
        <item name="android:fontFamily">@font/opensans_semibold</item>
    </style>
    <style name="Widget.TextView.SubHeader1.Bold">
        <item name="android:textSize">15sp</item>
        <item name="android:fontFamily">@font/opensans_bold</item>
    </style>

    <style name="Widget.TextView.SubHeader2">
        <item name="android:textSize">14sp</item>
        <item name="android:fontFamily">@font/opensans_regular</item>
    </style>
    <style name="Widget.TextView.SubHeader2.Medium">
        <item name="android:textSize">14sp</item>
        <item name="android:fontFamily">@font/opensans_semibold</item>
    </style>

    <style name="Widget.TextView.BodyCopy1">
        <item name="android:textSize">13sp</item>
        <item name="android:fontFamily">@font/opensans_regular</item>
    </style>
    <style name="Widget.TextView.BodyCopy1.Medium">
        <item name="android:textSize">13sp</item>
        <item name="android:fontFamily">@font/opensans_semibold</item>
    </style>

    <style name="Widget.TextView.BodyCopy2">
        <item name="android:textSize">12sp</item>
        <item name="android:fontFamily">@font/opensans_regular</item>
    </style>
    <style name="Widget.TextView.BodyCopy2.Medium">
        <item name="android:textSize">12sp</item>
        <item name="android:fontFamily">@font/opensans_semibold</item>
    </style>
    <style name="Widget.TextView.BodyCopy2.Bold">
        <item name="android:textSize">12sp</item>
        <item name="android:fontFamily">@font/opensans_bold</item>
    </style>

    <style name="Widget.TextView.BodyCopy3">
        <item name="android:textSize">11sp</item>
        <item name="android:fontFamily">@font/opensans_regular</item>
    </style>
    <style name="Widget.TextView.BodyCopy3.Medium">
        <item name="android:textSize">11sp</item>
        <item name="android:fontFamily">@font/opensans_semibold</item>
    </style>

    <style name="spinnerDivider" parent="android:Widget.ListView.DropDown">
        <item name="android:divider">@color/very_light_gray</item>
        <item name="android:dividerHeight">1dp</item>
    </style>

    <style name="CheckBoxStyle" parent="Base.Theme.AppCompat">
        <item name="colorAccent">@color/light_green</item>
        <item name="android:textColorSecondary">@color/gray_de</item>
    </style>
</resources>
