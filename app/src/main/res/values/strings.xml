<resources>
    <string name="app_name">OPS Outbound</string>
    <string name="action_settings">Settings</string>
    <string name="section_format">Hello World from section: %1$d</string>

    // Provider Service Label
    <string name="gojek_logistic_provider">Gojek</string>
    <string name="honestbee_logistic_provider">Honest Bee</string>
    <string name="singapore_post_logistic_provider">Singapore Post</string>
    <string name="self_collect_logistic_provider">Self Collect</string>
    <string name="unknown_logistic_provider">Unknown Provider</string>
    <string name="styletheory_logistic_provider">Style Theory</string>
    <string name="piing_logistic_provider">Piing</string>

    // General Label
    <string name="warehouse_sale_label">Warehouse Sale</string>
    <string name="vip_label">VIP</string>
    <string name="first_box_label">First Box</string>
    <string name="no_paper_label">No Paper</string>
    <string name="swap_label">Swap</string>
    <string name="missing_label">Missing</string>
    <string name="picked_label">Picked</string>
    <string name="how_is_this_item_condition_label">How is this item condition?</string>
    <string name="batches_label">Batches</string>
    <string name="fail_reason_label">Fail Reason</string>
    <string name="pass_label">Pass</string>
    <string name="no_attachment_picture_label">No attachment picture</string>
    <string name="camera_label">Camera</string>
    <string name="in_complete_label">In Complete</string>
    <string name="complete_label">Complete</string>
    <string name="cancel_label">Cancel</string>
    <string name="add_notes_label">Add Notes</string>
    <string name="add_notes_optional_hint">Add notes (optional)</string>
    <string name="sewing_label">Sewing</string>
    <string name="stain_label">Stain</string>
    <string name="other_label">Other</string>
    <string name="sign_in_label">Sign In</string>
    <string name="password_label">Password</string>
    <string name="email_label">Email</string>
    <string name="forgot_your_password_label">Forgot your password?</string>
    <string name="name_label">Name</string>
    <string name="additional_information_label">Additional Information</string>
    <string name="error_fill_required_field">Please fill all required field</string>
    <string name="sign_out_label">Sign Out</string>
    <string name="take_label">Take</string>
    <string name="err_something_when_wrong">Oops, Something when wrong.</string>
    <string name="err_failed_to_update_status">Failed to Update Status</string>
    <string name="not_found_label">Not Found</string>
    <string name="complete_batch_label">Complete Rack</string>
    <string name="swap_item_label">Swap Item</string>
    <string name="swap_dialog_title">Are you sure you want to swap this item?</string>
    <string name="swap_dialog_body">We will find the similar items, and swapped it for you</string>
    <string name="backlog_label">Picking</string>
    <string name="items_completed_label">Items completed</string>
    <string name="items_left_label">Items left</string>
    <string name="rack_label">Rack</string>
    <string name="items_label">Items</string>
    <string name="boxes_label">Boxes</string>
    <string name="boxes_picked_label">Boxes Picked</string>
    <string name="items_picked_label">Items Picked</string>
    <string name="progress_by_label">Progress By</string>
    <string name="continue_label">Continue</string>
    <string name="err_fail_notes_required">Fail notes required</string>
    <string name="err_please_complet_all_shipment">Please complete all shipment</string>
    <string name="close_label">Close</string>
    <string name="new_password_label">New Password</string>
    <string name="confirm_new_password_label">Confirm new Password</string>
    <string name="creating_your_account_label">Creating your account</string>
    <string name="login_success_label">Login Success</string>
    <string name="err_incorrect_username_or_password">Incorrect username or password.</string>
    <string name="err_user_does_not_exist">User does not exist.</string>
    <string name="select_all">Select All</string>
    <string name="send">Send</string>
    <string name="ninjavan_logistic_provider">ninjavan</string>
    <string name="boxes_qa_label">Boxes QA</string>
    <string name="items_qa_label">Items QA</string>
    <string name="has_not_been_picked_up_label">Has not been picked up</string>
    <string name="spoilt_zipper_label">Spoilt Zipper</string>
    <string name="fabric_defect_label">Fabric Defect</string>
    <string name="bad_odour_label">Bad Odour</string>
    <string name="missing_belt_label">Missing Belt</string>
    <string name="wrong_item_label">Wrong Item</string>
    <string name="hardware_damage_label">Hardware Damage</string>

    <string name="rfid_label">RFID</string>
    <string name="size_label">Size</string>
    <string name="brand_label">Brand</string>
    <string name="category_label">Category</string>
    <string name="parts_label">Parts</string>
    <string name="notes_label">Notes</string>
    <string name="color_label">Color</string>
    <string name="status_label">Status</string>
    <string name="detachable_label">Detachable</string>
    <string name="photo_done_label">Photo Done</string>
    <string name="done_taking_a_photo_label">Done taking a photo?</string>
    <string name="err_empty_attachment_picture">Attachment picture is empty</string>
    <string name="complete_order_and_print_label"><![CDATA[Complete order & Print Label]]></string>
    <string name="complete_order_label">Complete order</string>
    <string name="shipping_boxes_label">Shipping Boxes</string>
    <string name="tote_bag_label">Tote Bag</string>
    <string name="no_tote_bag_label">No Tote Bag</string>
    <string name="no_shipping_boxes_label">No Shipping Boxes</string>

    <string name="missing_detachables_label">Missing Detachables</string>
    <string name="missing_hardware_label">Missing Hardware</string>
    <string name="stained_bag_label">Stained Bag</string>
    <string name="bags_with_defected_parts_label">Bag with Defected Parts</string>
    <string name="bags_with_defected_hardware_label">Bags with Defected Hardware</string>
    <string name="hardware_defect_label">Hardware defect</string>
    <string name="smelly_bag_label">Smelly bag</string>
    <string name="edging_label">Edging</string>

    <string name="warn_ready_item_for_rented">Item not picked up yet.</string>
    <string name="warn_ready_item_for_picking">Item being picked up.</string>
    <string name="warn_ready_item_for_picked">Item not QA yet.</string>
    <string name="warn_ready_item_for_paid">Item not picked up yet.</string>
    <string name="warn_ready_item_for_not_found">Item is missing from our warehouse.</string>
    <string name="warn_ready_item_for_qa">Item being QA.</string>
    <string name="warn_ready_item_for_qa_passed">item not photographed yet.</string>
    <string name="warn_ready_item_for_qa_failed">Item failed from QA.</string>
    <string name="warn_ready_item_for_qa_failed_confirmed">Item failed from QA and waiting for swap.</string>
    <string name="warn_ready_item_for_photo_qa">Item being photographed.</string>

    <string name="warn_rfid_notfound_title">Item not in the list</string>
    <string name="warn_rfid_notfound_message">Please scan another item\n\nScanned RFID number: %1$s</string>
    <string name="warn_rfid_can_not_process_title">Item cannot be processed</string>
    <string name="warn_rfid_current_status_message">The item current status is: \'%1$s\'\n\nScanned RFID number: %2$s</string>

    <string name="join_label">Join</string>
    <string name="boxes_photo_label">Boxes Photographer</string>
    <string name="items_photo_label">Items Photographer</string>
    <string name="boxes_packed_label">Boxes Packed</string>
    <string name="items_packed_label">Items Packed</string>
    <string name="completed_label">Completed</string>
    <string name="choose_or_take_photos_label">Choose or take photos</string>
    <string name="expedite_label">Expedite</string>
    <string name="self_collect_label">Self Collect</string>
    <string name="fail_label">Fail</string>
    <string name="send_shipment_dialog_body">The process may take a while, please wait until the process is done</string>
    <string name="send_shipment_dialog_title">You will sending %1$s boxes, using %2$s</string>
    <string name="send_box_label">Send Box</string>
    <string name="box_already_sent_label">Box already sent!</string>
    <string name="please_select_the_shipment_label">Please select the shipment</string>
    <string name="shipment_id_label">Shipment Id</string>
    <string name="no_batch_to_display_label">No racks to display</string>
    <string name="shipment_empty_state_title_label">Looks like all boxes already shipped</string>
    <string name="pick_up_date_label">Pick up Date</string>
    <string name="signing_out_label">Signing Out...</string>
    <string name="err_update_user_data">Please update user data</string>
    <string name="swap_dialog_loading_title">Currently swapping item</string>
    <string name="swap_dialog_item_not_found">Sorry, there is no item found :(</string>
    <string name="swap_dialog_contact_help">Please contact the customer service to help you</string>

    <string name="divider">:</string>

    <!-- Settings -->
    <string name="generate_title">Generate Batch</string>
    <string name="settings_title">Settings</string>
    <string name="region_text">Region</string>
    <string name="vertical_text">Vertical</string>
    <string name="batch_date_text">Batch Date</string>
    <string name="settings_region_id">Indonesia</string>
    <string name="settings_region_sg">Singapore</string>
    <string name="settings_vertical_apparel">Apparel</string>
    <string name="settings_vertical_bags">Bags</string>
    <string name="settings_barcode_apparel_title">Barcode - Apparel &amp; On Demand</string>
    <string name="settings_barcode_bags_title">Barcode - Bags</string>
    <string name="settings_barcode_on">On</string>
    <string name="settings_barcode_off">Off</string>
    <string name="settings_button_generate_batch">Generate</string>
    <string name="settings_button_apply_filter">Apply</string>
    <string name="settings_dialog_generate_batch_title">Generate Batch</string>
    <string name="settings_dialog_generate_batch_content">You are going to generate this batch for</string>
    <string name="settings_dialog_generate_batch_region_title">Region</string>
    <string name="settings_dialog_generate_batch_vertical_title">Vertical</string>
    <string name="settings_dialog_generate_batch_time_title">Batch</string>
    <string name="settings_dialog_generate_batch_divider">:</string>
    <string name="settings_failed_fetch_batch_config_title">Failed To Load Batch Configs!</string>
    <string name="settings_failed_fetch_batch_config_content">Sorry, please contact our tech team!</string>
    <string name="settings_batch_config_not_active">Vertical "%s" is not active yet!</string>
    <string name="settings_app_version_debug">OPS Outbound Version %1$s %2$s</string>
    <string name="settings_app_version_release">OPS Outbound Version %1$s</string>
    <string name="ok_button">OK</string>

    <!--Go Send Fragment-->
    <string name="go_send_fragment_to_be_ordered">To be ordered</string>
    <string name="go_send_fragment_ordered">Ordered</string>
    <string name="gosend_fragment_order_go_send">Order GO Send</string>
    <string name="pickup_at">Pickup at :</string>

    <!--Packed Fragment-->
    <string name="packed_fragment_cancel_shipment">Cancel Shipment</string>
    <string name="packed_fragment_cancel_gojek_dialog_title">Are you sure you want to cancel this shipment?</string>
    <string name="packed_fragment_cancel_gojek_dialog_message">This action can’t be undone. You can re order by using To be ordered tab</string>
    <string name="packed_fragment_shipment_canceled">Shipment Canceled</string>
    <string name="packed_fragment_gojek_ordered">Gojek Ordered</string>

    <string name="rack_name_on_demand">%1$s - %2$s</string>
    <string name="rack_name_regular">%1$s%2$s</string>
    <string name="rack_name_photo_detail">%1$s / %2$s</string>

    <string name="detail_item_title">%1$s / %2$s%3$s</string>
    <string name="detail_item_on_demand_title">%1$s / %2$s - %3$s</string>

    <string name="packed_fragment_received_by_warehouse_dialog_title">Do you want to confirm receiving this box?</string>
    <string name="packed_fragment_received_by_warehouse_dialog_message">This action can’t be undone.</string>

    <!--QC Detail-->
    <string name="qc_title">%s - QC</string>
    <string name="box_title">Box %s</string>
    <string name="qc_quality_check_status_title">Quality Check Status</string>
    <string name="qc_accuracy_swap_button">Accuracy Swap</string>
    <string name="qc_info_barcode_setting_off_title">Please turn on your barcode setting</string>
    <string name="qc_info_barcode_setting_off_content">You can only perform QC using barcode scanners. Please go to Settings and switch to Barcode - On.</string>
    <string name="go_to_settings_title">Go To Settings</string>

    <!--  Accuracy Swap  -->
    <string name="accuracy_swap_title">Accuracy Swap</string>
    <string name="accuracy_swap_info">To swap this item, please make sure you follow the reasons below.</string>
    <string name="accuracy_swap_reason_title">Swap Reason</string>
    <string name="accuracy_swap_now_title">Swap Now</string>
    <string name="accuracy_swap_scan_barcode">Scan Barcode</string>
    <string name="accuracy_swap_this_item">Swapping This Item</string>
    <string name="accuracy_swap_bottom_sheet_scan_info">Please scan the new item that you\'ve found.</string>
    <string name="accuracy_swap_bottom_sheet_scan_loading">Please wait, currently swapping your item...</string>
    <string name="error_swap_message">The item that you’ve scanned has different designer, style, or size. Please recheck your item or search for other replacement.</string>
    <string name="error_swap_message_no_network">Oops, swap failed. Please make sure you have a stable internet connection.</string>
    <string name="error_retry">Retry</string>
    <string name="error_scan_other_item">Scan Other Item</string>
    <string name="error_scan_parent_item">Scan Parent Item</string>
    <string name="error_confirm">Got It</string>
    <string-array name="swap_reason_list">
        <item>Select swap reason</item>
        <item>Wrong Label Number</item>
    </string-array>

    <!-- Swap By Scan -->
    <string name="confirm_button_text">Confirm</string>
    <string name="no_available_item_found_title">No Available items found</string>
    <string name="no_available_item_found_text">Please contact Customer Experience to arrange a new item for the customer</string>
    <string name="old_item_label_text">Previous</string>
    <string name="new_item_label_text">New</string>
    <string name="item_found_swap_note">After you tap \'Confirm\', your item will be swapped. Please perform the necessary QC on the new item.</string>
    <string name="swap_by_scan_qc_info">After you tap \'Confirm\', please go to the Picking tab to pick and process your new item.</string>
    <string name="item_swapping_loading_text">Please wait, currently swapping your item…</string>

    <!--  Quality Swap  -->
    <string name="quality_swap_title">Quality Swap</string>
    <string name="quality_check_title">Quality Check</string>
    <string name="quality_swap_info">You can only swap if this item has failed QC. Please choose the reason below and explain further in the notes.</string>
    <string name="quality_swap_reason_title">Fail Reason</string>
    <string name="quality_notes_title">Notes</string>
    <string name="quality_notes_placeholder">Tap to type hold reason details here</string>
    <string name="quality_photos_title">Photos</string>
    <string name="quality_photos_button_title">Upload item photos</string>
    <string name="quality_swap_now_title">Swap Now</string>
    <string name="quality_swap_now_confirm_title">Confirm</string>
    <string name="quality_swap_success_upload_photos">Photo successfully uploaded!</string>
    <string name="quality_swap_bottom_sheet_apply_title">Applying Status</string>
    <string name="quality_swap_bottom_sheet_apply_content">Please wait, we\'re applying your QA Failed status...</string>
    <string-array name="apparel_fail_reason_list">
        <item>Select fail reason</item>
        <item>Sewing</item>
        <item>Stain</item>
        <item>Spoilt zipper</item>
        <item>Fabric defect</item>
        <item>Hardware damage</item>
        <item>Bad odour</item>
        <item>Missing belt</item>
        <item>Wrong designer / style / size</item>
        <item>Other</item>
    </string-array>
    <string-array name="bags_fail_reason_list">
        <item>Select fail reason</item>
        <item>Missing Detachables</item>
        <item>Missing Hardware</item>
        <item>Stained Bag</item>
        <item>Bag with Defected Parts</item>
        <item>Bags with Defected Hardware</item>
        <item>Hardware defect</item>
        <item>Smelly bag</item>
        <item>Edging</item>
    </string-array>

    <!-- Process Dialog -->
    <string name="process_dialog_complete_dialog_title_success">Rack %1$s, Complete</string>
    <string name="process_dialog_complete_dialog_title_error">There is incomplete item in this rack</string>

    <!--  GoSend Radio Group  -->
    <string name="gosend_to_be_ordered">To be ordered</string>
    <string name="gosend_ordered">Ordered</string>

    <!--  Packing  -->
    <string name="packing_detail_items_title">Item</string>
    <string name="packing_detail_items_subtitle">List of items in this box</string>
    <string name="packing_detail_pickup_at">Pickup at  :</string>
    <string name="packing_detail_packing_info_title">Packing Information</string>
    <string name="packing_detail_packing_info_subtitle">Please make sure to review customer requests</string>
    <string name="packing_detail_customer_request_title">Customer Request</string>
    <string name="packing_detail_service_title">Service</string>
    <string name="packing_detail_surprise_kit_title">Edits [GREEN]</string>
    <string name="packing_detail_reselling_title">Reselling [GREY]</string>
    <string name="packing_detail_service_procedure_type_sample">Amaze, Lock&amp;Go</string>
    <string name="procedure_type_one_to_one">Amaze</string>
    <string name="procedure_type_regular_with_lock">Lock&amp;Go</string>
    <string name="procedure_type_one_to_one_with_lock">Amaze, Lock&amp;Go</string>
    <string name="packing_detail_error_update_item_status">Error update item status: %1$s</string>

    <!--  Rewards  -->
    <string name="rewards_text">Reward(s)</string>
    <string name="rewards_list_text">List of reward(s) in this box</string>

    <!-- Permission Strings -->
    <string name="permission_required">Permission Required</string>
    <string name="go_to_settings">Go to Settings</string>
    <string name="camera_permission_rationale">Camera permission is needed to take photos.</string>
    <string name="storage_permission_rationale">Storage permission is needed to access your photos.</string>
    <string name="all_permissions_rationale">Camera and storage permissions are needed to take and access photos.</string>
</resources>
