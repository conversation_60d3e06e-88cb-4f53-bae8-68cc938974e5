<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.viewModelComponent.HeaderTotalBatchViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/Widget.TextView.SubHeader.Medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_medium"
                android:text="@{viewModel.totalBatch}"
                android:textStyle="bold" />

            <TextView
                style="@style/Widget.TextView.SubHeader"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_tiny"
                android:text="@string/batches_label"
                android:textColor="@color/very_dark_gray" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_small"
            android:background="@color/gray_2e8"
            android:visibility="gone" />
    </LinearLayout>
</layout>