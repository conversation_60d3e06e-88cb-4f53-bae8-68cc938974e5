<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <import type="androidx.core.content.ContextCompat" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.viewModelComponent.ServiceMethodWidgetViewModel" />
    </data>

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <TextView
            style="@style/Widget.TextView.SubHeader2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@{viewModel.serviceName}" />

    </RelativeLayout>
</layout>