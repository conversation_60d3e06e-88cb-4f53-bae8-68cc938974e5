<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="androidx.core.content.ContextCompat" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.viewModelComponent.LabelBoxViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_semi_medium"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/icon_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:scaleType="fitCenter"
            android:srcDrawable="@{viewModel.labelType.drawableRes}"
            app:layout_constraintBottom_toBottomOf="@id/name_label"
            app:layout_constraintEnd_toStartOf="@id/name_label"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/name_label"
            style="@style/Widget.TextView.SubHeader2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_small"
            android:layout_marginEnd="@dimen/space_medium_large"
            android:textBinding="@{viewModel.labelName}"
            android:textColor="@color/very_dark_gray"
            app:layout_constraintStart_toEndOf="@id/icon_label"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="No Tote Bag" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>