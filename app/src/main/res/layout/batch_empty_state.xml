<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <import type="android.view.View" />

        <variable
            name="title"
            type="String" />
    </data>

    <RelativeLayout xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/constraintLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context="co.styletheory.ops.outbound.android.main.view.MainActivity$PlaceholderFragment">


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_my_box_ready" />

            <TextView
                android:id="@+id/section_label"
                style="@style/Widget.TextView.Header1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_medium"
                android:padding="@dimen/space_medium"
                android:text="@{title}"
                android:textColor="@color/gray_9b"
                android:textStyle="bold" />
        </LinearLayout>

    </RelativeLayout>
</layout>