<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.packed.viewModel.impl.PackedItemViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/space_medium"
        android:layout_marginRight="@dimen/space_medium"
        android:layout_marginTop="@dimen/space_medium"
        android:background="@drawable/rect_white_border_gray_rad_4"
        android:onClick="@{() -> viewModel.onBoxClicked()}"
        android:orientation="vertical"
        android:paddingTop="@dimen/space_medium">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatCheckBox
                android:id="@+id/check_box"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="@dimen/space_semi_medium"
                android:visibility="@{viewModel.checkedBoxVisibility}"
                android:layout_marginTop="@dimen/space_medium"
                android:checked="@{viewModel.isSelected}"
                android:onClick="@{() -> viewModel.onCheckButtonClicked()}" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_toStartOf="@id/check_box"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/package_name"
                    style="@style/Widget.TextView.Header2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingEnd="@dimen/space_large"
                    android:paddingStart="@dimen/space_large"
                    android:text="@{viewModel.packageName, default= `ST-1001`}"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/package_author"
                    style="@style/Widget.TextView.SubHeader1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:paddingEnd="@dimen/space_large"
                    android:paddingStart="@dimen/space_large"
                    android:text="@{viewModel.packageAuthor, default= `Siti Nurbaya`}" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/space_large"
                    android:layout_marginTop="@dimen/space_semi_medium"
                    android:orientation="horizontal"
                    android:paddingEnd="@dimen/space_large"
                    android:paddingStart="@dimen/space_large">

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/pickup_at" />

                    <TextView
                        style="@style/Widget.TextView.SubHeader2.Medium"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/space_small"
                        android:text="@{viewModel.pickupAt, default= `20.dd`}"
                        android:textStyle="bold" />
                </LinearLayout>
            </LinearLayout>

        </RelativeLayout>

        <include
            layout="@layout/divider_section_content"
            bind:isVisible="@{true}" />

        <co.styletheory.ops.outbound.android.view.AdapterFlowLayout
            android:id="@+id/flow_label"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="@dimen/space_medium"
            android:paddingEnd="@dimen/space_medium"
            android:paddingStart="@dimen/space_medium"
            android:paddingTop="@dimen/space_small"
            app:adapterFlowLayoutId="@{@layout/label_view}"
            app:adapterFlowLayoutItems="@{viewModel.labelItems}"
            app:flChildSpacing="@dimen/space_medium"
            app:flRowSpacing="@dimen/space_small" />


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="@{viewModel.trackingVisibility}">

            <include
                layout="@layout/divider_section_content"
                bind:isVisible="@{true}" />

            <TextView
                android:id="@+id/text_view_tracking_status"
                style="@style/Widget.TextView.Header2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:paddingBottom="@dimen/space_semi_medium"
                android:paddingEnd="@dimen/space_medium"
                android:paddingStart="@dimen/space_medium"
                android:paddingTop="@dimen/space_semi_medium"
                android:text="@{viewModel.trackingStatus, default= `ST-1001`}"
                android:textColor="@color/very_light_gray"
                android:textStyle="bold"
                android:visibility="@{viewModel.trackingStatusVisibility}" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/text_view_tracking_status"
                android:paddingBottom="@dimen/space_semi_medium"
                android:paddingEnd="@dimen/space_medium"
                android:paddingStart="@dimen/space_medium"
                android:paddingTop="@dimen/space_semi_medium"
                android:visibility="@{viewModel.courierInfoVisibility}">

                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/image_view_courier"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_centerVertical="true"
                    android:imageUrl="@{viewModel.courierImage}" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_view_call"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:minHeight="@dimen/space_large"
                    android:minWidth="@dimen/space_large"
                    android:onClick="@{() -> viewModel.onPhoneButtonClicked()}"
                    android:src="@drawable/ic_call_blue" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/space_medium"
                    android:layout_toEndOf="@id/image_view_courier"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/text_view_courier_name"
                        style="@style/Widget.TextView.SubHeader2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{viewModel.courierName}"
                        android:textColor="@color/very_dark_gray"
                        bind:text="Andhika" />

                    <TextView
                        style="@style/Widget.TextView.BodyCopy1.Medium"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_tiny"
                        android:text="@{viewModel.courierPhone, default= `ST-1001`}"
                        android:textColor="@color/very_dark_gray"
                        android:textStyle="bold" />
                </LinearLayout>

            </RelativeLayout>

        </RelativeLayout>

    </LinearLayout>
</layout>