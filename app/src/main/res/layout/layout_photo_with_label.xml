<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.viewModelComponent.PhotoWithLabelViewModel" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:onClick="@{() -> viewModel.imageClick()}">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/image_view_item"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:imageUrl="@{viewModel.imageUrl}"
            app:enableZoomImage="@{false}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <RelativeLayout
            android:id="@+id/selection_mask_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="@{viewModel.resellingInventoryTypeVisibility}"
            app:layout_constraintBottom_toBottomOf="parent">

            <TextView
                android:id="@+id/reselling_inventory_type_text_view"
                style="@style/Widget.TextView.BodyCopy2.Medium"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:backgroundResource="@{viewModel.photoLabelBgColor}"
                android:ellipsize="end"
                android:maxLines="1"
                android:paddingTop="@dimen/space_tiny"
                android:paddingBottom="@dimen/space_tiny"
                android:textAlignment="center"
                android:textBinding="@{viewModel.resellingInventoryType}"
                android:textColor="@color/white"
                tools:text="@string/warehouse_sale_label"
                tools:visibility="visible"
                tools:background="@drawable/bg_orange_top_radius_4"/>
        </RelativeLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>