<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <import type="android.view.View" />

        <variable
            name="isVisible"
            type="boolean" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="@{isVisible ? View.VISIBLE : View.GONE}">

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/very_light_gray" />
    </LinearLayout>
</layout>