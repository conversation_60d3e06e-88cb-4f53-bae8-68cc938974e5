<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.ItemBoxRewardSectionViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:enabled="@{viewModel.isRewardItemCheckedEnabled}"
        android:onClick="@{() -> viewModel.onRewardItemClicked()}"
        android:selected="@{viewModel.isRewardItemChecked}">

        <ImageView
            android:id="@+id/image_view_reward"
            android:layout_width="@dimen/space_very_large"
            android:layout_height="@dimen/space_very_large"
            android:layout_centerInParent="true"
            android:layout_marginStart="@dimen/space_semi_medium"
            android:layout_marginTop="@dimen/space_medium"
            android:scaleType="fitCenter"
            android:src="@drawable/ic_gift_reward_icon_for_style_stars"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/item_reward_name" />

        <TextView
            android:id="@+id/item_reward_name"
            style="@style/Widget.TextView.SubHeader.Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_medium"
            android:layout_marginEnd="@dimen/space_very_large"
            android:maxLines="2"
            android:paddingTop="@dimen/space_medium_large"
            android:paddingBottom="@dimen/space_medium_large"
            android:textBinding="@{viewModel.rewardName}"
            android:textColor="@color/black_19"
            app:layout_constraintStart_toEndOf="@id/image_view_reward"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="LANEIGE Water Bank Hydro Kit" />

        <CheckBox
            android:id="@+id/item_checkbox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_medium_large"
            android:layout_marginEnd="@dimen/space_medium"
            android:background="@drawable/selector_checkbox_background"
            android:bindChecked="@{viewModel.isRewardItemChecked}"
            android:button="@null"
            android:clickable="false"
            android:focusable="false"
            android:enabled="@{viewModel.isRewardItemCheckedEnabled}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_very_little"
            android:background="@color/divider"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/item_reward_name" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>