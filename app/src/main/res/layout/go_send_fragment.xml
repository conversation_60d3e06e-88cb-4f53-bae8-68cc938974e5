<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.goSend.viewModel.GoSendViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.savvyapps.togglebuttonlayout.ToggleButtonLayout
            android:id="@+id/toggleGosend"
            android:layout_width="match_parent"
            android:layout_height="@dimen/tab_height"
            android:layout_gravity="center_horizontal"
            android:layout_margin="@dimen/space_small"
            app:allowDeselection="false"
            app:cardBackgroundColor="@color/white"
            app:cardCornerRadius="@dimen/space_tiny"
            app:cardElevation="@dimen/no_space"
            app:customLayout="@layout/toggle_custom_item"
            app:dividerColor="@color/transparent"
            app:menu="@menu/menu_gosend_radio_group"
            app:selectedColor="@color/light_green"
            app:toggleMode="even" />

        <co.styletheory.ops.outbound.android.general.view.HackyViewPager
            android:id="@+id/view_pager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>
</layout>