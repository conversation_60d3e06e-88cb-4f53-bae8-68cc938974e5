<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.batch.viewModel.BatchStepViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="@dimen/space_semi_medium"
            android:paddingEnd="@dimen/space_small"
            android:paddingStart="@dimen/space_small"
            android:paddingTop="@dimen/space_semi_medium">

            <LinearLayout
                android:id="@+id/container_picker"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="@dimen/space_tiny">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_profile" />

                <TextView
                    style="@style/Widget.TextView.BodyCopy2"
                    android:layout_width="wrap_content"
                    android:minWidth="50dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/space_small"
                    android:text="@{viewModel.stepType, default=`Photographer`}"
                    android:textColor="@color/very_dark_gray"
                    android:textSize="12sp" />

                <TextView
                    style="@style/Widget.TextView.BodyCopy2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/space_small"
                    android:text="@string/divider"
                    android:textColor="@color/very_dark_gray"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/text_view_picker_name"
                    style="@style/Widget.TextView.BodyCopy2.Medium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/space_small"
                    android:text="@{viewModel.pickerName, default=`-`}"
                    android:textSize="12sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/container_picker"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/container_point"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/space_small"
                    android:layout_marginTop="2dp"
                    android:orientation="vertical">

                    <CheckedTextView
                        android:layout_width="13dp"
                        android:layout_height="13dp"
                        android:background="@drawable/selector_circle_start_point"
                        android:checked="true" />

                    <View
                        android:layout_width="2dp"
                        android:layout_height="11dp"
                        android:layout_gravity="center_horizontal"
                        android:background="@color/very_light_gray_9f0" />

                    <CheckedTextView
                        android:layout_width="13dp"
                        android:layout_height="13dp"
                        android:background="@drawable/selector_circle_start_point"
                        android:checked="false" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:layout_toEndOf="@id/container_point"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2"
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/space_small"
                        android:text="Start"
                        android:textColor="@color/very_dark_gray"
                        android:textSize="12sp" />

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="5.5dp"
                        android:text="@string/divider"
                        android:textColor="@color/very_dark_gray"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/text_view_start_time"
                        style="@style/Widget.TextView.BodyCopy2.Medium"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/space_small"
                        android:text="@{viewModel.startTime, default=`-`}"
                        android:textSize="12sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/container_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="100dp"
                    android:layout_marginStart="6dp"
                    android:layout_marginTop="22dp"
                    android:layout_toEndOf="@id/container_point"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2"
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/space_small"
                        android:text="End"
                        android:textColor="@color/very_dark_gray"
                        android:textSize="12sp" />

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="5.5dp"
                        android:text="@string/divider"
                        android:textColor="@color/very_dark_gray"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/text_view_end_time"
                        style="@style/Widget.TextView.BodyCopy2.Medium"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/space_small"
                        android:text="@{viewModel.endTime, default=`-`}"
                        android:textSize="12sp" />
                </LinearLayout>

                <RelativeLayout
                    android:layout_width="100dp"
                    android:layout_height="wrap_content"
                    android:layout_alignBottom="@id/container_time"
                    android:layout_alignParentEnd="true">

                    <Button
                        android:id="@+id/button_take"
                        style="@style/Widget.Button.Small.SemiBold"
                        android:layout_width="wrap_content"
                        android:layout_height="28dp"
                        android:layout_alignParentEnd="true"
                        android:background="@drawable/rect_green_border_greencaf_rad_4"
                        android:onClick="@{() -> viewModel.stepActionButtonClick()}"
                        android:paddingEnd="@dimen/space_medium"
                        android:paddingStart="@dimen/space_medium"
                        android:text="@{viewModel.buttonText, default=`Check`}"
                        android:textColor="@color/white"
                        android:visibility="@{viewModel.showButton ? View.VISIBLE : View.GONE}" />

                    <LinearLayout
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:orientation="horizontal"
                        android:visibility="@{viewModel.showProgress ? View.VISIBLE : View.GONE}">

                        <View
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:layout_marginTop="2dp"
                            android:background="@drawable/circle_orange" />

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2.Medium"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="@dimen/space_small"
                            android:ellipsize="end"
                            android:gravity="center_vertical"
                            android:maxLines="2"
                            android:text="@{viewModel.progressBy, default=`Progress by Saipull`}"
                            android:textSize="12sp" />
                    </LinearLayout>
                </RelativeLayout>
            </RelativeLayout>
        </RelativeLayout>

        <include
            layout="@layout/divider_section_content"
            bind:isVisible="@{true}" />
    </LinearLayout>
</layout>