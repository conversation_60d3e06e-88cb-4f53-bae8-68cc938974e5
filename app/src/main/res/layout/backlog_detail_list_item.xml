<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel.impl.BacklogDetailItemViewModelImpl" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/space_small"
            android:background="@color/white"
            android:visibility="@{viewModel.itemSectionVisibility}">

            <LinearLayout
                android:id="@+id/container_sold_to"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/space_medium"
                android:background="@drawable/rect_orange_fe_rad_4"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/space_small"
                android:visibility="@{viewModel.itemPurchasedVisibility}"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="gone">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/space_small"
                    android:contentDescription="@string/app_name"
                    android:src="@drawable/ic_purchased" />

                <TextView
                    style="@style/Widget.TextView.SubHeader2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@{viewModel.soldTo}"
                    android:textColor="@color/orange_d9" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/linearLayout3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingStart="@dimen/space_semi_medium"
                android:paddingTop="@dimen/space_semi_medium"
                android:paddingEnd="@dimen/space_semi_medium"
                android:paddingBottom="@dimen/space_semi_medium"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/container_sold_to">


                <RelativeLayout
                    android:layout_width="@dimen/space_size_150"
                    android:layout_height="@dimen/space_size_250"
                    android:background="@color/white">

                    <include
                        android:id="@+id/photo_image_view"
                        layout="@layout/layout_photo_with_label"
                        android:viewModel="@{viewModel.photoWithLabelVM}" />

                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/space_semi_medium"
                    android:orientation="vertical">

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2.Bold"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{viewModel.location, default=`Row 1 lower`}"
                        android:textColor="@color/gray_9b"
                        android:textStyle="bold"
                        android:visibility="@{viewModel.isShowLocation}"
                        tools:visibility="gone" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/space_small">

                        <Button
                            android:id="@+id/button_swap"
                            style="@style/Widget.Button.Small"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:background="@drawable/selector_button_white_gray_border"
                            android:enabled="@{viewModel.swap ? true : false}"
                            android:focusable="false"
                            android:foreground="?attr/selectableItemBackground"
                            android:onClick="@{() -> viewModel.swapClick()}"
                            android:padding="@dimen/space_tiny"
                            android:paddingStart="@dimen/space_semi_medium"
                            android:paddingEnd="@dimen/space_semi_medium"
                            android:text="@string/swap_label"
                            android:textColor="@drawable/selector_text_black_gray"
                            android:visibility="@{viewModel.swapButtonVisibility}" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="@dimen/space_medium"
                            android:layout_toStartOf="@id/button_swap"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/text_view_name"
                                style="@style/Widget.TextView.SubHeader.Medium"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.itemName, default=`Empty Name`}"
                                android:textColor="@color/very_dark_gray" />

                        </LinearLayout>

                    </RelativeLayout>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="@string/app_name"
                        android:paddingStart="@dimen/no_space"
                        android:paddingTop="@dimen/size_spacing_horizontal_list"
                        android:paddingEnd="@dimen/size_spacing_horizontal_list"
                        android:paddingBottom="@dimen/space_semi_medium"
                        android:src="@drawable/ic_barcode_scanner"
                        android:visibility="@{viewModel.scannerIconVisibility}" />

                    <LinearLayout
                        android:id="@+id/llBrand"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:visibility="@{viewModel.isShowLocation}"
                        tools:visibility="gone">

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2"
                            android:layout_width="55dp"
                            android:layout_height="wrap_content"
                            android:text="@string/brand_label"
                            android:textColor="@color/gray_9b" />

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2"
                            android:layout_width="12dp"
                            android:layout_height="wrap_content"
                            android:text="@string/divider"
                            android:textAlignment="center"
                            android:textColor="@color/gray_9b" />

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2.Bold"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{viewModel.brand, default=`Stylestalker`}"
                            android:textColor="@color/black"
                            android:textStyle="bold" />
                    </LinearLayout>


                    <LinearLayout
                        android:id="@+id/llSize"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2"
                            android:layout_width="55dp"
                            android:layout_height="wrap_content"
                            android:text="@string/size_label"
                            android:textColor="@color/gray_9b" />

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2"
                            android:layout_width="12dp"
                            android:layout_height="wrap_content"
                            android:text="@string/divider"
                            android:textAlignment="center"
                            android:textColor="@color/gray_9b" />

                        <TextView
                            style="@style/Widget.TextView.SubHeader1.Bold"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{viewModel.itemSize, default=`S/UK`}"
                            android:textColor="@color/blue_cff"
                            android:textStyle="bold" />

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2.Medium"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingStart="@dimen/space_tiny"
                            android:paddingEnd="@dimen/space_tiny"
                            android:text="@{viewModel.productOrder, default=`[2]`}"
                            android:textColor="@color/gray_9b" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llColor"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2"
                            android:layout_width="55dp"
                            android:layout_height="wrap_content"
                            android:text="@string/color_label"
                            android:textColor="@color/gray_9b" />

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2"
                            android:layout_width="12dp"
                            android:layout_height="wrap_content"
                            android:text="@string/divider"
                            android:textAlignment="center"
                            android:textColor="@color/gray_9b" />

                        <co.styletheory.ops.outbound.android.view.AdapterLinearLayout
                            adapterLinearLayoutId="@{@layout/color_item_view_item}"
                            adapterLinearLayoutItems="@{viewModel.colorItems}"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:paddingStart="1dp"
                            android:paddingEnd="1dp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llCategory"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2"
                            android:layout_width="55dp"
                            android:layout_height="wrap_content"
                            android:text="@string/category_label"
                            android:textColor="@color/gray_9b" />

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2"
                            android:layout_width="12dp"
                            android:layout_height="wrap_content"
                            android:text="@string/divider"
                            android:textAlignment="center"
                            android:textColor="@color/gray_9b" />

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2.Bold"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{viewModel.category, default=`Dress`}"
                            android:textColor="@color/black"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llParts"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:orientation="horizontal"
                        android:visibility="@{viewModel.partsVisibility}">

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2"
                            android:layout_width="55dp"
                            android:layout_height="wrap_content"
                            android:text="@string/parts_label"
                            android:textColor="@color/gray_9b" />

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2"
                            android:layout_width="12dp"
                            android:layout_height="wrap_content"
                            android:text="@string/divider"
                            android:textAlignment="center"
                            android:textColor="@color/gray_9b" />

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2.Bold"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{viewModel.parts, default=`Belt`}"
                            android:textColor="@color/black"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llDetachable"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:orientation="horizontal"
                        android:visibility="@{viewModel.detachableVisibility}">

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2"

                            android:layout_width="65dp"
                            android:layout_height="wrap_content"
                            android:text="@string/detachable_label"
                            android:textColor="@color/gray_9b" />

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2"
                            android:layout_width="12dp"
                            android:layout_height="wrap_content"
                            android:text="@string/divider"
                            android:textAlignment="center"
                            android:textColor="@color/gray_9b" />

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2.Bold"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{viewModel.detachable, default=`-`}"
                            android:textColor="@color/black"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llNotes"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2"
                            android:layout_width="55dp"
                            android:layout_height="wrap_content"
                            android:text="@string/notes_label"
                            android:textColor="@color/gray_9b" />

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2"
                            android:layout_width="12dp"
                            android:layout_height="wrap_content"
                            android:text="@string/divider"
                            android:textAlignment="center"
                            android:textColor="@color/gray_9b" />

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{viewModel.notes, default=`Something notes`}"
                            android:textColor="@color/very_dark_gray" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llRack"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_semi_medium"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2"
                            android:layout_width="55dp"
                            android:layout_height="wrap_content"
                            android:text="@string/rack_label"
                            android:textColor="@color/light_green" />

                        <TextView
                            style="@style/Widget.TextView.BodyCopy2"
                            android:layout_width="12dp"
                            android:layout_height="wrap_content"
                            android:text="@string/divider"
                            android:textAlignment="center"
                            android:textColor="@color/gray_9b" />

                        <TextView
                            style="@style/Widget.TextView.SubHeader.Bold"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@{viewModel.rack, default=`A11`}"
                            android:textColor="@color/light_green"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <TextView
                        style="@style/Widget.TextView.SubHeader"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/status_label"
                        android:textColor="#FF2A3033" />

                    <TextView
                        style="@style/Widget.TextView.BodyCopy1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{viewModel.errorMessage}"
                        android:textColor="@color/red"
                        android:visibility="@{viewModel.errorMessage.isEmpty() ? View.GONE : View.VISIBLE}" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_small"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:weightSum="2">

                        <CheckedTextView
                            android:id="@+id/text_view_picked"
                            style="@style/Widget.Button.Small"
                            android:layout_width="@dimen/no_space"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:background="@drawable/selector_button_rect_green_rad_5"
                            android:checked="@{viewModel.picked ? true : false}"
                            android:enabled="@{viewModel.isPickedButtonEnabled}"
                            android:focusable="false"
                            android:foreground="?attr/selectableItemBackground"
                            android:onClick="@{() -> viewModel.pickedClick()}"
                            android:padding="@dimen/space_tiny"
                            android:paddingStart="@dimen/space_semi_medium"
                            android:paddingEnd="@dimen/space_semi_medium"
                            android:text="@string/picked_label"
                            android:textAlignment="gravity"
                            android:textColor="@drawable/selector_text_white_black" />

                        <CheckedTextView
                            android:id="@+id/text_view_not_found"
                            style="@style/Widget.Button.Small"
                            android:layout_width="@dimen/no_space"
                            android:layout_height="match_parent"
                            android:layout_marginStart="@dimen/space_small"
                            android:layout_weight="1"
                            android:background="@drawable/selector_button_white_red_rad_5"
                            android:checked="@{viewModel.missing ? true : false}"
                            android:focusable="false"
                            android:foreground="?attr/selectableItemBackground"
                            android:onClick="@{() -> viewModel.missingClick()}"
                            android:padding="@dimen/space_tiny"
                            android:paddingStart="@dimen/space_semi_medium"
                            android:paddingEnd="@dimen/space_semi_medium"
                            android:text="@string/not_found_label"
                            android:textAlignment="gravity"
                            android:textColor="@drawable/selector_text_white_black" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <RelativeLayout
                android:id="@+id/overlay_loading"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/rect_transparent_dark_gray"
                android:visibility="@{viewModel.showOverlayLoading ? View.VISIBLE : View.GONE}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="gone">

                <ProgressBar
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:layout_centerInParent="true"
                    android:indeterminateTint="@color/white" />
            </RelativeLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
</layout>
