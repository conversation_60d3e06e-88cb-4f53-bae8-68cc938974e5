<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.packed.viewModel.PackedChildViewModel" />
    </data>

    <RelativeLayout
        android:id="@+id/main_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.malinskiy.superrecyclerview.SuperRecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:mainLayoutId="@layout/layout_recyclerview_verticalscroll"
            app:scrollbarStyle="insideOverlay" />

        <RelativeLayout
            android:id="@+id/empty_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="@{viewModel.packedItems.isEmpty() ? View.VISIBLE : View.GONE}">

            <include
                layout="@layout/batch_empty_state"
                bind:title="@{@string/shipment_empty_state_title_label}" />
        </RelativeLayout>
    </RelativeLayout>

</layout>
