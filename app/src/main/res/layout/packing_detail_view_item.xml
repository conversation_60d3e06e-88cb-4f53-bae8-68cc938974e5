<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.packing.viewModel.impl.PackingItemViewModelImpl" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_semi_medium"
            android:layout_marginTop="@dimen/space_semi_medium"
            android:layout_marginBottom="@dimen/space_semi_medium"
            android:orientation="horizontal">

            <RelativeLayout
                android:layout_width="@dimen/qc_image_item_width"
                android:layout_height="@dimen/qc_image_item_height"
                android:background="@color/white">

                <include
                    android:id="@+id/photo_image_view"
                    layout="@layout/layout_photo_with_label"
                    android:viewModel="@{viewModel.setPhotoWithLabelVM}" />

            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_semi_medium"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:weightSum="1">

                    <RelativeLayout
                        android:layout_width="@dimen/no_space"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.8"
                        android:visibility="@{viewModel.scannerIconVisibility}">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/ic_barcode_scanner" />

                    </RelativeLayout>

                    <LinearLayout
                        android:layout_width="@dimen/no_space"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.2"
                        android:gravity="center">

                        <CheckBox
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="@{viewModel.isItemScanned}"
                            android:clickable="false"
                            android:theme="@style/CheckBoxStyle"
                            android:visibility="@{viewModel.checkMarkRfidVisibility}" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/space_medium"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/text_view_name"
                            style="@style/Widget.TextView.SubHeader.Medium"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{viewModel.itemName, default=`Empty Name`}"
                            android:textColor="@color/very_dark_gray" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2"
                        android:layout_width="60dp"
                        android:layout_height="wrap_content"
                        android:text="@string/size_label"
                        android:textColor="@color/gray_9b" />

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2"
                        android:layout_width="12dp"
                        android:layout_height="wrap_content"
                        android:text="@string/divider"
                        android:textAlignment="center"
                        android:textColor="@color/gray_9b" />

                    <TextView
                        style="@style/Widget.TextView.SubHeader1.Bold"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{viewModel.itemSize, default=`S/UK`}"
                        android:textColor="@color/blue_cff"
                        android:textStyle="bold" />

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2.Medium"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingStart="@dimen/space_tiny"
                        android:paddingEnd="@dimen/space_tiny"
                        android:text="@{viewModel.productOrder, default=`[2]`}"
                        android:textColor="@color/gray_9b" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2"
                        android:layout_width="60dp"
                        android:layout_height="wrap_content"
                        android:text="@string/color_label"
                        android:textColor="@color/gray_9b" />

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2"
                        android:layout_width="12dp"
                        android:layout_height="wrap_content"
                        android:text="@string/divider"
                        android:textAlignment="center"
                        android:textColor="@color/gray_9b" />

                    <co.styletheory.ops.outbound.android.view.AdapterLinearLayout
                        adapterLinearLayoutId="@{@layout/color_item_view_item}"
                        adapterLinearLayoutItems="@{viewModel.colorItems}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingStart="1dp"
                        android:paddingEnd="1dp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2"
                        android:layout_width="60dp"
                        android:layout_height="wrap_content"
                        android:text="@string/category_label"
                        android:textColor="@color/gray_9b" />

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2"
                        android:layout_width="12dp"
                        android:layout_height="wrap_content"
                        android:text="@string/divider"
                        android:textAlignment="center"
                        android:textColor="@color/gray_9b" />

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2.Bold"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{viewModel.category, default=`Dress`}"
                        android:textColor="@color/black"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:orientation="horizontal"
                    android:visibility="@{viewModel.partsVisibility}">

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2"
                        android:layout_width="60dp"
                        android:layout_height="wrap_content"
                        android:text="@string/parts_label"
                        android:textColor="@color/gray_9b" />

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2"
                        android:layout_width="12dp"
                        android:layout_height="wrap_content"
                        android:text="@string/divider"
                        android:textAlignment="center"
                        android:textColor="@color/gray_9b" />

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2.Bold"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{viewModel.parts, default=`Belt`}"
                        android:textColor="@color/black"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llDetachable"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:orientation="horizontal"
                    android:visibility="@{viewModel.detachableVisibility}">

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2"
                        android:layout_width="65dp"
                        android:layout_height="wrap_content"
                        android:text="@string/detachable_label"
                        android:textColor="@color/gray_9b" />

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2"
                        android:layout_width="12dp"
                        android:layout_height="wrap_content"
                        android:text="@string/divider"
                        android:textAlignment="center"
                        android:textColor="@color/gray_9b" />

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2.Bold"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{viewModel.detachable, default=`-`}"
                        android:textColor="@color/black"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2"
                        android:layout_width="60dp"
                        android:layout_height="wrap_content"
                        android:text="@string/notes_label"
                        android:textColor="@color/gray_9b" />

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2"
                        android:layout_width="12dp"
                        android:layout_height="wrap_content"
                        android:text="@string/divider"
                        android:textAlignment="center"
                        android:textColor="@color/gray_9b" />

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{viewModel.notes, default=`Something notes`}"
                        android:textColor="@color/very_dark_gray" />
                </LinearLayout>
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_medium"
            android:layout_marginEnd="@dimen/space_medium"
            android:layout_marginBottom="@dimen/space_medium"
            android:background="@drawable/rect_pink_rad_4"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/space_small"
            android:visibility="@{viewModel.itemStatusVisibility}">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/space_small"
                android:src="@drawable/ic_alert" />

            <TextView
                style="@style/Widget.TextView.SubHeader2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textBinding="@{viewModel.itemStatus}"
                android:textColor="@color/red" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_medium"
            android:layout_marginEnd="@dimen/space_medium"
            android:layout_marginBottom="@dimen/space_medium"
            android:background="@drawable/rect_orange_fe_rad_4"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/space_small"
            android:visibility="@{viewModel.itemPurchasedVisibility}">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/space_small"
                android:src="@drawable/ic_purchased" />

            <TextView
                style="@style/Widget.TextView.SubHeader2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@{viewModel.soldTo}"
                android:textColor="@color/orange_d9" />
        </LinearLayout>

        <include
            layout="@layout/divider_section_content"
            bind:isVisible="@{true}" />
    </LinearLayout>
</layout>
