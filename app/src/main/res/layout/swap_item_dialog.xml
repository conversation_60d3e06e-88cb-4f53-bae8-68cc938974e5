<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.swap.SwapItemDialogViewModel" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rect_white_no_border_rad_4"
        android:padding="@dimen/space_large">

        <LinearLayout
            android:id="@+id/container_item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/image_view_item"
                android:layout_width="60dp"
                android:layout_height="90dp"
                android:imageUrl="@{viewModel.imageUrl}" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/space_semi_medium"
                android:orientation="vertical">

                <TextView
                    style="@style/Widget.TextView.SubHeader.Medium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{viewModel.itemName, default=`Empty Name`}"
                    android:textColor="@color/very_dark_gray" />

                <TextView
                    style="@style/Widget.TextView.BodyCopy2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{viewModel.itemSize, default=`Size: S/UK [2]`}"
                    android:textColor="@color/gray_9b" />
            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/text_view_dialog_title"
            style="@style/Widget.TextView.Header2.Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/container_item"
            android:layout_marginTop="@dimen/space_medium"
            android:text="@{viewModel.dialogTitle, default=`Empty Name`}"
            android:textColor="@color/very_dark_gray" />

        <ProgressBar
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_below="@id/text_view_dialog_title"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/space_large"
            android:layout_marginTop="@dimen/space_large"
            android:visibility="@{viewModel.showLoading ? View.VISIBLE : View.GONE}" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/text_view_dialog_title"
            android:layout_marginTop="@dimen/space_semi_medium"
            android:orientation="vertical">

            <TextView
                style="@style/Widget.TextView.BodyCopy2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{viewModel.dialogBody, default=`Empty Name`}"
                android:textColor="@color/white_8a" />


            <TextView
                style="@style/Widget.Button.Dialog"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="64dp"
                android:foreground="?attr/selectableItemBackground"
                android:onClick="@{() -> viewModel.leftButtonClick()}"
                android:text="@string/close_label"
                android:textColor="@color/gray_75"
                android:visibility="@{viewModel.showCloseButton ? View.VISIBLE : View.GONE}" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/space_medium"
                android:layout_marginTop="@dimen/space_large"
                android:gravity="end"
                android:orientation="horizontal"
                android:visibility="@{viewModel.hideActionButton ? View.GONE : View.VISIBLE}">

                <TextView
                    style="@style/Widget.Button.Dialog"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:foreground="?attr/selectableItemBackground"
                    android:onClick="@{() -> viewModel.leftButtonClick()}"
                    android:text="@{viewModel.dialogLeftButtonText, default=`Cancel`}"
                    android:textColor="@color/gray_75" />

                <TextView
                    style="@style/Widget.Button.Dialog"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/space_medium"
                    android:foreground="?attr/selectableItemBackground"
                    android:onClick="@{() -> viewModel.swapItemClick()}"
                    android:text="@{viewModel.dialogRightButtonText, default=`Swap Item`}"
                    android:textColor="@color/light_green" />

            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>
</layout>