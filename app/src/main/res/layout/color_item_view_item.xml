<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.viewModelComponent.ColorItemViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/image_view_item"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:renderColor="@{viewModel.colorHex}"
            app:riv_border_color="@color/gray_9b"
            app:riv_border_width="1dp"
            app:riv_corner_radius="10dp"
            app:riv_oval="true" />

        <TextView
            style="@style/Widget.TextView.BodyCopy2.Bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_tiny"
            android:text="@{viewModel.colorName, default=`Black`}"
            android:textColor="@color/black"
            android:textStyle="bold" />
    </LinearLayout>

</layout>