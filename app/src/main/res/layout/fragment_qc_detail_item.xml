<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.impl.QcDetailItemViewModelImpl" />
    </data>

    <ScrollView
        android:id="@+id/itemScroll"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:descendantFocusability="beforeDescendants"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/space_semi_medium">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/rect_white_border_gray_rad_4"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/space_medium"
                    android:paddingTop="@dimen/space_semi_medium"
                    android:paddingEnd="@dimen/space_medium"
                    android:paddingBottom="@dimen/space_tiny">

                    <LinearLayout
                        android:id="@+id/layoutTitle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/space_medium"
                        android:layout_toStartOf="@id/container_complete"
                        android:orientation="vertical">

                        <TextView
                            style="@style/Widget.TextView.Header1.Medium"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black_2a"
                            android:text="@{viewModel.customerName, default=`Customer Name / Rack`}"
                            android:textStyle="bold" />

                        <TextView
                            style="@style/Widget.TextView.BodyCopy1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_tiny"
                            android:textColor="@color/black_2a"
                            android:visibility="@{viewModel.titleVisibility}"
                            android:text="@{viewModel.title, default=`Shipment ID`}" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_small"
                            android:layout_marginBottom="@dimen/space_small"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:contentDescription="@string/packing_detail_service_title"
                                android:layout_marginEnd="@dimen/space_small"
                                android:src="@drawable/ic_methods"/>

                            <co.styletheory.ops.outbound.android.view.AdapterFlowLayout
                                android:id="@+id/service_methods"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                app:adapterFlowLayoutId="@{@layout/widget_service_methods_qc}"
                                app:adapterFlowLayoutItems="@{viewModel.businessMethodNameList}"
                                app:flChildSpacing="@dimen/space_medium"
                                app:flRowSpacing="@dimen/space_small" />

                        </LinearLayout>
                    </LinearLayout>

                    <RelativeLayout
                        android:id="@+id/container_complete"
                        android:layout_width="@dimen/space_size_40"
                        android:layout_height="@dimen/space_size_40"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:background="@{viewModel.isComplete ? @drawable/circle_green : @drawable/circle_white_border_gray}">

                        <ImageView
                            android:layout_width="@dimen/space_medium_large"
                            android:layout_height="@dimen/space_medium"
                            android:layout_centerInParent="true"
                            android:scaleType="fitCenter"
                            android:src="@{viewModel.isComplete ? @drawable/ic_check_white : @drawable/ic_check_gray}" />

                    </RelativeLayout>

                </RelativeLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="@{viewModel.labelBoxItems.size > 0 ? View.VISIBLE : View.GONE}">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_very_little"
                        android:layout_marginTop="@dimen/space_little"
                        android:background="@color/very_light_gray" />

                    <co.styletheory.ops.outbound.android.view.AdapterFlowLayout
                        android:id="@+id/flow_label"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_small"
                        android:layout_marginBottom="@dimen/space_small"
                        android:paddingStart="@dimen/space_medium"
                        android:paddingTop="@dimen/space_small"
                        android:paddingEnd="@dimen/space_medium"
                        android:paddingBottom="@dimen/space_small"
                        app:adapterFlowLayoutId="@{@layout/label_view}"
                        app:adapterFlowLayoutItems="@{viewModel.labelBoxItems}"
                        app:flChildSpacing="@dimen/space_medium"
                        app:flRowSpacing="@dimen/space_small" />

                </LinearLayout>


            </LinearLayout>

            <co.styletheory.ops.outbound.android.view.AdapterLinearLayout
                android:id="@+id/adapterItems"
                adapterLinearLayoutId="@{@layout/qc_picked_view_item}"
                adapterLinearLayoutItems="@{viewModel.qaPickedItems}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingTop="@dimen/space_tiny"
                android:paddingBottom="@dimen/space_tiny" />

        </LinearLayout>
    </ScrollView>
</layout>