<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.viewModelComponent.AlertDialogViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rect_white_no_border_rad_4"
        android:orientation="vertical"
        android:padding="@dimen/space_large">

        <LinearLayout
            android:id="@+id/flow_logistic_provider"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:visibility="@{viewModel.logisticNameVisibility}">

            <include
                android:id="@+id/logistic_provider_name_section_container"
                layout="@layout/layout_logistic_provider_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:viewModel="@{viewModel.setupLogisticProviderNameViewModel()}" />
        </LinearLayout>

        <TextView
            android:id="@+id/text_view_dialog_title"
            style="@style/Widget.TextView.Header2.Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_medium"
            android:text="@{viewModel.dialogTitle, default=`Empty Name`}"
            android:textColor="@color/very_dark_gray" />

        <TextView
            style="@style/Widget.TextView.BodyCopy2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_medium"
            android:textSize="@dimen/text_size_16"
            android:text="@{viewModel.dialogBody, default=`Empty Name`}"
            android:textColor="@color/white_8a" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_very_large"
            android:gravity="end"
            android:orientation="horizontal">

            <TextView
                style="@style/Widget.Button.Dialog"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:foreground="?attr/selectableItemBackground"
                android:onClick="@{() -> viewModel.leftButtonClicked()}"
                android:text="@{viewModel.leftButtonText}"
                android:textColor="@color/gray_75"
                tools:text="@string/cancel"
                android:visibility="@{viewModel.leftButtonVisibility}"/>

            <TextView
                style="@style/Widget.Button.Dialog"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_medium"
                android:foreground="?attr/selectableItemBackground"
                android:onClick="@{() -> viewModel.rightButtonClicked()}"
                android:text="@{viewModel.rightButtonText}"
                android:textColor="@color/light_green"
                tools:text="@string/ok_button" />

        </LinearLayout>
    </LinearLayout>
</layout>