<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingBottom="@dimen/space_small"
    android:paddingEnd="@dimen/space_medium"
    android:paddingStart="@dimen/space_medium"
    android:paddingTop="@dimen/space_small">

    <TextView
        android:id="@+id/textViewRegion"
        style="@style/Widget.TextView.SubHeader2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/space_small"
        android:layout_marginEnd="@dimen/space_medium"
        android:layout_marginStart="@dimen/space_medium"
        android:layout_marginTop="@dimen/space_small"
        android:background="?android:attr/selectableItemBackground"
        android:gravity="center_vertical"
        android:text="@string/region_text"
        android:textColor="@color/white" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_overflow_menu" />

</LinearLayout>