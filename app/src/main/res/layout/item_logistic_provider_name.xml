<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel.ItemLogisticProviderNameViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingEnd="@dimen/space_tiny">

        <TextView
            style="@style/Widget.TextView.SubHeader2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundResource="@{viewModel.backgroundColorResource}"
            android:padding="@dimen/space_tiny"
            android:textBinding="@{viewModel.providerName}"
            android:textColorResource="@{viewModel.providerTextColor}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="@drawable/rect_black_26_rad_4"
            tools:text="WOW" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>