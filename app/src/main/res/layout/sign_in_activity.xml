<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.signIn.SignInViewModelImpl" />
    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/text_view_title"
                style="@style/Widget.TextView.Header1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/space_medium"
                android:layout_marginStart="@dimen/space_large"
                android:text="@string/sign_in_label"
                android:textSize="24sp"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/form_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/space_large"
                android:layout_marginStart="@dimen/space_large"
                android:background="@drawable/rect_white_border_gray_rad_4"
                android:orientation="vertical"
                android:padding="@dimen/space_medium">

                <LinearLayout
                    android:id="@+id/container_sign_in"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="@{viewModel.showAdditionalField ? View.GONE : View.VISIBLE}">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_text_email"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/email_label"
                            android:inputType="textEmailAddress"
                            app:editTextBinding="@{viewModel.emailText}" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_small">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_text_password"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/password_label"
                            android:inputType="textPassword"
                            app:editTextBinding="@{viewModel.passwordText}" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <TextView
                        style="@style/Widget.TextView.SubHeader2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="@dimen/space_tiny"
                        android:onClick="forgotPasswordClick"
                        android:text="@string/forgot_your_password_label"
                        android:textColor="@color/gray_9b" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/container_additional_field"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="@{viewModel.showAdditionalField ? View.VISIBLE : View.GONE}">

                    <TextView
                        style="@style/Widget.TextView.SubHeader2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_tiny"
                        android:text="@string/creating_your_account_label"
                        android:textColor="@color/gray_9b" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_text_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/name_label"
                            android:inputType="textPersonName"
                            app:editTextBinding="@{viewModel.nameText}" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_text_new_password"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/new_password_label"
                            android:inputType="textPassword"
                            app:editTextBinding="@{viewModel.newPasswordText}" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_text_confirm_new_password"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/confirm_new_password_label"
                            android:inputType="textPassword"
                            app:editTextBinding="@{viewModel.newConfirmPasswordText}" />
                    </com.google.android.material.textfield.TextInputLayout>
                </LinearLayout>

                <TextView
                    android:id="@+id/button_sign_in"
                    style="@style/Widget.TextView.Header2.Medium"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/space_very_large"
                    android:background="@drawable/selector_button_green_white_border_gray_rad_4"
                    android:clickable="true"
                    android:enabled="@{viewModel.enableSignInButton ? true : false}"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground"
                    android:onClick="signInClick"
                    android:padding="@dimen/space_semi_medium"
                    android:text="@string/sign_in_label"
                    android:textAlignment="center"
                    android:textColor="@drawable/selector_text_white_gray" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_large" />
        </LinearLayout>
    </ScrollView>

</layout>