<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.scanBarcodeBottomSheet.viewModel.ScanBarcodeBottomSheetViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true"
        android:elevation="@dimen/space_small"
        android:focusable="true"
        app:behavior_hideable="false"
        app:behavior_peekHeight="@dimen/no_space"
        app:layout_behavior="@string/bottom_sheet_behavior">

        <ImageView
            android:id="@+id/imageClose"
            android:layout_width="@dimen/space_large"
            android:layout_height="@dimen/space_large"
            android:contentDescription="@string/close_label"
            android:src="@drawable/ic_close"
            android:onClick="@{() -> viewModel.onClose()}"
            android:layout_margin="@dimen/space_medium"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>

        <TextView
            android:id="@+id/textHeader"
            style="@style/Widget.TextView.Header2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/accuracy_swap_this_item"
            android:layout_margin="@dimen/space_medium"
            android:textColor="@color/black_percent_80"
            app:layout_constraintStart_toEndOf="@+id/imageClose"
            app:layout_constraintTop_toTopOf="parent"/>

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/editTextScannedRfid"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_very_little"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:singleLine="true"
            android:onEditTextKeyListener="@{viewModel.onEditTextKeyListener}"
            app:editTextBinding="@{viewModel.scannedRfidText}"
            app:layout_constraintTop_toBottomOf="@+id/textHeader"
            app:layout_constraintStart_toStartOf="parent">
            <requestFocus />
        </androidx.appcompat.widget.AppCompatEditText>

        <ImageView
            android:id="@+id/imageBarcode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@string/accuracy_swap_scan_barcode"
            android:src="@drawable/ic_barcode"
            android:layout_marginTop="@dimen/space_size_45"
            app:layout_constraintTop_toTopOf="@+id/editTextScannedRfid"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <TextView
            android:id="@+id/scanInfo"
            style="@style/Widget.TextView.SubHeader1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_medium"
            android:layout_marginEnd="@dimen/space_medium"
            android:layout_marginTop="@dimen/space_very_large"
            android:layout_marginBottom="@dimen/space_medium"
            android:textColor="@color/black_2a"
            android:text="@string/accuracy_swap_bottom_sheet_scan_info"
            app:layout_constraintTop_toBottomOf="@+id/imageBarcode"
            app:layout_constraintStart_toStartOf="parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_size_60"
            app:layout_constraintTop_toBottomOf="@+id/scanInfo"
            app:layout_constraintStart_toStartOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>