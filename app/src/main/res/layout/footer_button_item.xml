<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.viewModelComponent.FooterButtonViewModel" />
    </data>

    <LinearLayout
        android:paddingBottom="@dimen/space_medium_large"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="@dimen/space_semi_medium"
        android:paddingEnd="@dimen/space_semi_medium"
        android:paddingTop="@dimen/space_very_little">

        <CheckedTextView
            android:id="@+id/footerButton"
            style="@style/Widget.Button.Big"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_size_45"
            android:background="@drawable/selector_button_rect_green_rad_5"
            android:checked="true"
            android:clickable="true"
            android:enabled="@{viewModel.enable ? true : false}"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackground"
            android:onClick="@{() -> viewModel.footerButtonClick()}"
            android:text="@{viewModel.buttonTitle}"
            android:textAlignment="gravity"
            android:textColor="@drawable/selector_text_white_black"
            android:textStyle="bold" />
    </LinearLayout>
</layout>