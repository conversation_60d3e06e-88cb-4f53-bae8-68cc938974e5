<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="toolbarVM"
            type="co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.photoDetail.viewModel.impl.PhotoDetailViewModelImpl" />
    </data>

    <LinearLayout
        android:id="@+id/main_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include
            android:id="@+id/toolbar_wrapper"
            layout="@layout/general_toolbar"
            android:viewModel="@{toolbarVM}" />

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabs"
            android:layout_width="match_parent"
            android:layout_height="@dimen/tab_height"
            android:background="@color/white_f9"
            android:clipToPadding="false"
            android:paddingLeft="@dimen/space_very_small"
            android:paddingRight="@dimen/space_very_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tabGravity="fill"
            app:tabIndicatorColor="@color/light_green"
            app:tabIndicatorHeight="@dimen/space_little"
            app:tabMaxWidth="@dimen/no_width"
            app:tabMode="scrollable"
            app:tabPaddingEnd="@dimen/space_semi_medium"
            app:tabPaddingStart="@dimen/space_semi_medium"
            app:tabSelectedTextColor="@color/black_1f"
            app:tabTextAppearance="@style/SubTabTextAppearance"
            app:tabTextColor="@color/gray_64" />

        <co.styletheory.ops.outbound.android.general.view.HackyViewPager
            android:id="@+id/view_pager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>

</layout>