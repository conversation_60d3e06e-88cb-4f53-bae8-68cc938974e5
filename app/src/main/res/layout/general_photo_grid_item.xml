<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.viewModelComponent.GeneralPhotoGridItemViewModel" />

        <import type="android.view.View" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_columnWeight="1"
        android:adjustViewBounds="true"
        android:gravity="fill">

        <co.styletheory.ops.outbound.android.general.view.SquareImageView xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/imageview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackground"
            android:imageUrl="@{viewModel.localUrl ? viewModel.localImageUrl : viewModel.imageUrl}"
            android:onClick="@{() -> viewModel.onImageClicked()}" />

        <ProgressBar
            android:id="@+id/contentLoadingProgressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:visibility="@{viewModel.loadingIndicatorStatus}" />

        <ImageButton
            android:id="@+id/button_delete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentTop="true"
            android:layout_marginEnd="5dp"
            android:layout_marginTop="5dp"
            android:background="@null"
            android:onClick="@{() -> viewModel.onButtonDeleteClicked()}"
            android:src="@android:drawable/ic_delete"
            android:visibility="@{viewModel.editable ? View.VISIBLE : View.GONE}" />
    </RelativeLayout>

</layout>

