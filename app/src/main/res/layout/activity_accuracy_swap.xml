<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="toolbarVM"
            type="co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel" />

        <variable
            name="footerButtonVM"
            type="co.styletheory.ops.outbound.android.viewModelComponent.FooterButtonViewModel" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.accuracySwap.viewModel.AccuracySwapViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:fitsSystemWindows="false">

        <include
            android:id="@+id/toolbar_wrapper"
            layout="@layout/general_toolbar"
            android:viewModel="@{toolbarVM}" />

        <View
            android:id="@+id/toolbarDropShadow"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_very_little"
            android:background="@drawable/toolbar_dropshadow"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/toolbar_wrapper" />

        <LinearLayout
            android:id="@+id/layoutInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/space_medium"
            android:background="@drawable/rect_light_gray_rad_4"
            android:orientation="horizontal"
            android:padding="@dimen/space_small"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/toolbarDropShadow">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginEnd="@dimen/space_small"
                android:contentDescription="@string/accuracy_swap_title"
                android:src="@drawable/ic_info" />

            <TextView
                style="@style/Widget.TextView.SubHeader2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/accuracy_swap_info" />

        </LinearLayout>

        <TextView
            android:id="@+id/swapReasonTitle"
            style="@style/Widget.TextView.SubHeader.Bold"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_medium"
            android:layout_marginTop="@dimen/space_size_40"
            android:layout_marginEnd="@dimen/space_medium"
            android:layout_marginBottom="@dimen/space_medium"
            android:text="@string/accuracy_swap_reason_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/layoutInfo" />

        <Spinner
            android:id="@+id/swapReasonSpinner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/space_medium"
            android:background="@drawable/spinner_background"
            android:dropDownVerticalOffset="@dimen/space_size_50"
            android:popupBackground="@color/white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/swapReasonTitle" />

        <LinearLayout
            android:id="@+id/layoutSwapNowButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipToPadding="false"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <include
                android:id="@+id/swapNowButton"
                layout="@layout/footer_button_item"
                android:viewModel="@{footerButtonVM}" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>