<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.packing.viewModel.impl.PackingDetailItemViewModelImpl" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_medium"
            android:layout_marginTop="@dimen/space_semi_medium"
            android:layout_marginEnd="@dimen/space_medium"
            android:layout_marginBottom="@dimen/space_semi_medium">

            <RelativeLayout
                android:id="@+id/container_complete"
                android:layout_width="37dp"
                android:layout_height="37dp"
                android:layout_alignParentTop="true"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:background="@{viewModel.itemsComplete ? @drawable/circle_green : @drawable/circle_white_border_gray}">

                <ImageView
                    android:layout_width="19dp"
                    android:layout_height="14dp"
                    android:layout_centerInParent="true"
                    android:scaleType="fitCenter"
                    android:src="@{viewModel.itemsComplete ? @drawable/ic_check_white : @drawable/ic_check_gray}" />

            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    style="@style/Widget.TextView.Header1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/no_space"
                    android:paddingEnd="@dimen/space_size_36"
                    android:text="@{viewModel.customerName, default=`Customer Name / Rack`}" />

                <TextView
                    style="@style/Widget.TextView.BodyCopy1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_tiny"
                    android:layout_marginBottom="@dimen/space_small"
                    android:text="@{viewModel.title, default=`Tracking ID`}"
                    android:visibility="@{viewModel.titleVisibility}" />

                <include
                    layout="@layout/divider_section_content"
                    bind:isVisible="@{true}" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_small"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/Widget.TextView.BodyCopy2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/packing_detail_pickup_at" />

                    <TextView
                        style="@style/Widget.TextView.SubHeader1.Bold"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/space_small"
                        android:text="@{viewModel.pickupAt, default= `20.dd`}"
                        android:textSize="@dimen/text_size_14" />
                </LinearLayout>
            </LinearLayout>


        </RelativeLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/rect_white_no_border_rad_4"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/gray_white"
                        android:orientation="vertical"
                        android:padding="@dimen/space_medium">

                        <TextView
                            style="@style/Widget.TextView.Header1.Medium"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_small"
                            android:text="@string/packing_detail_items_title"
                            android:textSize="@dimen/text_size_16" />

                        <TextView
                            style="@style/Widget.TextView.SubHeader2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/packing_detail_items_subtitle"
                            android:textSize="@dimen/text_size_14" />

                    </LinearLayout>

                    <co.styletheory.ops.outbound.android.view.AdapterLinearLayout
                        android:id="@+id/adapterLinearItems"
                        adapterLinearLayoutId="@{@layout/packing_detail_view_item}"
                        adapterLinearLayoutItems="@{viewModel.readyItems}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingStart="@dimen/space_very_little"
                        android:paddingTop="@dimen/space_small"
                        android:paddingEnd="@dimen/space_very_little"/>

                    <LinearLayout
                        android:orientation="vertical"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="@{viewModel.boxRewardsSectionVisibility}">

                        <include
                            layout="@layout/box_reward_section"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:viewModel="@{viewModel.setBoxRewardViewModel()}" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/gray_white"
                        android:orientation="vertical"
                        android:padding="@dimen/space_medium">

                        <TextView
                            style="@style/Widget.TextView.Header1.Medium"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_small"
                            android:text="@string/packing_detail_packing_info_title"
                            android:textSize="@dimen/text_size_16" />

                        <TextView
                            style="@style/Widget.TextView.SubHeader2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/packing_detail_packing_info_subtitle"
                            android:textSize="@dimen/text_size_14" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:orientation="vertical"
                        android:padding="@dimen/space_medium"
                        android:visibility="@{viewModel.labelItemVisibility}">

                        <TextView
                            style="@style/Widget.TextView.SubHeader2.Medium"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/packing_detail_customer_request_title" />

                        <co.styletheory.ops.outbound.android.view.AdapterFlowLayout
                            android:id="@+id/flow_label"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingTop="@dimen/space_small"
                            app:adapterFlowLayoutId="@{@layout/label_view}"
                            app:adapterFlowLayoutItems="@{viewModel.labelItems}"
                            app:flChildSpacing="@dimen/space_medium"
                            app:flRowSpacing="@dimen/space_small" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_semi_medium"
                        android:background="@color/gray_white" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:orientation="vertical"
                        android:padding="@dimen/space_medium">

                        <TextView
                            style="@style/Widget.TextView.SubHeader2.Medium"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/packing_detail_service_title" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_medium"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/space_small"
                                android:contentDescription="@string/packing_detail_service_title"
                                android:src="@drawable/ic_methods" />

                            <co.styletheory.ops.outbound.android.view.AdapterFlowLayout
                                android:id="@+id/service_methods"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                app:adapterFlowLayoutId="@{@layout/widget_service_methods}"
                                app:adapterFlowLayoutItems="@{viewModel.businessMethodNameList}"
                                app:flChildSpacing="@dimen/space_medium"
                                app:flRowSpacing="@dimen/space_small" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_medium"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/space_small"
                                android:contentDescription="@string/packing_detail_service_title"
                                android:src="@drawable/ic_logistics" />

                            <co.styletheory.ops.outbound.android.view.AdapterFlowLayout
                                android:id="@+id/service_logistics"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                app:adapterFlowLayoutId="@{@layout/widget_service_methods}"
                                app:adapterFlowLayoutItems="@{viewModel.logisticNameList}"
                                app:flChildSpacing="@dimen/space_medium"
                                app:flRowSpacing="@dimen/space_small" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_medium"
                            android:orientation="horizontal"
                            android:visibility="@{viewModel.procedureTypeVisibility}">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/space_small"
                                android:contentDescription="@string/packing_detail_service_title"
                                android:src="@drawable/ic_tag" />

                            <co.styletheory.ops.outbound.android.view.AdapterFlowLayout
                                android:id="@+id/service_procedure_type"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                app:adapterFlowLayoutId="@{@layout/widget_service_methods}"
                                app:adapterFlowLayoutItems="@{viewModel.procedureTypeList}"
                                app:flChildSpacing="@dimen/space_medium"
                                app:flRowSpacing="@dimen/space_small" />

                        </LinearLayout>

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_medium_large"
                        android:background="@color/gray_white" />

                    <CheckedTextView
                        style="@style/Widget.TextView.Header1.Medium"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/space_small"
                        android:layout_marginTop="@dimen/space_medium"
                        android:layout_marginEnd="@dimen/space_small"
                        android:layout_marginBottom="@dimen/space_semi_medium"
                        android:background="@drawable/selector_button_green_white_border_gray_rad_4"
                        android:checked="true"
                        android:enabled="@{viewModel.enableCompleteButton ? true : false}"
                        android:foreground="?attr/selectableItemBackground"
                        android:gravity="center"
                        android:onClick="@{() -> viewModel.completeButtonClick()}"
                        android:padding="@dimen/space_semi_medium"
                        android:text="@{viewModel.completeButtonText, default=`Completed`}"
                        android:textAlignment="gravity"
                        android:textColor="@drawable/selector_text_white_gray" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_semi_medium" />
            </LinearLayout>

        </ScrollView>

    </LinearLayout>
</layout>
