<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.packed.viewModel.PackedViewModel" />

        <import type="android.view.View" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/gojek_tobeordered_buttons_container"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_size_36"
            android:layout_alignParentBottom="true"
            android:background="@drawable/rect_white_border_gray_rad_4"
            android:orientation="horizontal"
            android:visibility="@{viewModel.gojekToBeOrderedActionVisibility}">

            <Button
                style="@style/Widget.TextView.SubHeader2.Medium"
                android:layout_width="@dimen/no_space"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@color/white"
                android:onClick="@{() -> viewModel.onSelectAllClicked()}"
                android:text="@string/select_all"
                android:textAlignment="center"
                android:textColor="@color/very_dark_gray" />

            <View
                android:layout_width="@dimen/divider_height"
                android:layout_height="match_parent"
                android:background="@color/very_light_gray" />

            <Button
                style="@style/Widget.TextView.SubHeader2.Medium"
                android:layout_width="@dimen/no_space"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@color/light_green"
                android:onClick="@{() -> viewModel.onOrderGosendClicked()}"
                android:text="@string/gosend_fragment_order_go_send"
                android:textAlignment="center"
                android:textColor="@color/white" />
        </LinearLayout>

        <View
            android:id="@+id/divider_buttons_container"
            android:layout_width="match_parent"
            android:layout_height="@dimen/divider_height"
            android:layout_above="@id/gojek_tobeordered_buttons_container"
            android:background="@color/very_light_gray"
            android:visibility="@{viewModel.gojekToBeOrderedActionVisibility}"/>

        <LinearLayout
            android:id="@+id/content_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/divider_buttons_container"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white_f9"
                android:orientation="vertical"
                android:paddingEnd="@dimen/space_semi_medium"
                android:paddingStart="@dimen/space_semi_medium"
                android:paddingTop="@dimen/space_small">

                <TextView
                    style="@style/Widget.TextView.BodyCopy2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/pick_up_date_label" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:descendantFocusability="beforeDescendants"
                    android:focusableInTouchMode="true"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatEditText
                        style="@style/Widget.TextView.BodyCopy1"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/space_size_45"
                        android:clickable="true"
                        android:drawableEnd="@drawable/ic_arrow_down_small_9b"
                        android:drawablePadding="@dimen/space_small"
                        android:drawableStart="@drawable/ic_calendar_9b"
                        android:focusable="false"
                        android:inputType="text"
                        android:maxLines="1"
                        android:onClick="@{() -> viewModel.shipmentDateClicked()}"
                        android:textAlignment="textStart"
                        android:textColor="@color/gray_9b"
                        app:editTextBinding="@{viewModel.shipmentDate}" />

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/edit_text_search"
                        style="@style/Widget.TextView.BodyCopy1"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_size_45"
                        android:layout_marginStart="@dimen/space_semi_medium"
                        android:drawablePadding="@dimen/space_small"
                        android:drawableStart="@drawable/ic_search_small_9b"
                        android:hint="@string/search_title"
                        android:imeOptions="actionDone"
                        android:inputType="text"
                        android:maxLines="1"
                        android:minWidth="@dimen/space_size_120"
                        android:paddingEnd="@dimen/space_semi_medium"
                        android:textColor="@color/gray_9b"
                        android:visibility="visible"
                        app:editTextBinding="@{viewModel.searchQuery}" />
                </LinearLayout>
            </LinearLayout>

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tabs"
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_size_40"
                android:background="@color/white_f9"
                android:clipToPadding="false"
                android:paddingLeft="@dimen/space_very_small"
                android:paddingRight="@dimen/space_very_small"
                app:tabGravity="fill"
                app:tabIndicatorColor="@color/light_green"
                app:tabIndicatorHeight="@dimen/space_tiny"
                app:tabMaxWidth="@dimen/no_space"
                app:tabMode="scrollable"
                app:tabPaddingEnd="@dimen/space_semi_medium"
                app:tabPaddingStart="@dimen/space_semi_medium"
                app:tabSelectedTextColor="@color/light_green"
                app:tabTextAppearance="@style/SubTabTextAppearance"
                app:tabTextColor="@color/gray_18" />

            <include
                layout="@layout/divider_section_content"
                bind:isVisible="@{true}" />

            <co.styletheory.ops.outbound.android.general.view.HackyViewPager
                android:id="@+id/view_pager"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:elevation="@dimen/space_very_small"
                app:layout_behavior="co.styletheory.ops.outbound.android.general.CustomScrollingViewBehavior" />

        </LinearLayout>


    </RelativeLayout>
</layout>