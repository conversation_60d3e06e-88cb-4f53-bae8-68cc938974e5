<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.photoDetail.viewModel.impl.PhotoDetailItemViewModelImpl" />
    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_semi_medium" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/rect_white_border_gray_rad_4"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/space_semi_medium"
                    android:layout_marginEnd="@dimen/space_medium"
                    android:layout_marginStart="@dimen/space_medium"
                    android:layout_marginTop="@dimen/space_semi_medium">

                    <RelativeLayout
                        android:id="@+id/container_complete"
                        android:layout_width="37dp"
                        android:layout_height="37dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:background="@{viewModel.isComplete ? @drawable/circle_green : @drawable/circle_white_border_gray}">

                        <ImageView
                            android:layout_width="19dp"
                            android:layout_height="14dp"
                            android:layout_centerInParent="true"
                            android:scaleType="fitCenter"
                            android:src="@{viewModel.isComplete ? @drawable/ic_check_white : @drawable/ic_check_gray}" />

                    </RelativeLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/space_medium"
                        android:layout_toStartOf="@id/container_complete"
                        android:orientation="vertical">

                        <TextView
                            style="@style/Widget.TextView.Header1.Medium"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{viewModel.customerName, default=`Customer Name / Rack`}"
                            android:textStyle="bold" />

                        <TextView
                            style="@style/Widget.TextView.BodyCopy1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_tiny"
                            android:visibility="@{viewModel.titleVisibility}"
                            android:text="@{viewModel.title, default=`Tracking ID`}" />
                    </LinearLayout>


                </RelativeLayout>

                <include
                    layout="@layout/divider_section_content"
                    bind:isVisible="@{true}" />

                <co.styletheory.ops.outbound.android.view.AdapterFlowLayout
                    android:id="@+id/flow_label"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingBottom="@dimen/space_small"
                    android:paddingEnd="@dimen/space_medium"
                    android:paddingStart="@dimen/space_medium"
                    android:paddingTop="@dimen/space_small"
                    app:adapterFlowLayoutId="@{@layout/label_view}"
                    app:adapterFlowLayoutItems="@{viewModel.labelItems}"
                    app:flChildSpacing="@dimen/space_medium"
                    app:flRowSpacing="@dimen/space_small" />

                <include
                    layout="@layout/divider_section_content"
                    bind:isVisible="@{viewModel.labelItems.size > 0}" />

                <co.styletheory.ops.outbound.android.view.AdapterLinearLayout
                    adapterLinearLayoutId="@{@layout/qa_photo_view_item}"
                    adapterLinearLayoutItems="@{viewModel.qaPhotoItems}"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingEnd="1dp"
                    android:paddingStart="1dp"
                    android:paddingTop="@dimen/space_small" />

                <TextView
                    style="@style/Widget.TextView.Header1.Medium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:enabled="@{viewModel.isComplete ? true : false}"
                    android:padding="@dimen/space_medium"
                    android:text="@{viewModel.completeButtonText}"
                    android:textSize="24sp" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_semi_medium" />
        </LinearLayout>
    </ScrollView>
</layout>
