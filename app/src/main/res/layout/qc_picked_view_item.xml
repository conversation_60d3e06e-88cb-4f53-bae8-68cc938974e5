<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.impl.QcPickedViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space_tiny"
        android:layout_marginBottom="@dimen/space_tiny">

        <LinearLayout
            android:layout_width="@dimen/no_space"
            android:layout_height="match_parent"
            android:background="@drawable/rect_white_border_gray_rad_4"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/space_semi_medium"
                android:background="@drawable/rect_orange_fe_rad_4"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/space_small"
                android:visibility="@{viewModel.soldToVisibility}">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/space_small"
                    android:src="@drawable/ic_purchased" />

                <TextView
                    style="@style/Widget.TextView.SubHeader2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@{viewModel.soldTo}"
                    android:textColor="@color/orange_d9" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_small"
                android:layout_marginBottom="@dimen/space_small"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingStart="@dimen/space_small"
                android:paddingEnd="@dimen/space_small"
                android:visibility="@{viewModel.rfidCodeVisibility}">

                <TextView
                    style="@style/Widget.TextView.BodyCopy2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/rfid_label"
                    android:textColor="@color/gray_9b" />

                <TextView
                    style="@style/Widget.TextView.SubHeader.Medium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textBinding="@{viewModel.rfidCode}"
                    android:textColor="@color/blue_cff"
                    android:textSize="@dimen/text_size_32" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_very_little"
                android:background="@color/gray_e5" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_semi_medium"
                android:layout_marginTop="@dimen/space_semi_medium"
                android:layout_marginEnd="@dimen/space_semi_medium"
                android:orientation="horizontal">

                <FrameLayout
                    android:layout_width="@dimen/qc_image_item_width"
                    android:layout_height="@dimen/qc_image_item_height">

                    <include
                        android:id="@+id/photo_image_view"
                        layout="@layout/layout_photo_with_label"
                        android:viewModel="@{viewModel.setPhotoWithLabelVM}" />
                </FrameLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/space_semi_medium">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            style="@style/Widget.TextView.SubHeader.Medium"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{viewModel.itemName, default=`Empty Name`}"
                            android:textColor="@color/very_dark_gray" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingStart="@dimen/no_space"
                            android:paddingTop="@dimen/size_spacing_horizontal_list"
                            android:paddingEnd="@dimen/size_spacing_horizontal_list"
                            android:paddingBottom="@dimen/space_semi_medium"
                            android:src="@drawable/ic_barcode_scanner"
                            android:visibility="@{viewModel.scannerIconVisibility}" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="@dimen/label_width_60"
                                android:layout_height="wrap_content"
                                android:text="@string/size_label"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="@dimen/space_semi_medium"
                                android:layout_height="wrap_content"
                                android:text="@string/divider"
                                android:textAlignment="center"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.SubHeader1.Bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.itemSize, default=`S/UK`}"
                                android:textStyle="bold" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2.Medium"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingStart="@dimen/space_tiny"
                                android:paddingEnd="@dimen/space_tiny"
                                android:text="@{viewModel.productOrder, default=`[2]`}"
                                android:textColor="@color/gray_9b" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_little"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="@dimen/label_width_60"
                                android:layout_height="wrap_content"
                                android:text="@string/color_label"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="@dimen/space_semi_medium"
                                android:layout_height="wrap_content"
                                android:text="@string/divider"
                                android:textAlignment="center"
                                android:textColor="@color/gray_9b" />

                            <co.styletheory.ops.outbound.android.view.AdapterLinearLayout
                                adapterLinearLayoutId="@{@layout/color_item_view_item}"
                                adapterLinearLayoutItems="@{viewModel.colorItems}"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:paddingStart="@dimen/space_very_little"
                                android:paddingEnd="@dimen/space_very_little" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_little"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="@dimen/label_width_60"
                                android:layout_height="wrap_content"
                                android:text="@string/category_label"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="@dimen/space_semi_medium"
                                android:layout_height="wrap_content"
                                android:text="@string/divider"
                                android:textAlignment="center"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2.Bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.category, default=`Dress`}"
                                android:textColor="@color/black"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llParts"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_little"
                            android:orientation="horizontal"
                            android:visibility="@{viewModel.partsVisibility}">

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="@dimen/label_width_60"
                                android:layout_height="wrap_content"
                                android:text="@string/parts_label"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="@dimen/space_semi_medium"
                                android:layout_height="wrap_content"
                                android:text="@string/divider"
                                android:textAlignment="center"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2.Bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.parts, default=`Belt`}"
                                android:textColor="@color/black"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llDetachable"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_little"
                            android:orientation="horizontal"
                            android:visibility="@{viewModel.detachableVisibility}">

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="@dimen/space_size_70"
                                android:layout_height="wrap_content"
                                android:text="@string/detachable_label"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="@dimen/space_semi_medium"
                                android:layout_height="wrap_content"
                                android:text="@string/divider"
                                android:textAlignment="center"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2.Bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.detachable, default=`-`}"
                                android:textColor="@color/black"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_little"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="@dimen/label_width_60"
                                android:layout_height="wrap_content"
                                android:text="@string/notes_label"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="@dimen/space_semi_medium"
                                android:layout_height="wrap_content"
                                android:text="@string/divider"
                                android:textAlignment="center"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.notes, default=`Something notes`}"
                                android:textColor="@color/very_dark_gray" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/llRack"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_semi_medium"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="@dimen/label_width_60"
                                android:layout_height="wrap_content"
                                android:text="@string/rack_label"
                                android:textColor="@color/light_green" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="@dimen/space_semi_medium"
                                android:layout_height="wrap_content"
                                android:text="@string/divider"
                                android:textAlignment="center"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.SubHeader.Bold"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.rack, default=`A - 11`}"
                                android:textColor="@color/light_green"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <Button
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_small"
                            android:background="@drawable/selector_transparent_border_dark_gray_rad_4"
                            android:enabled="@{viewModel.isAccuracySwapEnabled}"
                            android:onClick="@{() -> viewModel.accuracySwapClick()}"
                            android:text="@string/qc_accuracy_swap_button"
                            android:textColor="@drawable/selector_text_white_black"
                            android:visibility="@{viewModel.accuracySwapButtonVisibility}" />

                    </LinearLayout>
                </RelativeLayout>

            </LinearLayout>

            <TextView
                style="@style/Widget.TextView.SubHeader.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_medium"
                android:layout_marginTop="@dimen/space_medium"
                android:text="@string/qc_quality_check_status_title"
                android:textColor="@color/very_dark_gray" />

            <TextView
                style="@style/Widget.TextView.BodyCopy3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_medium"
                android:text="@{viewModel.errorMessage}"
                android:textColor="@color/red"
                android:visibility="@{viewModel.errorVisibility}" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_medium"
                android:layout_marginTop="@dimen/space_small"
                android:orientation="horizontal">

                <CheckedTextView
                    style="@style/Widget.Button.Small"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/selector_button_rect_green_rad_5"
                    android:checked="@{viewModel.pass}"
                    android:clickable="true"
                    android:enabled="@{viewModel.isButtonEnabled}"
                    android:focusable="false"
                    android:foreground="?attr/selectableItemBackground"
                    android:minWidth="@dimen/qc_image_item_width"
                    android:onClick="@{() -> viewModel.passClick()}"
                    android:padding="@dimen/space_tiny"
                    android:paddingStart="@dimen/space_semi_medium"
                    android:paddingEnd="@dimen/space_semi_medium"
                    android:text="@string/pass_label"
                    android:textAlignment="gravity"
                    android:textColor="@drawable/selector_text_white_black" />

                <CheckedTextView
                    android:id="@+id/button_fail_reason"
                    style="@style/Widget.Button.Small"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/space_small"
                    android:background="@drawable/selector_button_white_red_rad_5"
                    android:checked="@{viewModel.fail}"
                    android:clickable="true"
                    android:focusable="false"
                    android:foreground="?attr/selectableItemBackground"
                    android:minWidth="@dimen/qc_image_item_width"
                    android:onClick="@{() -> viewModel.failReasonClick(buttonFailReason)}"
                    android:padding="@dimen/space_tiny"
                    android:paddingStart="@dimen/space_semi_medium"
                    android:paddingEnd="@dimen/space_semi_medium"
                    android:text="@{viewModel.failCategory, default=@string/fail_reason_label}"
                    android:textAlignment="gravity"
                    android:textColor="@drawable/selector_text_white_black" />
            </LinearLayout>

            <include
                layout="@layout/divider_section_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_medium"
                android:visibility="@{viewModel.failReasonVisibility}" />

            <TextView
                style="@style/Widget.TextView.SubHeader"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_medium"
                android:layout_marginTop="@dimen/space_semi_medium"
                android:layout_marginEnd="@dimen/space_medium"
                android:text="@{viewModel.failReason}"
                android:textColor="@color/very_dark_gray"
                android:visibility="@{viewModel.failReasonVisibility}" />

            <include
                layout="@layout/divider_section_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_medium"
                bind:isVisible="@{true}" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_size_50"
                android:orientation="horizontal"
                android:visibility="@{viewModel.oldSwapSectionVisibility}">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_weight="2"
                    android:background="?attr/selectableItemBackground"
                    android:enabled="@{viewModel.fail}"
                    android:focusable="false"
                    android:gravity="center"
                    android:onClick="@{() -> viewModel.swapClick()}"
                    android:orientation="horizontal"
                    android:visibility="@{viewModel.swapVisibility}">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_edit"
                        android:tint="@{viewModel.fail ? @color/very_dark_gray : @color/very_light_gray}" />

                    <TextView
                        style="@style/Widget.TextView.SubHeader2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:enabled="@{viewModel.fail}"
                        android:padding="@dimen/space_semi_medium"
                        android:text="@string/swap_label"
                        android:textColor="@drawable/selector_text_black_gray" />
                </LinearLayout>

                <View
                    android:layout_width="@dimen/space_little"
                    android:layout_height="match_parent"
                    android:background="@color/very_light_gray" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_weight="2"
                    android:background="?attr/selectableItemBackground"
                    android:enabled="@{viewModel.fail}"
                    android:focusable="false"
                    android:gravity="center"
                    android:onClick="@{() -> viewModel.cameraClick()}"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_camera"
                        android:tint="@{viewModel.fail ? @color/very_dark_gray : @color/very_light_gray}" />

                    <TextView
                        style="@style/Widget.TextView.SubHeader2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:enabled="@{viewModel.fail}"
                        android:padding="@dimen/space_semi_medium"
                        android:text="@string/camera_label"
                        android:textColor="@drawable/selector_text_black_gray" />
                </LinearLayout>
            </LinearLayout>

        </LinearLayout>

        <RelativeLayout
            android:layout_width="@dimen/no_space"
            android:layout_height="@dimen/no_space"
            android:background="@drawable/rect_transparent_dark_gray_rad_4"
            android:visibility="@{viewModel.showOverlayLoading}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ProgressBar
                android:layout_width="@dimen/space_size_50"
                android:layout_height="@dimen/space_size_50"
                android:layout_centerInParent="true"
                android:indeterminateTint="@color/white" />
        </RelativeLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
