<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <import
            name="view"
            type="android.view.View" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.viewModelComponent.ProcessDialogViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rect_white_no_border_rad_4"
        android:gravity="center"
        android:onClick="@{() -> viewModel.containerClick()}"
        android:orientation="vertical"
        android:padding="@dimen/space_large">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_my_box_big" />

        <TextView
            style="@style/Widget.TextView.Header1.Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_semi_medium"
            android:text="@{viewModel.dialogTitle, default=`Empty Name`}"
            android:textColor="@color/very_dark_gray" />

        <ProgressBar
            android:layout_width="75dp"
            android:layout_height="75dp"
            android:visibility="@{viewModel.showProgress ? View.VISIBLE : View.GONE}"
            android:layout_marginTop="@dimen/space_very_large"/>

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="@{viewModel.showSuccess ? View.VISIBLE : View.GONE}"
            android:layout_marginTop="@dimen/space_very_large"
            android:tint="@color/light_green"
            android:src="@drawable/ic_check_blue"/>

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="@{viewModel.showError ? View.VISIBLE : View.GONE}"
            android:layout_marginTop="@dimen/space_very_large"
            android:src="@drawable/ic_error_red"/>

    </LinearLayout>
</layout>