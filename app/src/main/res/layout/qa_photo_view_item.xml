<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.photoDetail.viewModel.impl.PhotoItemViewModel" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_semi_medium"
                android:layout_marginTop="@dimen/space_semi_medium"
                android:layout_marginEnd="@dimen/space_semi_medium"
                android:padding="@dimen/space_small"
                android:gravity="center_vertical"
                android:background="@drawable/rect_orange_fe_rad_4"
                android:orientation="horizontal"
                android:visibility="@{viewModel.itemPurchasedVisibility}">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/space_small"
                    android:src="@drawable/ic_purchased" />

                <TextView
                    style="@style/Widget.TextView.SubHeader2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@{viewModel.soldTo}"
                    android:textColor="@color/orange_d9" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_semi_medium"
                android:layout_marginTop="@dimen/space_semi_medium"
                android:layout_marginEnd="@dimen/space_semi_medium"
                android:orientation="horizontal">

                <FrameLayout
                    android:layout_width="@dimen/space_size_150"
                    android:layout_height="@dimen/space_size_250">
                    <ImageView
                        android:id="@+id/image_view_item"
                        enableZoomImage="@{false}"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:onClick="@{() -> viewModel.imageClick()}"
                        android:imageUrl="@{viewModel.imageUrl}" />
                </FrameLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/space_semi_medium"
                    android:layout_marginBottom="@dimen/space_medium">

                    <ImageView
                        android:id="@+id/btnCamera"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:onClick="@{() -> viewModel.cameraClick()}"
                        android:src="@drawable/ic_camera"
                        android:tint="@color/very_dark_gray"
                        android:paddingTop="@dimen/space_tiny"
                        android:paddingEnd="@dimen/space_medium"
                        android:paddingStart="@dimen/space_medium"
                        android:paddingBottom="@dimen/space_medium"
                        android:layout_alignParentEnd="true"/>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/text_view_name"
                            style="@style/Widget.TextView.SubHeader.Medium"
                            android:layout_width="wrap_content"
                            android:maxWidth="@dimen/space_size_150"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/space_tiny"
                            android:text="@{viewModel.itemName}"
                            android:textColor="@color/very_dark_gray"
                            bind:text="Empty Name"/>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="60dp"
                                android:layout_height="wrap_content"
                                android:text="@string/size_label"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="12dp"
                                android:layout_height="wrap_content"
                                android:text="@string/divider"
                                android:textAlignment="center"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.SubHeader1.Bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.itemSize, default=`S/UK`}"
                                android:textColor="@color/blue_cff"
                                android:textStyle="bold" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2.Medium"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingStart="@dimen/space_tiny"
                                android:paddingEnd="@dimen/space_tiny"
                                android:text="@{viewModel.productOrder, default=`[2]`}"
                                android:textColor="@color/gray_9b" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="60dp"
                                android:layout_height="wrap_content"
                                android:text="@string/color_label"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="12dp"
                                android:layout_height="wrap_content"
                                android:text="@string/divider"
                                android:textAlignment="center"
                                android:textColor="@color/gray_9b" />

                            <co.styletheory.ops.outbound.android.view.AdapterLinearLayout
                                adapterLinearLayoutId="@{@layout/color_item_view_item}"
                                adapterLinearLayoutItems="@{viewModel.colorItems}"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:paddingStart="1dp"
                                android:paddingEnd="1dp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="60dp"
                                android:layout_height="wrap_content"
                                android:text="@string/category_label"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="12dp"
                                android:layout_height="wrap_content"
                                android:text="@string/divider"
                                android:textAlignment="center"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2.Bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.category, default=`Dress`}"
                                android:textColor="@color/black"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="60dp"
                                android:layout_height="wrap_content"
                                android:text="@string/parts_label"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="12dp"
                                android:layout_height="wrap_content"
                                android:text="@string/divider"
                                android:textAlignment="center"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2.Bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.parts, default=`Belt`}"
                                android:textColor="@color/black"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="60dp"
                                android:layout_height="wrap_content"
                                android:text="@string/notes_label"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="12dp"
                                android:layout_height="wrap_content"
                                android:text="@string/divider"
                                android:textAlignment="center"
                                android:textColor="@color/gray_9b" />

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.notes, default=`Something notes`}"
                                android:textColor="@color/very_dark_gray" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_semi_medium"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/Widget.TextView.BodyCopy2"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Rack : "
                                android:textColor="@color/light_green" />

                            <TextView
                                style="@style/Widget.TextView.SubHeader.Bold"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.rack, default=`A - 11`}"
                                android:textColor="@color/light_green"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <TextView
                            style="@style/Widget.TextView.SubHeader2.Medium"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_small"
                            android:text="@string/done_taking_a_photo_label"
                            android:textColor="#FF2A3033" />

                        <TextView
                            style="@style/Widget.TextView.BodyCopy3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{viewModel.errorMessage}"
                            android:textColor="@color/red"
                            android:visibility="@{viewModel.errorMessage.isEmpty() ? View.GONE : View.VISIBLE}" />

                        <CheckedTextView
                            style="@style/Widget.Button.Small"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_small"
                            android:background="@drawable/selector_button_rect_green_rad_5"
                            android:checked="@{viewModel.photoDone ? true : false}"
                            android:clickable="true"
                            android:focusable="false"
                            android:foreground="?attr/selectableItemBackground"
                            android:onClick="@{() -> viewModel.photoDoneClick()}"
                            android:padding="@dimen/space_tiny"
                            android:paddingStart="@dimen/space_semi_medium"
                            android:paddingEnd="@dimen/space_semi_medium"
                            android:text="@string/photo_done_label"
                            android:textAlignment="gravity"
                            android:textColor="@drawable/selector_text_white_black" />

                    </LinearLayout>
                </RelativeLayout>

            </LinearLayout>

            <include
                layout="@layout/divider_section_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_medium"
                bind:isVisible="@{true}" />

        </LinearLayout>

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/rect_transparent_dark_gray_rad_4"
            android:clickable="true"
            android:focusable="true"
            android:visibility="@{viewModel.showOverlayLoading ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ProgressBar
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_centerInParent="true"
                android:indeterminateTint="@color/white" />
        </RelativeLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
