<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/headersIndicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_arrow_down"
        android:layout_marginEnd="@dimen/space_small"
        android:contentDescription="@string/accuracy_swap_title"
        app:layout_constraintTop_toTopOf="@id/headersTitle"
        app:layout_constraintEnd_toEndOf="@id/headersTitle"
        app:layout_constraintBottom_toBottomOf="@id/headersTitle"/>

    <TextView
        android:id="@+id/headersTitle"
        style="@style/Widget.TextView.SubHeader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/space_small"
        android:paddingTop="@dimen/space_small"
        android:paddingBottom="@dimen/space_small"
        android:paddingEnd="?android:attr/expandableListPreferredItemIndicatorRight"
        android:ellipsize="marquee"
        android:singleLine="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintEnd_toStartOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>