<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.viewModelComponent.AlertDialogViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rect_white_no_border_rad_4"
        android:orientation="vertical"
        android:padding="@dimen/space_large">

        <LinearLayout
            android:id="@+id/flow_logistic_provider"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:visibility="@{viewModel.logisticNameVisibility}">

            <include
                android:id="@+id/logistic_provider_name_section_container"
                layout="@layout/layout_logistic_provider_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:viewModel="@{viewModel.setupLogisticProviderNameViewModel()}" />
        </LinearLayout>

        <TextView
            android:id="@+id/text_view_dialog_title"
            style="@style/Widget.TextView.Header2.Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_medium"
            android:text="@{viewModel.dialogTitle, default=`Empty Name`}"
            android:textColor="@color/very_dark_gray" />

        <TextView
            android:id="@+id/text_view_dialog_body"
            style="@style/Widget.TextView.Header2.Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_medium"
            android:text="@{viewModel.dialogBody, default=`Empty Name`}"
            android:textColor="@color/very_dark_gray" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    style="@style/Widget.TextView.BodyCopy2"
                    android:layout_width="@dimen/space_size_70"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_medium"
                    android:layout_marginEnd="@dimen/space_medium"
                    android:text="@string/settings_dialog_generate_batch_region_title"
                    android:textColor="@color/white_8a"
                    android:textSize="@dimen/text_size_16"
                    android:textStyle="bold" />

                <TextView
                    style="@style/Widget.TextView.BodyCopy2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_medium"
                    android:layout_marginEnd="@dimen/space_medium"
                    android:text="@string/settings_dialog_generate_batch_divider"
                    android:textColor="@color/white_8a"
                    android:textSize="@dimen/text_size_16"
                    android:textStyle="bold" />

                <TextView
                    style="@style/Widget.TextView.BodyCopy2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_medium"
                    android:text="@{viewModel.batchRegion, default=`Empty Region`}"
                    android:textColor="@color/white_8a"
                    android:textSize="@dimen/text_size_16" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    style="@style/Widget.TextView.BodyCopy2"
                    android:layout_width="@dimen/space_size_70"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_medium"
                    android:layout_marginEnd="@dimen/space_medium"
                    android:text="@string/settings_dialog_generate_batch_vertical_title"
                    android:textColor="@color/white_8a"
                    android:textSize="@dimen/text_size_16"
                    android:textStyle="bold" />

                <TextView
                    style="@style/Widget.TextView.BodyCopy2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_medium"
                    android:layout_marginEnd="@dimen/space_medium"
                    android:text="@string/settings_dialog_generate_batch_divider"
                    android:textColor="@color/white_8a"
                    android:textSize="@dimen/text_size_16"
                    android:textStyle="bold" />

                <TextView
                    style="@style/Widget.TextView.BodyCopy2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_medium"
                    android:text="@{viewModel.batchVertical, default=`Empty Vertical`}"
                    android:textColor="@color/white_8a"
                    android:textSize="@dimen/text_size_16" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="3">

                <TextView
                    style="@style/Widget.TextView.BodyCopy2"
                    android:layout_width="@dimen/space_size_70"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_medium"
                    android:layout_marginEnd="@dimen/space_medium"
                    android:text="@string/settings_dialog_generate_batch_time_title"
                    android:textColor="@color/white_8a"
                    android:textSize="@dimen/text_size_16"
                    android:textStyle="bold" />

                <TextView
                    style="@style/Widget.TextView.BodyCopy2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_medium"
                    android:layout_marginEnd="@dimen/space_medium"
                    android:text="@string/settings_dialog_generate_batch_divider"
                    android:textColor="@color/white_8a"
                    android:textSize="@dimen/text_size_16"
                    android:textStyle="bold" />

                <TextView
                    style="@style/Widget.TextView.BodyCopy2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_medium"
                    android:text="@{viewModel.batchTime, default=`Empty Time`}"
                    android:textColor="@color/white_8a"
                    android:textSize="@dimen/text_size_16" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_very_large"
            android:orientation="horizontal"
            android:weightSum="2">

            <TextView
                style="@style/Widget.Button.Dialog"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:foreground="?attr/selectableItemBackground"
                android:onClick="@{() -> viewModel.leftButtonClicked()}"
                android:text="@{viewModel.leftButtonText}"
                android:textColor="@color/gray_75"
                android:visibility="@{viewModel.leftButtonVisibility}"
                tools:text="@string/cancel" />

            <TextView
                style="@style/Widget.Button.Dialog"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:foreground="?attr/selectableItemBackground"
                android:onClick="@{() -> viewModel.rightButtonClicked()}"
                android:text="@{viewModel.rightButtonText}"
                android:textColor="@color/light_green"
                tools:text="@string/ok_button" />

        </LinearLayout>
    </LinearLayout>
</layout>