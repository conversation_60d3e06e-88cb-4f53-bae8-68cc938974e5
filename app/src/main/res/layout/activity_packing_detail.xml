<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="toolbarVM"
            type="co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.packing.viewModel.impl.PackingDetailViewModelImpl" />
    </data>

    <LinearLayout
        android:id="@+id/main_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include
            android:id="@+id/toolbar_wrapper"
            layout="@layout/general_toolbar"
            android:viewModel="@{toolbarVM}" />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/editTextScannedRfid"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_very_little"
            android:focusable="false"
            android:focusableInTouchMode="true"
            android:onEditTextKeyListener="@{viewModel.onEditTextKeyListener}"
            android:singleLine="true"
            app:editTextBinding="@{viewModel.scannedRfidText}">
            <requestFocus />
        </androidx.appcompat.widget.AppCompatEditText>

        <co.styletheory.ops.outbound.android.general.view.HackyViewPager
            android:id="@+id/view_pager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>

</layout>