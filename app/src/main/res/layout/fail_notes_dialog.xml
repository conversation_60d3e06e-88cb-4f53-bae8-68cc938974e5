<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.impl.FailNotesDialogViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rect_white_no_border_rad_4"
        android:orientation="vertical"
        android:padding="@dimen/space_large">

        <TextView
            style="@style/Widget.TextView.SubHeader2.Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/rect_red_border_rad_5"
            android:paddingBottom="@dimen/space_small"
            android:paddingEnd="@dimen/space_semi_medium"
            android:paddingStart="@dimen/space_semi_medium"
            android:paddingTop="@dimen/space_small"
            android:text="@{viewModel.failCategory, default=`Others`}"
            android:textColor="@color/red" />

        <androidx.appcompat.widget.AppCompatEditText
            style="@style/Widget.TextView.SubHeader2.Medium"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_medium"
            android:background="@drawable/rect_very_dark_gray_rad_4"
            android:gravity="top"
            android:hint="@string/add_notes_optional_hint"
            android:inputType="textMultiLine"
            android:minHeight="100dp"
            android:padding="@dimen/space_small"
            android:textColor="@color/very_dark_gray"
            android:textColorHint="@color/very_dark_gray"
            app:editTextBinding="@{viewModel.failReason}" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_large"
            android:gravity="end"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/test"
                style="@style/Widget.Button.Dialog"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:foreground="?attr/selectableItemBackground"
                android:onClick="@{() -> viewModel.cancelClick()}"
                android:text="@string/cancel_label"
                android:textColor="@color/gray_75" />

            <TextView
                style="@style/Widget.Button.Dialog"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_medium"
                android:foreground="?attr/selectableItemBackground"
                android:onClick="@{() -> viewModel.addNotesClick()}"
                android:text="@string/add_notes_label"
                android:textColor="@color/light_green" />

        </LinearLayout>

    </LinearLayout>
</layout>