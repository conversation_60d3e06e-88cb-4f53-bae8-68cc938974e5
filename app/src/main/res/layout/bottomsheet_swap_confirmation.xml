<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.swapConfirmationBottomSheet.viewModel.SwapConfirmationBottomSheetViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true"
        android:elevation="@dimen/space_small"
        android:focusable="true"
        app:behavior_hideable="false"
        app:behavior_peekHeight="@dimen/no_space"
        android:paddingBottom="@dimen/space_large"
        android:paddingStart="@dimen/no_space"
        app:layout_behavior="@string/bottom_sheet_behavior">

        <ImageView
            android:id="@+id/imageClose"
            android:layout_width="@dimen/space_large"
            android:layout_height="@dimen/space_large"
            android:layout_margin="@dimen/space_medium"
            android:contentDescription="@string/close_label"
            android:onClick="@{() -> viewModel.onCloseClick()}"
            android:src="@drawable/ic_close"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textHeader"
            style="@style/Widget.TextView.Header2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/space_medium"
            android:text="@string/accuracy_swap_this_item"
            android:textColor="@color/black_2a"
            app:layout_constraintStart_toEndOf="@+id/imageClose"
            app:layout_constraintTop_toTopOf="parent" />

        <!--region Item Found-->
        <ImageView
            android:id="@+id/image_view_old_item"
            android:layout_width="@dimen/space_size_80"
            android:layout_height="@dimen/space_size_120"
            android:layout_marginStart="@dimen/space_medium"
            android:layout_marginTop="@dimen/space_very_large"
            android:imageUrl="@{viewModel.oldItemImage}"
            android:visibility="@{viewModel.itemFoundVisibility}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textHeader" />

        <TextView
            style="@style/Widget.TextView.BodyCopy3.Medium"
            android:textColor="@color/white"
            android:layout_width="@dimen/space_size_80"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:visibility="@{viewModel.itemFoundVisibility}"
            android:layout_marginStart="@dimen/space_medium"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="@+id/image_view_old_item"
            android:text="@{viewModel.oldItemStyleCategory}"
            android:textAllCaps="true"
            android:background="@drawable/bg_swap_confirmation_style_category"
            tools:text="DRESSES"/>

        <TextView
            android:id="@+id/previous_item_label"
            style="@style/Widget.TextView.BodyCopy3.Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_medium"
            android:layout_marginTop="@dimen/space_size_36"
            android:background="@color/orange_e6_background"
            android:gravity="center"
            android:paddingStart="@dimen/space_small"
            android:paddingEnd="@dimen/space_small"
            android:text="@string/old_item_label_text"
            android:textAllCaps="true"
            android:textColor="@color/orange_e6"
            android:visibility="@{viewModel.itemFoundVisibility}"
            app:layout_constraintStart_toEndOf="@id/image_view_old_item"
            app:layout_constraintTop_toBottomOf="@id/textHeader" />

        <TextView
            android:id="@+id/item_name"
            style="@style/Widget.TextView.SubHeader2"
            android:layout_width="@dimen/no_space"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_medium"
            android:layout_marginEnd="@dimen/space_medium"
            android:layout_marginTop="@dimen/space_small"
            android:text="@{viewModel.oldItemName}"
            android:textColor="@color/black_2a"
            android:visibility="@{viewModel.itemFoundVisibility}"
            app:layout_constraintStart_toEndOf="@id/image_view_old_item"
            app:layout_constraintTop_toBottomOf="@id/previous_item_label"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="Fuchsia Jumpsuit" />

        <TextView
            android:id="@+id/designer_name"
            style="@style/Widget.TextView.Header2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_medium"
            android:layout_marginEnd="@dimen/space_medium"
            android:layout_marginTop="@dimen/space_tiny"
            android:text="@{viewModel.oldItemDesignerName}"
            android:textColor="@color/black_2a"
            android:visibility="@{viewModel.itemFoundVisibility}"
            app:layout_constraintStart_toEndOf="@id/image_view_old_item"
            app:layout_constraintTop_toBottomOf="@id/item_name"
            tools:text="Keepsake" />

        <TextView
            android:id="@+id/size_note_text"
            style="@style/Widget.TextView.BodyCopy2.Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_medium"
            android:layout_marginEnd="@dimen/space_medium"
            android:layout_marginTop="@dimen/space_small"
            android:text="@{viewModel.oldItemSizeNote}"
            android:textColor="@color/gray"
            android:visibility="@{viewModel.itemFoundVisibility}"
            app:layout_constraintStart_toEndOf="@id/image_view_old_item"
            app:layout_constraintTop_toBottomOf="@id/designer_name"
            tools:text="UK 10 [2C]" />

        <ImageView
            android:id="@+id/swap_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_size_40"
            android:layout_marginTop="@dimen/space_very_large"
            android:src="@drawable/ic_swap"
            android:visibility="@{viewModel.itemFoundVisibility}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/image_view_old_item" />

        <ImageView
            android:id="@+id/image_view_new_item"
            android:layout_width="@dimen/space_size_80"
            android:layout_height="@dimen/space_size_120"
            android:layout_marginStart="@dimen/space_medium"
            android:layout_marginTop="@dimen/space_very_large"
            android:imageUrl="@{viewModel.newItemImage}"
            android:visibility="@{viewModel.itemFoundVisibility}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/swap_icon" />

        <TextView
            style="@style/Widget.TextView.BodyCopy3.Medium"
            android:textColor="@color/white"
            android:layout_width="@dimen/space_size_80"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:visibility="@{viewModel.itemFoundVisibility}"
            android:layout_marginStart="@dimen/space_medium"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="@+id/image_view_new_item"
            android:text="@{viewModel.newItemStyleCategory}"
            android:textAllCaps="true"
            android:background="@drawable/bg_swap_confirmation_style_category"
            tools:text="DRESSES"/>

        <TextView
            android:id="@+id/new_item_label"
            style="@style/Widget.TextView.BodyCopy3.Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_medium"
            android:layout_marginEnd="@dimen/space_medium"
            android:layout_marginTop="@dimen/space_size_36"
            android:background="@color/green_caf_background"
            android:gravity="center"
            android:paddingStart="@dimen/space_small"
            android:paddingEnd="@dimen/space_small"
            android:text="@string/new_item_label_text"
            android:textAllCaps="true"
            android:textColor="@color/green_caf"
            android:visibility="@{viewModel.itemFoundVisibility}"
            app:layout_constraintStart_toEndOf="@id/image_view_new_item"
            app:layout_constraintTop_toBottomOf="@id/swap_icon" />

        <TextView
            android:id="@+id/new_item_name"
            style="@style/Widget.TextView.SubHeader2"
            android:layout_width="@dimen/no_space"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_medium"
            android:layout_marginEnd="@dimen/space_medium"
            android:layout_marginTop="@dimen/space_small"
            android:text="@{viewModel.newItemName}"
            android:textColor="@color/black_2a"
            android:visibility="@{viewModel.itemFoundVisibility}"
            app:layout_constraintStart_toEndOf="@id/image_view_new_item"
            app:layout_constraintTop_toBottomOf="@id/new_item_label"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="Fuchsia Jumpsuit" />

        <TextView
            android:id="@+id/new_designer_name"
            style="@style/Widget.TextView.Header2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_medium"
            android:layout_marginEnd="@dimen/space_medium"
            android:layout_marginTop="@dimen/space_tiny"
            android:text="@{viewModel.newItemDesignerName}"
            android:textColor="@color/black_2a"
            android:visibility="@{viewModel.itemFoundVisibility}"
            app:layout_constraintStart_toEndOf="@id/image_view_new_item"
            app:layout_constraintTop_toBottomOf="@id/new_item_name"
            tools:text="Keepsake" />

        <TextView
            android:id="@+id/new_size_note_text"
            style="@style/Widget.TextView.BodyCopy2.Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_medium"
            android:layout_marginEnd="@dimen/space_medium"
            android:layout_marginTop="@dimen/space_small"
            android:text="@{viewModel.newItemSizeNote}"
            android:textColor="@color/gray"
            android:visibility="@{viewModel.itemFoundVisibility}"
            app:layout_constraintStart_toEndOf="@id/image_view_new_item"
            app:layout_constraintTop_toBottomOf="@id/new_designer_name"
            tools:text="UK 10 [2C]" />

        <TextView
            android:id="@+id/textView2"
            style="@style/Widget.TextView.BodyCopy2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_medium"
            android:layout_marginTop="@dimen/space_medium_large"
            android:layout_marginEnd="@dimen/space_medium"
            android:background="@drawable/bg_swap_confirmation_note"
            android:paddingStart="@dimen/space_size_40"
            android:paddingTop="@dimen/space_small"
            android:paddingEnd="@dimen/space_small"
            android:paddingBottom="@dimen/space_small"
            android:text="@string/swap_by_scan_qc_info"
            android:textBinding="@{viewModel.swapInformation}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toStartOf="parent"
            android:visibility="@{viewModel.itemFoundVisibility}"
            app:layout_constraintTop_toBottomOf="@id/image_view_new_item" />

        <ImageView
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            android:src="@drawable/ic_alert_gray"
            app:layout_constraintBottom_toBottomOf="@id/textView2"
            android:layout_marginStart="@dimen/space_large"
            android:visibility="@{viewModel.itemFoundVisibility}"
            app:layout_constraintTop_toTopOf="@id/textView2" />

        <!--endregion-->

        <!--region Loading state-->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_size_80"
            app:layout_constraintTop_toBottomOf="@id/textHeader"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/loading_text"
            android:visibility="@{viewModel.loadingStateVisibility}"
            tools:visibility="gone"
            android:indeterminate="true"/>

        <TextView
            android:id="@+id/loading_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/item_swapping_loading_text"
            android:layout_marginStart="@dimen/space_medium"
            android:layout_marginEnd="@dimen/space_medium"
            android:layout_marginTop="@dimen/space_huge"
            style="@style/Widget.TextView.SubHeader2"
            app:layout_constraintTop_toBottomOf="@id/progress_bar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:visibility="@{viewModel.loadingStateVisibility}"
            tools:visibility="gone"
            android:textColor="@color/black_2a"/>
        <!--endregion-->

        <Button
            android:id="@+id/button"
            style="@style/Widget.TextView.SubHeader2.Medium"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space_medium"
            android:layout_marginEnd="@dimen/space_medium"
            android:layout_marginTop="@dimen/space_size_48"
            android:background="@drawable/rect_green_border_greencaf_rad_4"
            android:onClick="@{() -> viewModel.onConfirmButtonClick()}"
            android:paddingTop="@dimen/space_semi_medium"
            android:paddingBottom="@dimen/space_semi_medium"
            android:text="@string/confirm_button_text"
            android:textAllCaps="true"
            android:textColor="@color/white"
            android:visibility="@{viewModel.itemFoundVisibility}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView2" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>