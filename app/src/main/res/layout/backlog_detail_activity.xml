<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="toolbarVM"
            type="co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel.impl.BacklogDetailViewModelImpl" />
    </data>

    <LinearLayout
        android:id="@+id/main_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include
            android:id="@+id/toolbar_wrapper"
            layout="@layout/general_toolbar"
            android:viewModel="@{toolbarVM}" />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/editTextScannedRfid"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_very_little"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:onEditTextKeyListener="@{viewModel.onEditTextKeyListener}"
            android:singleLine="true"
            app:editTextBinding="@{viewModel.scannedRfidText}">
            <requestFocus />
        </androidx.appcompat.widget.AppCompatEditText>

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipeRefreshBacklog"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:scrollbars="vertical"/>

            </FrameLayout>

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    </LinearLayout>

</layout>