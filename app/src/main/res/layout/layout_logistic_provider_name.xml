<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel.LogisticProviderNameViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_shipping_address"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:items="@{viewModel.logisticNameItems}"
            android:viewSetting="@{viewModel.recyclerViewLogisticName}"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:itemCount="5"
            tools:listitem="@layout/item_logistic_provider_name"
            tools:orientation="horizontal" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>