<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="shimmerVisibility"
            type="Integer" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="@{shimmerVisibility}">

        <com.facebook.shimmer.ShimmerFrameLayout
            android:id="@+id/shimmerSettings"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_medium"
            android:layout_marginBottom="@dimen/space_medium"
            android:orientation="vertical"
            app:shimmer_duration="@integer/shimmer_duration">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/space_medium"
                    android:orientation="vertical"
                    android:paddingStart="@dimen/space_large"
                    android:paddingTop="@dimen/space_medium"
                    android:paddingEnd="@dimen/space_large"
                    android:paddingBottom="@dimen/space_medium">

                    <View
                        android:layout_width="@dimen/space_size_70"
                        android:layout_height="@dimen/space_medium_large"
                        android:background="@color/gray" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_medium_large"
                        android:layout_marginTop="@dimen/space_semi_medium"
                        android:background="@color/gray" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_medium_large"
                        android:layout_marginTop="@dimen/space_semi_medium"
                        android:background="@color/gray" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_very_little"
                    android:background="@color/very_light_gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_medium"
                    android:layout_marginBottom="@dimen/space_medium"
                    android:orientation="vertical"
                    android:paddingStart="@dimen/space_large"
                    android:paddingTop="@dimen/space_medium"
                    android:paddingEnd="@dimen/space_large"
                    android:paddingBottom="@dimen/space_medium">

                    <View
                        android:layout_width="@dimen/space_size_70"
                        android:layout_height="@dimen/space_medium_large"
                        android:background="@color/gray" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_medium_large"
                        android:layout_marginTop="@dimen/space_semi_medium"
                        android:background="@color/gray" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_medium_large"
                        android:layout_marginTop="@dimen/space_semi_medium"
                        android:background="@color/gray" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_very_little"
                    android:background="@color/very_light_gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_medium"
                    android:layout_marginBottom="@dimen/space_medium"
                    android:orientation="vertical"
                    android:paddingStart="@dimen/space_large"
                    android:paddingTop="@dimen/space_medium"
                    android:paddingEnd="@dimen/space_large"
                    android:paddingBottom="@dimen/space_medium">

                    <View
                        android:layout_width="@dimen/space_size_70"
                        android:layout_height="@dimen/space_medium_large"
                        android:background="@color/gray" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_medium_large"
                        android:layout_marginTop="@dimen/space_semi_medium"
                        android:background="@color/gray" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_medium_large"
                        android:layout_marginTop="@dimen/space_semi_medium"
                        android:background="@color/gray" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_very_little"
                    android:background="@color/very_light_gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_medium"
                    android:layout_marginBottom="@dimen/space_medium"
                    android:orientation="vertical"
                    android:paddingStart="@dimen/space_large"
                    android:paddingTop="@dimen/space_medium"
                    android:paddingEnd="@dimen/space_large"
                    android:paddingBottom="@dimen/space_medium">

                    <View
                        android:layout_width="@dimen/space_size_70"
                        android:layout_height="@dimen/space_medium_large"
                        android:background="@color/gray" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_medium_large"
                        android:layout_marginTop="@dimen/space_semi_medium"
                        android:background="@color/gray" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_medium_large"
                        android:layout_marginTop="@dimen/space_semi_medium"
                        android:background="@color/gray" />

                </LinearLayout>

            </LinearLayout>

        </com.facebook.shimmer.ShimmerFrameLayout>

    </RelativeLayout>

</layout>