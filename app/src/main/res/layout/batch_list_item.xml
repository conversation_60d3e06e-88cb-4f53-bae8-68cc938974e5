<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.batch.viewModel.BatchListViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/space_medium"
            android:paddingTop="@dimen/space_small"
            android:paddingEnd="@dimen/space_medium"
            android:paddingBottom="@dimen/space_small">

            <TextView
                android:id="@+id/text_view_batch_title"
                style="@style/Widget.TextView.Header1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{viewModel.batchTitle, default= `Batch One`}"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/text_view_total"
                style="@style/Widget.TextView.SubHeader"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/text_view_batch_title"
                android:layout_marginTop="@dimen/space_tiny"
                android:text="@{viewModel.totalBoxAndItem, default= `0 Boxes - 60 Items`}"
                android:textColor="@color/very_dark_gray" />

            <TextView
                android:id="@+id/text_view_rack"
                style="@style/Widget.TextView.SubHeader"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/text_view_batch_title"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="@dimen/space_tiny"
                android:text="@{viewModel.rack, default= `Rack A`}"
                android:textColor="@color/very_dark_gray" />
        </RelativeLayout>

        <include
            layout="@layout/divider_section_content"
            bind:isVisible="@{true}" />

        <co.styletheory.ops.outbound.android.view.AdapterFlowLayout
            android:id="@+id/flow_label"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/space_medium"
            android:paddingTop="@dimen/space_small"
            android:paddingEnd="@dimen/space_medium"
            android:paddingBottom="@dimen/space_small"
            android:visibility="@{viewModel.labelItems.size > 0 ? View.VISIBLE : View.GONE}"
            app:adapterFlowLayoutId="@{@layout/label_view}"
            app:adapterFlowLayoutItems="@{viewModel.labelItems}"
            app:flChildSpacing="@dimen/space_medium"
            app:flRowSpacing="@dimen/space_semi_medium" />

        <include
            layout="@layout/divider_section_content"
            bind:isVisible="@{viewModel.labelItems.size > 0}" />

        <LinearLayout
            android:id="@+id/flow_logistic_provider"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:paddingStart="@dimen/space_medium"
            android:paddingTop="@dimen/space_semi_medium"
            android:paddingEnd="@dimen/space_medium"
            android:paddingBottom="@dimen/space_semi_medium"
            android:visibility="@{viewModel.logisticProviderNameVisibility}">

            <include
                android:id="@+id/logistic_provider_name_section_container"
                layout="@layout/layout_logistic_provider_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:viewModel="@{viewModel.setupLogisticProviderNameViewModel()}" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:visibility="@{viewModel.logisticProviderNameVisibility}">

            <include
                layout="@layout/divider_section_content"
                bind:isVisible="@{true}" />
        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/space_medium"
            android:paddingTop="@dimen/space_semi_medium"
            android:paddingEnd="@dimen/space_medium"
            android:paddingBottom="@dimen/space_semi_medium">

            <TextView
                android:id="@+id/text_view_total_items"
                style="@style/Widget.TextView.SubHeader.Medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="@dimen/space_small"
                android:text="@{viewModel.totalItem, default= `0 Items Picked`}"
                android:textColor="@color/very_dark_gray"
                app:drawableStartCompat="@drawable/ic_hanger" />

            <TextView
                android:id="@+id/text_view_total_boxes"
                style="@style/Widget.TextView.SubHeader.Medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/text_view_total_items"
                android:layout_marginTop="@dimen/space_semi_medium"
                android:drawablePadding="@dimen/space_small"
                android:text="@{viewModel.totalBox, default= `0 Boxes Picked`}"
                android:textColor="@color/very_dark_gray"
                app:drawableStartCompat="@drawable/ic_my_box" />

            <TextView
                style="@style/Widget.TextView.SubHeader.Medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_toEndOf="@id/text_view_total_items"
                android:text="@{viewModel.itemLeft, default= `0 Items Left`}"
                android:textAlignment="textEnd"
                android:textColor="@color/dark_orange" />
        </RelativeLayout>

        <include
            layout="@layout/divider_section_content"
            bind:isVisible="@{true}" />

        <co.styletheory.ops.outbound.android.view.AdapterLinearLayout
            android:id="@+id/container_batch_step"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:adapterLinearLayoutId="@{@layout/batch_step_item_view}"
            app:adapterLinearLayoutItems="@{viewModel.batchStepItems}" />
    </LinearLayout>
</layout>
