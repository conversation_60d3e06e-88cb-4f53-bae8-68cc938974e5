<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel" />
    </data>

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/colorPrimary"
        android:theme="@style/AppTheme.AppBarOverlay"
        app:contentInsetStart="0dp"
        app:contentInsetStartWithNavigation="0dp"
        app:layout_scrollFlags="scroll|enterAlways|snap"
        app:subtitleTextColor="@color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/toolbar_title"
                style="@style/Widget.TextView.Header2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{viewModel.title, default= `Title`}" />

            <TextView
                android:id="@+id/toolbar_subtitle"
                style="@style/Widget.TextView.BodyCopy2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{viewModel.subtitle, default= `Subtitle`}"
                android:visibility="@{viewModel.subtitle.isEmpty() ? View.GONE : View.VISIBLE}" />
        </LinearLayout>
    </androidx.appcompat.widget.Toolbar>

</layout>
