<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <View
        android:id="@+id/divider_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/divider_height"
        android:background="@color/gray_9b" />

    <TextView
        android:id="@android:id/text1"
        style="@style/Widget.TextView.SubHeader2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/space_small"
        android:layout_marginEnd="@dimen/space_medium"
        android:layout_marginStart="@dimen/space_medium"
        android:layout_marginTop="@dimen/space_small"
        android:background="?android:attr/selectableItemBackground"
        android:gravity="center_vertical" />

    <View
        android:id="@+id/divider_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/divider_height"
        android:background="@color/gray_9b" />

</LinearLayout>