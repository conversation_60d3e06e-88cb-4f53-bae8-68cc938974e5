<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.photoManager.viewModel.AttachImageViewModel" />
    </data>

    <RelativeLayout
        android:layout_width="70dp"
        android:layout_height="wrap_content">

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/image_view_attach"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_margin="@dimen/space_small"
            android:centerImage="@{viewModel.imageUrl}"
            android:onClick="@{() -> viewModel.previewImage()}"
            android:scaleType="centerCrop"
            app:riv_corner_radius="8dp" />

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentEnd="true"
            android:src="@drawable/ic_error_red"
            android:visibility="gone" />
    </RelativeLayout>
</layout>