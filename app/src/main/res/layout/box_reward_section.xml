<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.BoxRewardSectionViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/rewards_label"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/gray_white"
            android:orientation="vertical"
            android:padding="@dimen/space_medium"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                style="@style/Widget.TextView.Header1.Medium"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_small"
                android:text="@string/rewards_text"
                android:textSize="@dimen/text_size_16" />

            <TextView
                style="@style/Widget.TextView.SubHeader2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/rewards_list_text"
                android:textSize="@dimen/text_size_14" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_box_reward"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:items="@{viewModel.rewardItems}"
            android:viewSetting="@{viewModel.recyclerViewSetting}"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintStart_toStartOf="@id/rewards_label"
            app:layout_constraintTop_toBottomOf="@id/rewards_label"
            tools:itemCount="2"
            tools:listitem="@layout/box_reward_section_item" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>