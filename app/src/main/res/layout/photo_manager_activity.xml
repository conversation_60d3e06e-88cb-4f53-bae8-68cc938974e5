<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.photoManager.viewModel.PhotoManagerViewModel" />

        <variable
            name="toolbarVM"
            type="co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include
            android:id="@+id/toolbar_wrapper"
            layout="@layout/general_toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:viewModel="@{toolbarVM}" />

        <LinearLayout
            android:id="@+id/product_wrapper"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/toolbar_wrapper"
            android:background="@color/white"
            android:foreground="?attr/selectableItemBackground"
            android:orientation="horizontal"
            android:visibility="gone">

            <ImageView
                android:id="@+id/product_imageview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="7"
                android:background="@color/white"
                android:padding="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@color/white"
                android:clickable="true"
                android:focusable="true"
                android:orientation="vertical"
                android:padding="8dp">

                <TextView
                    android:id="@+id/designer_name_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="sans-serif-medium"
                    android:text="@{viewModel.designerName}"
                    android:textColor="@color/black"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/product_name_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text="@{viewModel.itemName}"
                    android:textColor="@color/gray_9b"
                    android:textSize="12sp" />
            </LinearLayout>
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/product_wrapper"
            android:scrollbars="vertical" />

    </RelativeLayout>
</layout>