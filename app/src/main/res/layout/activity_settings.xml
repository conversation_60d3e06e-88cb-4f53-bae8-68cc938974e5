<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="co.styletheory.ops.outbound.android.model.enums.Regions" />
        <import type="co.styletheory.ops.outbound.android.model.enums.VerticalTypes" />
        <import type="co.styletheory.ops.outbound.android.model.enums.Barcode" />

        <variable
            name="toolbarVM"
            type="co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.settings.viewModel.SettingsViewModel" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <include
            android:id="@+id/toolbar_wrapper"
            layout="@layout/general_toolbar"
            android:viewModel="@{toolbarVM}" />

        <include
            android:id="@+id/shimmerPlaceholder"
            layout="@layout/activity_settings_shimmer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/btnGenerateBatch"
            android:layout_below="@+id/toolbar_wrapper"
            tools:shimmerVisibility="@{viewModel.shimmerVisibility}" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/btnGenerateBatch"
            android:layout_below="@+id/toolbar_wrapper"
            android:visibility="@{viewModel.sectionVisibility}">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/llAppVersion"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="@dimen/space_very_small"
                    android:background="@color/gray_white">

                    <TextView
                        style="@style/Widget.TextView.SubHeader2.Medium"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@{viewModel.outboundAppVersion}"
                        android:gravity="center"/>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llregion"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_medium"
                    android:layout_marginBottom="@dimen/space_medium"
                    android:orientation="vertical"
                    android:paddingStart="@dimen/space_large"
                    android:paddingTop="@dimen/space_medium"
                    android:paddingEnd="@dimen/space_large"
                    android:paddingBottom="@dimen/space_medium"
                    android:layout_below="@+id/llAppVersion">

                    <TextView
                        style="@style/Widget.TextView.Header2.Medium"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/region_text"
                        android:textColor="@color/black"
                        android:textStyle="bold" />

                    <co.styletheory.ops.outbound.android.view.AdapterRadioGroupLayout
                        android:id="@+id/containerBatchConfigRegion"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:radioGroupItems="@{viewModel.regionConfigViewModelList}"
                        android:radioGroupLayoutId="@{@layout/settings_batch_config_item}" />

                </LinearLayout>

                <View
                    android:id="@+id/view1"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_very_little"
                    android:layout_below="@+id/llregion"
                    android:background="@color/very_light_gray" />

                <LinearLayout
                    android:id="@+id/llvertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/view1"
                    android:orientation="vertical"
                    android:paddingStart="@dimen/space_large"
                    android:paddingTop="@dimen/space_medium"
                    android:paddingEnd="@dimen/space_large"
                    android:paddingBottom="@dimen/space_medium">

                    <TextView
                        style="@style/Widget.TextView.Header2.Medium"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/vertical_text"
                        android:textColor="@color/black"
                        android:textStyle="bold" />

                    <co.styletheory.ops.outbound.android.view.AdapterRadioGroupLayout
                        android:id="@+id/containerBatchConfigVertical"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:radioGroupItems="@{viewModel.verticalTypeConfigViewModelList}"
                        android:radioGroupLayoutId="@{@layout/settings_batch_config_item}" />

                </LinearLayout>

                <View
                    android:id="@+id/viewBatchDate"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_very_little"
                    android:layout_below="@+id/llvertical"
                    android:background="@color/very_light_gray"
                    android:visibility="@{viewModel.settingsPageVisibility}"/>

                <LinearLayout
                    android:id="@+id/llBatchDate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/viewBatchDate"
                    android:orientation="vertical"
                    android:paddingStart="@dimen/space_large"
                    android:paddingTop="@dimen/space_medium"
                    android:paddingEnd="@dimen/space_large"
                    android:paddingBottom="@dimen/space_medium"
                    android:visibility="@{viewModel.settingsPageVisibility}">

                    <TextView
                        style="@style/Widget.TextView.Header2.Medium"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/batch_date_text"
                        android:textColor="@color/black"
                        android:textStyle="bold" />

                    <androidx.appcompat.widget.AppCompatEditText
                        style="@style/Widget.TextView.BodyCopy1"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_size_45"
                        android:clickable="true"
                        android:drawableEnd="@drawable/ic_arrow_down_small_9b"
                        android:drawablePadding="@dimen/space_small"
                        android:drawableStart="@drawable/ic_calendar_9b"
                        android:focusable="false"
                        android:inputType="text"
                        android:maxLines="1"
                        android:onClick="@{() -> viewModel.batchDateClicked()}"
                        android:textAlignment="textStart"
                        android:textColor="@color/gray_9b"
                        app:editTextBinding="@{viewModel.batchDate}" />

                </LinearLayout>

                <View
                    android:id="@+id/view2"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_very_little"
                    android:layout_below="@+id/llBatchDate"
                    android:background="@color/very_light_gray"
                    android:visibility="@{viewModel.settingsPageVisibility}"/>

                <LinearLayout
                    android:id="@+id/llBarcodeApparel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/view2"
                    android:layout_marginTop="@dimen/space_medium"
                    android:orientation="vertical"
                    android:paddingStart="@dimen/space_large"
                    android:paddingTop="@dimen/space_medium"
                    android:paddingEnd="@dimen/space_large"
                    android:paddingBottom="@dimen/space_medium"
                    android:visibility="@{viewModel.barcodeApparelVisibility}">

                    <TextView
                        style="@style/Widget.TextView.Header2.Medium"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/settings_barcode_apparel_title"
                        android:textColor="@color/black"
                        android:textStyle="bold" />

                    <RadioGroup
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:checkedBinding="@{viewModel.barcodeApparelRadioButton}"
                        android:orientation="vertical">

                        <RadioButton
                            android:id="@+id/radioBarcodeApparelOn"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:onClick='@{() -> viewModel.onRadioButtonChecked(Barcode.APPAREL_ON.value)}'
                            android:text="@string/settings_barcode_on" />

                        <RadioButton
                            android:id="@+id/radioBarcodeApparelOff"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:onClick='@{() -> viewModel.onRadioButtonChecked(Barcode.APPAREL_OFF.value)}'
                            android:text="@string/settings_barcode_off"
                            tools:checked="true" />

                    </RadioGroup>

                </LinearLayout>

                <View
                    android:id="@+id/view3"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_very_little"
                    android:layout_below="@+id/llBarcodeApparel"
                    android:background="@color/very_light_gray" />

                <LinearLayout
                    android:id="@+id/llBarcodeBags"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/view3"
                    android:layout_marginTop="@dimen/space_medium"
                    android:layout_marginBottom="@dimen/space_medium"
                    android:orientation="vertical"
                    android:paddingStart="@dimen/space_large"
                    android:paddingTop="@dimen/space_medium"
                    android:paddingEnd="@dimen/space_large"
                    android:paddingBottom="@dimen/space_medium"
                    android:visibility="@{viewModel.barcodeBagsVisibility}">

                    <TextView
                        style="@style/Widget.TextView.Header2.Medium"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/settings_barcode_bags_title"
                        android:textColor="@color/black"
                        android:textStyle="bold" />

                    <RadioGroup
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:checkedBinding="@{viewModel.barcodeBagsRadioButton}"
                        android:orientation="vertical">

                        <RadioButton
                            android:id="@+id/radioBarcodeBagsOn"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:onClick='@{() -> viewModel.onRadioButtonChecked(Barcode.BAGS_ON.value)}'
                            android:text="@string/settings_barcode_on" />

                        <RadioButton
                            android:id="@+id/radioBarcodeBagsOff"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:onClick='@{() -> viewModel.onRadioButtonChecked(Barcode.BAGS_OFF.value)}'
                            android:text="@string/settings_barcode_off"
                            tools:checked="true" />

                    </RadioGroup>

                </LinearLayout>

                <View
                    android:id="@+id/view4"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/space_very_little"
                    android:layout_below="@+id/llBarcodeBags"
                    android:background="@color/very_light_gray" />

            </RelativeLayout>

        </ScrollView>

        <Button
            android:id="@+id/btnGenerateBatch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_margin="@dimen/space_small"
            android:background="@drawable/rect_green_border_greencaf_rad_4"
            android:foreground="?attr/selectableItemBackground"
            android:onClick="@{() -> viewModel.onBtnGenerateBatchClicked()}"
            android:padding="@dimen/space_semi_medium"
            android:text="@string/settings_button_generate_batch"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_18"
            android:visibility="@{viewModel.btnGenerateBatchVisibility}" />

        <Button
            android:id="@+id/btnApplyFilter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_margin="@dimen/space_small"
            android:background="@drawable/rect_green_border_greencaf_rad_4"
            android:foreground="?attr/selectableItemBackground"
            android:onClick="@{() -> viewModel.onBtnApplyClicked()}"
            android:padding="@dimen/space_semi_medium"
            android:text="@string/settings_button_apply_filter"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_18"
            android:visibility="@{viewModel.settingsPageVisibility}" />

    </RelativeLayout>
</layout>