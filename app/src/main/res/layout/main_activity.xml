<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context="co.styletheory.ops.outbound.android.main.view.MainActivity">

    <data>

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.main.viewModel.MainViewModelImpl" />

        <import type="android.view.View" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/AppTheme.AppBarOverlay">

            <include
                android:id="@id/toolbar_wrapper"
                layout="@layout/general_toolbar" />

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tabs"
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:clipToPadding="false"
                android:paddingLeft="6dp"
                android:paddingRight="6dp"
                app:tabIndicatorColor="@color/white"
                app:tabIndicatorHeight="1dp"
                app:tabMode="scrollable"
                app:tabPaddingEnd="14dp"
                app:tabPaddingStart="14dp"
                app:tabSelectedTextColor="@color/white"
                app:tabTextAppearance="@style/TabTextAppearance"
                app:tabTextColor="@color/white_4d" />

        </com.google.android.material.appbar.AppBarLayout>

        <co.styletheory.ops.outbound.android.general.view.HackyViewPager
            android:id="@+id/view_pager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="co.styletheory.ops.outbound.android.general.CustomScrollingViewBehavior" />

        <com.jetradarmobile.snowfall.SnowfallView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="@{viewModel.snowVisibility}"
            app:snowflakeSizeMax="@dimen/space_large"
            app:snowflakeImage="@drawable/ic_soft_snowflake"/>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>
