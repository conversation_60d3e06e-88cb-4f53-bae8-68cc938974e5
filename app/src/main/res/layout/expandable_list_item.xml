<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/itemText"
        style="@style/Widget.TextView.SubHeader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/space_small"
        android:paddingTop="@dimen/space_small"
        android:paddingBottom="@dimen/space_small"
        android:paddingEnd="?android:attr/expandableListPreferredChildIndicatorRight"
        android:ellipsize="marquee"
        android:singleLine="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>