<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.viewModel.LoadingScanBarcodeBottomSheetViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:elevation="@dimen/space_small"
        android:focusable="true"
        app:behavior_hideable="false"
        android:paddingBottom="@dimen/space_size_60"
        app:behavior_peekHeight="@dimen/no_space"
        app:layout_behavior="@string/bottom_sheet_behavior">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/left"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.4" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/right"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.6" />

        <ImageView
            android:id="@+id/imageClose"
            android:layout_width="@dimen/space_large"
            android:layout_height="@dimen/space_large"
            android:layout_margin="@dimen/space_medium"
            android:contentDescription="@string/close_label"
            android:onClick="@{() -> viewModel.onClose()}"
            android:src="@drawable/ic_close"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textHeader"
            style="@style/Widget.TextView.Header2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/space_medium"
            android:textColor="@color/black_percent_80"
            android:textBinding="@{viewModel.bottomSheetTitle}"
            app:layout_constraintStart_toEndOf="@+id/imageClose"
            app:layout_constraintTop_toTopOf="parent" />


        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="@dimen/no_space"
            android:layout_height="@dimen/no_space"
            android:layout_marginTop="@dimen/space_size_70"
            app:layout_constraintDimensionRatio="W, 1:1"
            app:layout_constraintLeft_toLeftOf="@id/left"
            app:layout_constraintRight_toRightOf="@id/right"
            app:layout_constraintTop_toBottomOf="@id/textHeader" />

        <TextView
            android:id="@+id/scanInfo"
            style="@style/Widget.TextView.SubHeader1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/space_medium"
            android:layout_marginTop="@dimen/space_size_70"
            android:layout_marginRight="@dimen/space_medium"
            android:textColor="@color/black_2a"
            android:textBinding="@{viewModel.bottomSheetContent}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/progressBar" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>