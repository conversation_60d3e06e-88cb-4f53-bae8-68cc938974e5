<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.settings.viewModel.impl.BatchConfigItemViewModel" />
    </data>

    <RadioButton
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:checked="@{viewModel.isChecked}"
        android:text="@{viewModel.name}"
        android:onClick="@{() -> viewModel.onRadioButtonClicked()}"/>

</layout>