<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android" xmlns:aapt="http://schemas.android.com/aapt"
    android:viewportWidth="24"
    android:viewportHeight="24"
    android:width="24dp"
    android:height="24dp">
    <path
        android:pathData="M15 12A3 3 0 0 1 9 12A3 3 0 0 1 15 12Z"
        android:strokeColor="#191818"
        android:strokeWidth="1" />
    <path
        android:pathData="M12 9L12 5"
        android:strokeColor="#191818"
        android:strokeWidth="1"
        android:strokeLineCap="square" />
    <group
        android:rotation="45"
        android:translateX="12.02081"
        android:translateY="0.9791845">
        <path
            android:pathData="M8.5 4.5L8.5 0.5"
            android:strokeColor="#191818"
            android:strokeWidth="1"
            android:strokeLineCap="square" />
        <path
            android:pathData="M7.085786 15.08579L7.085786 11.08579"
            android:strokeColor="#191818"
            android:strokeWidth="1"
            android:strokeLineCap="square" />
        <group
            android:rotation="90"
            android:translateX="20.17157"
            android:translateY="-6">
            <path
                android:pathData="M13.08579 9.085786L13.08579 5.085786"
                android:strokeColor="#191818"
                android:strokeWidth="1"
                android:strokeLineCap="square" />
        </group>
        <group
            android:rotation="90"
            android:translateX="11"
            android:translateY="6">
            <path
                android:pathData="M2.5 10.5L2.5 6.5"
                android:strokeColor="#191818"
                android:strokeWidth="1"
                android:strokeLineCap="square" />
        </group>
    </group>
    <path
        android:pathData="M21 12A1 1 0 0 1 19 12A1 1 0 0 1 21 12Z"
        android:strokeColor="#191818"
        android:strokeWidth="1" />
    <path
        android:pathData="M19 17A1 1 0 0 1 17 17A1 1 0 0 1 19 17Z"
        android:fillType="evenOdd"
        android:fillColor="#191818" />
    <path
        android:pathData="M19 7A1 1 0 0 1 17 7A1 1 0 0 1 19 7Z"
        android:fillType="evenOdd"
        android:fillColor="#191818" />
    <path
        android:pathData="M7 17A1 1 0 0 1 5 17A1 1 0 0 1 7 17Z"
        android:fillType="evenOdd"
        android:fillColor="#191818" />
    <path
        android:pathData="M7 7A1 1 0 0 1 5 7A1 1 0 0 1 7 7Z"
        android:fillType="evenOdd"
        android:fillColor="#191818" />
    <path
        android:pathData="M13 20A1 1 0 0 1 11 20A1 1 0 0 1 13 20Z"
        android:strokeColor="#191818"
        android:strokeWidth="1" />
    <path
        android:pathData="M13 4A1 1 0 0 1 11 4A1 1 0 0 1 13 4Z"
        android:strokeColor="#191818"
        android:strokeWidth="1" />
    <path
        android:pathData="M5 12A1 1 0 0 1 3 12A1 1 0 0 1 5 12Z"
        android:strokeColor="#191818"
        android:strokeWidth="1" />
    <path
        android:pathData="M12 19L12 15"
        android:strokeColor="#191818"
        android:strokeWidth="1"
        android:strokeLineCap="square" />
    <group
        android:rotation="90"
        android:translateX="29"
        android:translateY="-5">
        <path
            android:pathData="M17 14L17 10"
            android:strokeColor="#191818"
            android:strokeWidth="1"
            android:strokeLineCap="square" />
    </group>
    <group
        android:rotation="90"
        android:translateX="19"
        android:translateY="5">
        <path
            android:pathData="M7 14L7 10"
            android:strokeColor="#191818"
            android:strokeWidth="1"
            android:strokeLineCap="square" />
    </group>
</vector>