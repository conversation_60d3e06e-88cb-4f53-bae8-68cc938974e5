<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:drawable="@android:color/white"
        android:state_pressed="true" />
    <item
        android:drawable="@android:color/white"
        android:state_selected="true" />
    <item android:state_checked="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/gray_e5" />
        </shape>
    </item>
    <item android:top="-2dp" android:left="-2dp" android:right="-2dp">
        <shape android:shape="rectangle">
            <stroke
                android:width="1dp"
                android:color="@color/very_light_gray" />
            <solid android:color="@color/white" />
        </shape>
    </item>

    <item
        android:gravity="center_vertical|right"
        android:drawable="@drawable/ic_arrow_down"/>
</layer-list>