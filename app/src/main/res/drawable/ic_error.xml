<vector android:autoMirrored="true" android:height="200dp"
    android:viewportHeight="200" android:viewportWidth="200"
    android:width="200dp" xmlns:android="http://schemas.android.com/apk/res/android">
    <path android:fillAlpha="0.05" android:fillColor="#50E3C2"
        android:fillType="nonZero"
        android:pathData="M147.863,172.17C147.863,172.17 154.699,157.811 176.718,152.458C198.738,147.105 209.256,118.627 183.684,86.846C158.112,55.065 176.718,-18.925 88.967,20.09C88.967,20.09 74.38,27.891 47.773,47.244C11.126,69.513 -38.794,146.627 59.107,182.747C59.136,182.747 119.216,209.234 147.863,172.17Z"
        android:strokeAlpha="0.05" android:strokeColor="#00000000" android:strokeWidth="1"/>
    <group>
        <clip-path android:pathData="M46,52.889l118.669,0l0,116.387l-118.669,0z"/>
        <path android:fillColor="#2A3033" android:fillType="evenOdd"
            android:pathData="M105.338,162.651C105.912,161.687 106.319,161.08 106.648,160.436C110.012,153.866 113.405,147.312 116.691,140.705C117.649,138.779 119.003,138.002 121.172,138.016C131.147,138.084 141.122,138.06 151.096,138.052C155.394,138.048 157.752,135.736 157.752,131.553C157.754,109.779 157.754,88.004 157.752,66.229C157.752,62.034 155.32,59.673 151.007,59.673C120.554,59.671 90.101,59.671 59.648,59.673C55.322,59.673 52.918,62.017 52.917,66.237C52.915,76.756 52.915,87.276 52.915,97.795C52.915,109.137 52.913,120.479 52.919,131.82C52.921,134.739 54.418,136.981 56.977,137.699C57.976,137.98 59.066,138.038 60.116,138.04C69.914,138.064 79.712,138.09 89.509,138.028C91.615,138.016 92.982,138.73 93.922,140.61C97.242,147.249 100.647,153.847 104.03,160.453C104.359,161.095 104.767,161.697 105.338,162.651M105.43,52.89C120.749,52.89 136.067,52.888 151.385,52.891C159.12,52.892 164.665,58.315 164.667,65.884C164.67,87.879 164.67,109.876 164.667,131.872C164.665,139.392 159.179,144.833 151.513,144.859C142.242,144.891 132.972,144.882 123.702,144.845C122.729,144.841 122.207,145.12 121.775,145.985C119.04,151.463 116.254,156.918 113.468,162.372C112.916,163.453 112.367,164.549 111.687,165.554C108.324,170.534 102.294,170.514 98.96,165.506C98.191,164.352 97.577,163.094 96.943,161.858C94.233,156.562 91.525,151.263 88.862,145.942C88.455,145.131 87.973,144.843 87.048,144.846C77.777,144.88 68.507,144.893 59.236,144.858C52.312,144.833 47.338,140.69 46.129,134.013C46.014,133.38 46.006,132.722 46.006,132.075C46,109.949 45.997,87.823 46.004,65.697C46.006,58.391 51.619,52.896 59.077,52.892C74.529,52.884 89.979,52.89 105.43,52.89"
            android:strokeColor="#00000000" android:strokeWidth="1"/>
    </group>
    <path android:fillColor="#2A3033" android:fillType="evenOdd"
        android:pathData="M101.359,86.674L101.366,86.674C101.366,89.233 101.358,91.791 101.369,94.349C101.379,96.943 102.98,98.705 105.313,98.717C107.647,98.728 109.302,96.958 109.307,94.399C109.318,89.282 109.319,84.165 109.307,79.049C109.301,76.547 107.558,74.686 105.292,74.716C103.067,74.744 101.378,76.571 101.362,78.999C101.346,81.557 101.359,84.116 101.359,86.674M116.216,86.813C116.215,89.549 116.26,92.286 116.206,95.021C116.09,101.021 111.279,105.695 105.293,105.676C99.375,105.658 94.547,100.987 94.471,95.071C94.399,89.511 94.396,83.949 94.472,78.39C94.552,72.503 99.41,67.802 105.313,67.761C111.157,67.721 116.044,72.432 116.203,78.341C116.279,81.162 116.216,83.989 116.216,86.813"
        android:strokeColor="#00000000" android:strokeWidth="1"/>
    <path android:fillColor="#2A3033" android:fillType="evenOdd"
        android:pathData="M109.304,124.631C109.338,122.456 107.588,120.652 105.409,120.614C103.228,120.577 101.408,122.317 101.366,124.48C101.322,126.661 103.06,128.45 105.259,128.488C107.467,128.526 109.27,126.806 109.304,124.631M116.22,124.557C116.182,130.712 111.316,135.526 105.168,135.489C99.251,135.452 94.424,130.5 94.444,124.491C94.466,118.485 99.48,113.512 105.432,113.597C111.406,113.682 116.256,118.609 116.22,124.557"
        android:strokeColor="#00000000" android:strokeWidth="1"/>
</vector>
