<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base application theme for Android 13+ -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/color_primary</item>
        <item name="colorPrimaryDark">@color/color_primary_dark</item>
        <item name="colorAccent">@color/light_green</item>
        <item name="android:windowBackground">@color/gray_e5</item>
        <item name="android:buttonStyle">@style/Widget.Button</item>
        <item name="android:textViewStyle">@style/Widget.TextView</item>
        <item name="popupTheme">@style/AppTheme.PopupOverlay</item>
        <item name="dropDownListViewStyle">@style/spinnerDivider</item>

        <!-- Edge-to-edge configurations for Android 13+ -->
        <item name="android:statusBarColor">@color/color_primary</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar">true</item>

        <!-- Android 13+ specific settings -->
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">false</item>
    </style>

    <style name="AppTheme.NoActionBar" parent="AppTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
</resources>
