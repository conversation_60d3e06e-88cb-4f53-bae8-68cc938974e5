package co.styletheory.ops.outbound.android.feature.swap

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLResult
import co.styletheory.ops.outbound.android.BaseTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.general.event.DismissDialogEvent
import co.styletheory.ops.outbound.android.general.event.ShowToastEvent
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.Style
import co.styletheory.ops.outbound.android.model.response.CheckSwapAvailabilityResponse
import co.styletheory.ops.outbound.android.util.Next
import com.nhaarman.mockitokotlin2.*
import io.mockk.spyk
import org.junit.Assert.*
import org.junit.Test
import org.mockito.InjectMocks

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 08 March 2018.
 * Description
 *
 * <EMAIL>
 */
class SwapItemDialogViewModelTest : BaseTest() {

    @InjectMocks
    lateinit var viewModel: SwapItemDialogViewModel

    override fun setup() {
        super.setup()
        whenever(context.getString(R.string.cancel_label)).thenReturn("Cancel")
        whenever(context.getString(R.string.swap_item_label)).thenReturn("Swap Item")
        whenever(context.getString(R.string.swap_dialog_title)).thenReturn("Are you sure you want to swap this item?")
        whenever(context.getString(R.string.swap_dialog_body)).thenReturn("We will find the similar items, and swapped it for you")
        whenever(context.getString(R.string.swap_dialog_loading_title)).thenReturn("Currently swapping item")
        whenever(context.getString(R.string.swap_dialog_item_not_found)).thenReturn("Sorry, there is no item found :(")
        whenever(context.getString(R.string.swap_dialog_contact_help)).thenReturn("Please contact the customer service to help you")
    }

    @Test
    fun bindViewModel() {
        val style = mock<Style>()
        whenever(style.gallery).thenReturn(listOf("Url"))
        whenever(style.name).thenReturn("Aijek")

        val shipmentId = "12"
        val boxId = "12"
        val customerEmail = "<EMAIL>"
        val batchItem = mock<BatchItem>()
        whenever(batchItem.style).thenReturn(style)
        whenever(batchItem.labelSize).thenReturn("UK")
        whenever(batchItem.order).thenReturn("2")

        val callback = mock<Next<BatchItem>>()

        viewModel.bindViewModel(shipmentId, boxId, customerEmail, batchItem, callback)

        assertEquals("Url", viewModel.imageUrl.get())
        assertEquals("Cancel", viewModel.dialogLeftButtonText.get())
        assertEquals("Swap Item", viewModel.dialogRightButtonText.get())
        assertEquals("Are you sure you want to swap this item?", viewModel.dialogTitle.get())
        assertEquals("We will find the similar items, and swapped it for you", viewModel.dialogBody.get())
        assertEquals("Aijek", viewModel.itemName.get())
        assertEquals("UK [2]", viewModel.itemSize.get())
    }

    @Test
    fun swapItemClick_whenBatchItemNull() {
        viewModel.batchItem = null
        viewModel.swapItemClick()

        assertFalse(viewModel.showLoading.get())
        assertFalse(viewModel.hideActionButton.get())
        assertTrue(viewModel.dialogTitle.isEmpty())
        assertTrue(viewModel.dialogBody.isEmpty())
        verify(dataService, times(0)).checkSwapItemAvailability(any(), any())
    }

    @Test
    fun swapItemClick_whenBatchItemNotNull() {
        val batchItem = mock<BatchItem>()
        whenever(batchItem.labelSize).thenReturn("SS")

        viewModel.batchItem = batchItem
        viewModel.swapItemClick()

        assertTrue(viewModel.showLoading.get())
        assertTrue(viewModel.hideActionButton.get())
        assertEquals("Currently swapping item", viewModel.dialogTitle.get())
        assertEquals("", viewModel.dialogBody.get())
        verify(dataService, times(1)).checkSwapItemAvailability(any(), any())
    }

    @Test
    fun checkAvailabilityCallback_onSuccessAndResultIsNotEmpty() {
        val item = mock<BatchItem>()

        val data = mock<GraphQLData<CheckSwapAvailabilityResponse>>()
        val result = mock<GraphQLResult<CheckSwapAvailabilityResponse>>()
        whenever(data.data).thenReturn(result)
        whenever(result.result).thenReturn(CheckSwapAvailabilityResponse(listOf(item)))

        viewModel.checkAvailabilityCallback.onSuccess(data)

        assertNotNull(viewModel.newBatchItem)
    }

    @Test
    fun checkAvailabilityCallback_onSuccessAndResultIsEmpty() {
        val data = mock<GraphQLData<CheckSwapAvailabilityResponse>>()
        val result = mock<GraphQLResult<CheckSwapAvailabilityResponse>>()
        whenever(data.data).thenReturn(result)
        whenever(result.result).thenReturn(CheckSwapAvailabilityResponse(emptyList()))

        viewModel.checkAvailabilityCallback.onSuccess(data)

        assertEquals("Sorry, there is no item found :(", viewModel.dialogTitle.get())
        assertEquals("Please contact the customer service to help you", viewModel.dialogBody.get())
        assertFalse(viewModel.showLoading.get())
        assertTrue(viewModel.showCloseButton.get())
        assertNull(viewModel.newBatchItem)
        verify(dataService, times(0)).swapItem(any(), any())
    }

    @Test
    fun checkAvailabilityCallback_onError() {
        viewModel.checkAvailabilityCallback.onError("Error")

        assertTrue(viewModel.showCloseButton.get())
        assertFalse(viewModel.showLoading.get())
        verify(eventBus, times(1)).post(any<ShowToastEvent>())
    }

    @Test
    fun swapItem() {
        viewModel.swapItem()
        verify(dataService, times(1)).swapItem(any(), any())
    }

    @Test
    fun swapBoxItem() {
        viewModel = spyk(viewModel)
        viewModel.swapBoxItem()
        verify(dataService, times(1)).swapBoxItem(any(), any())
    }

    @Test
    fun swapItemCallback_onSuccessBatchItemNotNull() {
        val swapCallback = mock<Next<BatchItem>>()
        viewModel.swapCallback = swapCallback
        viewModel.newBatchItem = mock()

        viewModel.swapItemCallback.onSuccess(null)

        verify(eventBus, times(1)).post(any<DismissDialogEvent>())
        verify(swapCallback, times(1)).apply(any())
    }

    @Test
    fun swapItemCallback_onSuccessBatchItemNull() {
        val swapCallback = mock<Next<BatchItem>>()
        viewModel.swapCallback = swapCallback
        viewModel.newBatchItem = null

        viewModel.swapItemCallback.onSuccess(null)

        verify(eventBus, times(1)).post(any<DismissDialogEvent>())
        verify(swapCallback, times(0)).apply(any())
    }

    @Test
    fun swapItemCallback_onError() {
        viewModel.swapItemCallback.onError("")

        verify(eventBus, times(1)).post(any<DismissDialogEvent>())
        verify(eventBus, times(1)).post(any<ShowToastEvent>())
    }

    @Test
    fun swapBoxItemCallback_onSuccess() {
        viewModel = spy(viewModel)
        whenever(viewModel.swapItem()).then { doNothing() }
        viewModel.swapBoxItemCallback.onSuccess(null)
        verify(viewModel).swapItem()
    }

    @Test
    fun swapBoxItemCallback_onError() {
        viewModel.swapBoxItemCallback.onError("")
        verify(eventBus, times(1)).post(any<DismissDialogEvent>())
        verify(eventBus, times(1)).post(any<ShowToastEvent>())
    }

    @Test
    fun leftButtonClick() {
        viewModel.leftButtonClick()
        verify(eventBus, times(1)).post(any<DismissDialogEvent>())
    }
}