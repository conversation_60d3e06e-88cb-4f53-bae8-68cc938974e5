package co.styletheory.ops.outbound.android.feature.qcDetail

import android.view.View
import co.styletheory.ops.outbound.android.BaseTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.backlogDetail.event.BacklogItemSwapClickEvent
import co.styletheory.ops.outbound.android.feature.photoManager.event.TakePictureEvent
import co.styletheory.ops.outbound.android.feature.photoManager.viewModel.AttachImageViewModel
import co.styletheory.ops.outbound.android.feature.qcDetail.event.QcDetailUIEvent
import co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.impl.QcPickedViewModel
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.model.*
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.model.enums.ResellingInventoryType
import co.styletheory.ops.outbound.android.viewModelComponent.PhotoWithLabelViewModel
import com.nhaarman.mockitokotlin2.*
import io.mockk.*
import io.mockk.impl.annotations.RelaxedMockK
import org.amshove.kluent.shouldBeEqualTo
import org.junit.Assert.*
import org.junit.Test
import org.mockito.InjectMocks

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 20 February 2018.
 * Description
 *
 * <EMAIL>
 */
class QcPickedViewModelTest : BaseTest() {

    @InjectMocks
    lateinit var viewModel: QcPickedViewModel

    @RelaxedMockK
    lateinit var photoWithLabelVM: PhotoWithLabelViewModel

    override fun setup() {
        super.setup()
        whenever(context.getString(R.string.fail_label)).thenReturn("Fail")
    }

    @Test
    fun firstState() {
        assertNull(viewModel.refreshCompleteState)
        assertNull(viewModel.removeSwappedItem)
        assertNull(viewModel.batchItem)

        assertTrue(viewModel.batchId.isEmpty())
        assertTrue(viewModel.rackSection.isEmpty())
        assertTrue(viewModel.rackName.isEmpty())
        assertTrue(viewModel.shipmentId.isEmpty())

        assertTrue(viewModel.itemName.isEmpty())
        assertTrue(viewModel.itemSize.isEmpty())
        assertTrue(viewModel.errorMessage.isEmpty())
        assertTrue(viewModel.failCategory.isEmpty())
        assertTrue(viewModel.failReason.isEmpty())
        assertTrue(viewModel.rack.isEmpty())
        assertTrue(viewModel.category.isEmpty())
        assertTrue(viewModel.notes.isEmpty())
        assertTrue(viewModel.productOrder.isEmpty())

        assertTrue(viewModel.colorItems.isEmpty())

        assertFalse(viewModel.pass.get())
        assertFalse(viewModel.fail.get())
        assertEquals(View.GONE, viewModel.showOverlayLoading.get())
        assertTrue(viewModel.isButtonEnabled.get())

        assertTrue(viewModel.detachable.isEmpty())
        assertTrue(viewModel.rfidCode.isHtml)
        assertEquals(View.GONE, viewModel.partsVisibility.get())
        assertEquals(View.GONE, viewModel.detachableVisibility.get())
        assertEquals(View.GONE, viewModel.swapVisibility.get())
        assertEquals(View.GONE, viewModel.rfidCodeVisibility.get())
        viewModel.photoWithLabelVM = photoWithLabelVM
    }

    @Test
    fun bindViewModel() {
        val rackSection = "12"
        val rackName = "A"
        val shipmentId = "STY"
        val boxId = "12"
        val customerEmail = "<EMAIL>"

        val designer: Designer = mock()
        whenever(designer.name).thenReturn("TY")

        val color: Color = mock()
        whenever(color.colorCode).thenReturn("#FFF")

        val style: Style = mock()
        whenever(style.designer).thenReturn(designer)
        whenever(style.colors).thenReturn(listOf(color))
        whenever(style.gallery).thenReturn(emptyList())
        whenever(style.primaryCategory).thenReturn("Belt")

        val additionalProperties: ItemPropertiesType = mock()
        whenever(additionalProperties.detachable).thenReturn("yes")
        whenever(additionalProperties.detachableDetail).thenReturn("Strap")

        val batchItem: BatchItem = mock()
        whenever(batchItem.style).thenReturn(style)
        whenever(batchItem.labelSize).thenReturn("XS")
        whenever(batchItem.order).thenReturn("3")
        whenever(batchItem.parts).thenReturn(listOf("pt1", "pt2"))
        whenever(batchItem.qaFailedReason).thenReturn("Empty")
        whenever(batchItem.notes).thenReturn("notes")
        whenever(batchItem.rfidFormatted).thenReturn("<html><body>RFID CODE</body></html>")
        whenever(batchItem.additionalProperties).thenReturn(additionalProperties)
        whenever(context.getString(R.string.rack_name_regular)).thenReturn("$rackName$rackSection")

        viewModel.photoWithLabelVM = photoWithLabelVM
        val eventSlot = slot<String>()
        every { photoWithLabelVM.bindPhotoWithLabel(capture(eventSlot), any()) } just Runs

        viewModel.bindViewModel(batchItem, rackSection, rackName, shipmentId, boxId, customerEmail)

        assertNotNull(viewModel.batchItem)
        assertTrue(viewModel.rackSection.isNotEmpty())
        assertTrue(viewModel.rackName.isNotEmpty())
        assertTrue(viewModel.shipmentId.isNotEmpty())

        assertEquals("TY", viewModel.itemName.get())
        assertEquals("XS", viewModel.itemSize.get())
        assertEquals("[3]", viewModel.productOrder.get())
        assertEquals(context.getString(R.string.rack_name_regular), viewModel.rack.get())
        assertEquals("Belt", viewModel.category.get())
        assertEquals("pt1,pt2", viewModel.parts.get())
        assertEquals("Empty", viewModel.failReason.get())
        assertEquals("Fail", viewModel.failCategory.get())
        assertEquals("notes", viewModel.notes.get())
        assertEquals("Strap", viewModel.detachable.get())
        assertEquals("<html><body>RFID CODE</body></html>", viewModel.rfidCode.get())

        assertFalse(viewModel.pass.get())
        assertFalse(viewModel.fail.get())
        verify { photoWithLabelVM.bindPhotoWithLabel(any(), any()) }
    }

    @Test
    fun bindViewModel_WithBatchStyle() {
        val rackSection = "12"
        val rackName = "A"
        val shipmentId = "STY"
        val boxId = "12"
        val customerEmail = "<EMAIL>"

        val designer: Designer = mock()
        whenever(designer.name).thenReturn("TY")

        val color: Color = mock()
        whenever(color.colorCode).thenReturn("#FFF")

        val style: Style = mock()
        whenever(style.designer).thenReturn(designer)
        whenever(style.colors).thenReturn(listOf(color))
        whenever(style.gallery).thenReturn(listOf("Url"))
        whenever(style.primaryCategory).thenReturn("Belt")

        val additionalProperties: ItemPropertiesType = mock()
        whenever(additionalProperties.detachable).thenReturn("yes")
        whenever(additionalProperties.detachableDetail).thenReturn("Strap")

        val batchItem: BatchItem = mock()
        whenever(batchItem.style).thenReturn(style)
        whenever(batchItem.labelSize).thenReturn("XS")
        whenever(batchItem.order).thenReturn("3")
        whenever(batchItem.parts).thenReturn(listOf("pt1", "pt2"))
        whenever(batchItem.qaFailedReason).thenReturn("Empty")
        whenever(batchItem.notes).thenReturn("notes")
        whenever(batchItem.rfidFormatted).thenReturn("<html><body>RFID CODE</body></html>")
        whenever(batchItem.additionalProperties).thenReturn(additionalProperties)
        whenever(context.getString(R.string.rack_name_regular)).thenReturn("$rackName$rackSection")

        val eventSlot = slot<String>()
        every {
            viewModel.photoWithLabelVM = photoWithLabelVM
            photoWithLabelVM.bindPhotoWithLabel(capture(eventSlot), any())
        } just Runs

        viewModel.bindViewModel(batchItem, rackSection, rackName, shipmentId, boxId, customerEmail)

        assertNotNull(viewModel.batchItem)
        assertTrue(viewModel.rackSection.isNotEmpty())
        assertTrue(viewModel.rackName.isNotEmpty())
        assertTrue(viewModel.shipmentId.isNotEmpty())

        assertEquals("TY", viewModel.itemName.get())
        assertEquals("XS", viewModel.itemSize.get())
        assertEquals("[3]", viewModel.productOrder.get())
        assertEquals(context.getString(R.string.rack_name_regular), viewModel.rack.get())
        assertEquals("Belt", viewModel.category.get())
        assertEquals("pt1,pt2", viewModel.parts.get())
        assertEquals("Empty", viewModel.failReason.get())
        assertEquals("Fail", viewModel.failCategory.get())
        assertEquals("notes", viewModel.notes.get())
        assertEquals("Strap", viewModel.detachable.get())
        assertEquals("<html><body>RFID CODE</body></html>", viewModel.rfidCode.get())

        assertFalse(viewModel.pass.get())
        assertFalse(viewModel.fail.get())

        verify { photoWithLabelVM.bindPhotoWithLabel(any(), any()) }
    }

    @Test
    fun bindViewModel_WithDifferentValue() {
        val rackSection = "12"
        val rackName = "A"
        val shipmentId = "STY"
        val boxId = "12"
        val customerEmail = "<EMAIL>"

        val designer: Designer = mock()
        whenever(designer.name).thenReturn("TY")

        val color: Color = mock()
        whenever(color.colorCode).thenReturn("#FFF")

        val style: Style = mock()
        whenever(style.designer).thenReturn(designer)
        whenever(style.colors).thenReturn(listOf(color))
        whenever(style.gallery).thenReturn(emptyList())
        whenever(style.primaryCategory).thenReturn("Belt")

        val additionalProperties: ItemPropertiesType = mock()
        whenever(additionalProperties.detachable).thenReturn("yes")
        whenever(additionalProperties.detachableDetail).thenReturn("Strap")

        val batchItem: BatchItem = mock()
        whenever(batchItem.style).thenReturn(style)
        whenever(batchItem.labelSize).thenReturn("XS")
        whenever(batchItem.label).thenReturn("Label")
        whenever(batchItem.order).thenReturn("3")
        whenever(batchItem.parts).thenReturn(listOf("pt1", "pt2"))
        whenever(batchItem.qaFailedReason).thenReturn("")
        whenever(batchItem.outboundQaImages).thenReturn(listOf("Image"))
        whenever(batchItem.notes).thenReturn("notes")
        whenever(batchItem.rfidFormatted).thenReturn("<html><body>RFID CODE</body></html>")
        whenever(batchItem.additionalProperties).thenReturn(additionalProperties)
        whenever(context.getString(R.string.rack_name_regular)).thenReturn("$rackName$rackSection")

        viewModel.photoWithLabelVM = photoWithLabelVM
        val eventSlot = slot<String>()
        every { photoWithLabelVM.bindPhotoWithLabel(capture(eventSlot), any()) } just Runs

        viewModel.bindViewModel(batchItem, rackSection, rackName, shipmentId, boxId, customerEmail)

        assertNotNull(viewModel.batchItem)
        assertTrue(viewModel.rackSection.isNotEmpty())
        assertTrue(viewModel.rackName.isNotEmpty())
        assertTrue(viewModel.shipmentId.isNotEmpty())
        assertEquals("TY", viewModel.itemName.get())
        assertEquals("XS", viewModel.itemSize.get())
        assertEquals("[3]", viewModel.productOrder.get())
        assertEquals(context.getString(R.string.rack_name_regular), viewModel.rack.get())
        assertEquals("Belt", viewModel.category.get())
        assertEquals("pt1,pt2", viewModel.parts.get())
        assertEquals("", viewModel.failReason.get())
        assertEquals("Fail", viewModel.failCategory.get())
        assertEquals("notes", viewModel.notes.get())
        assertEquals("Strap", viewModel.detachable.get())
        assertEquals("<html><body>RFID CODE</body></html>", viewModel.rfidCode.get())

        assertFalse(viewModel.pass.get())
        assertFalse(viewModel.fail.get())
        verify { photoWithLabelVM.bindPhotoWithLabel(any(), any()) }
    }

    @Test
    fun checkShowApparelItems() {
        viewModel.showApparelItems()
        assertEquals(View.VISIBLE, viewModel.partsVisibility.get())
    }

    @Test
    fun checkShowBagsItems() {
        viewModel.showBagsItems()
        assertEquals(View.VISIBLE, viewModel.detachableVisibility.get())
    }

    @Test
    fun checkShowRfidCode_isBarcodeSettingIsOn() {
        whenever(featureFlagUtil.isBarcodeSettingIsOn()).thenReturn(true)
        viewModel.showRfidCode()
        assertFalse(viewModel.isButtonEnabled.get())
        assertEquals(View.VISIBLE, viewModel.scannerIconVisibility.get())
        assertEquals(View.VISIBLE, viewModel.rfidCodeVisibility.get())
    }

    @Test
    fun checkShowRfidCode_isBarcodeSettingIsOff() {
        whenever(featureFlagUtil.isBarcodeSettingIsOn()).thenReturn(false)
        viewModel.showRfidCode()
        assertTrue(viewModel.isButtonEnabled.get())
        assertEquals(View.GONE, viewModel.scannerIconVisibility.get())
        assertEquals(View.VISIBLE, viewModel.rfidCodeVisibility.get())
    }

    @Test
    fun checkShowSwapButton() {
        viewModel.showSwapButton()
        assertEquals(View.VISIBLE, viewModel.swapVisibility.get())
    }

    @Test
    fun addColorItem() {
        val color: Color = mock()
        whenever(color.name).thenReturn("Red")
        whenever(color.colorCode).thenReturn("#000000")

        assertTrue(viewModel.colorItems.isEmpty())
        viewModel.addColorItem(color)
        assertEquals(1, viewModel.colorItems.size)
    }

    @Test
    fun addNotes() {
        viewModel.addNotes("Swing", "Test")
        assertEquals("Swing", viewModel.failCategory.get())
        assertEquals("Test", viewModel.failReason.get())
        assertTrue(viewModel.fail.get())
        assertFalse(viewModel.pass.get())
    }

    @Test
    fun updateBatchSuccess() {
        viewModel.updateBatchCallback.onSuccess(null)

        assertEquals(View.GONE, viewModel.showOverlayLoading.get())
        assertTrue(viewModel.errorMessage.isEmpty())
        assertFalse(viewModel.fail.get())
        assertTrue(viewModel.pass.get())
    }

    @Test
    fun updateBatchError() {
        val message = "Error"
        whenever(context.getString(R.string.err_failed_to_update_status)).thenReturn("Failed to Update Status")
        viewModel.updateBatchCallback.onError(message)

        assertEquals(View.GONE, viewModel.showOverlayLoading.get())
        assertFalse(!viewModel.errorMessage.isEmpty())
        assertFalse(viewModel.fail.get())
        assertFalse(viewModel.pass.get())
    }

    @Test
    fun setQAPickedState() {
        //When status Equals QA_PASSED
        viewModel.setQAPickedState(ProductStatus.QA_PASSED)
        assertTrue(viewModel.pass.get())
        assertFalse(viewModel.fail.get())

        //When status Equals QA_FAILED
        viewModel.setQAPickedState(ProductStatus.QA_FAILED)
        assertFalse(viewModel.pass.get())
        assertTrue(viewModel.fail.get())

        //if user SG/HK and PhotoQADone
        whenever(userStorage.isUserRegionID()).thenReturn(false)
        viewModel.setQAPickedState(ProductStatus.PHOTO_QA_DONE)
        assertTrue(viewModel.pass.get())
        assertFalse(viewModel.fail.get())

        //if user ID and PhotoQADone
        whenever(userStorage.isUserRegionID()).thenReturn(true)
        viewModel.setQAPickedState(ProductStatus.PHOTO_QA_DONE)
        assertFalse(viewModel.pass.get())
        assertFalse(viewModel.fail.get())

        //else
        viewModel.setQAPickedState(ProductStatus.PICKED)
        assertFalse(viewModel.pass.get())
        assertFalse(viewModel.fail.get())
    }

    @Test
    fun passClick_apparel_passTrue() {
        viewModel.pass.set(true)
        viewModel.passClick()
        assertEquals(View.GONE, viewModel.showOverlayLoading.get())
        verify(dataService, times(0)).updateBatchItemStatus(any(), any())
    }

    @Test
    fun passClick_apparel_passFalse() {
        whenever(userStorage.isVerticalTypeApparel()).thenReturn(true)
        viewModel.pass.set(false)
        viewModel.passClick()
        assertEquals(View.VISIBLE, viewModel.showOverlayLoading.get())
        verify(dataService, times(1)).updateBatchItemStatus(any(), any())
    }

    @Test
    fun passClick_SG_region_passFalse() {
        whenever(userStorage.isUserRegionID()).thenReturn(false)
        viewModel.pass.set(false)
        viewModel.passClick()
        assertEquals(View.VISIBLE, viewModel.showOverlayLoading.get())
        verify(dataService, times(1)).updateBatchItemStatus(any(), any())
    }

    @Test
    fun passClick_HK_region_passFalse() {
        whenever(userStorage.isUserRegionID()).thenReturn(false)
        viewModel.pass.set(false)
        viewModel.passClick()
        assertEquals(View.VISIBLE, viewModel.showOverlayLoading.get())
        verify(dataService, times(1)).updateBatchItemStatus(any(), any())
    }

    @Test
    fun passClick_bags_passTrue() {
        viewModel.pass.set(true)
        viewModel.passClick()
        assertEquals(View.GONE, viewModel.showOverlayLoading.get())
        verify(dataService, times(0)).updateBatchItemStatus(any(), any())
    }

    @Test
    fun passClick_bags_passFalse() {
        whenever(userStorage.isVerticalTypeBags()).thenReturn(true)
        viewModel.pass.set(false)
        viewModel.passClick()
        assertEquals(View.VISIBLE, viewModel.showOverlayLoading.get())
        verify(dataService, times(1)).updateBatchItemStatus(any(), any())
    }

    @Test
    fun failReasonClick() {
        // when fail equals false should not trigger post event
        viewModel.fail.set(true)
        viewModel.failReasonClick(View(context))
        verify(eventBus, times(0)).post(any<QcDetailUIEvent.FailReasonButtonClick>())

        // when fail equals true should  trigger post event
        viewModel.fail.set(false)
        viewModel.failReasonClick(View(context))
        verify(eventBus, times(1)).post(any<QcDetailUIEvent.FailReasonButtonClick>())
    }

    @Test
    fun swapClick() {
        viewModel.swapClick()
        verify(eventBus, times(1)).post(any<BacklogItemSwapClickEvent>())
    }

    @Test
    fun cameraClick() {
        viewModel.cameraClick()
        verify(eventBus, times(1)).post(any<TakePictureEvent>())
    }

    @Test
    fun setButtonEnabled() {
        viewModel.setupEnableButton(true)
        assertTrue(viewModel.isButtonEnabled.get())
    }

    @Test
    fun setButtonDisabled() {
        viewModel.setupEnableButton(false)
        assertFalse(viewModel.isButtonEnabled.get())
    }

    @Test
    fun setupRackName_isUserOnDemand() {
        whenever(userStorage.isUserOnDemandAndRegionID()).thenReturn(true)
        whenever(context.getString(R.string.rack_name_on_demand)).thenReturn("Rack - A1")
        viewModel.setupRackName("Rack", "A1")
        assertEquals(context.getString(R.string.rack_name_on_demand), viewModel.rack.get())
    }

    @Test
    fun setupRackName_isNotUserOnDemand() {
        whenever(userStorage.isUserOnDemandAndRegionID()).thenReturn(false)
        whenever(context.getString(R.string.rack_name_regular)).thenReturn("RackA1")
        viewModel.setupRackName("Rack", "A1")
        assertEquals(context.getString(R.string.rack_name_regular), viewModel.rack.get())
    }

    @Test
    fun setupAccuracySwapButton_shouldVisible() {
        whenever(userStorage.isVerticalTypeApparel()).thenReturn(true)
        whenever(featureFlagUtil.isAccuracySwapIsOn()).thenReturn(true)
        viewModel.setupAccuracySwapButton()
        assertTrue(viewModel.isAccuracySwapEnabled.get())
        assertEquals(View.VISIBLE, viewModel.accuracySwapButtonVisibility.get())
    }

    @Test
    fun setupAccuracySwapButton_shouldHide() {
        whenever(userStorage.isVerticalTypeApparel()).thenReturn(false)
        whenever(featureFlagUtil.isAccuracySwapIsOn()).thenReturn(true)
        viewModel.setupAccuracySwapButton()
        assertFalse(viewModel.isAccuracySwapEnabled.get())
        assertEquals(View.GONE, viewModel.accuracySwapButtonVisibility.get())
    }

    @Test
    fun setupAccuracySwapButton_featureFlagIsOn_shouldVisible() {
        whenever(userStorage.isVerticalTypeApparel()).thenReturn(true)
        whenever(featureFlagUtil.isAccuracySwapIsOn()).thenReturn(true)
        viewModel.setupAccuracySwapButton()
        assertTrue(viewModel.isAccuracySwapEnabled.get())
        assertEquals(View.VISIBLE, viewModel.accuracySwapButtonVisibility.get())
    }

    @Test
    fun setupAccuracySwapButton_featureFlagIsOff_shouldHide() {
        whenever(userStorage.isVerticalTypeApparel()).thenReturn(true)
        whenever(featureFlagUtil.isAccuracySwapIsOn()).thenReturn(false)
        viewModel.setupAccuracySwapButton()
        assertFalse(viewModel.isAccuracySwapEnabled.get())
        assertEquals(View.GONE, viewModel.accuracySwapButtonVisibility.get())
    }

    @Test
    fun setupAccuracySwapButton_isResellingItem_shouldHide() {
        val batchItem: BatchItem = mock()
        whenever(batchItem.isResellingItem()).thenReturn(false)
        viewModel.batchItem = batchItem

        viewModel.setupAccuracySwapButton()
        assertFalse(viewModel.isAccuracySwapEnabled.get())
        assertEquals(View.GONE, viewModel.accuracySwapButtonVisibility.get())
    }

    @Test
    fun setupOldSwapVisibility_featureFlagIsOn_shouldVisible() {
        whenever(featureFlagUtil.isOldQualityCheckSwapOn()).thenReturn(true)
        viewModel.setupOldSwapVisibility()
        assertEquals(View.VISIBLE, viewModel.oldSwapSectionVisibility.get())
    }

    @Test
    fun setupOldSwapVisibility_featureFlagIsOff_shouldHide() {
        whenever(featureFlagUtil.isOldQualityCheckSwapOn()).thenReturn(false)
        viewModel.setupOldSwapVisibility()
        assertEquals(View.GONE, viewModel.oldSwapSectionVisibility.get())
    }

    @Test
    fun setupResellingSwapVisibility_isNotResellingItem_shouldVisible() {
        val batchItem: BatchItem = mock()
        whenever(batchItem.isResellingItem()).thenReturn(false)
        whenever(featureFlagUtil.isOldQualityCheckSwapOn()).thenReturn(true)
        whenever(featureFlagUtil.isQualitySwapResellingDisabled(batchItem)).thenReturn(false)
        viewModel.batchItem = MockProvider.batchItem().copy(resellingInventoryType = ResellingInventoryType.NORMAL.type)
        viewModel.setupResellingSwapVisibility(batchItem)
        assertEquals(View.VISIBLE, viewModel.swapVisibility.get())
    }

    @Test
    fun setupResellingSwapVisibility_isResellingItem_shouldHide() {
        val batchItem: BatchItem = mock()
        whenever(batchItem.isResellingItem()).thenReturn(true)
        whenever(featureFlagUtil.isQualitySwapResellingDisabled(batchItem)).thenReturn(true)
        viewModel.batchItem = MockProvider.batchItem().copy(resellingInventoryType = ResellingInventoryType.NORMAL.type)
        viewModel.setupResellingSwapVisibility(batchItem)
        assertEquals(View.GONE, viewModel.swapVisibility.get())
    }

    @Test
    fun setupResellingSwapVisibility_isWarehouseSelling_shouldHide() {
        val batchItem: BatchItem = mock()
        whenever(batchItem.isResellingItem()).thenReturn(true)
        whenever(featureFlagUtil.isQualitySwapResellingDisabled(batchItem)).thenReturn(true)
        viewModel.batchItem = MockProvider.batchItem()
            .copy(resellingInventoryType = ResellingInventoryType.WAREHOUSE.type)
        viewModel.setupResellingSwapVisibility(batchItem)
        assertEquals(View.GONE, viewModel.swapVisibility.get())
    }

    @Test
    fun clickAccuracySwap() {
        viewModel.accuracySwapClick()
        verify(eventBus, times(1)).post(any<QcDetailUIEvent.OnAccuracySwapButtonClicked>())
    }

    @Test
    fun setAccuracySwapButtonEnabled() {
        viewModel.setupEnableAccuracySwapButton(true)
        assertTrue(viewModel.isAccuracySwapEnabled.get())
    }

    @Test
    fun setAccuracySwapButtonDisabled() {
        viewModel.setupEnableAccuracySwapButton(false)
        assertFalse(viewModel.isAccuracySwapEnabled.get())
    }

    @Test
    fun checkUpdateView() {
        viewModel.soldTo.set("")
        viewModel.updateView()
        viewModel.soldToVisibility.get() shouldBeEqualTo View.GONE

        viewModel.soldTo.set("Sold To Yoga Ganteng")
        viewModel.updateView()
        viewModel.soldToVisibility.get() shouldBeEqualTo View.VISIBLE

        viewModel.qaItemImages.clear()
        viewModel.updateView()
        viewModel.attachementVisibility.get() shouldBeEqualTo View.VISIBLE

        val photo = mockk<AttachImageViewModel>()
        mockkConstructor(photo::class)
        every { photo.imageUrl } returns mockk()
        every { anyConstructed<AttachImageViewModel>().imageUrl.set(any()) } returns mockk()
        viewModel.qaItemImages.add(photo)
        viewModel.updateView()
        viewModel.attachementVisibility.get() shouldBeEqualTo View.GONE

        viewModel.failReason.set("")
        viewModel.updateView()
        viewModel.failReasonVisibility.get() shouldBeEqualTo View.GONE

        viewModel.failReason.set("Item is failed")
        viewModel.updateView()
        viewModel.failReasonVisibility.get() shouldBeEqualTo View.VISIBLE
    }

    @Test
    fun setPhotoWithLabelViewModel() {
        viewModel.photoWithLabelVM = photoWithLabelVM
        viewModel.setPhotoWithLabelVM() shouldBeEqualTo viewModel.photoWithLabelVM
    }

}