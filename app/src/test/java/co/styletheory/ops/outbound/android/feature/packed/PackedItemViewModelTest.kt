package co.styletheory.ops.outbound.android.feature.packed

import android.view.View
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.feature.packed.viewModel.impl.PackedItemViewModel
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.general.event.CallEvent
import co.styletheory.ops.outbound.android.model.Customer
import co.styletheory.ops.outbound.android.model.Shipment
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.amshove.kluent.*
import org.junit.Test

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 20 February 2018.
 * Description
 *
 * <EMAIL>
 */

class PackedItemViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: PackedItemViewModel

    @Test
    fun firstState() {
        viewModel.isSelected.get().shouldBeFalse()
        viewModel.packageName.get().shouldBeEmpty()
        viewModel.packageAuthor.get().shouldBeEmpty()
        viewModel.labelItems.shouldBeEmpty()
        viewModel.onItemCheckedCallback.shouldBeNull()
    }

    @Test
    fun bindViewModel_showTrackingInfo_withCourierName() {
        viewModel = spyk(viewModel)
        val shipment = MockProvider.shipment()
            .copy(courier = Shipment.Courier("gosend"), customer = Customer(isVip = true))
        viewModel.showTrackingInfo = true

        viewModel.bindViewModel(shipment)

        viewModel.apply {
            shipment shouldBeEqualTo shipment
            packageName.get() shouldBeEqualTo shipment.tracking?.id
            packageAuthor.get() shouldBeEqualTo shipment.customer?.name
            labelItems.isEmpty() shouldBeEqualTo false
            trackingVisibility.get() shouldBeEqualTo View.VISIBLE

            courierInfoVisibility.get() shouldBeEqualTo View.VISIBLE
            trackingStatusVisibility.get() shouldBeEqualTo View.GONE
            courierName.get() shouldBeEqualTo shipment.courier?.name
            courierPhone.get() shouldBeEqualTo shipment.courier?.phone
            courierImage.get() shouldBeEqualTo shipment.courier?.imageUrl

            verify { goSendCheckboxHandle() }
        }
    }

    @Test
    fun bindViewModel_notShowTrackingInfo_withoutCourierName() {
        viewModel = spyk(viewModel)
        val timeMock = mockk<Shipment.Time>()
        every { timeMock.getScheduledPickupFormattedText() } returns "test"
        val shipment = MockProvider.shipment().copy(shipmentTime = timeMock)
        viewModel.showTrackingInfo = false

        viewModel.bindViewModel(shipment)

        viewModel.apply {
            shipment shouldBeEqualTo shipment
            packageName.get() shouldBeEqualTo shipment.tracking?.id
            packageAuthor.get() shouldBeEqualTo shipment.customer?.name
            pickupAt.get() shouldBeEqualTo "test"
            labelItems.isEmpty() shouldBeEqualTo true
            trackingVisibility.get() shouldBeEqualTo View.GONE

            courierInfoVisibility.get() shouldBeEqualTo View.GONE
            trackingStatusVisibility.get() shouldBeEqualTo View.VISIBLE
            trackingStatus.get() shouldBeEqualTo shipment.tracking?.status

            verify { viewModel.goSendCheckboxHandle() }
        }

        viewModel.showTrackingInfo = null

        viewModel.bindViewModel(shipment)

        viewModel.trackingVisibility.get() shouldBeEqualTo View.GONE
    }

    @Test
    fun onCheckButtonClick() {
        viewModel.isSelected.get().shouldBeFalse()
        viewModel.onCheckButtonClicked()
        viewModel.isSelected.get().shouldBeTrue()
    }

    @Test
    fun onPhoneButtonClicked() {
        viewModel.onPhoneButtonClicked()
        verify(exactly = 1) { eventBus.post(any<CallEvent>()) }
    }

    @Test
    fun hideCheckBox() {
        viewModel.hideCheckbox()
        viewModel.checkedBoxVisibility.get() shouldBeEqualTo View.GONE
    }

    @Test
    fun goSendCheckboxHandle_showTracking() {
        viewModel.showTrackingInfo = true

        viewModel.goSendCheckboxHandle()

        viewModel.checkedBoxVisibility.get() shouldBeEqualTo View.GONE
    }

    @Test
    fun goSendCheckboxHandle_notShowTracking() {
        viewModel.showTrackingInfo = false

        viewModel.goSendCheckboxHandle()

        viewModel.checkedBoxVisibility.get() shouldBeEqualTo View.VISIBLE
    }

    @Test
    fun goSendCheckboxHandle_trackingNull() {
        viewModel.showTrackingInfo = null

        viewModel.goSendCheckboxHandle()

        viewModel.checkedBoxVisibility.get() shouldBeEqualTo View.GONE
    }

    @Test
    fun goSendCheckboxHandle_null() {
        viewModel.shipment = null
        viewModel.goSendCheckboxHandle()
        viewModel.checkedBoxVisibility.get() shouldBeEqualTo View.GONE

        viewModel.shipment = Shipment(tracking = null)
        viewModel.goSendCheckboxHandle()
        viewModel.checkedBoxVisibility.get() shouldBeEqualTo View.GONE
    }

    @Test
    fun onCheckButtonClicked() {
        viewModel = spyk(viewModel)
        viewModel.isSelected.set(false)
        viewModel.onCheckButtonClicked()
        viewModel.isSelected.get().shouldBeTrue()
    }
}