package co.styletheory.ops.outbound.android.feature.qcDetail

import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.impl.FailNotesDialogViewModel
import co.styletheory.ops.outbound.android.general.event.DismissDialogEvent
import co.styletheory.ops.outbound.android.general.event.DismissProgressDialogEvent
import co.styletheory.ops.outbound.android.general.event.ShowProgressDialogEvent
import co.styletheory.ops.outbound.android.resources.BatchItemResource
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import org.amshove.kluent.shouldBeEqualTo
import org.junit.Test

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 07 March 2018.
 * Description
 *
 * <EMAIL>
 */

class FailNotesDialogViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: FailNotesDialogViewModel

    @Test
    fun cancelClick() {
        viewModel.cancelClick()
        verify { eventBus.post(any<DismissDialogEvent>()) }
    }

    @Test
    fun addNotesClick() {
        viewModel.addNotesClick()
        verify {
            eventBus.post(any<ShowProgressDialogEvent>())
            dataService.updateBatchItemStatus(any(), any())
        }
    }

    @Test
    fun addNotesClick_notEmpty() {
        val resourceSlot = slot<BatchItemResource>()
        viewModel.batchId = "id"
        viewModel.shipmentId = "shipment id"
        val batchItemIds = listOf("id")
        viewModel.batchItemId = "id"
        every { dataService.updateBatchItemStatus(capture(resourceSlot), any()) } just Runs
        viewModel.addNotesClick()
        verify {
            eventBus.post(any<ShowProgressDialogEvent>())
            dataService.updateBatchItemStatus(any(), any())
            resourceSlot.captured.batchId shouldBeEqualTo "id"
            resourceSlot.captured.shipmentId shouldBeEqualTo "shipment id"
            resourceSlot.captured.itemIds shouldBeEqualTo batchItemIds
        }
    }

    @Test
    fun updateBatchItemCallback_whenSuccess() {
        viewModel.addNotesResult = mockk()
        every { viewModel.addNotesResult?.success(any()) } returns mockk()
        viewModel.updateStatusCallback.onSuccess(null)

        verify(exactly = 1) { viewModel.addNotesResult?.success(any()) }
        verify {
            eventBus.post(any<DismissDialogEvent>())
            eventBus.post(any<DismissProgressDialogEvent>())
        }
    }

    @Test
    fun updateBatchItemCallback_whenError() {
        viewModel.addNotesResult = mockk()
        every { viewModel.addNotesResult?.failure(any()) } returns mockk()
        viewModel.updateStatusCallback.onError("Error")

        verify(exactly = 1) { viewModel.addNotesResult?.failure(any()) }
        verify {
            eventBus.post(any<DismissDialogEvent>())
            eventBus.post(any<DismissProgressDialogEvent>())
        }
    }

}