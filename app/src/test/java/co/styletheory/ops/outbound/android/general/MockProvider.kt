package co.styletheory.ops.outbound.android.general

import co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel.impl.BacklogDetailItemViewModelImpl
import co.styletheory.ops.outbound.android.feature.batch.viewModel.BatchListViewModel
import co.styletheory.ops.outbound.android.feature.settings.viewModel.impl.BatchConfigItemViewModel
import co.styletheory.ops.outbound.android.model.*
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.model.response.ShipmentResponse
import org.joda.time.DateTime
import java.util.*

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 18 June 2018.
 * Description
 *
 * <EMAIL>
 */
object MockProvider {

    fun batch(batchItem: BatchItem = batchItem()): Batch {
        return Batch(
                "12",
                "Batch A",
                "13",
                listOf(batchItem()),
                "<PERSON>",
                "<EMAIL>",
                listOf(),
                DateTime.now(),
                DateTime.now(),
                "<PERSON>",
                "<EMAIL>",
                listOf(),
                DateTime.now(),
                DateTime.now(),
                "John",
                "<EMAIL>",
                listOf(),
                DateTime.now(),
                DateTime.now(),
                "John",
                "<EMAIL>",
                listOf(),
                DateTime.now(),
                DateTime.now(),
                listOf("1", "2"),
                false,
                false,
                false,
                false,
                DateTime.now(),
                listOf(shipment(listOf(batchItem))),
                listOf(batchItem),
                listOf(logisticProvider()),
                rack()
        )
    }

    fun batchEmptyShipment(batchItem: BatchItem = batchItem()): Batch {
        return Batch(
                "12",
                "Batch A",
                "13",
                listOf(batchItem()),
                "John",
                "<EMAIL>",
                listOf(),
                DateTime.now(),
                DateTime.now(),
                "John",
                "<EMAIL>",
                listOf(),
                DateTime.now(),
                DateTime.now(),
                "John",
                "<EMAIL>",
                listOf(),
                DateTime.now(),
                DateTime.now(),
                "John",
                "<EMAIL>",
                listOf(),
                DateTime.now(),
                DateTime.now(),
                listOf("1", "2"),
                false,
                false,
                false,
                false,
                DateTime.now(),
                listOf(),
                listOf(batchItem),
                listOf(logisticProvider()),
                rack()
        )
    }

    fun logisticProvider(): LogisticProvider {
        return LogisticProvider("12", "SingPost", providerLogo(), "sg")
    }

    fun providerLogo(): LogisticProvider.ProviderLogo {
        return LogisticProvider.ProviderLogo("image1", 200, 200)
    }

    fun batchItem(status: ProductStatus = ProductStatus.AVAILABLE, rfid: String? = getScannedRfidTag()): BatchItem {
        return BatchItem(
                "12",
                "Vip",
                "XS",
                "Euro",
                status,
                "9",
                "Stain",
                "QaFailedReason",
                "notes",
                listOf("image1", "image2"),
                listOf("image1", "image2"),
                listOf("Belt"),
                rfid,
                "123456",
                12,
                12,
                0,
                0,
                style(),
                ItemPropertiesType("detachable", "detachable detail"),
                rackSlotLocation = BatchItem.RackSlotLocation(
                    row = BatchItem.RackSlotLocation.Row(
                        name = "Row 1",
                        sortOrder = 1
                    ),
                    rack = BatchItem.RackSlotLocation.Rack(
                        name = "Rack 1",
                        sortOrder = 1
                    )
                )
        )
    }

    fun batchItemStyleColor(status: ProductStatus = ProductStatus.AVAILABLE, rfid: String? = getScannedRfidTag()): BatchItem {
        return BatchItem(
                "12",
                "Vip",
                "XS",
                "Euro",
                status,
                "9",
                "Stain",
                "QaFailedReason",
                "notes",
                listOf("image1", "image2"),
                listOf("image1", "image2"),
                listOf("Belt"),
                rfid,
                "123456",
                12,
                12,
                0,
                0,
                styleColor(),
                ItemPropertiesType("detachable", "detachable detail")
        )
    }

    fun style(): Style {
        return Style(
                "123",
                "StyleName",
                "dress",
                listOfGalleries(),
                designer(),
                listOf()
        )
    }

    fun styleColor(): Style {
        return Style(
                "123",
                "StyleName",
                "dress",
                listOfGalleries(),
                designer(),
                listOf(color())
        )
    }

    fun color(): Color {
        return Color(
                "123",
                "Red",
                "#000000"
        )
    }

    fun designer(): Designer {
        return Designer(
                "1233",
                "Apri"
        )
    }

    fun shipment(batchList: List<BatchItem> = listOf(batchItem())): Shipment {
        return Shipment(
                "12",
                "12",
                "Delivery",
                "Normal",
                "transit",
                shipmentTracking(),
                shipmentLogisticProvider(),
                null,
                null,
                DateTime.now(),
                null,
                "logisticMethod",
                false,
                null,
                "sg",
                listOf("1", "2"),
                "Subscription",
                "Regular",
                false,
                false,
                batchList,
                customer(),
                "<NAME_EMAIL>",
                shipmentBox()
        )
    }

    fun shipmentEmptyTrackingId(batchList: List<BatchItem> = listOf(batchItem())): Shipment {
        return Shipment(
                "12",
                "12",
                "Delivery",
                "Normal",
                "transit",
                Shipment.Tracking(),
                LogisticProvider(),
                null,
                null,
                DateTime.now(),
                null,
                "logisticMethod",
                false,
                null,
                "sg",
                listOf("1", "2"),
                "Subscription",
                "Regular",
                false,
                false,
                batchList,
                customer(),
                "<NAME_EMAIL>",
                Box()
        )
    }

    fun shipmentComplete(batchList: List<BatchItem> = listOf(batchItem())): Shipment {
        return Shipment(
                "12",
                "12",
                "Delivery",
                "Normal",
                "transit",
                shipmentTracking(),
                shipmentLogisticProvider(),
                null,
                null,
                DateTime.now(),
                null,
                "logisticMethod",
                false,
                null,
                "sg",
                listOf("1", "2"),
                "Subscription",
                "Regular",
                false,
                true,
                batchList,
                customer(),
                "<NAME_EMAIL>",
                shipmentBox()
        )
    }

    fun shipmentMeta(): ShipmentResponse.Meta {
        return ShipmentResponse.Meta(2, listOf("12", "13"))
    }

    fun shipmentTracking(): Shipment.Tracking {
        return Shipment.Tracking("12", "Delivered")
    }

    fun shipmentLogisticProvider(): LogisticProvider {
        return LogisticProvider("1", "WOW")
    }

    fun shipmentBox(): Box {
        return Box("1", "status", false, listOf(boxReward()))
    }

    fun boxReward(): BoxReward {
        return BoxReward("LANEIGE Water Bank Hydro Kit", "description reward item")
    }

    fun customer(): Customer {
        return Customer(
                "12",
                "John",
                "<EMAIL>",
                false,
                false,
                false,
                null
        )
    }

    fun rack(): Rack {
        return Rack("12", "A", "sg")
    }

    fun user(): User {
        return User("John", "<EMAIL>", "+*********", listOf())
    }

    fun listOfGalleries(): List<Gallery> {
        val gallery = ArrayList<Gallery>()
        gallery.add(Gallery(listOf("Url")))
        return gallery
    }

    fun listOfBatch(): List<Batch> {
        val batchList = ArrayList<Batch>()
        batchList.add(batch())
        return batchList
    }

    fun listOfBatchItem(): List<BatchItem> {
        val batchItemList = ArrayList<BatchItem>()
        batchItemList.add(batchItem())
        return batchItemList
    }

    fun emptyBatchList(): List<Batch> {
        return ArrayList()
    }

    fun batchConfigItemRegionViewModel() = BatchConfigItemViewModel().apply {
        bindRegionView(region())
    }

    fun batchConfigItemVerticalTypeViewModel() = BatchConfigItemViewModel().apply {
        bindVerticalView(vertical())
    }

    fun emptyBatchListViewModel(): ArrayList<BatchListViewModel> {
        return ArrayList()
    }

    fun backlogDetailItemViewModel() = BacklogDetailItemViewModelImpl().apply {
        bindViewModel(batch(), batchItem())
    }

    fun getScannedRfidTag(): String {
        return "123456789012"
    }

    fun getSystemCalendar(): Calendar {
        return Calendar.getInstance()
    }

    fun listOfBatchConfigResult() = listOf(batchConfigResult())
    fun batchConfigResult(): BatchConfig.Result {
        return BatchConfig.Result(
                id = "1",
                regionId = "id",
                regionOutboundOrder = 1,
                verticalTypeId = "apparel",
                verticalTypeCode = "ap",
                verticalTypeOutboundOrder = 1,
                batchGenerationTime = BatchConfig.BatchGenerationTime(1, 0),
                region = BatchConfig.BatchConfigAttribute("id", "Indonesia"),
                vertical = BatchConfig.BatchConfigAttribute("ap", "Apparel")
        )
    }

    fun region(): Region {
        return Region(
                id = "id",
                orderId = 1,
                name = "Indonesia"
        )
    }

    fun vertical(): VerticalType {
        return VerticalType(
                id = "ap",
                orderId = 1,
                code = "apparel",
                name = "Apparel"
        )
    }
}