package co.styletheory.ops.outbound.android.general

import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.model.enums.LabelType
import co.styletheory.ops.outbound.android.viewModelComponent.LabelBoxViewModel
import io.mockk.impl.annotations.InjectMockKs
import org.amshove.kluent.shouldBeEqualTo
import org.junit.Test

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 16 December 2017.
 * Description
 *
 * <EMAIL>
 */

class LabelBoxViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: LabelBoxViewModel

    @Test
    fun firstState() {
        viewModel.labelType.get() shouldBeEqualTo null
        viewModel.labelName.get() shouldBeEqualTo ""
    }

    @Test
    fun checkBindViewModel() {
        viewModel.bindViewModel(LabelType.VIP)
        viewModel.labelType.get() shouldBeEqualTo LabelType.VIP
        viewModel.labelName.get() shouldBeEqualTo LabelType.VIP.stringRes
    }
}