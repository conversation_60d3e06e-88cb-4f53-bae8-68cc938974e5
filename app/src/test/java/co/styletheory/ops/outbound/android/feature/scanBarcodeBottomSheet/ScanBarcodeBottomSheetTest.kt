package co.styletheory.ops.outbound.android.feature.scanBarcodeBottomSheet

import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.feature.scanBarcodeBottomSheet.event.ScanBarcodeBottomSheetUIEvent
import co.styletheory.ops.outbound.android.feature.scanBarcodeBottomSheet.viewModel.impl.ScanBarcodeBottomSheetViewModelImpl
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.model.SwapItem
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.spyk
import io.mockk.verify
import org.junit.Test

/**
 * Created by Yoga C. Pranata on 13/05/20.
 * Android Engineer
 */
class ScanBarcodeBottomSheetTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: ScanBarcodeBottomSheetViewModelImpl

    override fun setup() {
        super.setup()
        viewModel = spyk(viewModel, recordPrivateCalls = true)
    }

    @Test
    fun checkOnKeyTapped_barcodeSettingIsOn() {
        val rfid = "**********"
        val swapItem = SwapItem(MockProvider.batchItem(), boxId = "ABC", shipmentId = "XYZ")
        viewModel.scannedRfidText.set(rfid)
        viewModel.swapItem = swapItem
        every { featureFlagUtil.isBarcodeSettingIsOn() } returns true
        viewModel.onKeyTapped()
        verify { eventBus.post(any<ScanBarcodeBottomSheetUIEvent.OnScannedRfid>()) }
    }

    @Test
    fun checkOnKeyTapped_barcodeSettingIsOff() {
        val rfid = "**********"
        val swapItem = SwapItem(MockProvider.batchItem(), boxId = "ABC", shipmentId = "XYZ")
        viewModel.scannedRfidText.set(rfid)
        viewModel.swapItem = swapItem
        every { featureFlagUtil.isBarcodeSettingIsOn() } returns false
        viewModel.onKeyTapped()
        verify(exactly = 0) { eventBus.post(ScanBarcodeBottomSheetUIEvent.OnScannedRfid(viewModel.swapItem, rfid)) }
    }

    @Test
    fun checkOnCloseBottomSheet() {
        viewModel.onClose()
        verify { eventBus.post(ScanBarcodeBottomSheetUIEvent.OnCloseClicked) }
    }
}