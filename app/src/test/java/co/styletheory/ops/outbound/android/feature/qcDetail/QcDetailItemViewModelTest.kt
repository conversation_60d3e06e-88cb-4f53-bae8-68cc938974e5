package co.styletheory.ops.outbound.android.feature.qcDetail

import android.view.View
import androidx.databinding.ObservableBoolean
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.qcDetail.event.QcDetailUIEvent
import co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.impl.QcDetailItemViewModelImpl
import co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.impl.QcPickedViewModel
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.model.Customer
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.util.GeneralCallback
import co.styletheory.ops.outbound.android.util.Next
import co.styletheory.ops.outbound.android.viewModelComponent.LabelBoxViewModel
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import org.amshove.kluent.shouldBe
import org.amshove.kluent.shouldBeEqualTo
import org.junit.Test

/**
 * styletheory-ops-outbound-android
 * Created by Jessica Setyani on 12 March 2019.
 */

class QcDetailItemViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: QcDetailItemViewModelImpl

    override fun setup() {
        super.setup()
        every { context.getString(R.string.complete_label) } returns "Complete"
        every { context.getString(R.string.in_complete_label) } returns "In Complete"
    }

    @Test
    fun checkFirstInit() {
        viewModel.title.get() shouldBeEqualTo ""
        viewModel.titleVisibility.get() shouldBeEqualTo View.VISIBLE
        viewModel.completeButtonText.get() shouldBeEqualTo ""
        viewModel.labelBoxItems.size shouldBeEqualTo 0
        viewModel.qaPickedItems.size shouldBeEqualTo 0
        viewModel.isComplete.get() shouldBeEqualTo false
        viewModel.shipment shouldBe null
        viewModel.rackSection shouldBeEqualTo ""
        viewModel.rackName shouldBeEqualTo ""
        viewModel.batchId shouldBeEqualTo ""
        viewModel.batchId shouldBeEqualTo ""
    }

    @Test
    fun mapShipment_whenShipmentIsNull() {
        viewModel.mapShipment()
        viewModel.title.get() shouldBeEqualTo ""
        viewModel.customerName.get() shouldBeEqualTo ""
        viewModel.labelBoxItems.size shouldBeEqualTo 0
        viewModel.qaPickedItems.size shouldBeEqualTo 0
        viewModel.isComplete.get() shouldBeEqualTo false
    }

    @Test
    fun mapShipment_whenShipmentIsNotNull_isUserSG() {
        viewModel = spyk(viewModel)
        every { userStorage.isUserRegionSG() } returns true
        val tracking = mockk<Shipment.Tracking>()
        every { tracking.id } returns "12"

        val customer = mockk<Customer>()
        every { customer.name } returns "Apri"
        every { customer.isVip } returns true
        every { customer.noPaper } returns true

        val shipment = mockk<Shipment>()
        every { shipment.tracking } returns tracking
        every { shipment.customer } returns customer
        every { shipment.box?.isFirstBox } returns true
        every { shipment.isBusinessMethodReselling() } returns false
        every { shipment.isBusinessMethodSurpriseKit() } returns false
        every { shipment.businessMethod } returns "Subscription"
        every { shipment.items } returns emptyList()

        mockkObject(LabelBoxViewModel.Companion)
        val mockLabel = mockk<LabelBoxViewModel>()
        every { LabelBoxViewModel.create(any<Shipment>()) } returns listOf(mockLabel)
        every { viewModel.addQaPickedItemsForSG(shipment.items) } answers { nothing }
        every { viewModel.completeButtonRefreshState() } just runs

        viewModel.rackName = "A"
        viewModel.rackSection = "12"
        viewModel.shipment = shipment

        viewModel.mapShipment()

        viewModel.customerName.get() shouldBeEqualTo context.getString(R.string.detail_item_title)
            .format(viewModel.shipment?.customer?.name, viewModel.rackName, viewModel.rackSection)
        viewModel.title.get() shouldBeEqualTo "12"

        verify(exactly = 1) {
            viewModel.addQaPickedItemsForSG(any())
            viewModel.completeButtonRefreshState()
        }
    }

    @Test
    fun mapShipment_whenShipmentIsNotNull_isUserID() {
        viewModel = spyk(viewModel)
        every { userStorage.isUserRegionID() } returns true
        val tracking = mockk<Shipment.Tracking>()
        every { tracking.id } returns "12"

        val customer = mockk<Customer>()
        every { customer.name } returns "Apri"
        every { customer.isVip } returns true
        every { customer.noPaper } returns true

        val shipment = mockk<Shipment>()
        every { shipment.tracking } returns tracking
        every { shipment.customer } returns customer
        every { shipment.box?.isFirstBox } returns true
        every { shipment.isBusinessMethodReselling() } returns false
        every { shipment.isBusinessMethodSurpriseKit() } returns false
        every { shipment.businessMethod } returns "Subscription"
        every { shipment.items } returns emptyList()

        mockkObject(LabelBoxViewModel.Companion)
        val mockLabel = mockk<LabelBoxViewModel>()
        every { LabelBoxViewModel.create(any<Shipment>()) } returns listOf(mockLabel)
        every { viewModel.addQaPickedItemsForID(shipment.items) } answers { nothing }
        every { viewModel.completeButtonRefreshState() } just runs

        viewModel.rackName = "A"
        viewModel.rackSection = "12"
        viewModel.shipment = shipment

        viewModel.mapShipment()

        viewModel.customerName.get() shouldBeEqualTo context.getString(R.string.detail_item_title)
            .format(viewModel.shipment?.customer?.name, viewModel.rackName, viewModel.rackSection)
        viewModel.title.get() shouldBeEqualTo "12"

        verify(exactly = 1) {
            viewModel.addQaPickedItemsForID(any())
            viewModel.completeButtonRefreshState()
        }
    }

    @Test
    fun mapShipment_whenShipmentIsNotNullButEmptyId_isUserSG() {
        viewModel = spyk(viewModel)
        every { userStorage.isUserRegionSG() } returns true
        val tracking = mockk<Shipment.Tracking>()
        every { tracking.id } returns ""

        val customer = mockk<Customer>()
        every { customer.name } returns "Apri"
        every { customer.isVip } returns true
        every { customer.noPaper } returns true

        val shipment = mockk<Shipment>()
        every { shipment.tracking } returns tracking
        every { shipment.customer } returns customer
        every { shipment.box?.isFirstBox } returns true
        every { shipment.isBusinessMethodReselling() } returns false
        every { shipment.isBusinessMethodSurpriseKit() } returns false
        every { shipment.businessMethod } returns "Subscription"
        every { shipment.items } returns emptyList()

        mockkObject(LabelBoxViewModel.Companion)
        val mockLabel = mockk<LabelBoxViewModel>()
        every { LabelBoxViewModel.create(any<Shipment>()) } returns listOf(mockLabel)
        every { viewModel.addQaPickedItemsForSG(shipment.items) } answers { nothing }
        every { viewModel.completeButtonRefreshState() } just runs

        viewModel.rackName = "A"
        viewModel.rackSection = "12"
        viewModel.shipment = shipment

        viewModel.mapShipment()

        viewModel.customerName.get() shouldBeEqualTo context.getString(R.string.detail_item_title)
            .format(viewModel.shipment?.customer?.name, viewModel.rackName, viewModel.rackSection)
        viewModel.title.get() shouldBeEqualTo ""
        viewModel.titleVisibility.get() shouldBeEqualTo View.GONE
        verify(exactly = 1) {
            viewModel.addQaPickedItemsForSG(any())
            viewModel.completeButtonRefreshState()
        }
    }

    @Test
    fun mapShipment_whenShipmentIsNotNullButEmptyId_isUserHK() {
        viewModel = spyk(viewModel)
        every { userStorage.isUserRegionHK() } returns true
        val tracking = mockk<Shipment.Tracking>()
        every { tracking.id } returns ""

        val customer = mockk<Customer>()
        every { customer.name } returns "Apri"
        every { customer.isVip } returns true
        every { customer.noPaper } returns true

        val shipment = mockk<Shipment>()
        every { shipment.tracking } returns tracking
        every { shipment.customer } returns customer
        every { shipment.box?.isFirstBox } returns true
        every { shipment.isBusinessMethodReselling() } returns false
        every { shipment.isBusinessMethodSurpriseKit() } returns false
        every { shipment.businessMethod } returns "Subscription"
        every { shipment.items } returns emptyList()

        mockkObject(LabelBoxViewModel.Companion)
        val mockLabel = mockk<LabelBoxViewModel>()
        every { LabelBoxViewModel.create(any<Shipment>()) } returns listOf(mockLabel)
        every { viewModel.addQaPickedItemsForSG(shipment.items) } answers { nothing }
        every { viewModel.completeButtonRefreshState() } just runs

        viewModel.rackName = "A"
        viewModel.rackSection = "12"
        viewModel.shipment = shipment

        viewModel.mapShipment()

        viewModel.customerName.get() shouldBeEqualTo context.getString(R.string.detail_item_title)
            .format(viewModel.shipment?.customer?.name, viewModel.rackName, viewModel.rackSection)
        viewModel.title.get() shouldBeEqualTo ""
        viewModel.titleVisibility.get() shouldBeEqualTo View.GONE
        verify(exactly = 1) {
            viewModel.addQaPickedItemsForSG(any())
            viewModel.completeButtonRefreshState()
        }
    }

    @Test
    fun mapShipment_whenShipmentIsNotNullButEmptyId_isUserID() {
        viewModel = spyk(viewModel)
        every { userStorage.isUserRegionID() } returns true
        val tracking = mockk<Shipment.Tracking>()
        every { tracking.id } returns ""

        val customer = mockk<Customer>()
        every { customer.name } returns "Apri"
        every { customer.isVip } returns true
        every { customer.noPaper } returns true

        val shipment = mockk<Shipment>()
        every { shipment.tracking } returns tracking
        every { shipment.customer } returns customer
        every { shipment.box?.isFirstBox } returns true
        every { shipment.isBusinessMethodReselling() } returns false
        every { shipment.isBusinessMethodSurpriseKit() } returns false
        every { shipment.businessMethod } returns "Subscription"
        every { shipment.items } returns emptyList()

        mockkObject(LabelBoxViewModel.Companion)
        val mockLabel = mockk<LabelBoxViewModel>()
        every { LabelBoxViewModel.create(any<Shipment>()) } returns listOf(mockLabel)
        every { viewModel.addQaPickedItemsForID(shipment.items) } answers { nothing }
        every { viewModel.completeButtonRefreshState() } just runs

        viewModel.rackName = "A"
        viewModel.rackSection = "12"
        viewModel.shipment = shipment

        viewModel.mapShipment()

        viewModel.customerName.get() shouldBeEqualTo context.getString(R.string.detail_item_title)
            .format(viewModel.shipment?.customer?.name, viewModel.rackName, viewModel.rackSection)
        viewModel.title.get() shouldBeEqualTo ""
        viewModel.titleVisibility.get() shouldBeEqualTo View.GONE
        verify(exactly = 1) {
            viewModel.addQaPickedItemsForID(any())
            viewModel.completeButtonRefreshState()
        }
    }

    @Test
    fun addQaPickedItemsSG() {
        viewModel = spyk(viewModel)
        mockkConstructor(QcPickedViewModel::class)
        val batchItem = MockProvider.batchItem().copy(status = ProductStatus.QA)
        val pickedItems = listOf(batchItem)
        val qaItem = mockk<QcPickedViewModel>()
        every { qaItem.batchId } returns "12"

        val call = mockk<GeneralCallback>()
        every { qaItem.refreshCompleteState } returns call

        val removeSwappedItem = mockk<Next<QcPickedViewModel?>>()
        every { qaItem.removeSwappedItem } returns removeSwappedItem

        every {
            anyConstructed<QcPickedViewModel>().bindViewModel(
                any(), any(), any(), any(), any(), any()
            )
        } returns mockk()
        every { viewModel.setItemWhenVerticalType(any()) } just Runs

        viewModel.addQaPickedItemsForSG(pickedItems)

        qaItem.batchId shouldBeEqualTo "12"
        qaItem.refreshCompleteState shouldBeEqualTo call
        qaItem.removeSwappedItem shouldBeEqualTo removeSwappedItem
        viewModel.qaPickedItems.size shouldBeEqualTo pickedItems.size
        verify(exactly = 1) { viewModel.isHaveRfid(any()) }
        verify(exactly = 1) { viewModel.setItemWhenVerticalType(any()) }
        verify(exactly = 1) { viewModel.checkClickedStatus(any(), any()) }
    }

    @Test
    fun addQaPickedItemsID() {
        viewModel = spyk(viewModel)
        mockkConstructor(QcPickedViewModel::class)
        val batchItem = MockProvider.batchItem().copy(status = ProductStatus.QA)
        val pickedItems = listOf(batchItem)
        val qaItem = mockk<QcPickedViewModel>()
        every { qaItem.batchId } returns "12"

        val call = mockk<GeneralCallback>()
        every { qaItem.refreshCompleteState } returns call

        val removeSwappedItem = mockk<Next<QcPickedViewModel?>>()
        every { qaItem.removeSwappedItem } returns removeSwappedItem

        every {
            anyConstructed<QcPickedViewModel>().bindViewModel(
                any(), any(), any(), any(), any(), any()
            )
        } returns mockk()
        every { viewModel.setItemWhenVerticalType(any()) } just Runs

        viewModel.addQaPickedItemsForID(pickedItems)

        qaItem.batchId shouldBeEqualTo "12"
        qaItem.refreshCompleteState shouldBeEqualTo call
        qaItem.removeSwappedItem shouldBeEqualTo removeSwappedItem
        viewModel.qaPickedItems.size shouldBeEqualTo pickedItems.size
        verify(exactly = 1) { viewModel.isHaveRfid(any()) }
        verify(exactly = 1) { viewModel.setItemWhenVerticalType(any()) }
        verify(exactly = 1) { viewModel.checkClickedStatus(any(), any()) }
    }

    @Test
    fun enableButton() {
        viewModel = spyk(viewModel)
        val qaItem = mockk<QcPickedViewModel>()
        viewModel.qaPickedItems.add(qaItem)
        every { qaItem.setupEnableButton(any()) } answers { nothing }
        every { qaItem.setupEnableAccuracySwapButton(any()) } answers { nothing }

        viewModel.enableButton(0)

        verify {
            qaItem.setupEnableButton(any())
            qaItem.setupEnableAccuracySwapButton(any())
        }
    }

    @Test
    fun checkScannedText_foundItem() {
        viewModel = spyk(viewModel)
        val scannedText = MockProvider.getScannedRfidTag()
        val batchItem = MockProvider.batchItem()
        val qaPickedItem = mockk<QcPickedViewModel>()

        viewModel.qaPickedItems.add(qaPickedItem)
        every { qaPickedItem.batchItem } returns batchItem
        every { viewModel.enableButton(any()) } answers { nothing }

        viewModel.checkRfidText(scannedText)

        scannedText shouldBeEqualTo batchItem.rfid
        verify(exactly = 1) { viewModel.enableButton(any()) }
        verify(exactly = 1) { eventBus.post(any<QcDetailUIEvent.OnScrollToPosition>()) }
    }

    @Test
    fun isHaveRfid() {
        viewModel = spyk(viewModel)
        val qaItem = mockk<QcPickedViewModel>()
        every { qaItem.batchItem } answers { MockProvider.batchItem() }
        every { qaItem.showRfidCode() } answers { nothing }

        viewModel.isHaveRfid(qaItem)

        verify(exactly = 1) { qaItem.showRfidCode() }
    }

    @Test
    fun setItemWhenVerticalType_bags() {
        val qaItem = mockk<QcPickedViewModel>()
        every { userStorage.isVerticalTypeBags() } returns true
        every { qaItem.showBagsItems() } answers { nothing }

        viewModel.setItemWhenVerticalType(qaItem)

        verify(exactly = 1) { qaItem.showBagsItems() }
    }

    @Test
    fun setItemWhenVerticalType_apparels_isOnDemandUser() {
        viewModel = spyk(viewModel)
        val qaItem = mockk<QcPickedViewModel>()
        every { userStorage.isUserOnDemandAndRegionID() } returns true
        every { qaItem.showApparelItems() } answers { nothing }

        viewModel.setItemWhenVerticalType(qaItem)

        verify(exactly = 1) { qaItem.showApparelItems() }
    }

    @Test
    fun setItemWhenVerticalType_apparels_notOnDemandUser() {
        val qaItem = mockk<QcPickedViewModel>()
        every { userStorage.isUserOnDemandAndRegionID() } returns false
        every { qaItem.showApparelItems() } answers { nothing }
        every { qaItem.showSwapButton() } answers { nothing }
        every { qaItem.setupResellingSwapVisibility(any()) } answers { nothing }
        every { qaItem.batchItem } returns mockk()

        viewModel.setItemWhenVerticalType(qaItem)

        verify(exactly = 1) { qaItem.showApparelItems() }
    }

    @Test
    fun checkClickedStatus() {
        val productStatus = ProductStatus.QA_PASSED
        val qaItem = mockk<QcPickedViewModel>()
        every { qaItem.setupEnableButton(any()) } answers { nothing }

        viewModel.checkClickedStatus(productStatus, qaItem)

        verify(exactly = 1) { qaItem.setupEnableButton(any()) }
    }

    @Test
    fun isQAItemComplete_whenNotComplete() {
        val pickedItem = mockk<QcPickedViewModel>()
        every { (pickedItem.pass) } returns ObservableBoolean(false)
        every { (pickedItem.fail) } returns ObservableBoolean(false)

        viewModel.qaPickedItems.add(pickedItem)

        viewModel.isQAItemComplete() shouldBeEqualTo false
    }

    @Test
    fun isQAItemComplete_whenComplete() {
        val pickedItem = mockk<QcPickedViewModel>()
        every { pickedItem.pass } returns ObservableBoolean(true)
        every { pickedItem.fail } returns ObservableBoolean(true)

        viewModel.qaPickedItems.add(pickedItem)

        viewModel.isQAItemComplete() shouldBeEqualTo true
    }

    @Test
    fun completeButtonRefreshState_whenComplete() {
        isQAItemComplete_whenComplete()

        viewModel.completeButtonRefreshState()

        viewModel.isComplete.get() shouldBeEqualTo true
        viewModel.completeButtonText.get() shouldBeEqualTo "Complete"
    }

    @Test
    fun completeButtonRefreshState_whenInComplete() {
        isQAItemComplete_whenNotComplete()

        viewModel.completeButtonRefreshState()

        viewModel.isComplete.get() shouldBeEqualTo false
        viewModel.completeButtonText.get() shouldBeEqualTo "In Complete"
    }

    @Test
    fun completeButtonRefreshState_fromCallback() {
        isQAItemComplete_whenNotComplete()

        viewModel.refreshCompleteState.callback()

        viewModel.isComplete.get() shouldBeEqualTo false
        viewModel.completeButtonText.get() shouldBeEqualTo "In Complete"
    }

    @Test
    fun removeSwappedItem_apply() {
        val item = mockk<QcPickedViewModel>()

        viewModel.qaPickedItems.add(item)

        viewModel.removeSwappedItem.apply(item)
        viewModel.qaPickedItems.isEmpty() shouldBeEqualTo true
        verify(exactly = 1) { eventBus.post(any<QcDetailUIEvent.RemoveShipmentQaPicked>()) }
    }

    @Test
    fun setupTitle_isOnDemand() {
        viewModel.rackName = "M"
        viewModel.rackSection = "12"
        viewModel.shipment = MockProvider.shipment()

        every { userStorage.isUserOnDemandAndRegionID() } returns true

        viewModel.setupCustomerLabel(viewModel.shipment?.customer?.name, viewModel.rackName, viewModel.rackSection)
        viewModel.customerName.get() shouldBeEqualTo context.getString(R.string.detail_item_on_demand_title)
            .format(viewModel.shipment?.customer?.name, viewModel.rackName, viewModel.rackSection)
    }

    @Test
    fun setupTitle_isNotOnDemand() {
        viewModel.rackName = "M"
        viewModel.rackSection = "12"
        viewModel.shipment = MockProvider.shipment()

        every { userStorage.isUserOnDemandAndRegionID() } returns false

        viewModel.setupCustomerLabel(viewModel.shipment?.customer?.name, viewModel.rackName, viewModel.rackSection)
        viewModel.customerName.get() shouldBeEqualTo context.getString(R.string.detail_item_title)
            .format(viewModel.shipment?.customer?.name, viewModel.rackName, viewModel.rackSection)
    }

    @Test
    fun checkSetupBusinessMethod_isSurpriseKit() {
        val shipment = MockProvider.shipment().copy(businessMethod = "SurpriseKit")
        viewModel.setupBusinessMethod(shipment)
        viewModel.businessMethodNameList.size shouldBeEqualTo 1
        viewModel.businessMethodNameList[0].serviceName.get() shouldBeEqualTo context.getString(R.string.packing_detail_surprise_kit_title)
    }

    @Test
    fun checkSetupBusinessMethod_isReselling() {
        val shipment = MockProvider.shipment().copy(businessMethod = "Reselling")
        viewModel.setupBusinessMethod(shipment)
        viewModel.businessMethodNameList.size shouldBeEqualTo 1
        viewModel.businessMethodNameList[0].serviceName.get() shouldBeEqualTo context.getString(R.string.packing_detail_reselling_title)
    }

    @Test
    fun checkSetupBusinessMethod() {
        val shipment = MockProvider.shipment()
        viewModel.setupBusinessMethod(shipment)
        viewModel.businessMethodNameList.size shouldBeEqualTo 1
        viewModel.businessMethodNameList[0].serviceName.get() shouldBeEqualTo shipment.businessMethod
    }
}