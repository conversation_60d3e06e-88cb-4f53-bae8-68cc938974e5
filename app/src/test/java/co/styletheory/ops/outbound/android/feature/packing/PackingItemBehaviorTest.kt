package co.styletheory.ops.outbound.android.feature.packing

import android.view.View
import co.styletheory.ops.outbound.android.BaseBehaviorTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.packing.viewModel.impl.PackingItemViewModelImpl
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.viewModelComponent.PhotoWithLabelViewModel
import io.kotlintest.Spec
import io.kotlintest.extensions.TopLevelTest
import io.mockk.*
import org.amshove.kluent.shouldBeEmpty
import org.amshove.kluent.shouldBeEqualTo
import org.amshove.kluent.shouldBeFalse
import org.amshove.kluent.shouldNotBeEqualTo

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019-09-23.
 */
class PackingItemBehaviorTest : BaseBehaviorTest<PackingItemViewModelImpl>(PackingItemViewModelImpl()) {

    lateinit var userStorage: UserStorage
    private lateinit var photoWithLabelVM: PhotoWithLabelViewModel

    override fun beforeSpecClass(spec: Spec, tests: List<TopLevelTest>) {
        super.beforeSpecClass(spec, tests)
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        userStorage = mockk(relaxed = true)
        photoWithLabelVM = mockk(relaxed = true)

        viewModel.userStorage = userStorage
        viewModel.photoWithLabelVM = photoWithLabelVM
    }

    override fun resetFieldState() {
        super.resetFieldState()
        setPrivateField("rackSection", "")
        setPrivateField("rackName", "")

        viewModel.batchItem = null
        viewModel.itemName.set("")
        viewModel.itemSize.set("")
        viewModel.rack.set("")
        viewModel.category.set("")
        viewModel.parts.set("")
        viewModel.notes.set("")
        viewModel.soldTo.set("")
        viewModel.productOrder.set("")
        viewModel.itemStatus.set("")
        viewModel.colorItems.clear()
        viewModel.detachable.set("")
        viewModel.partsVisibility.set(View.GONE)
        viewModel.detachableVisibility.set(View.GONE)
        viewModel.scannerIconVisibility.set(View.GONE)
        viewModel.checkMarkRfidVisibility.set(View.GONE)
        viewModel.haveScannedRfid.set(false)
        viewModel.itemStatusVisibility.set(View.VISIBLE)
    }

    init {
        given("I open them rack detail with vertical type is bags") {
            `when`("I see rack detail") {
                every { userStorage.isVerticalTypeBags() } returns true

                then("Rack is loading") {
                    viewModel.itemName.get().shouldBeEmpty()
                    viewModel.itemSize.get().shouldBeEmpty()
                    viewModel.rack.get().shouldBeEmpty()
                    viewModel.category.get().shouldBeEmpty()
                    viewModel.parts.get().shouldBeEmpty()
                    viewModel.notes.get().shouldBeEmpty()
                    viewModel.productOrder.get().shouldBeEmpty()
                    viewModel.soldTo.get().shouldBeEmpty()
                    viewModel.colorItems.size shouldBeEqualTo 0
                    viewModel.detachableVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.partsVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.scannerIconVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.checkMarkRfidVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.haveScannedRfid.get().shouldBeFalse()
                    viewModel.itemStatus.get() shouldBeEqualTo ""
                }
            }

            and(" Rack has shown") {
                val batchItem = MockProvider.batchItemStyleColor(ProductStatus.AVAILABLE)
                val rackName = "A"
                val rackSection = "11"
                val eventSlot = slot<String>()
                every { photoWithLabelVM.bindPhotoWithLabel(capture(eventSlot), any()) } just Runs

                `when`("Rack item already mapped") {
                    viewModel.bindViewModel(batchItem, rackSection, rackName)
                    viewModel.showBagsItems()

                    then("Item attribute has set") {
                        viewModel.itemName.get() shouldBeEqualTo "Apri"
                        viewModel.itemSize.get() shouldBeEqualTo "XS"
                        viewModel.rack.get() shouldBeEqualTo mContext.getString(R.string.rack_name_regular)
                            .format(rackName, rackSection)
                        viewModel.category.get() shouldBeEqualTo "dress"
                        viewModel.parts.get() shouldBeEqualTo "Belt"
                        viewModel.notes.get() shouldBeEqualTo "notes"
                        viewModel.productOrder.get() shouldBeEqualTo "[9]"
                        viewModel.soldTo.get() shouldBeEqualTo "Vip"
                        viewModel.colorItems.size shouldNotBeEqualTo 0
                        viewModel.detachableVisibility.get() shouldBeEqualTo View.VISIBLE
                        viewModel.partsVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.scannerIconVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.checkMarkRfidVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.haveScannedRfid.get().shouldBeFalse()
                        viewModel.itemStatus.get() shouldBeEqualTo ""
                        verify { photoWithLabelVM.bindPhotoWithLabel(any(), any()) }
                        eventSlot.captured shouldBeEqualTo "Url"
                    }
                }
            }
        }

        given("I open the rack detail with vertical type is apparel") {

            `when`("I see rack detail") {
                resetFieldState()
                every { userStorage.isVerticalTypeBags() } returns false

                then("Rack is loading") {
                    viewModel.itemName.get().shouldBeEmpty()
                    viewModel.itemSize.get().shouldBeEmpty()
                    viewModel.rack.get().shouldBeEmpty()
                    viewModel.category.get().shouldBeEmpty()
                    viewModel.parts.get().shouldBeEmpty()
                    viewModel.notes.get().shouldBeEmpty()
                    viewModel.productOrder.get().shouldBeEmpty()
                    viewModel.soldTo.get().shouldBeEmpty()
                    viewModel.colorItems.size shouldBeEqualTo 0
                    viewModel.detachableVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.partsVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.scannerIconVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.checkMarkRfidVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.haveScannedRfid.get().shouldBeFalse()
                    viewModel.itemStatus.get() shouldBeEqualTo ""
                }
            }

            and("Rack has shown") {
                val style = null
                val batchItem = MockProvider.batchItemStyleColor(ProductStatus.AVAILABLE).copy(style = style)
                val rackName = "A"
                val rackSection = "11"

                `when`("Rack item already mapped") {
                    val eventSlot = slot<String>()
                    every { photoWithLabelVM.bindPhotoWithLabel(capture(eventSlot), any()) } just Runs
                    viewModel.bindViewModel(batchItem, rackSection, rackName)
                    viewModel.showApparelItems()

                    then("Item attribute has set") {
                        viewModel.itemName.get() shouldBeEqualTo ""
                        viewModel.itemSize.get() shouldBeEqualTo "XS"
                        viewModel.rack.get() shouldBeEqualTo mContext.getString(R.string.rack_name_regular)
                            .format(rackName, rackSection)
                        viewModel.category.get() shouldBeEqualTo ""
                        viewModel.parts.get() shouldBeEqualTo "Belt"
                        viewModel.notes.get() shouldBeEqualTo "notes"
                        viewModel.productOrder.get() shouldBeEqualTo "[9]"
                        viewModel.soldTo.get() shouldBeEqualTo "Vip"
                        viewModel.detachableVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.partsVisibility.get() shouldBeEqualTo View.VISIBLE
                        viewModel.scannerIconVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.checkMarkRfidVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.haveScannedRfid.get().shouldBeFalse()
                        viewModel.itemStatus.get() shouldBeEqualTo ""
                        verify { photoWithLabelVM.bindPhotoWithLabel("Url", any()) }
                    }
                }
            }
        }

        given("I open the rack detail with vertical type is apparel and user is on demand region ID") {

            `when`("I see rack detail") {
                resetFieldState()
                every { userStorage.isVerticalTypeBags() } returns false
                every { userStorage.isUserOnDemandAndRegionID() } returns true

                then("Rack is loading") {
                    viewModel.itemName.get().shouldBeEmpty()
                    viewModel.itemSize.get().shouldBeEmpty()
                    viewModel.rack.get().shouldBeEmpty()
                    viewModel.category.get().shouldBeEmpty()
                    viewModel.parts.get().shouldBeEmpty()
                    viewModel.notes.get().shouldBeEmpty()
                    viewModel.productOrder.get().shouldBeEmpty()
                    viewModel.soldTo.get().shouldBeEmpty()
                    viewModel.colorItems.size shouldBeEqualTo 0
                    viewModel.detachableVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.partsVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.scannerIconVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.checkMarkRfidVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.haveScannedRfid.get().shouldBeFalse()
                    viewModel.itemStatus.get() shouldBeEqualTo ""
                }
            }

            and("Rack has shown") {
                val batchItem = MockProvider.batchItemStyleColor(ProductStatus.AVAILABLE)
                val rackName = "A"
                val rackSection = "11"

                `when`("Rack item already mapped") {
                    viewModel.bindViewModel(batchItem, rackSection, rackName)
                    viewModel.showApparelItems()

                    then("Item attribut has set") {
                        viewModel.itemName.get() shouldBeEqualTo "Apri"
                        viewModel.itemSize.get() shouldBeEqualTo "XS"
                        viewModel.rack.get() shouldBeEqualTo mContext.getString(R.string.rack_name_on_demand)
                            .format(rackName, rackSection)
                        viewModel.category.get() shouldBeEqualTo "dress"
                        viewModel.parts.get() shouldBeEqualTo "Belt"
                        viewModel.notes.get() shouldBeEqualTo "notes"
                        viewModel.productOrder.get() shouldBeEqualTo "[9]"
                        viewModel.soldTo.get() shouldBeEqualTo "Vip"
                        viewModel.colorItems.size shouldNotBeEqualTo 0
                        viewModel.detachableVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.partsVisibility.get() shouldBeEqualTo View.VISIBLE
                        viewModel.scannerIconVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.checkMarkRfidVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.haveScannedRfid.get().shouldBeFalse()
                        viewModel.itemStatus.get() shouldBeEqualTo ""
                    }
                }
            }

            `when`("Item is rented") {
                viewModel.handleStatusItem(ProductStatus.RENTED)

                then("Item status is not picked up yet") {
                    viewModel.itemStatus.get() shouldBeEqualTo R.string.warn_ready_item_for_rented
                }
            }

            `when`("Item is picking") {
                viewModel.handleStatusItem(ProductStatus.PICKING)

                then("Item status is being picked up") {
                    viewModel.itemStatus.get() shouldBeEqualTo R.string.warn_ready_item_for_picking
                }
            }

            `when`("Item is picked") {
                viewModel.handleStatusItem(ProductStatus.PICKED)

                then("Item not qa yet") {
                    viewModel.itemStatus.get() shouldBeEqualTo R.string.warn_ready_item_for_picked
                }
            }

            `when`("Item is paid") {
                viewModel.handleStatusItem(ProductStatus.PAID)

                then("Item not picking yet") {
                    viewModel.itemStatus.get() shouldBeEqualTo R.string.warn_ready_item_for_paid
                }
            }

            `when`("Item is notfound") {
                viewModel.handleStatusItem(ProductStatus.NOT_FOUND)

                then("Item status is missing from warehouse") {
                    viewModel.itemStatus.get() shouldBeEqualTo R.string.warn_ready_item_for_not_found
                }
            }

            `when`("Item is qa") {
                viewModel.handleStatusItem(ProductStatus.QA)

                then("Item status is being qa") {
                    viewModel.itemStatus.get() shouldBeEqualTo R.string.warn_ready_item_for_qa
                }
            }

            `when`("Item is qa passed") {
                viewModel.handleStatusItem(ProductStatus.QA_PASSED)

                then("Item status is not photograph yet") {
                    viewModel.itemStatus.get() shouldBeEqualTo R.string.warn_ready_item_for_qa_passed
                }
            }

            `when`("Item is qa failed") {
                viewModel.handleStatusItem(ProductStatus.QA_FAILED)

                then("Item status is failed from qa") {
                    viewModel.itemStatus.get() shouldBeEqualTo R.string.warn_ready_item_for_qa_failed
                }
            }

            `when`("Item is qa failed confirmed") {
                viewModel.handleStatusItem(ProductStatus.QA_FAILED_CONFIRMED)

                then("Item status is failed from qa and waiting for swap") {
                    viewModel.itemStatus.get() shouldBeEqualTo R.string.warn_ready_item_for_qa_failed_confirmed
                }
            }

            `when`("Item is photo qa") {
                viewModel.handleStatusItem(ProductStatus.PHOTO_QA)

                then("Item status is being photograph") {
                    viewModel.itemStatus.get() shouldBeEqualTo R.string.warn_ready_item_for_photo_qa
                }
            }

            `when`("Barcode setting is on") {
                every { featureFlagUtil.isBarcodeSettingIsOn() } returns true
                viewModel.showScannerIcon()

                then("RFID and scanner icon is visible") {
                    viewModel.checkMarkRfidVisibility.get() shouldBeEqualTo View.VISIBLE
                    viewModel.scannerIconVisibility.get() shouldBeEqualTo View.VISIBLE
                }
            }
        }

        given("I open Reselling packing detail") {
            `when`("I have a purchased item") {
                resetFieldState()
                val purchasedBy = "<NAME_EMAIL>"
                val batchItem = MockProvider.batchItem(ProductStatus.PACKING).copy(label = purchasedBy)
                viewModel.setupPurchasedItem(batchItem)

                then("I should be able to see purchased label") {
                    viewModel.itemPurchasedVisibility.get() shouldBeEqualTo View.VISIBLE
                    viewModel.soldTo.get() shouldBeEqualTo purchasedBy
                }
            }

            `when`("The purchased item is empty") {
                resetFieldState()
                val batchItem = MockProvider.batchItem(ProductStatus.PACKING).copy(label = "")
                viewModel.setupPurchasedItem(batchItem)

                then("I should NOT be able to see purchased label") {
                    viewModel.itemPurchasedVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.soldTo.get() shouldBeEqualTo ""
                }
            }

            `when`("I don't have a purchased item") {
                resetFieldState()
                val batchItem = MockProvider.batchItem(ProductStatus.PACKING).copy(label = null)
                viewModel.setupPurchasedItem(batchItem)

                then("I should NOT be able to see purchased label") {
                    viewModel.itemPurchasedVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.soldTo.get() shouldBeEqualTo ""
                }
            }
        }

        given("I have photo section") {
            `when`("I see photo with label section") {
                viewModel.setPhotoWithLabelVM() shouldBeEqualTo viewModel.photoWithLabelVM
            }
        }

    }

}