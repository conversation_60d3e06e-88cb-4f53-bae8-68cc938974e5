package co.styletheory.ops.outbound.android.feature.errorSwapBottomSheet

import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.errorSwapBottomSheet.event.ErrorSwapItemBottomSheetUIEvent
import co.styletheory.ops.outbound.android.feature.errorSwapBottomSheet.viewModel.impl.ErrorSwapItemBottomSheetViewModelImpl
import co.styletheory.ops.outbound.android.model.enums.ErrorSwapType
import co.styletheory.ops.outbound.android.model.enums.SwapType
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.spyk
import io.mockk.verify
import org.amshove.kluent.shouldBeEqualTo
import org.junit.Test


/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 23/06/20.
 */
class ErrorSwapItemBottomSheetTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: ErrorSwapItemBottomSheetViewModelImpl

    override fun setup() {
        super.setup()
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        viewModel.errorResponse = errorResponse
    }

    @Test
    fun checkOnCloseBottomSheet() {
        viewModel.onClose()
        verify { eventBus.post(ErrorSwapItemBottomSheetUIEvent.OnCloseClicked) }
    }

    @Test
    fun bindError() {
        every { errorResponse.getErrorBodyDescription(any()) } returns "Error"

        every { errorResponse.getErrorBodyCode(any()) } returns "OUT-1001"
        viewModel.bindError("Error")
        viewModel.errorMessage.get() shouldBeEqualTo "Error"
        viewModel.errorButtonLabel.get() shouldBeEqualTo R.string.error_confirm

        every { errorResponse.getErrorBodyCode(any()) } returns "OUT-1004"
        viewModel.bindError("Error")
        viewModel.errorMessage.get() shouldBeEqualTo "Error"
        viewModel.errorButtonLabel.get() shouldBeEqualTo R.string.error_confirm

        every { errorResponse.getErrorBodyCode(any()) } returns "OUT-1002"
        viewModel.bindError("Error")
        viewModel.errorMessage.get() shouldBeEqualTo "Error"
        viewModel.errorButtonLabel.get() shouldBeEqualTo R.string.error_scan_other_item

        every { errorResponse.getErrorBodyCode(any()) } returns "OUT-1003"
        viewModel.bindError("Error")
        viewModel.errorMessage.get() shouldBeEqualTo "Error"
        viewModel.errorButtonLabel.get() shouldBeEqualTo R.string.error_scan_parent_item

        every { errorResponse.getErrorBodyCode(any()) } returns "No network available, please check your WiFi or Data connection"
        viewModel.bindError("Error")
        viewModel.errorMessage.get() shouldBeEqualTo R.string.error_swap_message_no_network
        viewModel.errorButtonLabel.get() shouldBeEqualTo R.string.error_retry

        every { errorResponse.getErrorBodyCode(any()) } returns ""
        viewModel.bindError("Error")
        viewModel.errorMessage.get() shouldBeEqualTo "Error"
        viewModel.errorButtonLabel.get() shouldBeEqualTo R.string.error_confirm
    }

    @Test
    fun onClickErrorButton() {
        viewModel.swapType = SwapType.QUALITY_SWAP.swapType
        viewModel.onClickErrorButton()
        verify { eventBus.post(ErrorSwapItemBottomSheetUIEvent.OnConfirmQualitySwapClick) }

        viewModel.swapType = SwapType.ACCURACY_SWAP.swapType

        viewModel.errorSwapType = ErrorSwapType.SCAN_OTHER_ITEM
        viewModel.onClickErrorButton()
        verify { eventBus.post(ErrorSwapItemBottomSheetUIEvent.OnScanAgainClick) }

        viewModel.errorSwapType = ErrorSwapType.SCAN_PARENT_ITEM
        viewModel.onClickErrorButton()
        verify { eventBus.post(ErrorSwapItemBottomSheetUIEvent.OnScanAgainClick) }

        viewModel.errorSwapType = ErrorSwapType.RETRY
        viewModel.onClickErrorButton()
        verify { eventBus.post(ErrorSwapItemBottomSheetUIEvent.OnRetryClick) }

        viewModel.errorSwapType = ErrorSwapType.CONFIRM
        viewModel.onClickErrorButton()
        verify { eventBus.post(ErrorSwapItemBottomSheetUIEvent.OnConfirmClick) }

    }

}