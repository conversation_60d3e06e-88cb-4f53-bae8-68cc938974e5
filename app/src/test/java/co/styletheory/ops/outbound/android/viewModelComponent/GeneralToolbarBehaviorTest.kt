package co.styletheory.ops.outbound.android.viewModelComponent

import co.styletheory.ops.outbound.android.BaseBehaviorTest
import io.kotlintest.Spec
import io.kotlintest.extensions.TopLevelTest
import io.mockk.spyk
import org.amshove.kluent.shouldBeEqualTo

/**
 * Created by Yoga C. Pranata on 2019-10-04.
 * Android Engineer
 */
class GeneralToolbarBehaviorTest : BaseBehaviorTest<GeneralToolbarViewModel>(GeneralToolbarViewModel()) {

    override fun beforeSpecClass(spec: Spec, tests: List<TopLevelTest>) {
        super.beforeSpecClass(spec, tests)
        viewModel = spyk(viewModel, recordPrivateCalls = true)
    }

    init {
        given("I open page that has a general toolbar viewModel") {
            `when`("I see general toolbar title") {
                viewModel.title.set("Rack A")
                then("I should see the input title") {
                    viewModel.title.get() shouldBeEqualTo "Rack A"
                }
            }

            `when`("I see general toolbar subtitle") {
                viewModel.subtitle.set("Rack subtitle")
                then("I should see the input subtitle") {
                    viewModel.subtitle.get() shouldBeEqualTo "Rack subtitle"
                }
            }
        }
    }
}