package co.styletheory.ops.outbound.android

import android.content.Context
import co.styletheory.ops.outbound.android.general.viewModel.base.BaseViewModel
import co.styletheory.ops.outbound.android.util.featureflag.FeatureFlagUtil
import io.kotlintest.Spec
import io.kotlintest.extensions.TopLevelTest
import io.kotlintest.specs.BehaviorSpec
import io.mockk.mockk
import io.mockk.unmockkAll
import org.apache.commons.lang3.reflect.FieldUtils
import org.greenrobot.eventbus.EventBus

/**
 * Created by Yoga C. Pranata on 2019-09-11.
 * Android Engineer
 */
abstract class BaseBehaviorTest<T : BaseViewModel<*>>(private var viewModelImpl: T) : BehaviorSpec() {

    lateinit var viewModel: T
    lateinit var eventBus: EventBus
    lateinit var mContext: Context
    lateinit var featureFlagUtil: FeatureFlagUtil

    override fun afterSpec(spec: Spec) {
        super.afterSpec(spec)
        unmockkAll()
    }

    override fun beforeSpecClass(spec: Spec, tests: List<TopLevelTest>) {
        super.beforeSpecClass(spec, tests)
        eventBus = mockk(relaxed = true)
        mContext = mockk(relaxed = true)
        featureFlagUtil = mockk(relaxed = true)

        viewModel = viewModelImpl
        viewModel.eventBus = eventBus
        viewModel.context = mContext
        viewModel.featureFlagUtil = featureFlagUtil
    }

    protected fun setPrivateField(fieldName: String, value: Any?) {
        FieldUtils.writeDeclaredField(viewModel, fieldName, value, true)
    }

    protected fun getPrivateField(fieldName: String): Any? {
        return FieldUtils.readDeclaredField(viewModel, fieldName, true)
    }

    open fun resetFieldState() {}
}