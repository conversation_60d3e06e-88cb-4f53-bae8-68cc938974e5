package co.styletheory.ops.outbound.android.feature.accuracySwap

import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.accuracySwap.viewModel.impl.AccuracySwapViewModelImpl
import co.styletheory.ops.outbound.android.viewModelComponent.FooterButtonViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.spyk
import org.amshove.kluent.shouldBeEqualTo
import org.junit.Test

/**
 * Created by Yoga C. Pranata on 08/05/20.
 * Android Engineer
 */
class AccuracySwapTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var toolbarVM: GeneralToolbarViewModel

    @InjectMockKs
    lateinit var footerButtonVM: FooterButtonViewModel

    @InjectMockKs
    lateinit var viewModel: AccuracySwapViewModelImpl

    override fun setup() {
        super.setup()
        viewModel = spyk(viewModel, recordPrivateCalls = true)

        viewModel.toolbarViewModel = toolbarVM
        viewModel.footerButtonViewModel = footerButtonVM
    }

    @Test
    fun checkAfterInject() {
        viewModel.afterInject()
        viewModel.footerButtonViewModel.buttonTitle.get() shouldBeEqualTo context.getString(R.string.accuracy_swap_now_title)
        viewModel.footerButtonViewModel.enable.get() shouldBeEqualTo false
    }

    @Test
    fun checkToolbarAndFooterViewModel() {
        viewModel.toolbarViewModel() shouldBeEqualTo toolbarVM
        viewModel.footerButtonViewModel() shouldBeEqualTo footerButtonVM
    }

    @Test
    fun checkSelectedSwapReason_selectOneOfTheReason() {
        viewModel.selectedSwapReasonPosition(1)
        viewModel.footerButtonViewModel.enable.get() shouldBeEqualTo true
    }

    @Test
    fun checkSelectedSwapReason_selectTheFirstList() {
        viewModel.selectedSwapReasonPosition(0)
        viewModel.footerButtonViewModel.enable.get() shouldBeEqualTo false
    }
}