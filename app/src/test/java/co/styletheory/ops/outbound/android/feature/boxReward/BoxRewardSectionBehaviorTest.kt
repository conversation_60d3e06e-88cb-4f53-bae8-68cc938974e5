package co.styletheory.ops.outbound.android.feature.boxReward

import androidx.recyclerview.widget.LinearLayoutManager
import co.styletheory.ops.outbound.android.BaseBehaviorTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.impl.BoxRewardSectionViewModelImpl
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.general.recyclerView.RecyclerViewSetting
import org.amshove.kluent.shouldBeEqualTo

/**
 * Created by <PERSON> on 12/2/20.
 */
class BoxRewardSectionBehaviorTest : BaseBehaviorTest<BoxRewardSectionViewModelImpl>(BoxRewardSectionViewModelImpl()) {

    init {
        given("I see box reward section") {
            `when`("I have box reward") {
                val rewards = listOf(MockProvider.boxReward())
                viewModel.setBoxRewards(rewards, false)

                then("I should see list of reward item") {
                    viewModel.recyclerViewSetting.layoutId shouldBeEqualTo R.layout.box_reward_section_item
                    viewModel.recyclerViewSetting.orientation shouldBeEqualTo LinearLayoutManager.VERTICAL
                    viewModel.recyclerViewSetting.layoutManagerType shouldBeEqualTo RecyclerViewSetting.LayoutManagerType.LINEAR
                }
            }

            `when`("I complete checked all item at box reward") {
                val rewards = listOf(MockProvider.boxReward())

                viewModel.setBoxRewards(rewards, true)

                then("I should see list of reward item") {
                    viewModel.rewardItems.size shouldBeEqualTo 1
                }
            }
        }
    }
}