package co.styletheory.ops.outbound.android.feature.photoDetail

import co.styletheory.android.network.core.APICallback
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.photoDetail.view.PhotoDetailItemFragment
import co.styletheory.ops.outbound.android.feature.photoDetail.viewModel.impl.PhotoDetailViewModelImpl
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.amshove.kluent.shouldBeEqualTo
import org.amshove.kluent.shouldBeFalse
import org.amshove.kluent.shouldBeInstanceOf
import org.amshove.kluent.shouldBeTrue
import org.apache.commons.lang3.reflect.FieldUtils
import org.junit.Test

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 17 December 2017.
 * Description
 *
 * <EMAIL>
 */
class PhotoDetailViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: PhotoDetailViewModelImpl

    @InjectMockKs
    lateinit var toolbarVM: GeneralToolbarViewModel

    private fun setPrivateField(fieldName: String, value: Any?) {
        FieldUtils.writeDeclaredField(viewModel, fieldName, value, true)
    }

    private fun getPrivateField(fieldName: String): Any? {
        return FieldUtils.readDeclaredField(viewModel, fieldName, true)
    }

    override fun setup() {
        super.setup()
        viewModel.toolbarViewModel = toolbarVM
        viewModel = spyk(viewModel, recordPrivateCalls = true)
    }

    @Test
    fun firstState() {
        viewModel.shipmentId shouldBeEqualTo ""
        viewModel.batchItem shouldBeEqualTo null
        viewModel.batchName() shouldBeEqualTo ""
        viewModel.toolbarViewModel.title.get() shouldBeEqualTo ""
        viewModel.toolbarViewModel.subtitle.get() shouldBeEqualTo ""
        viewModel.generalToolbarViewModel() shouldBeEqualTo viewModel.toolbarViewModel

        getPrivateField("batchId") shouldBeEqualTo ""
        getPrivateField("batch") shouldBeEqualTo null
        getPrivateField("batchStatus") shouldBeEqualTo null
    }

    @Test
    fun setBatchId_WithSpecificBatch() {
        viewModel.setBatchId("BatchID")
        getPrivateField("batchId") shouldBeEqualTo "BatchID"
    }

    @Test
    fun setBatchStatus_WithSpecificBatch() {
        viewModel.setBatchStatus(BatchStatus.IN_BACKLOG)
        getPrivateField("batchStatus") shouldBeEqualTo BatchStatus.IN_BACKLOG
    }

    @Test
    fun changeToolbarSubtitle_WithSomeText() {
        viewModel.changeToolbarSubtitle("Subtitle")
        viewModel.toolbarViewModel.subtitle.get() shouldBeEqualTo "Subtitle"
    }

    @Test
    fun getTotalViewCount_WillReturnShipmentSize() {
        viewModel.totalViewCount shouldBeEqualTo viewModel.shipments.size
    }

    @Test
    fun cacheFragment_shouldReturnTrue() {
        viewModel.cacheFragment().shouldBeTrue()
    }

    @Test
    fun getPageTitleAtPosition_shouldReturn_EmptyString() {
        val position = 2
        viewModel.getPageTitleAtPosition(position) shouldBeEqualTo context.getString(R.string.box_title).format(position + 1)
    }

    @Test
    fun getItemTypeAtPosition_ShouldReturnQCDetailFragment_WithSpecificRackTitle() {
        val shipment = MockProvider.shipment()
        viewModel.shipments.add(shipment)

        viewModel.getItemTypeAtPosition(0) shouldBeInstanceOf PhotoDetailItemFragment::class
    }

    @Test
    fun getItemTypeAtPosition_ShouldReturnQCDetailFragment() {
        val shipment: Shipment = mockk()
        every { shipment.items } returns emptyList()
        viewModel.shipments.add(shipment)

        viewModel.getItemTypeAtPosition(0) shouldBeInstanceOf PhotoDetailItemFragment::class
    }

    @Test
    fun fetchBatchDetail_ReturnSuccess_WithProductStatusIsPhotoQa() {
        every { dataService.fetchBatchDetail(any(), any()) } answers {
            val response: GraphQLData<Batch> = mockk()
            val batchItem = MockProvider.batchItem(ProductStatus.PHOTO_QA)
            val batch = MockProvider.batch(batchItem)
            every { response.data.result } returns batch
            secondArg<APICallback<GraphQLData<Batch>, String>>().onSuccess(response)
        }

        viewModel.fetchBatchDetail(null)
        verify(exactly = 1) { dataService.fetchBatchDetail(any(), any()) }

        viewModel.toolbarViewModel.title.get() shouldBeEqualTo "Batch A - Photo"
        viewModel.toolbarViewModel.subtitle.get() shouldBeEqualTo "1 of 1 box"
    }

    @Test
    fun fetchBatchDetail_ReturnSuccess_WithProductStatusIsAvailable() {
        every { dataService.fetchBatchDetail(any(), any()) } answers {
            val response: GraphQLData<Batch> = mockk()
            val batchItem = MockProvider.batchItem(ProductStatus.AVAILABLE)
            val batch = MockProvider.batch(batchItem)
            every { response.data.result } returns batch
            secondArg<APICallback<GraphQLData<Batch>, String>>().onSuccess(response)
        }

        viewModel.fetchBatchDetail(null)
        verify(exactly = 1) { dataService.fetchBatchDetail(any(), any()) }

        viewModel.toolbarViewModel.title.get() shouldBeEqualTo "Batch A - Photo"
        viewModel.toolbarViewModel.subtitle.get() shouldBeEqualTo "empty picked box"
    }

    @Test
    fun fetchBatchDetail_ReturnError() {
        every { dataService.fetchBatchDetail(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Batch>, String>>().onError(null)
        }

        viewModel.fetchBatchDetail(null)
        verify(exactly = 1) { dataService.fetchBatchDetail(any(), any()) }

    }

    @Test
    fun completeBatch_ReturnSuccess() {
        val callbackMock = mockk<Result<Void?, String?>>()
        every { callbackMock.success(any()) } answers { nothing }
        every { dataService.completeBatch(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Batch>, String>>().onSuccess(null)
        }

        viewModel.completeBatch(null)
        viewModel.completeBatch(callbackMock)

        verify(exactly = 2) { dataService.completeBatch(any(), any()) }
        verify(exactly = 1) { callbackMock.success(null) }
    }

    @Test
    fun completeBatch_ReturnError() {
        val callbackMock = mockk<Result<Void?, String?>>()
        every { callbackMock.failure(any()) } answers { nothing }
        every { dataService.completeBatch(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Batch>, String>>().onError(null)
        }

        viewModel.completeBatch(null)
        viewModel.completeBatch(callbackMock)

        verify(exactly = 2) { dataService.completeBatch(any(), any()) }
        verify(exactly = 1) { callbackMock.failure(any()) }
    }

    @Test
    fun isAllBatchItemShipmentCompletedPhoto_ReturnFalse_BecauseEmptyShipments() {
        viewModel.isAllBatchItemShipmentCompletedPhoto().shouldBeFalse()
    }

    @Test
    fun isAllBatchItemShipmentCompletedPhoto_ReturnFalse_BecauseEmptyShipmentItemIsNotCompleteYet() {
        val shipment = MockProvider.shipment()

        viewModel.shipments.add(shipment)
        viewModel.isAllBatchItemShipmentCompletedPhoto().shouldBeFalse()
    }

    @Test
    fun isAllBatchItemShipmentCompletedPhoto_ReturnTrue_BecauseEmptyShipmentItemIsComplete() {
        val batchItem = MockProvider.batchItem(ProductStatus.QA_PASSED)
        val shipment = MockProvider.shipmentComplete(listOf(batchItem))

        viewModel.shipments.add(shipment)
        viewModel.isAllBatchItemShipmentCompletedPhoto().shouldBeTrue()
    }

    @Test
    fun isAllBatchItemShipmentCompletedPhoto_ReturnTrue_BecauseEmptyShipmentItemIsCompleteQABox() {
        val batchItem = MockProvider.batchItem(ProductStatus.QA_PASSED)
        val shipment = MockProvider.shipment(listOf(batchItem))

        viewModel.shipments.add(shipment)
        viewModel.isAllBatchItemShipmentCompletedPhoto().shouldBeTrue()
    }

    @Test
    fun isAllBatchItemShipmentCompletedPhoto_ReturnTrue() {
        val batchItem = MockProvider.batchItem(ProductStatus.QA_PASSED)
        val shipment = MockProvider.shipmentComplete(listOf(batchItem))

        viewModel.shipments.add(shipment)
        viewModel.isAllBatchItemShipmentCompletedPhoto().shouldBeTrue()
    }
}