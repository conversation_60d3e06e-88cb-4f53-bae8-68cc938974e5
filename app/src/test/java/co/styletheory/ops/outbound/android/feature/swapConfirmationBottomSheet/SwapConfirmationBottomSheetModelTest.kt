package co.styletheory.ops.outbound.android.feature.swapConfirmationBottomSheet

import android.view.View
import co.styletheory.android.network.core.APICallback
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.swapConfirmationBottomSheet.event.SwapConfirmationBottomDialogNetworkEvent
import co.styletheory.ops.outbound.android.feature.swapConfirmationBottomSheet.event.SwapConfirmationBottomDialogUIEvent
import co.styletheory.ops.outbound.android.feature.swapConfirmationBottomSheet.viewModel.impl.SwapConfirmationBottomSheetViewModelImpl
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.model.enums.SwapType
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import org.amshove.kluent.shouldBeEqualTo
import org.junit.Test

class SwapConfirmationBottomSheetModelTest: BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: SwapConfirmationBottomSheetViewModelImpl

    @Test
    fun default_state() {
        viewModel.apply {
            loadingStateVisibility.get() shouldBeEqualTo View.GONE
            oldItemImage.get() shouldBeEqualTo ""
            oldItemName.get() shouldBeEqualTo ""
            oldItemDesignerName.get() shouldBeEqualTo ""
            oldItemSizeNote.get() shouldBeEqualTo ""
            oldItemStyleCategory.get() shouldBeEqualTo ""
            newItemImage.get() shouldBeEqualTo ""
            newItemName.get() shouldBeEqualTo ""
            newItemDesignerName.get() shouldBeEqualTo ""
            newItemSizeNote.get() shouldBeEqualTo ""
            newItemStyleCategory.get() shouldBeEqualTo ""
        }
    }

    @Test
    fun setItems() {
        val oldItem = SwapItem(MockProvider.batchItem(), "boxid", "shipmentid")
        val newItem = MockProvider.batchItem()

        viewModel.setItems(oldItem, newItem, SwapType.ACCURACY_SWAP.swapType, "AAA")

        viewModel.apply {
            oldItemImage.get() shouldBeEqualTo "Url"
            oldItemName.get() shouldBeEqualTo oldItem.item?.style?.name
            oldItemDesignerName.get() shouldBeEqualTo oldItem.item?.style?.designer?.name
            oldItemSizeNote.get() shouldBeEqualTo "XS [9]"
            oldItemStyleCategory.get() shouldBeEqualTo "dress"

            newItemImage.get() shouldBeEqualTo "Url"
            newItemName.get() shouldBeEqualTo newItem.style?.name
            newItemDesignerName.get() shouldBeEqualTo newItem.style?.designer?.name
            newItemSizeNote.get() shouldBeEqualTo "XS [9]"
            newItemStyleCategory.get() shouldBeEqualTo "dress"
        }

        getPrivateFieldWithType<String>(viewModel, "oldItemBoxId") shouldBeEqualTo "boxid"
        getPrivateFieldWithType<String>(viewModel, "oldItemShipmentId") shouldBeEqualTo "shipmentid"
    }

    @Test
    fun setItems_nullStyle() {
        val oldItem = SwapItem(MockProvider.batchItem().copy(style = null), "boxid", "shipmentid")
        val newItem = MockProvider.batchItem().copy(style = null)

        viewModel.setItems(oldItem, newItem, SwapType.ACCURACY_SWAP.swapType, "AAA")

        viewModel.apply {
            oldItemImage.get() shouldBeEqualTo ""
            oldItemName.get() shouldBeEqualTo ""
            oldItemDesignerName.get() shouldBeEqualTo ""

            newItemImage.get() shouldBeEqualTo ""
            newItemName.get() shouldBeEqualTo ""
            newItemDesignerName.get() shouldBeEqualTo ""
        }

        getPrivateFieldWithType<String>(viewModel, "oldItemBoxId") shouldBeEqualTo "boxid"
        getPrivateFieldWithType<String>(viewModel, "oldItemShipmentId") shouldBeEqualTo "shipmentid"
    }

    @Test
    fun onConfirmButtonClick() {
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        viewModel.setItems(SwapItem(), MockProvider.batchItem(), SwapType.ACCURACY_SWAP.swapType, "AAA")

        viewModel.onConfirmButtonClick()

        verify { viewModel["showLoadingState"]() }
        verify { viewModel["confirmSwap"]() }
    }

    @Test
    fun showLoadingState() {
        callPrivateFun(viewModel, "showLoadingState")

        viewModel.loadingStateVisibility.get() shouldBeEqualTo View.VISIBLE
        viewModel.itemFoundVisibility.get() shouldBeEqualTo View.GONE
    }

    @Test
    fun showItemFoundState() {
        callPrivateFun(viewModel, "showItemFoundState")
        viewModel.loadingStateVisibility.get() shouldBeEqualTo View.GONE
        viewModel.itemFoundVisibility.get() shouldBeEqualTo View.VISIBLE
    }

    @Test
    fun showSwapInfo_accuracySwap() {
        viewModel.swapType = SwapType.ACCURACY_SWAP.swapType
        callPrivateFun(viewModel, "showItemFoundState")
        viewModel.swapInformation.get() shouldBeEqualTo R.string.item_found_swap_note
    }

    @Test
    fun showSwapInfo_qualitySwap() {
        viewModel.swapType = SwapType.QUALITY_SWAP.swapType
        callPrivateFun(viewModel, "showItemFoundState")
        viewModel.swapInformation.get() shouldBeEqualTo R.string.swap_by_scan_qc_info
    }

    @Test
    fun confirmSwap_accuracySwap() {
        viewModel.dataService = dataService
        viewModel.setItems(SwapItem(), MockProvider.batchItem(), SwapType.ACCURACY_SWAP.swapType, "AAA")

        every { dataService.accuracySwapItem(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Void>, String>>().onSuccess(mockk())
        }

        callPrivateFun(viewModel, "confirmSwap")

        verify { eventBus.post(SwapConfirmationBottomDialogNetworkEvent.OnSwapSuccess) }
    }

    @Test
    fun swapItemInBox_qualitySwap() {
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        viewModel.dataService = dataService
        viewModel.setItems(SwapItem(), MockProvider.batchItem(), SwapType.QUALITY_SWAP.swapType, "AAA")

        callPrivateFun(viewModel, "confirmSwap")
    }

    @Test
    fun swapItemInShipment_qualitySwap() {
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        viewModel.dataService = dataService
        viewModel.setItems(SwapItem(), MockProvider.batchItem(), SwapType.QUALITY_SWAP.swapType, "<EMAIL>")

        every { dataService.swapItem(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Void>, String>>().onSuccess(mockk())
        }

        callPrivateFun(viewModel, "callSwapItemInShipment")

        verify { viewModel["callSwapItemInShipment"]() }
    }

    @Test
    fun confirmSwap_accuracySwap_failure() {
        val eventSlot = slot<SwapConfirmationBottomDialogNetworkEvent.OnSwapFailed>()
        viewModel.setItems(SwapItem(), MockProvider.batchItem(), SwapType.ACCURACY_SWAP.swapType, "AAA")
        viewModel.dataService = dataService
        viewModel.errorResponse = errorResponse

        every { errorResponse.getErrorBodyDescription("error") } returns "error"
        every { dataService.accuracySwapItem(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Void>, String>>().onError("error")
        }

        callPrivateFun(viewModel, "confirmSwap")

        verify { eventBus.post(capture(eventSlot)) }
        eventSlot.captured.errorMessage shouldBeEqualTo "error"
    }

    @Test
    fun swapItemInBox_qualitySwap_failure() {
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        val eventSlot = slot<SwapConfirmationBottomDialogNetworkEvent.OnSwapFailed>()
        viewModel.setItems(SwapItem(), MockProvider.batchItem(), SwapType.QUALITY_SWAP.swapType, "<EMAIL>")
        viewModel.dataService = dataService
        viewModel.errorResponse = errorResponse

        every { errorResponse.getErrorBodyDescription("error") } returns "error"
        every { dataService.swapBoxItem(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Void>, String>>().onError("error")
        }

        callPrivateFun(viewModel, "callSwapItemInBox", MockProvider.batchItem())

        verify { eventBus.post(capture(eventSlot)) }
        eventSlot.captured.errorMessage shouldBeEqualTo "error"
    }

    @Test
    fun swapItemInShipment_qualitySwap_failure() {
        val eventSlot = slot<SwapConfirmationBottomDialogNetworkEvent.OnSwapFailed>()
        viewModel.setItems(SwapItem(), MockProvider.batchItem(), SwapType.QUALITY_SWAP.swapType, "<EMAIL>")
        viewModel.dataService = dataService
        viewModel.errorResponse = errorResponse

        every { errorResponse.getErrorBodyDescription("error") } returns "error"
        every { dataService.swapItem(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Void>, String>>().onError("error")
        }

        callPrivateFun(viewModel, "callSwapItemInShipment")

        verify { eventBus.post(capture(eventSlot)) }
        eventSlot.captured.errorMessage shouldBeEqualTo "error"
    }

    @Test
    fun onCloseClick() {
        viewModel.onCloseClick()

        verify { eventBus.post(SwapConfirmationBottomDialogUIEvent.OnCloseClick) }
    }

    @Test
    fun updateStatusCallback_onSuccessBatchItem() {
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        viewModel.dataService = dataService
        viewModel.setItems(SwapItem(), MockProvider.batchItem(), SwapType.QUALITY_SWAP.swapType, "AAA")

        every { dataService.updateBatchItemStatus(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Void>, String>>().onSuccess(mockk())
        }

        callPrivateFun(viewModel, "confirmSwap")
    }

    @Test
    fun updateStatusCallback_onError() {
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        viewModel.dataService = dataService
        viewModel.setItems(SwapItem(), MockProvider.batchItem(), SwapType.QUALITY_SWAP.swapType, "AAA")

        every { dataService.updateBatchItemStatus(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Void>, String>>().onError("")
        }

        callPrivateFun(viewModel, "confirmSwap")
        verify { eventBus.post(any<SwapConfirmationBottomDialogNetworkEvent.OnSwapFailed>()) }
    }

    @Test
    fun updateStatusCallback_onErrorNull() {
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        viewModel.dataService = dataService
        viewModel.setItems(SwapItem(), MockProvider.batchItem(), SwapType.QUALITY_SWAP.swapType, "AAA")

        every { dataService.updateBatchItemStatus(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Void>, String>>().onError(null)
        }

        callPrivateFun(viewModel, "confirmSwap")
        verify { eventBus.post(any<SwapConfirmationBottomDialogNetworkEvent.OnSwapFailed>()) }
    }
}