package co.styletheory.ops.outbound.android.feature.photoManager

import co.styletheory.ops.outbound.android.BaseTest
import co.styletheory.ops.outbound.android.feature.photoManager.viewModel.AttachImageViewModel
import co.styletheory.ops.outbound.android.general.event.PreviewImageEvent
import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.times
import com.nhaarman.mockitokotlin2.verify
import org.junit.Assert.assertTrue
import org.junit.Test
import org.mockito.InjectMocks

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 07 March 2018.
 * Description
 *
 * <EMAIL>
 */
class AttachImageViewModelTest : BaseTest() {

    @InjectMocks
    lateinit var viewModel: AttachImageViewModel

    @Test
    fun initialState() {
        assertTrue(viewModel.imageUrl.isEmpty())
    }

    @Test
    fun previewImage() {
        viewModel.previewImage()
        verify(eventBus, times(1)).post(any<PreviewImageEvent>())
    }
}