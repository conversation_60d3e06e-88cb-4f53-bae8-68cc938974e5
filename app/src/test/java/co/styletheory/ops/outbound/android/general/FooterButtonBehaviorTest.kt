package co.styletheory.ops.outbound.android.general

import co.styletheory.ops.outbound.android.BaseBehaviorTest
import co.styletheory.ops.outbound.android.viewModelComponent.FooterButtonViewModel
import io.kotlintest.Spec
import io.kotlintest.extensions.TopLevelTest
import io.mockk.spyk
import io.mockk.verify

/**
 * Created by <PERSON> on 02/12/21.
 */
class FooterButtonBehaviorTest : BaseBehaviorTest<FooterButtonViewModel>(FooterButtonViewModel()) {

    override fun beforeSpecClass(spec: Spec, tests: List<TopLevelTest>) {
        super.beforeSpecClass(spec, tests)
        viewModel = spyk(viewModel, recordPrivateCalls = true)
    }

    init {
        given("I see Footer Button") {
            `when`("I click Footer Button") {
                viewModel.footerButtonClick()

                then("I would be able to click <PERSON><PERSON> <PERSON><PERSON>") {
                    verify(exactly = 1) {
                        eventBus.post(any<FooterButtonViewModel.CompleteButtonEvent>())
                    }
                }
            }
        }

    }
}