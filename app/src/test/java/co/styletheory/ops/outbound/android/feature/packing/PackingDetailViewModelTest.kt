package co.styletheory.ops.outbound.android.feature.packing

import co.styletheory.android.network.core.APICallback
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.feature.packing.event.PackingItemUIEvent
import co.styletheory.ops.outbound.android.feature.packing.view.PackingDetailItemFragment
import co.styletheory.ops.outbound.android.feature.packing.viewModel.impl.PackingDetailViewModelImpl
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.amshove.kluent.*
import org.junit.Test

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 19 December 2017.
 * Description
 *
 * <EMAIL>
 */
class PackingDetailViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: PackingDetailViewModelImpl

    @InjectMockKs
    lateinit var toolbarVM: GeneralToolbarViewModel

    override fun setup() {
        super.setup()
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        viewModel.toolbarViewModel = toolbarVM
    }

    @Test
    fun firstState() {
        viewModel.batch shouldBeEqualTo null
        viewModel.batchStatus shouldBeEqualTo null
        viewModel.batchId.shouldBeEmpty()
        viewModel.scannedRfidText.get().shouldBeEmpty()
        viewModel.shipments.size shouldBeEqualTo 0
        viewModel.toolbarViewModel.title.get().shouldBeEmpty()
        viewModel.toolbarViewModel.subtitle.get().shouldBeEmpty()
        viewModel.generalToolbarViewModel() shouldBeEqualTo viewModel.toolbarViewModel
    }

    @Test
    fun checkFetchBatchDetail() {
        viewModel.batchId = "1"
        viewModel.fetchBatchDetail(null)
        verify(exactly = 1) { dataService.fetchBatchDetail(any(), any()) }
    }

    @Test
    fun checkCompleteBatch() {
        viewModel.completeBatch(null)
        verify(exactly = 1) { dataService.completeBatch(any(), any()) }
    }

    @Test
    fun checkChangeSubtitle() {
        val subtitle = "subtitle"
        viewModel.changeToolbarSubtitle(subtitle)
        viewModel.toolbarViewModel.subtitle.get() shouldBeEqualTo subtitle
    }

    @Test
    fun checkIsAllBatchItemShipmentCompletedPhoto() {
        viewModel.isAllBatchItemShipmentCompletedPacking().shouldBeFalse()
    }

    @Test
    fun checkIsAllBatchItemShipmentCompletedPhoto_ItemNotComplete() {
        val shipments = MockProvider.shipment(listOf(MockProvider.batchItem(ProductStatus.PACKING)))
        viewModel.shipments.add(shipments)
        viewModel.isAllBatchItemShipmentCompletedPacking().shouldBeFalse()
    }

    @Test
    fun checkIsAllBatchItemShipmentCompletedPhoto_ItemComplete() {
        val shipments = MockProvider.shipmentComplete(listOf(MockProvider.batchItem(ProductStatus.PACKING)))
        viewModel.shipments.add(shipments)
        viewModel.isAllBatchItemShipmentCompletedPacking().shouldBeTrue()
    }

    @Test
    fun checkOnKeyTapped_rfidFound_isBarcodeSettingsOn() {
        val shipments = MockProvider.shipment(listOf(MockProvider.batchItem(ProductStatus.PACKING)))
        viewModel.shipments.add(shipments)
        viewModel.scannedRfidText.set(MockProvider.getScannedRfidTag())
        every { featureFlagUtil.isBarcodeSettingIsOn() } returns true

        viewModel.onKeyTapped()

        verify { eventBus.post(any<PackingItemUIEvent.OnScannedRfidFoundPosition>()) }
    }

    @Test
    fun checkOnKeyTapped_rfidNotFound_isBarcodeSettingsOn() {
        val shipments = MockProvider.shipment(listOf(MockProvider.batchItem(ProductStatus.PACKING)))
        viewModel.shipments.add(shipments)
        viewModel.scannedRfidText.set("019202830484")
        every { featureFlagUtil.isBarcodeSettingIsOn() } returns true

        viewModel.onKeyTapped()

        verify { eventBus.post(any<PackingItemUIEvent.OnScannedRfidTextNotFound>()) }
    }

    @Test
    fun checkScannedText_rfidFoundInBatch() {
        every { viewModel.scannedRfidText.get() } returns MockProvider.getScannedRfidTag()
        every { viewModel.shipments } returns mutableListOf(MockProvider.shipment(MockProvider.listOfBatchItem()))

        viewModel.checkScannedText()

        verify { viewModel.checkBatchItemStatus(any(), any()) }
        verify { eventBus.post(any<PackingItemUIEvent.OnScannedRfidFoundPosition>()) }
    }

    @Test
    fun checkScannedText_rfidNotFoundInBatch() {
        every { viewModel.scannedRfidText.get() } returns "123123123123"
        every { viewModel.shipments } returns mutableListOf(MockProvider.shipment(MockProvider.listOfBatchItem()))

        viewModel.checkScannedText()

        verify { eventBus.post(any<PackingItemUIEvent.OnScannedRfidTextNotFound>()) }
    }

    @Test
    fun checkBatchItemStatus_isNotInReadyTab() {
        every { viewModel.batch } returns MockProvider.batch(MockProvider.batchItem(ProductStatus.RENTED))
        viewModel.checkBatchItemStatus(0, MockProvider.getScannedRfidTag())
        verify { eventBus.post(any<PackingItemUIEvent.OnScannedRfidTextNotFound>()) }
    }

    @Test
    fun getTotalViewCount_WillReturnShipmentSize() {
        viewModel.totalViewCount shouldBeEqualTo viewModel.shipments.size
    }

    @Test
    fun cacheFragment_shouldReturnTrue() {
        viewModel.cacheFragment().shouldBeTrue()
    }

    @Test
    fun getPageTitleAtPosition_shouldReturn_EmptyString() {
        viewModel.getPageTitleAtPosition(2) shouldBeEqualTo ""
    }

    @Test
    fun getItemTypeAtPosition_ShouldReturnPackingDetailItemFragment_WithSpecificRackTitle() {
        val shipment = MockProvider.shipment()
        viewModel.shipments.add(shipment)

        viewModel.getItemTypeAtPosition(0) shouldBeInstanceOf PackingDetailItemFragment::class
    }

    @Test
    fun getItemTypeAtPosition_ShouldReturnPackingDetailItemFragment() {
        val shipment: Shipment = mockk()
        every { shipment.items } returns emptyList()
        viewModel.shipments.add(shipment)

        viewModel.getItemTypeAtPosition(0) shouldBeInstanceOf PackingDetailItemFragment::class
    }

    @Test
    fun getItemTypeAtPosition_ShouldReturnPackingDetailItemFragment_WithList() {
        val shipment: Shipment = mockk()
        every { shipment.items } returns listOf(MockProvider.batchItem())
        viewModel.shipments.add(shipment)

        viewModel.getItemTypeAtPosition(0) shouldBeInstanceOf PackingDetailItemFragment::class
    }

    @Test
    fun fetchBatchDetail_ReturnSuccess_WithProductStatusIsPhotoQa() {
        every { dataService.fetchBatchDetail(any(), any()) } answers {
            val response: GraphQLData<Batch> = mockk()
            val batchItem = MockProvider.batchItem(ProductStatus.PHOTO_QA)
            val batch = MockProvider.batch(batchItem)
            every { response.data.result } returns batch
            secondArg<APICallback<GraphQLData<Batch>, String>>().onSuccess(response)
        }

        viewModel.fetchBatchDetail(null)
        verify(exactly = 1) { dataService.fetchBatchDetail(any(), any()) }

        viewModel.toolbarViewModel.title.get() shouldBeEqualTo "Batch A - Packing"
        viewModel.toolbarViewModel.subtitle.get() shouldBeEqualTo "1 of 1 box"
    }

    @Test
    fun fetchBatchDetail_ReturnSuccess_WithProductStatusIsAvailable() {
        every { dataService.fetchBatchDetail(any(), any()) } answers {
            val response: GraphQLData<Batch> = mockk()
            val batchItem = MockProvider.batchItem(ProductStatus.AVAILABLE)
            val batch = MockProvider.batch(batchItem)
            every { response.data.result } returns batch
            secondArg<APICallback<GraphQLData<Batch>, String>>().onSuccess(response)
        }

        viewModel.fetchBatchDetail(null)
        verify(exactly = 1) { dataService.fetchBatchDetail(any(), any()) }

        viewModel.toolbarViewModel.title.get() shouldBeEqualTo "Batch A - Packing"
        viewModel.toolbarViewModel.subtitle.get() shouldBeEqualTo "1 of 1 box"
    }

    @Test
    fun fetchBatchDetail_ReturnSuccess_WithProductStatusIsPhotoQa_ButEmptyBox() {
        every { dataService.fetchBatchDetail(any(), any()) } answers {
            val response: GraphQLData<Batch> = mockk()
            val batchItem = MockProvider.batchItem(ProductStatus.PHOTO_QA)
            val batch = MockProvider.batchEmptyShipment(batchItem)
            every { response.data.result } returns batch
            secondArg<APICallback<GraphQLData<Batch>, String>>().onSuccess(response)
        }

        viewModel.fetchBatchDetail(null)
        verify(exactly = 1) { dataService.fetchBatchDetail(any(), any()) }

        viewModel.toolbarViewModel.title.get() shouldBeEqualTo "Batch A - Packing"
        viewModel.toolbarViewModel.subtitle.get() shouldBeEqualTo "Empty picked box"
    }

    @Test
    fun fetchBatchDetail_ReturnError() {
        val callbackMock = mockk<Result<Void?, String?>>()
        every { callbackMock.failure(any()) } answers { nothing }
        every { dataService.fetchBatchDetail(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Batch>, String>>().onError(null)
        }

        viewModel.fetchBatchDetail(null)
        viewModel.fetchBatchDetail(callbackMock)

        verify(exactly = 2) { dataService.fetchBatchDetail(any(), any()) }
        verify(exactly = 1) { callbackMock.failure(any()) }
    }

    @Test
    fun completeBatch_ReturnSuccess() {
        val callbackMock = mockk<Result<Void?, String?>>()
        every { callbackMock.success(any()) } answers { nothing }
        every { dataService.completeBatch(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Batch>, String>>().onSuccess(null)
        }

        viewModel.completeBatch(null)
        viewModel.completeBatch(callbackMock)

        verify(exactly = 2) { dataService.completeBatch(any(), any()) }
        verify(exactly = 1) { callbackMock.success(null) }
    }

    @Test
    fun completeBatch_ReturnError() {
        val callbackMock = mockk<Result<Void?, String?>>()
        every { callbackMock.failure(any()) } answers { nothing }
        every { dataService.completeBatch(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Batch>, String>>().onError(null)
        }

        viewModel.completeBatch(null)
        viewModel.completeBatch(callbackMock)

        verify(exactly = 2) { dataService.completeBatch(any(), any()) }
        verify(exactly = 1) { callbackMock.failure(any()) }
    }
}