package co.styletheory.ops.outbound.android.feature.batch

import co.styletheory.android.network.core.RequestResult
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.batch.event.BatchStepActionClickEvent
import co.styletheory.ops.outbound.android.feature.batch.viewModel.BatchStepViewModel
import co.styletheory.ops.outbound.android.general.event.DismissProgressDialogEvent
import co.styletheory.ops.outbound.android.general.event.ShowProgressDialogEvent
import co.styletheory.ops.outbound.android.general.event.ShowToastEvent
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.model.enums.StepType
import co.styletheory.ops.outbound.android.resources.BeginBatchResource
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.spyk
import io.mockk.verify
import org.amshove.kluent.shouldBeEqualTo
import org.junit.Test

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 15 December 2017.
 * Description
 *
 * <EMAIL>
 */
class BatchStepViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: BatchStepViewModel

    override fun setup() {
        super.setup()
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        viewModel.context = context
        viewModel.userStorage = userStorage
        viewModel.dataService = dataService
        viewModel.errorResponse = errorResponse
    }

    @Test
    fun firstState() {
        viewModel.apply {
            showButton.get() shouldBeEqualTo false
            showProgress.get() shouldBeEqualTo false
            buttonText.get() shouldBeEqualTo ""
            progressBy.get() shouldBeEqualTo ""
            pickerName.get() shouldBeEqualTo ""
            endTime.get() shouldBeEqualTo ""
            startTime.get() shouldBeEqualTo ""
            stepType.get() shouldBeEqualTo ""
            buttonText.get() shouldBeEqualTo ""
            batchStatus shouldBeEqualTo null
            batch shouldBeEqualTo null
        }
    }

    @Test
    fun checkBatchResourceType() {
        viewModel.apply {
            getBatchType(BatchStatus.IN_BACKLOG) shouldBeEqualTo BeginBatchResource.Type.PICK
            getBatchType(BatchStatus.PICKING) shouldBeEqualTo BeginBatchResource.Type.PICK
            getBatchType(BatchStatus.PICKED) shouldBeEqualTo BeginBatchResource.Type.QA
            getBatchType(BatchStatus.QA_PROCESSING) shouldBeEqualTo BeginBatchResource.Type.QA
            getBatchType(BatchStatus.QA_DONE) shouldBeEqualTo BeginBatchResource.Type.PHOTO
            getBatchType(BatchStatus.PHOTO) shouldBeEqualTo BeginBatchResource.Type.PHOTO
            getBatchType(BatchStatus.PHOTO_QA_DONE) shouldBeEqualTo BeginBatchResource.Type.PACKING
            getBatchType(BatchStatus.PACKING) shouldBeEqualTo BeginBatchResource.Type.PACKING
            getBatchType(BatchStatus.READY_FOR_PACKING) shouldBeEqualTo BeginBatchResource.Type.PACKING
        }
    }

    @Test
    fun checkHandleStepActionShown() {
        every { userStorage.getUserEmail() } returns "<EMAIL>"
        every { context.getString(R.string.continue_label) } returns "Continue"
        every { context.getString(R.string.progress_by_label) } returns "Progress By"
        every { context.getString(R.string.join_label) } returns "Join"

        viewModel.setEmail("")
        viewModel.buttonText.set("")
        viewModel.progressBy.set("")
        viewModel.handleActionStep()
        viewModel.showProgress.get() shouldBeEqualTo false
        viewModel.showButton.get() shouldBeEqualTo true
        viewModel.buttonText.get() shouldBeEqualTo "Join"

        viewModel.setEmail("<EMAIL>")
        viewModel.buttonText.set("")
        viewModel.progressBy.set("")
        viewModel.handleActionStep()
        viewModel.showProgress.get() shouldBeEqualTo false
        viewModel.showButton.get() shouldBeEqualTo true
        viewModel.buttonText.get() shouldBeEqualTo "Continue"

        viewModel.emails.clear()
        viewModel.setEmail("<EMAIL>")
        viewModel.buttonText.set("")
        viewModel.progressBy.set("")
        viewModel.handleActionStep()
        viewModel.showButton.get() shouldBeEqualTo true
        viewModel.buttonText.get() shouldBeEqualTo "Join"
    }

    @Test
    fun checkStepButtonClick_withSameEmail() {
        viewModel.setEmail("<EMAIL>")
        every { userStorage.getUserEmail() } returns "<EMAIL>"
        viewModel.stepActionButtonClick()
        verify { eventBus.post(any<BatchStepActionClickEvent>()) }
    }

    @Test
    fun checkStepButtonClick_withSameEmail_onSuccess() {
        viewModel.setEmail("<EMAIL>")
        viewModel.batch = Batch(id = "idBatch")
        every { userStorage.getUserEmail() } returns "<EMAIL>"
        every { dataService.beginBatch(any(), any()) } answers {
            secondArg<RequestResult<GraphQLData<Void>, String>>().onSuccess(null)
        }
        viewModel.stepActionButtonClick()
        verify(exactly = 1) {
            eventBus.post(any<DismissProgressDialogEvent>())
            eventBus.post(any<BatchStepActionClickEvent>())
        }
    }

    @Test
    fun checkStepButtonClick_withSameEmail_onError() {
        viewModel.setEmail("<EMAIL>")
        every { userStorage.getUserEmail() } returns "<EMAIL>"
        every { dataService.beginBatch(any(), any()) } answers {
            secondArg<RequestResult<GraphQLData<Void>, String>>().onError(null)
        }
        viewModel.stepActionButtonClick()
        verify(exactly = 1) {
            eventBus.post(any<DismissProgressDialogEvent>())
            eventBus.post(any<ShowToastEvent>())
        }
    }

    @Test
    fun checkStepButtonClick_withDifferentEmail() {
        viewModel.setEmail("<EMAIL>")
        viewModel.setBatchStatus(BatchStatus.IN_BACKLOG)
        every { userStorage.getUserEmail() } returns "<EMAIL>"
        viewModel.stepActionButtonClick()
        verify {
            dataService.beginBatch(any(), any())
            eventBus.post(any<ShowProgressDialogEvent>())
        }
    }

    @Test
    fun checkBindViewModel() {
        viewModel.apply {
            bindViewModel(
                    StepType.PICKER,
                    mutableListOf("Apri"),
                    mutableListOf("<EMAIL>"),
                    actionText = "Take",
                    showAction = false
            )

            pickerName.get() shouldBeEqualTo "Apri"
            emails.contains("<EMAIL>") shouldBeEqualTo true
            startTime.get() shouldBeEqualTo "-"
            endTime.get() shouldBeEqualTo "-"
            buttonText.get() shouldBeEqualTo "Take"
            stepType.get() shouldBeEqualTo StepType.PICKER.text
        }
    }

    @Test
    fun checkBindViewModel_showAction() {
        viewModel.apply {
            bindViewModel(
                    StepType.PICKER,
                    mutableListOf(),
                    mutableListOf("<EMAIL>"),
                    start = "15:00",
                    end = "20:00",
                    actionText = "Take",
                    showAction = true
            )

            pickerName.get() shouldBeEqualTo "-"
            emails.contains("<EMAIL>") shouldBeEqualTo true
            startTime.get() shouldBeEqualTo "15:00"
            endTime.get() shouldBeEqualTo "20:00"
            buttonText.get() shouldBeEqualTo ""
            stepType.get() shouldBeEqualTo StepType.PICKER.text
            verify(exactly = 1) { viewModel.handleActionStep() }
        }
    }
    
    @Test
    fun batchStatus() {
        viewModel.setBatchStatus(BatchStatus.IN_BACKLOG)
        viewModel.batchStatus shouldBeEqualTo BatchStatus.IN_BACKLOG
    }
}
