package co.styletheory.ops.outbound.android.main

import android.view.View
import co.styletheory.android.network.core.APICallback
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.BaseBehaviorTest
import co.styletheory.ops.outbound.android.general.auth.event.SignOutFailedEvent
import co.styletheory.ops.outbound.android.general.auth.event.SignOutSuccessEvent
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.main.viewModel.MainViewModelImpl
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.util.AppConstant
import co.styletheory.ops.outbound.android.util.ErrorResponse
import io.kotlintest.Spec
import io.kotlintest.extensions.TopLevelTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.amshove.kluent.shouldBeEqualTo

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019-09-20.
 */
class MainBehaviorTest : BaseBehaviorTest<MainViewModelImpl>(MainViewModelImpl()) {

    lateinit var userStorage: UserStorage
    lateinit var dataService: DataService
    lateinit var errorResponse: ErrorResponse

    override fun beforeSpecClass(spec: Spec, tests: List<TopLevelTest>) {
        super.beforeSpecClass(spec, tests)
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        userStorage = mockk(relaxed = true)
        dataService = mockk(relaxed = true)
        errorResponse = mockk(relaxed = true)

        viewModel.userStorage = userStorage
        viewModel.dataService = dataService
        viewModel.errorResponse = errorResponse
    }

    init {
        given("I open the application with user region is Singapore/Hongkong") {
            every { userStorage.isUserRegionID() } returns false
            `when`("in first tab") {
                val position = 0

                viewModel.getTitleTabAtIndex(position)

                then("I'll see the Picking Tab") {
                    viewModel.getTitleTabAtIndex(position) shouldBeEqualTo AppConstant.MAIN_TAB.PICKING_TAB
                }
            }

            `when`("I swipe to second tab") {
                val position = 1

                viewModel.getTitleTabAtIndex(position)

                then("I'll see the QC Tab") {
                    viewModel.getTitleTabAtIndex(position) shouldBeEqualTo AppConstant.MAIN_TAB.QC_TAB
                }
            }

            `when`("I swipe to third tab") {
                val position = 2

                viewModel.getTitleTabAtIndex(position)

                then("I'll see the Packing Tab") {
                    viewModel.getTitleTabAtIndex(position) shouldBeEqualTo AppConstant.MAIN_TAB.PACKING_TAB
                }
            }

            `when`("I swipe to fourth tab") {
                val position = 3

                viewModel.getTitleTabAtIndex(position)

                then("I'll see the Packed Tab") {
                    viewModel.getTitleTabAtIndex(position) shouldBeEqualTo AppConstant.MAIN_TAB.PACKED_TAB
                }
            }
        }

        given("I open the application with user region is Indonesia apparel") {
            every { userStorage.isUserRegionID() } returns true

            `when`("in first tab") {
                val position = 0

                viewModel.getTitleTabAtIndex(position)

                then("I'll see the Picking Tab") {
                    viewModel.getTitleTabAtIndex(position) shouldBeEqualTo AppConstant.MAIN_TAB.PICKING_TAB
                }
            }

            `when`("I swipe to second tab") {
                val position = 1

                viewModel.getTitleTabAtIndex(position)

                then("I'll see the QC Tab") {
                    viewModel.getTitleTabAtIndex(position) shouldBeEqualTo AppConstant.MAIN_TAB.QC_TAB
                }
            }

            `when`("I swipe to third tab") {
                val position = 2

                viewModel.getTitleTabAtIndex(position)

                then("I'll see the Photo Tab") {
                    viewModel.getTitleTabAtIndex(position) shouldBeEqualTo AppConstant.MAIN_TAB.PHOTO_TAB
                }
            }

            `when`("I swipe to fourth tab") {
                val position = 3

                viewModel.getTitleTabAtIndex(position)

                then("I'll see the Packing Tab") {
                    viewModel.getTitleTabAtIndex(position) shouldBeEqualTo AppConstant.MAIN_TAB.PACKING_TAB
                }
            }

            `when`("I swipe to fifth tab") {
                val position = 4

                viewModel.getTitleTabAtIndex(position)

                then("I'll see the Packed Tab") {
                    viewModel.getTitleTabAtIndex(position) shouldBeEqualTo AppConstant.MAIN_TAB.PACKED_TAB
                }
            }
        }

        given("I open the application and logout") {
            `when`("Success Logout") {
                every { dataService.logout(any(), any()) } answers {
                    val response = mockk<GraphQLData<Boolean>>()
                    secondArg<APICallback<GraphQLData<Boolean>, String>>().onSuccess(response)
                }

                viewModel.doSignOut()

                then("Should post sign out success event bust and clear storage") {
                    verify { eventBus.post(any<SignOutSuccessEvent>()) }
                    verify { userStorage.clearUserStorage() }
                }
            }

            `when`("Error Logout") {
                every { dataService.logout(any(), any()) } answers {
                    secondArg<APICallback<GraphQLData<Boolean>, String>>().onError(null)
                }

                viewModel.doSignOut()

                then("Should post sign out failed event bust") {
                    verify { eventBus.post(any<SignOutFailedEvent>()) }
                }
            }
        }

        given("The Snowfall Flag is ON") {
            every { featureFlagUtil.snowfallVisibility.get() } returns View.VISIBLE
            When("I open the application") {
                viewModel.setupSnow()
                Then("I should see the snowflakes are falling") {
                    viewModel.snowVisibility.get() shouldBeEqualTo View.VISIBLE
                }
            }
        }

        given("The Snowfall Flag is OFF") {
            every { featureFlagUtil.snowfallVisibility.get() } returns View.GONE
            When("I open the application") {
                viewModel.setupSnow()
                Then("I should not see the snowflakes are falling") {
                    viewModel.snowVisibility.get() shouldBeEqualTo View.GONE
                }
            }
        }
    }
}

