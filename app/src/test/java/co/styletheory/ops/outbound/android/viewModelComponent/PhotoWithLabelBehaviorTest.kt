package co.styletheory.ops.outbound.android.viewModelComponent

import android.view.View
import co.styletheory.ops.outbound.android.BaseBehaviorTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.general.event.PreviewImageEvent
import io.mockk.verify
import org.amshove.kluent.shouldBeEqualTo

/**
 * Created by <PERSON> on 21/10/20.
 */
class PhotoWithLabelBehaviorTest : BaseBehaviorTest<PhotoWithLabelViewModel>(PhotoWithLabelViewModel()) {

    init {
        given("I see photo section") {
            `when`("item image is not found and item is not on sale") {
                viewModel.bindPhotoWithLabel("", null)

                then("I should not see label on photo") {
                    viewModel.imageUrl.get() shouldBeEqualTo ""
                    viewModel.resellingInventoryTypeVisibility.get() shouldBeEqualTo View.GONE
                }
            }

            `when`("item image is found and item is not on sale") {
                viewModel.bindPhotoWithLabel("image", null)

                then("I should not see label on photo") {
                    viewModel.imageUrl.get() shouldBeEqualTo "image"
                    viewModel.resellingInventoryTypeVisibility.get() shouldBeEqualTo View.GONE
                }
            }

            `when`("item is not on sale") {
                viewModel.bindPhotoWithLabel("image", "normal")

                then("I should not see label on photo") {
                    viewModel.imageUrl.get() shouldBeEqualTo "image"
                    viewModel.resellingInventoryTypeVisibility.get() shouldBeEqualTo View.GONE
                }
            }

            `when`("item is on sale") {
                viewModel.bindPhotoWithLabel("", "warehouse")

                then("I should see label on photo") {
                    viewModel.apply {
                        imageUrl.get() shouldBeEqualTo ""
                        resellingInventoryTypeVisibility.get() shouldBeEqualTo View.VISIBLE
                        photoLabelBgColor.get() shouldBeEqualTo R.drawable.bg_orange_top_radius_4
                        resellingInventoryType.get() shouldBeEqualTo R.string.warehouse_sale_label
                    }
                }
            }

            `when`("I click photo") {
                viewModel.imageClick()

                then("Preview image should show") {
                    verify { eventBus.post(any<PreviewImageEvent>()) }
                }
            }

        }
    }

}