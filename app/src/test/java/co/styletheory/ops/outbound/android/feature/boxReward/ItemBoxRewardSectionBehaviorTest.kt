package co.styletheory.ops.outbound.android.feature.boxReward

import co.styletheory.ops.outbound.android.BaseBehaviorTest
import co.styletheory.ops.outbound.android.feature.boxRewardSection.event.BoxRewardSectionUIEvent
import co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.impl.ItemBoxRewardSectionViewModelImpl
import co.styletheory.ops.outbound.android.general.MockProvider
import io.kotlintest.Spec
import io.kotlintest.extensions.TopLevelTest
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.amshove.kluent.shouldBeEqualTo

/**
 * Created by <PERSON> on 12/2/20.
 */
class ItemBoxRewardSectionBehaviorTest : BaseBehaviorTest<ItemBoxRewardSectionViewModelImpl>(ItemBoxRewardSectionViewModelImpl()) {

    override fun beforeSpecClass(spec: Spec, tests: List<TopLevelTest>) {
        super.beforeSpecClass(spec, tests)
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        viewModel.boxReward = mockk(relaxed = true)
    }

    init {
        given("I see box reward section") {
            `when`("I see reward item") {
                val reward = MockProvider.boxReward()
                viewModel.bindBoxReward(reward)

                then("I should see list of reward item") {
                    viewModel.apply {
                        rewardName.get() shouldBeEqualTo reward.title
                        isRewardItemChecked.get() shouldBeEqualTo viewModel.isRewardItemChecked.get()
                        isRewardItemCheckedEnabled.get() shouldBeEqualTo true
                    }
                }
            }
        }

        given("I already put item reward in the box") {
            `when`("I uncheck list the item I put in the box") {
                viewModel.isRewardItemChecked.set(true)
                viewModel.onRewardItemClicked()

                then("I should see checklist color change to green color") {
                    viewModel.isRewardItemChecked.get() shouldBeEqualTo false
                    verify { eventBus.post(any<BoxRewardSectionUIEvent.OnRewardClicked>()) }
                }
            }

            `when`("I check list the item I put in the box") {
                viewModel.isRewardItemChecked.set(false)
                viewModel.onRewardItemClicked()

                then("I should see checklist color change to green color") {
                    viewModel.isRewardItemChecked.get() shouldBeEqualTo true
                    verify { eventBus.post(any<BoxRewardSectionUIEvent.OnRewardClicked>()) }
                }
            }
        }

        given("I complete the box") {
            `when`("I see reward item") {
                val reward = MockProvider.boxReward()
                viewModel.completedBoxReward(reward)

                then("I should see list of reward item disabled and checked") {
                    viewModel.apply {
                        rewardName.get() shouldBeEqualTo reward.title
                        isRewardItemChecked.get() shouldBeEqualTo true
                        isRewardItemCheckedEnabled.get() shouldBeEqualTo false
                    }
                }
            }
        }

    }

}