package co.styletheory.ops.outbound.android.feature.backlogDetail

import co.styletheory.android.network.core.APICallback
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.backlogDetail.event.BacklogItemUIEvent
import co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel.BacklogDetailItemViewModel
import co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel.impl.BacklogDetailItemViewModelImpl
import co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel.impl.BacklogDetailViewModelImpl
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.viewModelComponent.FooterButtonViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.PhotoWithLabelViewModel
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.RelaxedMockK
import org.amshove.kluent.shouldBeEqualTo
import org.amshove.kluent.shouldBeFalse
import org.amshove.kluent.shouldBeTrue
import org.amshove.kluent.shouldNotBeEqualTo
import org.junit.Test

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 19 December 2017.
 * Description
 *
 * <EMAIL>
 */
class BacklogDetailViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var toolbarVM: GeneralToolbarViewModel

    @InjectMockKs
    lateinit var footerButtonVM: FooterButtonViewModel

    @InjectMockKs
    lateinit var viewModel: BacklogDetailViewModelImpl

    @RelaxedMockK
    lateinit var photoWithLabelVM: PhotoWithLabelViewModel

    private val BACKLOG_DETAIL_ITEM = 0
    private val COMPLETE_BUTTON_ITEM = 1

    override fun setup() {
        super.setup()
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        viewModel.dataService = dataService
        viewModel.footerButtonViewModel = footerButtonVM
        viewModel.toolbarViewModel = toolbarVM
    }

    @Test
    fun firstState() {
        viewModel.detailItems.size shouldBeEqualTo 0
        viewModel.footerButtonViewModel.buttonTitle.get() shouldBeEqualTo ""
        viewModel.batchId.get() shouldBeEqualTo ""
        viewModel.batchName.get() shouldBeEqualTo ""
        viewModel.batchStatus shouldBeEqualTo null
        viewModel.footerButtonViewModel.enable.get().shouldBeFalse()
        viewModel.onEditTextKeyListener shouldNotBeEqualTo null
        viewModel.totalItemCount shouldBeEqualTo 1
    }

    @Test
    fun afterInject() {
        viewModel.afterInject()
        viewModel.footerButtonViewModel.buttonTitle.get() shouldBeEqualTo context.getString(R.string.complete_batch_label)
        viewModel.footerButtonViewModel.enable.get() shouldBeEqualTo false
    }

    @Test
    fun checkFetchBatchDetail() {
        viewModel.fetchBatchDetail(null)
        verify(exactly = 1) { dataService.fetchBatchDetail(any(), any()) }
    }

    @Test
    fun checkRefreshBatchDetail() {
        viewModel.refreshBatchDetail(null)
        verify(exactly = 1) { dataService.fetchBatchDetail(any(), any()) }
    }

    @Test
    fun checkCompleteBacklog_onError() {
        val callbackMock = mockk<Result<Void?, String?>>()
        every { callbackMock.failure(any()) } answers { nothing }
        every { dataService.completeBatch(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Void>, String>>().onError(null)
        }

        viewModel.completeBacklog(null)
        viewModel.completeBacklog(callbackMock)

        verify(exactly = 2) { dataService.completeBatch(any(), any()) }
        verify(exactly = 1) { callbackMock.failure(any()) }
    }

    @Test
    fun checkCompleteBacklog_onSuccess() {
        val callbackMock = mockk<Result<Void?, String?>>()
        every { callbackMock.success(any()) } answers { nothing }
        every { dataService.completeBatch(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Void>, String>>().onSuccess(null)
        }

        viewModel.completeBacklog(null)
        viewModel.completeBacklog(callbackMock)

        verify(exactly = 2) { dataService.completeBatch(any(), any()) }
        verify(exactly = 1) { callbackMock.success(null) }
    }

    @Test
    fun checkOnEnterKeyTapped_isBarcodeSettingOn() {
        every { viewModel.scannedRfidText.get() } returns MockProvider.getScannedRfidTag()
        every { viewModel.scannedRfidText.set("") } just runs
        every { featureFlagUtil.isBarcodeSettingIsOn() } returns true

        viewModel.onKeyTapped()

        viewModel.scannedRfidText.get() shouldBeEqualTo MockProvider.getScannedRfidTag()
        verify { viewModel.checkScannedText() }
    }

    @Test
    fun checkOnEnterKeyTapped_isBarcodeSettingOff() {
        every { viewModel.scannedRfidText.get() } returns MockProvider.getScannedRfidTag()
        every { viewModel.scannedRfidText.set("") } just runs
        every { featureFlagUtil.isBarcodeSettingIsOn() } returns false
        viewModel.onKeyTapped()
        viewModel.scannedRfidText.get() shouldBeEqualTo MockProvider.getScannedRfidTag()
    }

    @Test
    fun checkScannedText_containScannedRfid() {
        val item = mockk<BacklogDetailItemViewModelImpl>()

        every { item.batchItem } returns MockProvider.batchItem()
        every { item.setupPickedButton(any()) } just runs
        every { item.pickedClick() } just runs

        viewModel.detailItems.add(item)
        viewModel.scannedRfidText.set(MockProvider.getScannedRfidTag())

        viewModel.checkScannedText()

        verify(exactly = 1) { eventBus.post(any<BacklogItemUIEvent.OnScannedRfidFoundPosition>()) }
    }

    @Test
    fun checkScannedText_notContainScannedRfid() {
        every { viewModel.scannedRfidText.get() } returns "123123123123"
        viewModel.checkScannedText()
        verify { eventBus.post(any<BacklogItemUIEvent.OnScannedRfidTextNotFound>()) }
    }

    @Test
    fun checkBatchItemStatus_isInCurrentBatch_butInAnotherTab() {
        every { viewModel.batch } returns MockProvider.batch()
        every { viewModel.scannedRfidText.get() } returns MockProvider.getScannedRfidTag()

        viewModel.checkBatchItemStatus()

        verify { eventBus.post(any<BacklogItemUIEvent.OnScannedRfidTextNotFound>()) }
    }

    @Test
    fun checkBatchItemStatus_isNotInCurrentBatch() {
        every { viewModel.batch } returns MockProvider.batch()
        every { viewModel.scannedRfidText.get() } returns "123123123123"

        viewModel.checkBatchItemStatus()

        verify { eventBus.post(any<BacklogItemUIEvent.OnScannedRfidTextNotFound>()) }
    }

    @Test
    fun checkAfterInject() {
        viewModel.afterInject()

        viewModel.footerButtonViewModel.buttonTitle.get() shouldBeEqualTo context.getString(R.string.complete_batch_label)
        viewModel.footerButtonViewModel.enable.get().shouldBeFalse()
    }

    @Test
    fun refreshCompleteStatusButton() {
        val batchItem: BacklogDetailItemViewModelImpl = mockk()
        every { batchItem.isComplete() } returns true
        every { batchItem.isHeaderSection } returns false

        viewModel.detailItems.addAll(listOf(batchItem))
        viewModel.refreshCompleteStatusButton()

        viewModel.toolbarViewModel.subtitle.get() shouldNotBeEqualTo ""
        viewModel.footerButtonViewModel.enable.get().shouldBeTrue()
    }

    @Test
    fun refreshCompleteStatusButton_ItemNotComplete() {
        val batchItem: BacklogDetailItemViewModelImpl = mockk()
        every { batchItem.isComplete() } returns false
        every { batchItem.isHeaderSection } returns false

        viewModel.detailItems.addAll(listOf(batchItem))
        viewModel.refreshCompleteStatusButton()

        viewModel.toolbarViewModel.subtitle.get() shouldNotBeEqualTo ""
        viewModel.footerButtonViewModel.enable.get().shouldBeFalse()
    }

    @Test
    fun checkFetchBatchDetail_WithResultError() {
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        val callback = mockk<Result<Void?, String?>>(relaxed = true)

        every { dataService.fetchBatchDetail(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Batch>, String>>().onError("error")
        }

        viewModel.fetchBatchDetail(callback)

        verify(exactly = 1) { callback.failure(any()) }
    }

    @Test
    fun checkFetchBatchDetailRefreshTrue_WithResultSuccess() {
        every { dataService.fetchBatchDetail(any(), any()) } answers {
            val response: GraphQLData<Batch> = mockk()
            val batchItem = MockProvider.batchItem(ProductStatus.AVAILABLE)
            val batch = MockProvider.batch(batchItem)
            every { response.data.result } returns batch
            secondArg<APICallback<GraphQLData<Batch>, String>>().onSuccess(response)
        }

        viewModel.refreshBatchDetail(null)

        viewModel.detailItems.size shouldBeEqualTo 0
        viewModel.batchName.get() shouldBeEqualTo "Batch A"
    }

    @Test
    fun checkFetchBatchDetailRefreshFalse_WithResultSuccess() {
        every { dataService.fetchBatchDetail(any(), any()) } answers {
            val response: GraphQLData<Batch> = mockk()
            val batchItem = MockProvider.batchItem(ProductStatus.AVAILABLE)
            val batch = MockProvider.batch(batchItem)
            every { response.data.result } returns batch
            secondArg<APICallback<GraphQLData<Batch>, String>>().onSuccess(response)
        }

        viewModel.fetchBatchDetail(null)

        viewModel.detailItems.size shouldBeEqualTo 0
        viewModel.batchName.get() shouldBeEqualTo "Batch A"
    }

    @Test
    fun checkFetchBatchDetailRefreshTrueAndProductPicking_WithResultSuccess() {
        every { dataService.fetchBatchDetail(any(), any()) } answers {
            mockkConstructor(BacklogDetailItemViewModelImpl::class)
            every { anyConstructed<BacklogDetailItemViewModelImpl>().bindViewModel(any(), any()) } returns mockk()

            val batchItem = MockProvider.batchItem(ProductStatus.PICKING)
            val response: GraphQLData<Batch> = mockk()
            val batch = MockProvider.batch(batchItem)
            every { response.data.result } returns batch
            secondArg<APICallback<GraphQLData<Batch>, String>>().onSuccess(response)
        }

        viewModel.refreshBatchDetail(null)

        viewModel.detailItems.size shouldNotBeEqualTo 0
        viewModel.batchName.get() shouldBeEqualTo "Batch A"
    }

    @Test
    fun checkFetchBatchDetailRefreshTrueAndProductRented_WithResultSuccess() {
        every { dataService.fetchBatchDetail(any(), any()) } answers {
            mockkConstructor(BacklogDetailItemViewModelImpl::class)
            every { anyConstructed<BacklogDetailItemViewModelImpl>().bindViewModel(any(), any()) } returns mockk()

            val batchItem = MockProvider.batchItem(ProductStatus.RENTED)
            val response: GraphQLData<Batch> = mockk()
            val batch = MockProvider.batch(batchItem)
            every { response.data.result } returns batch
            secondArg<APICallback<GraphQLData<Batch>, String>>().onSuccess(response)
        }

        viewModel.refreshBatchDetail(null)

        viewModel.detailItems.size shouldNotBeEqualTo 0
        viewModel.batchName.get() shouldBeEqualTo "Batch A"
    }

    @Test
    fun checkFetchBatchDetailRefreshTrueAndProductPicked_WithResultSuccess() {
        every { dataService.fetchBatchDetail(any(), any()) } answers {
            mockkConstructor(BacklogDetailItemViewModelImpl::class)
            every { anyConstructed<BacklogDetailItemViewModelImpl>().bindViewModel(any(), any()) } returns mockk()

            val batchItem = MockProvider.batchItem(ProductStatus.PICKED)
            val response: GraphQLData<Batch> = mockk()
            val batch = MockProvider.batch(batchItem)
            every { response.data.result } returns batch
            secondArg<APICallback<GraphQLData<Batch>, String>>().onSuccess(response)
        }

        viewModel.refreshBatchDetail(null)

        viewModel.detailItems.size shouldNotBeEqualTo 0
        viewModel.batchName.get() shouldBeEqualTo "Batch A"
    }

    @Test
    fun checkFetchBatchDetailRefreshTrueAndProductNotFound_WithResultSuccess() {
        every { dataService.fetchBatchDetail(any(), any()) } answers {
            mockkConstructor(BacklogDetailItemViewModelImpl::class)
            every { anyConstructed<BacklogDetailItemViewModelImpl>().bindViewModel(any(), any()) } returns mockk()

            val batchItem = MockProvider.batchItem(ProductStatus.PICKING)
            val response: GraphQLData<Batch> = mockk()
            val batch = MockProvider.batch(batchItem)
            every { response.data.result } returns batch
            secondArg<APICallback<GraphQLData<Batch>, String>>().onSuccess(response)
        }

        viewModel.refreshBatchDetail(null)

        viewModel.detailItems.size shouldNotBeEqualTo 0
        viewModel.batchName.get() shouldBeEqualTo "Batch A"
    }

    @Test
    fun mapBatchDetailItem_shouldBindBatchItemViewModel_isUserApparelAndRegionSGTrue() {
        viewModel = spyk(viewModel)
        val batchItem = MockProvider.batchItem(ProductStatus.RENTED)
        val batch = MockProvider.batch(batchItem)
        every { userStorage.isVerticalTypeApparel() } returns false

        viewModel.mapBatchDetailItem(batch)
        verify(exactly = 1) { viewModel.bindBatchItemViewModel(batch, batchItem) }
    }

    @Test
    fun mapBatchDetailItem_shouldNotBindBatchItemViewModel_isUserApparelAndRegionSGTrue() {
        viewModel = spyk(viewModel)
        val batchItem = MockProvider.batchItem(ProductStatus.AVAILABLE)
        val batch = MockProvider.batch(batchItem)
        every { userStorage.isVerticalTypeApparel() } returns false

        viewModel.mapBatchDetailItem(batch)
        verify(exactly = 0) { viewModel.bindBatchItemViewModel(batch, batchItem) }
    }

    @Test
    fun mapBatchDetailItem_shouldBindBatchItemViewModel_isUserApparelAndRegionSGFalse() {
        viewModel = spyk(viewModel)
        val batchItem = MockProvider.batchItem(ProductStatus.RENTED)
        val batch = MockProvider.batch(batchItem)
        every { userStorage.isVerticalTypeApparel() } returns true

        every { featureFlagUtil.isBarcodeSettingIsOn() } returns true
        every { viewModel.bindBatchItemViewModel(any(), any()) } just Runs

        viewModel.mapBatchDetailItem(batch)
        verify(exactly = 1) { viewModel.bindBatchItemViewModel(batch, batchItem) }
    }

    @Test
    fun mapBatchDetailItem_shouldNotBindBatchItemViewModel_isUserApparelAndRegionSGFalse() {
        viewModel = spyk(viewModel)
        val batchItem = MockProvider.batchItem(ProductStatus.AVAILABLE)
        val batch = MockProvider.batch(batchItem)

        every { userStorage.isVerticalTypeApparel() } returns true

        viewModel.mapBatchDetailItem(batch)
        verify(exactly = 0) { viewModel.bindBatchItemViewModel(batch, batchItem) }
    }

    @Test
    fun mapBatchDetailItem_shouldNotBindBatchItemViewModel_isUserBagsAndRegionSGFalse() {
        viewModel = spyk(viewModel)
        val batchItem = MockProvider.batchItem(ProductStatus.AVAILABLE)
        val batch = MockProvider.batch(batchItem)

        every { userStorage.isVerticalTypeApparel() } returns false
        every { userStorage.isVerticalTypeBags() } returns true

        viewModel.mapBatchDetailItem(batch)
        verify(exactly = 0) { viewModel.bindBatchItemViewModel(batch, batchItem) }
    }

    @Test
    fun bindBatchItemViewModel() {
        viewModel = spyk(viewModel)
        val batchItem = MockProvider.batchItem(ProductStatus.RENTED)
        val batch = MockProvider.batch(batchItem)

        mockkConstructor(BacklogDetailItemViewModelImpl::class)
        every { anyConstructed<BacklogDetailItemViewModelImpl>().bindViewModel(batch, batchItem) } returns mockk()

        viewModel.bindBatchItemViewModel(batch, batchItem)
        verify(exactly = 1) { viewModel.setupBagsOrApparelItems(any()) }
    }

    @Test
    fun toolbarViewModel() {
        viewModel.toolbarViewModel() shouldBeEqualTo toolbarVM
    }

    @Test
    fun setupBagsOrApparelItems_isVerticalBags() {
        val item = spyk<BacklogDetailItemViewModelImpl>()
        every { userStorage.isVerticalTypeBags() } returns true
        viewModel.setupBagsOrApparelItems(item)
        verify(exactly = 1) { item.showBagsItems() }
    }

    @Test
    fun setupBagsOrApparelItems_isVerticalNotBags() {
        val item = spyk<BacklogDetailItemViewModelImpl>()
        every { userStorage.isVerticalTypeBags() } returns false
        viewModel.setupBagsOrApparelItems(item)
        verify(exactly = 1) { item.showApparelItems() }
    }

    @Test
    fun getViewModelAtPosition_getBacklogItemType() {
        val items = listOf(BacklogDetailItemViewModelImpl(), BacklogDetailItemViewModelImpl())
        viewModel.detailItems.addAll(items)
        viewModel.getViewModelAtPosition<BacklogDetailItemViewModel>(0) shouldBeEqualTo items[0]
    }

    @Test
    fun getViewModelAtPosition_getFooterItemType() {
        val footerItem = FooterButtonViewModel()
        viewModel.footerButtonViewModel = footerItem
        viewModel.detailItems.addAll(listOf(BacklogDetailItemViewModelImpl()))
        viewModel.getViewModelAtPosition<BacklogDetailItemViewModel>(1) shouldBeEqualTo footerItem
    }

    @Test
    fun getLayoutIdForItemType_getBacklogItem() {
        viewModel.getLayoutIdForItemType(0) shouldBeEqualTo R.layout.backlog_detail_list_item
    }

    @Test
    fun getLayoutIdForItemType_getFooterItem() {
        viewModel.getLayoutIdForItemType(1) shouldBeEqualTo R.layout.footer_button_item
    }

    @Test
    fun getItemTypeAtPosition_shouldCompleteButton() {
        viewModel.detailItems.addAll(listOf(BacklogDetailItemViewModelImpl()))
        viewModel.getItemTypeAtPosition(1) shouldBeEqualTo COMPLETE_BUTTON_ITEM
    }

    @Test
    fun getItemTypeAtPosition_shouldBacklogItem() {
        viewModel.detailItems.addAll(listOf(BacklogDetailItemViewModelImpl()))
        viewModel.getItemTypeAtPosition(0) shouldBeEqualTo BACKLOG_DETAIL_ITEM
    }
}