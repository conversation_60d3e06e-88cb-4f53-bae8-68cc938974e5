package co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLResult
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.event.LoadingScanBarcodeBottomSheetNetworkEvent
import co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.event.LoadingScanBarcodeBottomSheetUIEvent
import co.styletheory.ops.outbound.android.feature.loadingScanBarcodeBottomSheet.viewModel.impl.LoadingScanBarcodeBottomSheetViewModelImpl
import co.styletheory.ops.outbound.android.feature.qcDetail.event.QcDetailUIEvent
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.model.enums.SwapType
import co.styletheory.ops.outbound.android.model.response.CheckSwapAvailabilityResponse
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.amshove.kluent.shouldBeEqualTo
import org.junit.Test


/**
 * Created by Eminarti Sianturi on 23/06/20.
 */
class LoadingScanBarcodeBottomSheetTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: LoadingScanBarcodeBottomSheetViewModelImpl

    override fun setup() {
        super.setup()
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        viewModel.dataService = dataService
    }

    @Test
    fun checkOnCloseBottomSheet() {
        viewModel.onClose()
        verify { eventBus.post(LoadingScanBarcodeBottomSheetUIEvent.OnCloseClicked) }
    }

    @Test
    fun checkSwapItem_ResellingItem() {
        val swapItem = SwapItem(
            item = BatchItem("ABC", label = "purchased by Imel"),
            boxId = "A",
            shipmentId = "B",
            customerEmail = "C",
            failCategory = "D",
            failReason = "E"
        )

        viewModel.swapItem = swapItem
        viewModel.swapType = SwapType.QUALITY_SWAP.swapType
        viewModel.checkSwapItem()
    }

    @Test
    fun checkSwapItem_Bags() {
        val swapItem = SwapItem(
            item = BatchItem("ABC"),
            boxId = "A",
            shipmentId = "B",
            customerEmail = "C",
            failCategory = "D",
            failReason = "E"
        )

        every { userStorage.isVerticalTypeBags() } returns true
        viewModel.swapItem = swapItem
        viewModel.swapType = SwapType.QUALITY_SWAP.swapType
        viewModel.checkSwapItem()
    }

    @Test
    fun checkSwapItem_OnDemand() {
        val swapItem = SwapItem(
            item = BatchItem("ABC"),
            boxId = "A",
            shipmentId = "B",
            customerEmail = "C",
            failCategory = "D",
            failReason = "E"
        )

        every { userStorage.isUserOnDemandAndRegionID() } returns true
        viewModel.swapItem = swapItem
        viewModel.swapType = SwapType.QUALITY_SWAP.swapType
        viewModel.checkSwapItem()
    }

    @Test
    fun checkSwapItem_NoBoxId() {
        val swapItem = SwapItem(
            item = BatchItem("ABC"),
            boxId = "",
            shipmentId = "B",
            customerEmail = "C",
            failCategory = "D",
            failReason = "E"
        )

        every { userStorage.isUserOnDemandAndRegionID() } returns false
        every { userStorage.isVerticalTypeBags() } returns false
        viewModel.swapItem = swapItem
        viewModel.swapType = SwapType.QUALITY_SWAP.swapType
        viewModel.checkSwapItem()
    }

    @Test
    fun checkSwapItem_QualitySwap() {
        val swapItem = SwapItem(
            item = BatchItem("ABC"),
            boxId = "A",
            shipmentId = "B",
            customerEmail = "C",
            failCategory = "D",
            failReason = "E"
        )

        every { userStorage.isUserOnDemandAndRegionID() } returns false
        every { userStorage.isVerticalTypeBags() } returns false
        viewModel.swapItem = swapItem
        viewModel.swapType = SwapType.QUALITY_SWAP.swapType
        viewModel.checkSwapItem()
        verify { dataService.checkSwapItemAvailability(any(), any()) }
    }

    @Test
    fun checkSwapItem_AccuracySwap() {
        val swapItem = SwapItem(
            item = BatchItem("ABC"),
            boxId = "A",
            shipmentId = "B",
            customerEmail = "C",
            failCategory = "D",
            failReason = "E"
        )

        every { userStorage.isUserOnDemandAndRegionID() } returns false
        every { userStorage.isVerticalTypeBags() } returns false
        viewModel.swapItem = swapItem
        viewModel.swapType = SwapType.ACCURACY_SWAP.swapType
        viewModel.checkSwapItem()
        verify { dataService.getAccuracySwapItem(any(), any()) }
    }

    @Test
    fun checkSwapItem_NoSwapType() {
        val swapItem = SwapItem(boxId = "ABC")
        every { userStorage.isUserOnDemandAndRegionID() } returns false
        every { userStorage.isVerticalTypeBags() } returns false
        viewModel.swapItem = swapItem
        viewModel.swapType = ""
        viewModel.checkSwapItem()
        verify { eventBus.post(LoadingScanBarcodeBottomSheetUIEvent.OnCloseClicked) }
    }

    @Test
    fun swapItem_NoType() {
        viewModel.swapType = ""
        viewModel.checkSwapItem()
        verify(inverse = true) { dataService.getAccuracySwapItem(any(), any()) }
        verify(inverse = true) { dataService.checkSwapItemAvailability(any(), any()) }
    }

    @Test
    fun accuracySwapItemCallback_onSuccessBatchItem() {
        viewModel = spyk(viewModel)
        val batchItem = MockProvider.batchItem()
        val swapItem = SwapItem(MockProvider.batchItem())
        viewModel.swapItem = swapItem
        val eventSlots = mutableListOf<Any>()
        val callback = viewModel.accuracySwapItemCallback

        callback.onSuccess(GraphQLData(GraphQLResult(batchItem)))

        verify(exactly = 1) { eventBus.post(capture(eventSlots)) }

        (eventSlots[0] as LoadingScanBarcodeBottomSheetNetworkEvent.OnSwapItemFound).apply {
            newItem shouldBeEqualTo batchItem
        }
    }

    @Test
    fun accuracySwapItemCallback_onError() {
        viewModel.accuracySwapItemCallback.onError("")
        verify { eventBus.post(any<LoadingScanBarcodeBottomSheetUIEvent.OnErrorSwapItem>()) }
    }

    @Test
    fun accuracySwapItemCallback_onErrorNull() {
        viewModel.accuracySwapItemCallback.onError(null)
        verify { eventBus.post(any<LoadingScanBarcodeBottomSheetUIEvent.OnErrorSwapItem>()) }
    }

    @Test
    fun qualitySwapItemCallback_onSuccessAndResultIsNotEmpty() {
        val item = mock<BatchItem>()

        val data = mock<GraphQLData<CheckSwapAvailabilityResponse>>()
        val result = mock<GraphQLResult<CheckSwapAvailabilityResponse>>()
        whenever(data.data).thenReturn(result)
        whenever(result.result).thenReturn(CheckSwapAvailabilityResponse(listOf(item)))

        viewModel.qualitySwapItemCallback.onSuccess(data)
    }

    @Test
    fun qualitySwapItemCallback_onSuccessAndResultIsEmpty() {
        val data = mock<GraphQLData<CheckSwapAvailabilityResponse>>()
        val result = mock<GraphQLResult<CheckSwapAvailabilityResponse>>()
        whenever(data.data).thenReturn(result)
        whenever(result.result).thenReturn(CheckSwapAvailabilityResponse(emptyList()))

        viewModel.qualitySwapItemCallback.onSuccess(data)

        verify { eventBus.post(any<LoadingScanBarcodeBottomSheetUIEvent.OnErrorSwapItem>()) }
    }

    @Test
    fun qualitySwapItemCallback_onError() {
        viewModel.qualitySwapItemCallback.onError("Error")

        verify { eventBus.post(any<LoadingScanBarcodeBottomSheetUIEvent.OnErrorSwapItem>()) }
    }

    @Test
    fun qualitySwapItemCallback_onErrorNull() {
        viewModel.qualitySwapItemCallback.onError(null)

        verify { eventBus.post(any<LoadingScanBarcodeBottomSheetUIEvent.OnErrorSwapItem>()) }
    }

    @Test
    fun checkSetupBottomSheetContent_reselling() {
        val swapItem = mockk<SwapItem>()
        every { swapItem.item?.isResellingItem() } returns true
        viewModel.swapItem = swapItem
        viewModel.setupBottomSheetContent()
        viewModel.bottomSheetTitle.get() shouldBeEqualTo context.getString(R.string.quality_swap_bottom_sheet_apply_title)
        viewModel.bottomSheetContent.get() shouldBeEqualTo context.getString(R.string.quality_swap_bottom_sheet_apply_content)
    }

    @Test
    fun checkSetupBottomSheetContent_bags() {
        every { userStorage.isVerticalTypeBags() } returns true
        viewModel.setupBottomSheetContent()
        viewModel.bottomSheetTitle.get() shouldBeEqualTo context.getString(R.string.quality_swap_bottom_sheet_apply_title)
        viewModel.bottomSheetContent.get() shouldBeEqualTo context.getString(R.string.quality_swap_bottom_sheet_apply_content)
    }

    @Test
    fun checkSetupBottomSheetContent_onDemand() {
        every { userStorage.isUserOnDemandAndRegionID() } returns true
        viewModel.setupBottomSheetContent()
        viewModel.bottomSheetTitle.get() shouldBeEqualTo context.getString(R.string.quality_swap_bottom_sheet_apply_title)
        viewModel.bottomSheetContent.get() shouldBeEqualTo context.getString(R.string.quality_swap_bottom_sheet_apply_content)
    }

    @Test
    fun checkSetupBottomSheetContent() {
        viewModel.setupBottomSheetContent()
        viewModel.bottomSheetTitle.get() shouldBeEqualTo context.getString(R.string.accuracy_swap_this_item)
        viewModel.bottomSheetContent.get() shouldBeEqualTo context.getString(R.string.accuracy_swap_bottom_sheet_scan_loading)
    }

    @Test
    fun updateStatusCallback_onSuccessBatchItem() {
        viewModel.updateStatusCallback.onSuccess(null)
        verify { eventBus.post(any<QcDetailUIEvent.OnUpdateItemSuccess>()) }
        verify { eventBus.post(any<LoadingScanBarcodeBottomSheetUIEvent.OnUpdateItemQAFailed>()) }
    }

    @Test
    fun updateStatusCallback_onError() {
        viewModel.updateStatusCallback.onError("")
        verify { eventBus.post(any<QcDetailUIEvent.OnErrorSwapItem>()) }
        verify { eventBus.post(any<LoadingScanBarcodeBottomSheetUIEvent.OnUpdateItemQAFailed>()) }
    }

    @Test
    fun updateStatusCallback_onErrorNull() {
        viewModel.updateStatusCallback.onError(null)
        verify { eventBus.post(any<QcDetailUIEvent.OnErrorSwapItem>()) }
        verify { eventBus.post(any<LoadingScanBarcodeBottomSheetUIEvent.OnCloseClicked>()) }
    }
}