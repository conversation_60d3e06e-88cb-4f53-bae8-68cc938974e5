package co.styletheory.ops.outbound.android.feature.packing

import android.view.View
import co.styletheory.android.network.core.APICallback
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.impl.BoxRewardSectionViewModelImpl
import co.styletheory.ops.outbound.android.feature.boxRewardSection.viewModel.impl.ItemBoxRewardSectionViewModelImpl
import co.styletheory.ops.outbound.android.feature.packing.event.PackingItemUIEvent
import co.styletheory.ops.outbound.android.feature.packing.event.PackingNetworkEvent
import co.styletheory.ops.outbound.android.feature.packing.viewModel.impl.PackingDetailItemViewModelImpl
import co.styletheory.ops.outbound.android.feature.packing.viewModel.impl.PackingItemViewModelImpl
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.general.event.DismissProgressDialogEvent
import co.styletheory.ops.outbound.android.model.enums.LabelType
import co.styletheory.ops.outbound.android.model.enums.ProcedureType
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.model.enums.VerticalTypes
import co.styletheory.ops.outbound.android.viewModelComponent.LabelBoxViewModel
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import org.amshove.kluent.shouldBeEqualTo
import org.amshove.kluent.shouldBeFalse
import org.amshove.kluent.shouldBeTrue
import org.amshove.kluent.shouldNotBeEmpty
import org.junit.Test

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 19 December 2017.
 * Description
 *
 * <EMAIL>
 */
class PackingDetailItemViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: PackingDetailItemViewModelImpl

    @InjectMockKs
    lateinit var boxRewardSectionVM: BoxRewardSectionViewModelImpl

    private val rackSection = "12"
    private val rackName = "Batch A"
    val batch = MockProvider.batch()
    val customer = MockProvider.customer().copy(isVip = true, noPaper = true, noToteBag = true)
    val box = MockProvider.shipmentBox().copy(isFirstBox = true)
    val shipment = MockProvider.shipment().copy(customer = customer, box = box, procedureType = "OtherProcedure")

    override fun setup() {
        super.setup()
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        viewModel.shipments = shipment
        viewModel.logisticProviders = batch.logisticProviders
        viewModel.batchId.set(batch.id)
        viewModel.rackName.set(batch.rack?.name)
        viewModel.rackSection.set(rackSection)
        boxRewardSectionVM = mockk(relaxed = true)
        viewModel.boxRewardSectionVM = boxRewardSectionVM
        mockkConstructor(BoxRewardSectionViewModelImpl::class)
        every { anyConstructed<BoxRewardSectionViewModelImpl>().setBoxRewards(any(), any()) } returns mockk()
    }

    @Test
    fun firstState() {
        viewModel.title.get() shouldBeEqualTo ""
        viewModel.customerName.get() shouldBeEqualTo ""
        viewModel.completeButtonText.get() shouldBeEqualTo ""
        viewModel.labelItems.size shouldBeEqualTo 0
        viewModel.readyItems.size shouldBeEqualTo 0
        viewModel.rfidList.size shouldBeEqualTo 0
        viewModel.useShippingBox.get().shouldBeFalse()
        viewModel.useToteBag.get().shouldBeFalse()
        viewModel.enableCompleteButton.get().shouldBeFalse()
    }

    @Test
    fun checkMapShipment_bags() {
        mockkConstructor(PackingItemViewModelImpl::class)
        every { anyConstructed<PackingItemViewModelImpl>().bindViewModel(any(), any(), any()) } returns mockk()
        every { viewModel.checkBatchItemRfid(any()) } answers { nothing }
        every { userStorage.getUserVerticalType().name } returns VerticalTypes.BAGS.type
        every { userStorage.isVerticalTypeBags() } returns true
        every { context.getString(R.string.completed_label) } returns "Completed"

        viewModel.mapShipment()

        viewModel.customerName.get() shouldBeEqualTo context.getString(R.string.detail_item_title)
                .format(shipment.customer?.name.toString(), rackName, rackSection)
        viewModel.readyItems.shouldNotBeEmpty()
        viewModel.labelItems.size shouldBeEqualTo 4
        viewModel.title.get() shouldBeEqualTo "12"
    }

    @Test
    fun checkMapShipment_apparel() {
        mockkConstructor(PackingItemViewModelImpl::class)
        every { anyConstructed<PackingItemViewModelImpl>().bindViewModel(any(), any(), any()) } returns mockk()
        every { viewModel.checkBatchItemRfid(any()) } answers { nothing }
        every { userStorage.getUserVerticalType().name } returns VerticalTypes.APPAREL.type
        every { context.getString(R.string.completed_label) } returns "Completed"

        viewModel.mapShipment()

        viewModel.customerName.get() shouldBeEqualTo context.getString(R.string.detail_item_title)
                .format(shipment.customer?.name.toString(), rackName, rackSection)
        viewModel.readyItems.shouldNotBeEmpty()
        viewModel.labelItems.size shouldBeEqualTo 4
        viewModel.labelItems[3].labelType.get() shouldBeEqualTo LabelType.NO_TOTE_BAG
        viewModel.title.get() shouldBeEqualTo "12"
    }

    @Test
    fun checkMapShipment_apparel_EmptyShipmentID() {
        val batchItem = MockProvider.batchItem(ProductStatus.PHOTO_QA)
        val shipment = MockProvider.shipmentEmptyTrackingId(listOf(batchItem))

        viewModel.shipments = shipment

        mockkConstructor(PackingItemViewModelImpl::class)
        every { anyConstructed<PackingItemViewModelImpl>().bindViewModel(any(), any(), any()) } returns mockk()
        every { viewModel.checkBatchItemRfid(any()) } answers { nothing }
        every { userStorage.getUserVerticalType().name } returns VerticalTypes.APPAREL.type
        every { context.getString(R.string.completed_label) } returns "Completed"

        viewModel.mapShipment()

        viewModel.customerName.get() shouldBeEqualTo context.getString(R.string.detail_item_title)
                .format(shipment.customer?.name.toString(), rackName, rackSection)
        viewModel.readyItems.shouldNotBeEmpty()
        viewModel.labelItems.size shouldBeEqualTo 0
        viewModel.title.get() shouldBeEqualTo ""
    }

    @Test
    fun isReadyItemCompleted_true() {
        val shipments = MockProvider.shipment(listOf(MockProvider.batchItem(ProductStatus.PACKED)))
        viewModel.shipments = shipments
        viewModel.isReadyItemCompleted() shouldBeEqualTo true
    }

    @Test
    fun isReadyItemCompleted_shipmentNull_true() {
        viewModel.shipments = null
        viewModel.isReadyItemCompleted() shouldBeEqualTo true
    }

    @Test
    fun isReadyItemCompleted_shipmentItemEmpty_true() {
        viewModel.shipments = shipment.copy(items = listOf())
        viewModel.isReadyItemCompleted() shouldBeEqualTo true
    }

    @Test
    fun isReadyItemCompleted_false() {
        viewModel.isReadyItemCompleted() shouldBeEqualTo false
    }

    @Test
    fun checkIsEnableCompleteButton_isBarcodeSettingOn_RFID_true() {
        val shipments = MockProvider.shipment(listOf(MockProvider.batchItem(ProductStatus.PACKING)))
        val rfidList = mutableMapOf<String, Boolean>()
        rfidList[MockProvider.getScannedRfidTag()] = true

        every { featureFlagUtil.isBarcodeSettingIsOn() } returns true

        viewModel.rfidList = rfidList
        viewModel.shipments = shipments

        viewModel.isEnableCompleteButton() shouldBeEqualTo true
    }

    @Test
    fun checkIsEnableCompleteButton_isBarcodeSettingOn_RFID_false() {
        val shipments = MockProvider.shipment(listOf(MockProvider.batchItem(ProductStatus.PACKING)))
        val rfidList = mutableMapOf<String, Boolean>()
        rfidList[MockProvider.getScannedRfidTag()] = false

        every { featureFlagUtil.isBarcodeSettingIsOn() } returns true

        viewModel.rfidList = rfidList
        viewModel.shipments = shipments

        viewModel.isEnableCompleteButton() shouldBeEqualTo false
    }

    @Test
    fun checkIsEnableCompleteButton_isBarcodeSettingOff_RFID_true() {
        val shipments = MockProvider.shipment(listOf(MockProvider.batchItem(ProductStatus.PACKING)))
        val rfidList = mutableMapOf<String, Boolean>()
        rfidList[MockProvider.getScannedRfidTag()] = true

        every { featureFlagUtil.isBarcodeSettingIsOn() } returns false

        viewModel.rfidList = rfidList
        viewModel.shipments = shipments

        viewModel.isEnableCompleteButton() shouldBeEqualTo true
    }

    @Test
    fun checkIsEnableCompleteButton_batchStatus_false() {
        viewModel.isEnableCompleteButton() shouldBeEqualTo false
    }

    @Test
    fun checkIsEnableCompleteButton_batchStatus_true() {
        val shipments = MockProvider.shipment(listOf(MockProvider.batchItem(ProductStatus.PACKING)))
        viewModel.shipments = shipments
        viewModel.isEnableCompleteButton() shouldBeEqualTo true
    }

    @Test
    fun setCompleteButtonState_true() {
        val shipments = MockProvider.shipment(listOf(MockProvider.batchItem(ProductStatus.PACKED)))
        viewModel.shipments = shipments
        val boxReward = MockProvider.boxReward()
        viewModel.selectedBoxRewards.add(boxReward)

        viewModel.setCompleteButtonState()

        viewModel.itemsComplete.get() shouldBeEqualTo true
        viewModel.enableCompleteButton.get() shouldBeEqualTo false
        viewModel.completeButtonText.get() shouldBeEqualTo context.getString(R.string.completed_label)
    }

    @Test
    fun setCompleteButtonState_completeButton_true() {
        val shipments = MockProvider.shipment(listOf(MockProvider.batchItem(ProductStatus.PACKING))).copy(box = null)
        val rfidList = mutableMapOf<String, Boolean>()
        rfidList[MockProvider.getScannedRfidTag()] = true

        viewModel.rfidList = rfidList
        viewModel.shipments = shipments

        viewModel.setCompleteButtonState()

        viewModel.itemsComplete.get() shouldBeEqualTo true
        viewModel.enableCompleteButton.get() shouldBeEqualTo true
        viewModel.completeButtonText.get() shouldBeEqualTo context.getString(R.string.complete_order_label)
    }

    @Test
    fun setCompleteButtonState_completeButton_false() {
        val boxReward = MockProvider.boxReward()
        viewModel.selectedBoxRewards.add(boxReward)
        viewModel.setCompleteButtonState()

        viewModel.itemsComplete.get() shouldBeEqualTo false
        viewModel.enableCompleteButton.get() shouldBeEqualTo false
        viewModel.completeButtonText.get() shouldBeEqualTo context.getString(R.string.complete_order_label)
    }

    @Test
    fun setCompleteButtonState_completeButton_true_isAllBoxRewardChecked_false() {
        val shipments = MockProvider.shipment(listOf(MockProvider.batchItem(ProductStatus.PACKING)))
        val rfidList = mutableMapOf<String, Boolean>()
        rfidList[MockProvider.getScannedRfidTag()] = true
        viewModel.rfidList = rfidList
        viewModel.shipments = shipments

        viewModel.setCompleteButtonState()

        viewModel.itemsComplete.get() shouldBeEqualTo false
        viewModel.enableCompleteButton.get() shouldBeEqualTo false
        viewModel.completeButtonText.get() shouldBeEqualTo context.getString(R.string.complete_order_label)
    }

    @Test
    fun isAllBoxRewardChecked_shipment_empty() {
        viewModel.shipments = null
        viewModel.selectedBoxRewards
        viewModel.setCompleteButtonState()

        viewModel.isAllBoxRewardChecked() shouldBeEqualTo true
    }

    @Test
    fun isAllBoxRewardChecked_box_empty() {
        viewModel.shipments = MockProvider.shipment(listOf(MockProvider.batchItem(ProductStatus.PACKING)))
                .copy(box = null)
        viewModel.isAllBoxRewardChecked() shouldBeEqualTo true
    }

    @Test
    fun isAllBoxRewardChecked_boxReward_empty() {
        val box = MockProvider.shipmentBox().copy(boxReward = null)
        viewModel.shipments = MockProvider.shipment(listOf(MockProvider.batchItem(ProductStatus.PACKING)))
                .copy(box = box)
        viewModel.isAllBoxRewardChecked() shouldBeEqualTo true
    }

    @Test
    fun checkIsItemShouldInReady_true() {
        val batchItem = MockProvider.batchItem(ProductStatus.PACKING)
        viewModel.itemShouldInReady(batchItem) shouldBeEqualTo true
    }

    @Test
    fun checkIsItemShouldInReady_false() {
        val batchItem = MockProvider.batchItem()
        viewModel.itemShouldInReady(batchItem) shouldBeEqualTo false
    }

    @Test
    fun checkIsItemShouldInReady_false_BecauseItemNull() {
        viewModel.itemShouldInReady(null) shouldBeEqualTo false
    }

    @Test
    fun checkSetButtonCompleted() {
        viewModel.setButtonCompleted()
        viewModel.itemsComplete.get().shouldBeTrue()
        viewModel.enableCompleteButton.get().shouldBeFalse()
        viewModel.completeButtonText.get() shouldBeEqualTo context.getString(R.string.completed_label)
    }

    @Test
    fun checkCompleteButtonClick_shouldProcessReadyShipment() {
        every { viewModel.isEnableCompleteButton() } returns true
        every { viewModel.isReadyItemCompleted() } returns false
        val boxReward = MockProvider.boxReward()
        viewModel.selectedBoxRewards.add(boxReward)

        viewModel.completeButtonClick()

        verify { viewModel.completeReadyShipment() }
    }

    @Test
    fun completeReadyShipment_WithResultSuccess() {
        viewModel = spyk(viewModel)

        every { dataService.updateBatchItemStatus(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Void>, String>>().onSuccess(null)
        }
        val boxReward = MockProvider.boxReward()
        viewModel.selectedBoxRewards.add(boxReward)

        viewModel.completeReadyShipment()

        viewModel.itemsComplete.get().shouldBeTrue()
        viewModel.enableCompleteButton.get().shouldBeFalse()
        viewModel.completeButtonText.get() shouldBeEqualTo context.getString(R.string.completed_label)
        viewModel.boxRewardsSectionVisibility.get() shouldBeEqualTo View.VISIBLE
        verify { eventBus.post(any<DismissProgressDialogEvent>()) }
    }

    @Test
    fun completeReadyShipment_WithResultError() {
        viewModel = spyk(viewModel)

        every { dataService.updateBatchItemStatus(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Void>, String>>().onError(null)
        }

        viewModel.completeReadyShipment()

        verify {
            eventBus.post(any<DismissProgressDialogEvent>())
            eventBus.post(any<PackingNetworkEvent.OnErrorUpdateItemStatus>())
        }
    }

    @Test
    fun checkItemIsCompleted_true() {
        viewModel.itemIsCompleted(MockProvider.batchItem(ProductStatus.PACKED)).shouldBeTrue()
        viewModel.itemIsCompleted(MockProvider.batchItem(ProductStatus.RECEIVED_BY_CUSTOMER)).shouldBeTrue()
        viewModel.itemIsCompleted(MockProvider.batchItem(ProductStatus.TRANSIT_TO_CUSTOMER)).shouldBeTrue()
    }

    @Test
    fun checkItemIsCompleted_null_false() {
        viewModel.itemIsCompleted(null).shouldBeFalse()
    }

    @Test
    fun checkItemIsCompleted_false() {
        viewModel.itemIsCompleted(MockProvider.batchItem(ProductStatus.PACKING)).shouldBeFalse()
    }

    @Test
    fun checkSearchRfidItemPosition_positionNotFound() {
        mockkConstructor(PackingItemViewModelImpl::class)
        val rfidText = "123123123123"
        val batchItem = MockProvider.batchItem(ProductStatus.PACKING)
        every { anyConstructed<PackingItemViewModelImpl>().bindViewModel(any(), any(), any()) } returns mockk()
        every { viewModel.readyItems } returns mockk()
        every { viewModel.readyItems[0].batchItem } returns batchItem

        viewModel.searchRfidItemPosition(rfidText) shouldBeEqualTo -1
    }

    @Test
    fun checkRfidText_positionNotFound() {
        mockkConstructor(PackingItemViewModelImpl::class)
        val rfidText = "123123123123"
        val batchItem = MockProvider.batchItem(ProductStatus.PACKING)
        every { anyConstructed<PackingItemViewModelImpl>().bindViewModel(any(), any(), any()) } returns mockk()
        every { viewModel.readyItems } returns mockk()
        every { viewModel.readyItems[0].batchItem } returns batchItem

        viewModel.checkRfidText(rfidText)
    }

    @Test
    fun checkRfidText_positionFound_StatusPacking() {
        val item = mockk<PackingItemViewModelImpl>()
        mockkConstructor(item::class)
        val rfidText = "123456"
        val batchItem = MockProvider.batchItem(ProductStatus.PACKING, rfid = rfidText)
        every { anyConstructed<PackingItemViewModelImpl>().bindViewModel(any(), any(), any()) } returns mockk()
        viewModel.readyItems.add(item)
        every { item.batchItem } returns batchItem
        every { viewModel.readyItems[0].batchItem } returns batchItem

        viewModel.checkRfidText(rfidText)
        verify { eventBus.post(any<PackingItemUIEvent.OnScrollToPosition>()) }
    }

    @Test
    fun checkRfidText_positionFound_StatusNotPacking() {
        val item = mockk<PackingItemViewModelImpl>()
        mockkConstructor(item::class)
        val rfidText = "123456"
        val batchItem = MockProvider.batchItem(ProductStatus.PHOTO_QA, rfid = rfidText)
        every { anyConstructed<PackingItemViewModelImpl>().bindViewModel(any(), any(), any()) } returns mockk()
        viewModel.readyItems.add(item)
        every { item.batchItem } returns batchItem
        every { viewModel.readyItems[0].batchItem } returns batchItem

        viewModel.checkRfidText(rfidText)
        verify { eventBus.post(any<PackingItemUIEvent.OnScrollToPosition>()) }
    }

    @Test
    fun checkRfidText() {
        val item = mockk<PackingItemViewModelImpl>()
        mockkConstructor(item::class)

        val rfidText = "123456"
        val batchItem = MockProvider.batchItem(ProductStatus.PACKING, rfid = rfidText)

        every { anyConstructed<PackingItemViewModelImpl>().bindViewModel(any(), any(), any()) } returns mockk()
        viewModel.readyItems.add(item)
        viewModel.rfidList[rfidText] = false

        every { item.batchItem } returns batchItem
        every { item.haveScannedRfid } returns mockk()
        every { item.isItemScanned } returns mockk()
        every { item.haveScannedRfid.set(any()) } returns mockk()
        every { item.isItemScanned.set(any()) } returns mockk()

        every { viewModel.readyItems[0].batchItem } returns batchItem
        every { viewModel.readyItems[0].haveScannedRfid } returns mockk()
        every { viewModel.readyItems[0].isItemScanned } returns mockk()

        viewModel.checkRfidText(rfidText)
        verify { eventBus.post(any<PackingItemUIEvent.OnScrollToPosition>()) }
    }

    @Test
    fun setupTitle_isOnDemand() {
        every { userStorage.isUserOnDemandAndRegionID() } returns true
        viewModel.setupCustomerTitle(shipment.customer?.name, rackName, rackSection)
        viewModel.customerName.get() shouldBeEqualTo context.getString(R.string.detail_item_on_demand_title)
                .format(shipment.customer?.name, rackName, rackSection)
    }

    @Test
    fun setupTitle_isNotOnDemand() {
        every { userStorage.isUserOnDemandAndRegionID() } returns false
        viewModel.setupCustomerTitle(shipment.customer?.name, rackName, rackSection)
        viewModel.customerName.get() shouldBeEqualTo context.getString(R.string.detail_item_title)
                .format(shipment.customer?.name, rackName, rackSection)
    }

    @Test
    fun checkBatchItemRfid() {
        val rfidText = "123456"
        val batchItem = MockProvider.batchItem(ProductStatus.PACKING, rfid = rfidText)
        val item = PackingItemViewModelImpl()
        item.batchItem = batchItem
        item.isItemScanned.set(false)
        item.haveScannedRfid.set(false)
        every { anyConstructed<PackingItemViewModelImpl>().bindViewModel(any(), any(), any()) } returns mockk()
        viewModel.readyItems.add(item)

        viewModel.checkBatchItemRfid(batchItem)

        viewModel.rfidList[rfidText]?.shouldBeFalse()
        viewModel.readyItems.first().haveScannedRfid.get() shouldBeEqualTo false
        viewModel.readyItems.first().isItemScanned.get() shouldBeEqualTo false
    }

    @Test
    fun setupLabelVisibility_ListIsNotEmpty() {
        mockkObject(LabelBoxViewModel.Companion)
        val mockLabel = mockk<LabelBoxViewModel>()
        val listLabel = listOf(mockLabel)

        every { LabelBoxViewModel.create(shipment) } returns listLabel

        viewModel.setupLabelVisibility(listLabel)

        viewModel.labelItems.size shouldBeEqualTo 1
        viewModel.labelItemVisibility.get() shouldBeEqualTo View.VISIBLE
    }

    @Test
    fun setupLabelVisibility_ListIsEmpty() {
        viewModel.setupLabelVisibility(listOf())
        viewModel.labelItems.size shouldBeEqualTo 0
        viewModel.labelItemVisibility.get() shouldBeEqualTo View.GONE
    }

    @Test
    fun checkSetupBusinessMethod_isSurpriseKit() {
        val shipment = shipment.copy(businessMethod = "SurpriseKit")
        viewModel.setupBusinessMethod(shipment)
        viewModel.businessMethodNameList.size shouldBeEqualTo 1
        viewModel.businessMethodNameList[0].serviceName.get() shouldBeEqualTo context.getString(R.string.packing_detail_surprise_kit_title)
    }

    @Test
    fun checkSetupBusinessMethod_isReselling() {
        val shipment = shipment.copy(businessMethod = "Reselling")
        viewModel.setupBusinessMethod(shipment)
        viewModel.businessMethodNameList.size shouldBeEqualTo 1
        viewModel.businessMethodNameList[0].serviceName.get() shouldBeEqualTo context.getString(R.string.packing_detail_reselling_title)
    }

    @Test
    fun checkSetupBusinessMethod() {
        viewModel.setupBusinessMethod(shipment)
        viewModel.businessMethodNameList.size shouldBeEqualTo 1
        viewModel.businessMethodNameList[0].serviceName.get() shouldBeEqualTo shipment.businessMethod
    }

    @Test
    fun checkSetupLogisticMethod() {
        viewModel.setupLogisticMethod(shipment)
        viewModel.logisticNameList.size shouldBeEqualTo 1
        viewModel.logisticNameList[0].serviceName.get() shouldBeEqualTo shipment.logisticProvider?.name.toString()
    }

    @Test
    fun checkSetupLogisticMethod_logisticNull() {
        val shipment = shipment.copy(logisticProvider = null)
        viewModel.setupLogisticMethod(shipment)
        viewModel.logisticNameList.size shouldBeEqualTo 1
        viewModel.logisticNameList[0].serviceName.get() shouldBeEqualTo ""
    }

    @Test
    fun checkSetupProcedureTypeMethod() {
        viewModel.setupProcedureTypeMethod(shipment)
        viewModel.procedureTypeList.size shouldBeEqualTo 1
        viewModel.procedureTypeList[0].serviceName.get() shouldBeEqualTo shipment.procedureType
    }

    @Test
    fun checkSetupProcedureTypeMethod_shouldVisible_amaze() {
        val shipment = shipment.copy(procedureType = "OneToOne")
        viewModel.setupProcedureTypeMethod(shipment)
        viewModel.procedureTypeList.size shouldBeEqualTo 1
        viewModel.procedureTypeList[0].serviceName.get() shouldBeEqualTo context.getString(ProcedureType.ONE_TO_ONE.stringRes)
        viewModel.procedureTypeVisibility.get() shouldBeEqualTo View.VISIBLE
    }

    @Test
    fun checkSetupProcedureTypeMethod_shouldVisible_amaze_lockAndGo() {
        val shipment = shipment.copy(procedureType = "OneToOneWithLock")
        viewModel.setupProcedureTypeMethod(shipment)
        viewModel.procedureTypeList.size shouldBeEqualTo 1
        viewModel.procedureTypeList[0].serviceName.get() shouldBeEqualTo context.getString(ProcedureType.ONE_TO_ONE_WITH_LOCK.stringRes)
        viewModel.procedureTypeVisibility.get() shouldBeEqualTo View.VISIBLE
    }

    @Test
    fun checkSetupProcedureTypeMethod_shouldVisible_lockAndGo() {
        val shipment = shipment.copy(procedureType = "RegularWithLock")
        viewModel.setupProcedureTypeMethod(shipment)
        viewModel.procedureTypeList.size shouldBeEqualTo 1
        viewModel.procedureTypeList[0].serviceName.get() shouldBeEqualTo context.getString(ProcedureType.REGULAR_WITH_LOCK.stringRes)
        viewModel.procedureTypeVisibility.get() shouldBeEqualTo View.VISIBLE
    }

    @Test
    fun checkSetupProcedureTypeMethod_shouldHide() {
        val shipment = shipment.copy(procedureType = "Regular")
        viewModel.setupProcedureTypeMethod(shipment)
        viewModel.procedureTypeList.size shouldBeEqualTo 0
        viewModel.procedureTypeVisibility.get() shouldBeEqualTo View.GONE
    }

    @Test
    fun checkSetupTitleDivider_shouldHitEventBus() {
        val tracking = MockProvider.shipmentTracking().copy(id = "")
        val shipment = shipment.copy(tracking = tracking)
        viewModel.setupTitleDivider(shipment)
        viewModel.titleVisibility.get() shouldBeEqualTo View.INVISIBLE
    }

    @Test
    fun setBoxRewardViewModel() {
        viewModel.setBoxRewardViewModel() shouldBeEqualTo boxRewardSectionVM
    }

    @Test
    fun setupBoxReward_boxReward_empty() {
        viewModel.selectedBoxRewards.clear()
        val box = MockProvider.shipmentBox().copy(boxReward = emptyList())
        viewModel.shipments = MockProvider.shipment().copy(box = box)
        viewModel.setupBoxReward(false)
        viewModel.boxRewardsSectionVisibility.get() shouldBeEqualTo View.GONE
    }

    @Test
    fun setupBoxReward_boxReward_null() {
        viewModel.selectedBoxRewards.clear()
        val box = MockProvider.shipmentBox().copy(boxReward = null)
        viewModel.shipments = MockProvider.shipment().copy(box = box)
        viewModel.setupBoxReward(false)
        viewModel.boxRewardsSectionVisibility.get() shouldBeEqualTo View.GONE
    }

    @Test
    fun setupBoxReward_box_null() {
        viewModel.selectedBoxRewards.clear()
        viewModel.shipments = MockProvider.shipment().copy(box = null)
        viewModel.setupBoxReward(false)
        viewModel.boxRewardsSectionVisibility.get() shouldBeEqualTo View.GONE
    }

    @Test
    fun setupBoxReward_shipment_null() {
        viewModel.selectedBoxRewards.clear()
        viewModel.shipments = null
        viewModel.setupBoxReward(false)
        viewModel.boxRewardsSectionVisibility.get() shouldBeEqualTo View.GONE
    }

    @Test
    fun setupBoxReward_notEmpty() {
        viewModel.selectedBoxRewards.clear()
        val rewards = listOf(MockProvider.boxReward())
        val box = MockProvider.shipmentBox().copy(boxReward = rewards)
        every { anyConstructed<BoxRewardSectionViewModelImpl>().setBoxRewards(rewards, false) } just Runs
        viewModel.shipments = MockProvider.shipment().copy(box = box)
        viewModel.setupBoxReward(false)
        viewModel.boxRewardsSectionVisibility.get() shouldBeEqualTo View.VISIBLE
    }

    @Test
    fun setSelectedBoxRewards_checked_true() {
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        viewModel.apply {
            val boxReward = MockProvider.boxReward()
            val selectedItem = mockk<ItemBoxRewardSectionViewModelImpl>()
            every { selectedItem.isRewardItemChecked.get() } returns true
            every { selectedItem.boxReward } returns boxReward
            selectedBoxRewards.add(selectedItem.boxReward)

            viewModel.setSelectedBoxRewards(selectedItem)

            selectedBoxRewards.size shouldBeEqualTo 1
        }
    }

    @Test
    fun setSelectedBoxRewards_checked_false() {
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        viewModel.apply {
            val boxReward = MockProvider.boxReward()
            val selectedItem = mockk<ItemBoxRewardSectionViewModelImpl>()
            every { selectedItem.isRewardItemChecked.get() } returns false
            every { selectedItem.boxReward } returns boxReward

            viewModel.setSelectedBoxRewards(selectedItem)

            selectedBoxRewards.size shouldBeEqualTo 0
        }
    }
}