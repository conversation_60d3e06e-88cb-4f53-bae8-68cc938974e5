package co.styletheory.ops.outbound.android.feature.settings

import android.view.View
import co.styletheory.android.network.core.APICallback
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.BaseBehaviorTest
import co.styletheory.ops.outbound.android.BuildConfig
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.settings.event.SettingsNetworkEvent
import co.styletheory.ops.outbound.android.feature.settings.event.SettingsUIEvent
import co.styletheory.ops.outbound.android.feature.settings.viewModel.impl.SettingsViewModelImpl
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.BatchConfig
import co.styletheory.ops.outbound.android.model.BatchGenerationTime
import co.styletheory.ops.outbound.android.model.enums.Barcode
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.util.DateConstant
import co.styletheory.ops.outbound.android.util.DateUtil
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel
import io.kotlintest.Spec
import io.kotlintest.extensions.TopLevelTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.amshove.kluent.shouldBeEqualTo
import java.util.*

/**
 * Created by Yoga C. Pranata on 2019-09-11.
 * Android Engineer
 */
class SettingsBehaviorTest : BaseBehaviorTest<SettingsViewModelImpl>(SettingsViewModelImpl()) {

    lateinit var userStorage: UserStorage
    lateinit var dataService: DataService
    lateinit var errorResponse: ErrorResponse
    lateinit var toolbarVM: GeneralToolbarViewModel

    override fun beforeSpecClass(spec: Spec, tests: List<TopLevelTest>) {
        super.beforeSpecClass(spec, tests)
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        userStorage = mockk(relaxed = true)
        dataService = mockk(relaxed = true)
        errorResponse = mockk(relaxed = true)
        toolbarVM = mockk(relaxed = true)

        viewModel.userStorage = userStorage
        viewModel.dataService = dataService
        viewModel.errorResponse = errorResponse
        viewModel.toolbarViewModel = toolbarVM
    }

    init {
        given("Check Toolbar Settings Page") {
            When("I open settings page") {
                Then("I should see toolbar section") {
                    viewModel.toolbarViewModel() shouldBeEqualTo toolbarVM
                }
            }
        }

        given("Check View Model Initialization") {
            When("All flag is ON") {
                every { userStorage.isBarcodeApparelFlagOn() } returns true
                every { userStorage.isBarcodeBagsFlagOn() } returns true
                every { userStorage.getBatchDate() } returns MockProvider.getSystemCalendar()

                viewModel.initializeViewModel()

                Then("Barcode Apparel and Bags Sections Should show and checked ON") {
                    viewModel.barcodeApparelRadioButton.get() shouldBeEqualTo R.id.radioBarcodeApparelOn
                    viewModel.barcodeBagsRadioButton.get() shouldBeEqualTo R.id.radioBarcodeBagsOn
                    viewModel.batchDate.get() shouldBeEqualTo DateUtil.createDateStringFrom(
                            MockProvider.getSystemCalendar().time, DateConstant.PATTERN.ddMMMyyyy
                    )
                }
            }

            When("All Flag is OFF") {
                every { userStorage.isBarcodeApparelFlagOn() } returns false
                every { userStorage.isBarcodeBagsFlagOn() } returns false
                every { userStorage.getBatchDate() } returns MockProvider.getSystemCalendar()

                viewModel.initializeViewModel()

                Then("Barcode Apparel and Bags Sections Should hide and checked OFF") {
                    viewModel.barcodeApparelRadioButton.get() shouldBeEqualTo R.id.radioBarcodeApparelOff
                    viewModel.barcodeBagsRadioButton.get() shouldBeEqualTo R.id.radioBarcodeBagsOff
                    viewModel.batchDate.get() shouldBeEqualTo DateUtil.createDateStringFrom(
                            MockProvider.getSystemCalendar().time, DateConstant.PATTERN.ddMMMyyyy
                    )
                }
            }

            When("Only Barcode Apparel is ON") {
                every { userStorage.isBarcodeApparelFlagOn() } returns true
                every { userStorage.isBarcodeBagsFlagOn() } returns false
                every { userStorage.getBatchDate() } returns MockProvider.getSystemCalendar()

                viewModel.initializeViewModel()

                Then("Barcode Apparel is ON and Barcode Bags is OFF") {
                    viewModel.barcodeApparelRadioButton.get() shouldBeEqualTo R.id.radioBarcodeApparelOn
                    viewModel.barcodeBagsRadioButton.get() shouldBeEqualTo R.id.radioBarcodeBagsOff
                    viewModel.batchDate.get() shouldBeEqualTo DateUtil.createDateStringFrom(
                            MockProvider.getSystemCalendar().time, DateConstant.PATTERN.ddMMMyyyy
                    )
                }
            }

            When("Only Barcode Bags is ON") {
                every { userStorage.isBarcodeApparelFlagOn() } returns false
                every { userStorage.isBarcodeBagsFlagOn() } returns true
                every { userStorage.getBatchDate() } returns MockProvider.getSystemCalendar()

                viewModel.initializeViewModel()

                Then("Barcode Apparel is ON and Barcode Bags is OFF") {
                    viewModel.barcodeApparelRadioButton.get() shouldBeEqualTo R.id.radioBarcodeApparelOff
                    viewModel.barcodeBagsRadioButton.get() shouldBeEqualTo R.id.radioBarcodeBagsOn
                    viewModel.batchDate.get() shouldBeEqualTo DateUtil.createDateStringFrom(
                            MockProvider.getSystemCalendar().time, DateConstant.PATTERN.ddMMMyyyy
                    )
                }
            }

            When("I open settings page in NOT Release version") {
                viewModel.setupVersionAppTitle(isDebugVersion = true)

                Then("I Should be able to see debug version title") {
                    viewModel.outboundAppVersion.get() shouldBeEqualTo mContext.getString(R.string.settings_app_version_debug)
                            .format(BuildConfig.VERSION_NAME, BuildConfig.BUILD_TYPE)
                }
            }

            When("I open settings page in Release version") {
                viewModel.setupVersionAppTitle(isDebugVersion = false)

                Then("I Should be able to see debug version title") {
                    viewModel.outboundAppVersion.get() shouldBeEqualTo mContext.getString(R.string.settings_app_version_release)
                            .format(BuildConfig.VERSION_NAME)
                }
            }
        }

        given("Check on Radio Button Clicked") {
            When("Barcode Apparel settings ON is Clicked") {
                val inputString = Barcode.APPAREL_ON.value
                every { userStorage.isBarcodeApparelFlagOn() } returns true
                viewModel.onRadioButtonChecked(inputString)

                Then("Barcode Apparel settings should ON") {
                    viewModel.barcodeApparelRadioButton.get() shouldBeEqualTo R.id.radioBarcodeApparelOn
                    userStorage.isBarcodeApparelFlagOn() shouldBeEqualTo true
                }
            }

            When("Barcode Apparel Settings OFF is Clicked") {
                val inputString = Barcode.APPAREL_OFF.value
                every { userStorage.isBarcodeApparelFlagOn() } returns false
                viewModel.onRadioButtonChecked(inputString)

                Then("Barcode Apparel settings should OFF") {
                    viewModel.barcodeApparelRadioButton.get() shouldBeEqualTo R.id.radioBarcodeApparelOff
                    userStorage.isBarcodeApparelFlagOn() shouldBeEqualTo false
                }
            }

            When("Barcode Bags Settings ON is Clicked") {
                val inputString = Barcode.BAGS_ON.value
                every { userStorage.isBarcodeBagsFlagOn() } returns true
                viewModel.onRadioButtonChecked(inputString)

                Then("Barcode bags settings should ON") {
                    viewModel.barcodeBagsRadioButton.get() shouldBeEqualTo R.id.radioBarcodeBagsOn
                    userStorage.isBarcodeBagsFlagOn() shouldBeEqualTo true
                }
            }

            When("Barcode Bags Settings OFF is Clicked") {
                val inputString = Barcode.BAGS_OFF.value
                every { userStorage.isBarcodeBagsFlagOn() } returns false
                viewModel.onRadioButtonChecked(inputString)

                Then("Barcode bags settings should OFF") {
                    viewModel.barcodeBagsRadioButton.get() shouldBeEqualTo R.id.radioBarcodeBagsOff
                    userStorage.isBarcodeBagsFlagOn() shouldBeEqualTo false
                }
            }
        }

        given("Check Generate Button") {
            When("Batch Generation Button is Clicked") {
                viewModel.onBtnGenerateBatchClicked()
                Then("Should call find nearest hour function") {
                    verify(exactly = 1) {
                        viewModel.findNearestHourGenerationTime()
                        eventBus.post(any<SettingsUIEvent.OnNullGenerationTime>())
                    }
                }
            }
        }

        given("Check Generate Batch Button Visibility") {
            When("Is User Ops Manager") {
                every { userStorage.isUserOperationManager() } returns true
                viewModel.setupBtnGenerateBatchVisibility()

                Then("Should show generate batch button") {
                    viewModel.btnGenerateBatchVisibility.get() shouldBeEqualTo View.VISIBLE
                }
            }

            When("Is User NOT Ops Manager") {
                every { userStorage.isUserOperationManager() } returns false
                viewModel.setupBtnGenerateBatchVisibility()

                Then("Should hide generate batch button") {
                    viewModel.btnGenerateBatchVisibility.get() shouldBeEqualTo View.GONE
                }
            }
        }

        given("Check Barcode Apparel Status") {
            When("Barcode Apparel is ON") {
                every { userStorage.isBarcodeApparelFlagOn() } returns true
                Then("Should check barcode apparel settings ON") {
                    viewModel.getBarcodeApparelStatus() shouldBeEqualTo R.id.radioBarcodeApparelOn
                }
            }

            When("Barcode Apparel is OFF") {
                every { userStorage.isBarcodeApparelFlagOn() } returns false
                Then("Should check barcode apparel settings OFF") {
                    viewModel.getBarcodeApparelStatus() shouldBeEqualTo R.id.radioBarcodeApparelOff
                }
            }
        }

        given("Check Barcode Bags Status") {
            When("Barcode Bags is ON") {
                every { userStorage.isBarcodeBagsFlagOn() } returns true
                Then("Should check barcode bags settings ON") {
                    viewModel.getBarcodeBagsStatus() shouldBeEqualTo R.id.radioBarcodeBagsOn
                }
            }

            When("Barcode Bags is OFF") {
                every { userStorage.isBarcodeBagsFlagOn() } returns false
                Then("Should check barcode bags settings OFF") {
                    viewModel.getBarcodeBagsStatus() shouldBeEqualTo R.id.radioBarcodeBagsOff
                }
            }
        }

        given("Check Show All Section") {
            When("All fetching data is finished") {
                viewModel.showAllSection()

                Then("Should hide Shimmer and show all sections") {
                    viewModel.shimmerVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.sectionVisibility.get() shouldBeEqualTo View.VISIBLE
                    verify { viewModel.setupBtnGenerateBatchVisibility() }
                }
            }
        }

        given("I see setting region section") {
            and("Region settings is active") {
                When("I check the Indonesia Region") {
                    val region = MockProvider.region().copy(name = "Indonesia")
                    viewModel.regionList.add(region)
                    viewModel.saveCheckedRegionByName(region.name.toString())

                    Then("Should save Indonesia region name into user storage") {
                        verify { userStorage.setUserRegion(region) }
                    }
                }
            }
        }

        given("Check save vertical type by name") {
            When("Vertical Type is active") {
                val vertical = MockProvider.vertical()
                viewModel.verticalTypeList.add(vertical)
                viewModel.saveCheckedVerticalTypeByName(vertical.name.toString())

                Then("Should save vertical type name into user storage") {
                    verify { userStorage.setUserVerticalType(vertical) }
                }
            }
        }

        given("Check clear region selection") {
            When("Region config has checked value") {
                val item = MockProvider.batchConfigItemRegionViewModel()
                item.isChecked.set(true)
                viewModel.regionConfigViewModelList.add(item)
                viewModel.clearRegionSelection()

                Then("Should clear all selection") {
                    viewModel.regionConfigViewModelList.first().isChecked.get() shouldBeEqualTo false
                }
            }
        }

        given("Check clear vertical type selection") {
            When("Vertical Type config has checked value") {
                val item = MockProvider.batchConfigItemVerticalTypeViewModel()
                item.isChecked.set(true)
                viewModel.verticalTypeConfigViewModelList.add(item)
                viewModel.clearVerticalSelection()

                Then("Should clear all selection") {
                    viewModel.verticalTypeConfigViewModelList.first().isChecked.get() shouldBeEqualTo false
                }
            }
        }

        given("Check set selected region") {
            When("Selected region is on the list") {
                val item = MockProvider.batchConfigItemRegionViewModel()
                val checkedRegion = "Indonesia"
                viewModel.regionConfigViewModelList.add(item)
                viewModel.setSelectedRegion(checkedRegion)

                Then("Selected regions should checked") {
                    viewModel.regionConfigViewModelList.first().isChecked.get() shouldBeEqualTo true
                    verify {
                        viewModel.clearRegionSelection()
                        viewModel.saveCheckedRegionByName(any())
                    }
                }
            }

            When("Selected region is NOT on the list") {
                val item = MockProvider.batchConfigItemRegionViewModel()
                val checkedRegion = "Hongkong"
                viewModel.regionConfigViewModelList.add(item)
                viewModel.setSelectedRegion(checkedRegion)

                Then("Should check the first region on the list") {
                    viewModel.regionConfigViewModelList.first().isChecked.get() shouldBeEqualTo true
                    verify {
                        viewModel.clearRegionSelection()
                        viewModel.saveCheckedRegionByName(any())
                    }
                }
            }
        }

        given("Check set selected vertical type") {
            When("Selected vertical is on the list") {
                val item = MockProvider.batchConfigItemVerticalTypeViewModel()
                val checkedVertical = "Apparel"
                viewModel.verticalTypeConfigViewModelList.add(item)
                viewModel.setSelectedVertical(checkedVertical)

                Then("Should checked selected vertical") {
                    viewModel.verticalTypeConfigViewModelList.first().isChecked.get() shouldBeEqualTo true
                    verify {
                        viewModel.clearVerticalSelection()
                        viewModel.saveCheckedVerticalTypeByName(any())
                    }
                }
            }

            When("Selected vertical is NOT on the list") {
                val item = MockProvider.batchConfigItemRegionViewModel()
                val checkedVertical = "Unlimited"
                viewModel.verticalTypeConfigViewModelList.add(item)
                viewModel.setSelectedVertical(checkedVertical)

                Then("Should check the first vertical type on the list") {
                    viewModel.verticalTypeConfigViewModelList.first().isChecked.get() shouldBeEqualTo true
                    verify {
                        viewModel.clearRegionSelection()
                        viewModel.saveCheckedRegionByName(any())
                    }
                }
            }
        }

        given("Check generate batch") {
            When("Call generate batch service Success") {
                every { dataService.generateBatchList(any(), any()) } answers {
                    val response = mockk<GraphQLData<Batch>>()
                    secondArg<APICallback<GraphQLData<Batch>, String>>().onSuccess(response)
                }
                viewModel.generateBatch("11 September 2019 01:00")

                Then("Should call on batch generated event bus") {
                    verify { eventBus.post(any<SettingsUIEvent.OnBatchIsGenerated>()) }
                }
            }

            When("Call generate batch service Error") {
                every { dataService.generateBatchList(any(), any()) } answers {
                    secondArg<APICallback<GraphQLData<Batch>, String>>().onError("Error")
                }
                viewModel.generateBatch("11 September 2019 01:00")

                Then("Should call on batch generated event bus") {
                    verify { eventBus.post(any<SettingsUIEvent.OnBatchIsGenerated>()) }
                }
            }
        }

        given("Check fetch batch configs") {
            When("Call fetch batch configs Success") {
                every { dataService.fetchBatchConfigs(any(), any()) } answers {
                    val response = mockk<GraphQLData<List<BatchConfig.Result>>>()
                    every { response.data.result } returns MockProvider.listOfBatchConfigResult()
                    secondArg<APICallback<GraphQLData<List<BatchConfig.Result>>, String>>().onSuccess(response)
                }
                every { userStorage.getUserLogin() } returns MockProvider.user().copy(roles = listOf("sg_staff"))
                viewModel.fetchBatchConfigs()

                Then("Should process batch configs") {
                    verify {
                        viewModel.processBatchConfigs(any())
                        eventBus.post(any<SettingsNetworkEvent.OnSuccessFetchBatchConfigs>())
                    }
                }
            }

            When("process batch config null") {
                every { dataService.fetchBatchConfigs(any(), any()) } answers {
                    val response = mockk<GraphQLData<List<BatchConfig.Result>>>()
                    every { response.data.result } returns MockProvider.listOfBatchConfigResult()
                    secondArg<APICallback<GraphQLData<List<BatchConfig.Result>>, String>>().onSuccess(response)
                }
                every { userStorage.getUserLogin() } returns null
                viewModel.fetchBatchConfigs()

                Then("Should process batch configs") {
                    verify {
                        viewModel.processBatchConfigs(any())
                        eventBus.post(any<SettingsNetworkEvent.OnSuccessFetchBatchConfigs>())
                    }
                }
            }

            When("Call fetch batch configs Success and Result is Null") {
                every { dataService.fetchBatchConfigs(any(), any()) } answers {
                    val response = mockk<GraphQLData<List<BatchConfig.Result>>>()
                    every { response.data.result } returns listOf()
                    secondArg<APICallback<GraphQLData<List<BatchConfig.Result>>, String>>().onSuccess(response)
                }
                viewModel.fetchBatchConfigs()

                Then("Should hit error batch configs generation") {
                    verify {
                        eventBus.post(any<SettingsNetworkEvent.OnErrorFetchBatchConfigs>())
                    }
                }
            }

            When("Call fetch batch configs Error") {
                every { dataService.fetchBatchConfigs(any(), any()) } answers {
                    secondArg<APICallback<GraphQLData<Batch>, String>>().onError("Error")
                }
                viewModel.fetchBatchConfigs()

                Then("Should hit error batch configs generation") {
                    verify { eventBus.post(any<SettingsNetworkEvent.OnErrorFetchBatchConfigs>()) }
                }
            }
        }

        given("Check Batch Date") {
            When("I click batch date section") {
                viewModel.batchDateClicked()

                Then("should show date picker dialog") {
                    verify { eventBus.post(any<SettingsUIEvent.OnBatchDateClicked>()) }
                }
            }
            When("I select a date from date picker dialog") {
                viewModel.setBatchDate(2019, 11, 6)

                Then("I should see selected batch date from batch date section") {
                    viewModel.selectedCalendar.get(Calendar.YEAR) shouldBeEqualTo 2019
                    viewModel.selectedCalendar.get(Calendar.MONTH) shouldBeEqualTo 11
                    viewModel.selectedCalendar.get(Calendar.DAY_OF_MONTH) shouldBeEqualTo 6
                    viewModel.batchDate.get() shouldBeEqualTo DateUtil.createDateStringFrom(
                            viewModel.selectedCalendar.time, DateConstant.PATTERN.ddMMMMyyyy
                    )
                }
            }
        }

        given("Check apply button") {
            When("I click apply filter") {
                viewModel.onBtnApplyClicked()
                Then("I should redirect to main page") {
                    verify { eventBus.post(any<SettingsUIEvent.OnApplyFilterClicked>()) }
                }
            }
        }

        given("Check Show Generate Page") {
            When("I open generate batch page") {
                viewModel.showGeneratePage()
                Then("I should see generate batch page") {
                    viewModel.isGeneratePage.get() shouldBeEqualTo true
                    viewModel.barcodeApparelVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.barcodeBagsVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.settingsPageVisibility.get() shouldBeEqualTo View.GONE
                    verify { viewModel.setupBtnGenerateBatchVisibility() }
                }
            }

            When("I open settings page") {
                And("All flag is ON") {
                    every { featureFlagUtil.barcodeApparelVisibility.get() } returns View.VISIBLE
                    every { featureFlagUtil.barcodeBagsVisibility.get() } returns View.VISIBLE
                    viewModel.showSettingsPage()

                    Then("I should see settings page and barcode section") {
                        viewModel.isGeneratePage.get() shouldBeEqualTo false
                        viewModel.settingsPageVisibility.get() shouldBeEqualTo View.VISIBLE
                        viewModel.btnGenerateBatchVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.barcodeApparelVisibility.get() shouldBeEqualTo View.VISIBLE
                        viewModel.barcodeBagsVisibility.get() shouldBeEqualTo View.VISIBLE
                    }
                }

                And("All flag is OFF") {
                    every { featureFlagUtil.barcodeApparelVisibility.get() } returns View.GONE
                    every { featureFlagUtil.barcodeBagsVisibility.get() } returns View.GONE
                    viewModel.showSettingsPage()

                    Then("I should see settings page and NOT see barcode section") {
                        viewModel.isGeneratePage.get() shouldBeEqualTo false
                        viewModel.settingsPageVisibility.get() shouldBeEqualTo View.VISIBLE
                        viewModel.btnGenerateBatchVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.barcodeApparelVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.barcodeBagsVisibility.get() shouldBeEqualTo View.GONE
                    }
                }
            }
        }

        given("Check findNearestHourGenerationTime") {
            val item = BatchGenerationTime()
            item.generationTimeId = "id"
            item.regionId = "sg"
            item.verticalTypeCode = "ap"
            item.hourGenerationTime = 1
            item.minutesGenerationTime = 0
            viewModel.batchGenerationTimeList.add(item)
            When("Batch Generation Time is not null") {
                viewModel.findNearestHourGenerationTime()
                Then("Should convertToFormattedGenerationTime") {
                    verify {
                        eventBus.post(any<SettingsUIEvent.OnBtnGenerateBatchClicked>())
                    }
                }
            }
        }

    }
}