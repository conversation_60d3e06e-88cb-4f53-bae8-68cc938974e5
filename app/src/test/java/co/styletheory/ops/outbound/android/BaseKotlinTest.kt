package co.styletheory.ops.outbound.android

import android.content.Context
import co.styletheory.android.network.core.APIManager
import co.styletheory.android.testkit.unitTest.BaseUnitTest
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.general.viewModel.base.ViewModel
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.util.featureflag.FeatureFlagUtil
import co.styletheory.ops.outbound.android.util.notNull
import io.mockk.impl.annotations.RelaxedMockK
import org.greenrobot.eventbus.EventBus
import org.junit.Ignore
import kotlin.reflect.KMutableProperty
import kotlin.reflect.full.declaredFunctions
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 18 June 2018.
 * Description
 *
 * <EMAIL>
 */

@Ignore("Base test no need to be tested")
abstract class BaseKotlinTest : BaseUnitTest() {
    @RelaxedMockK
    lateinit var context: Context
    @RelaxedMockK
    lateinit var dataService: DataService
    @RelaxedMockK
    lateinit var apiManager: APIManager
    @RelaxedMockK
    lateinit var eventBus: EventBus
    @RelaxedMockK
    lateinit var userStorage: UserStorage
    @RelaxedMockK
    lateinit var featureFlagUtil: FeatureFlagUtil
    @RelaxedMockK
    lateinit var errorResponse: ErrorResponse

    protected fun callPrivateFun(viewModel: ViewModel, functionName: String, vararg params: Any?): Any? {
        viewModel::class.declaredFunctions.find { it.name == functionName }.notNull {
            it.isAccessible = true
            return if (params.isEmpty())
                it.call(viewModel)
            else
                it.call(viewModel, *params)
        }
        return null
    }

    protected fun setPrivateField(viewModel: ViewModel, fieldName: String, value: Any?) {
        val memberProperties = viewModel::class.memberProperties.find { it.name == fieldName }
        memberProperties?.isAccessible = true
        if(memberProperties is KMutableProperty<*>)
            memberProperties.setter.call(viewModel, value)
    }

    @Suppress("UNCHECKED_CAST")
    protected fun <U> getPrivateFieldWithType(viewModel: ViewModel, fieldName: String): U {
        val memberProperties = viewModel::class.memberProperties.find { it.name == fieldName }
        memberProperties?.isAccessible = true
        return memberProperties?.call(viewModel) as U
    }
}