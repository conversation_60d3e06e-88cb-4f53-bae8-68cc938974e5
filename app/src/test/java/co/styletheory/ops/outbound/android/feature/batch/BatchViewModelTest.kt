package co.styletheory.ops.outbound.android.feature.batch

import co.styletheory.android.network.core.APICallback
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.batch.viewModel.BatchListViewModel
import co.styletheory.ops.outbound.android.feature.batch.viewModel.impl.BatchViewModelImpl
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.viewModelComponent.HeaderTotalBatchViewModel
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import org.amshove.kluent.shouldBeEqualTo
import org.amshove.kluent.shouldBeFalse
import org.amshove.kluent.shouldBeTrue
import org.junit.Test

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 13 December 2017.
 * Description
 *
 * <EMAIL>
 */
class BatchViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var batchViewModel: BatchViewModelImpl

    @InjectMockKs
    lateinit var headerTotalBatchViewModel: HeaderTotalBatchViewModel

    override fun setup() {
        super.setup()
        batchViewModel = spyk(batchViewModel, recordPrivateCalls = true)
        batchViewModel.dataService = dataService
        batchViewModel.headerTotalBatchViewModel = headerTotalBatchViewModel
    }

    @Test
    fun firstState() {
        batchViewModel.totalItemCount shouldBeEqualTo 0
        batchViewModel.dataService shouldBeEqualTo dataService
        batchViewModel.headerTotalBatchViewModel shouldBeEqualTo headerTotalBatchViewModel
        batchViewModel.items.size shouldBeEqualTo 0
        batchViewModel.showEmptyState.get().shouldBeTrue()
        batchViewModel.shouldShowEmptyState().shouldBeTrue()
    }

    @Test
    fun batchEmptyResult() {
        val itemSize = 0
        val batchList = MockProvider.emptyBatchListViewModel()

        batchViewModel.items = batchList

        batchViewModel.totalItemCount shouldBeEqualTo itemSize
        batchViewModel.shouldShowEmptyState().shouldBeTrue()
    }


    @Test
    fun batchWithResult() {
        val itemSize = 2
        val batchList = ArrayList<BatchListViewModel>()
        val item = mockk<BatchListViewModel>()
        batchList.add(item)

        batchViewModel.items = batchList

        batchViewModel.totalItemCount shouldBeEqualTo itemSize
        batchViewModel.shouldShowEmptyState().shouldBeFalse()
    }

    @Test
    fun setBatchStatus() {
        val batchStatus = BatchStatus.PACKED
        batchViewModel.setBatchStatus(batchStatus)
        batchViewModel.batchStatus() shouldBeEqualTo batchStatus
    }

    @Test
    fun fetchBatch_isRefresh_false() {
        val callback = mockk<Result<Void?, String?>>()
        batchViewModel.fetchBatch(callback)
        verify { batchViewModel.fetchBatch(any(), any()) }
    }

    @Test
    fun fetchBatch_isRefresh_true() {
        val callback = mockk<Result<Void?, String?>>()
        batchViewModel.refreshBatch(callback)
        verify { batchViewModel.fetchBatch(any(), any()) }
    }

    @Test
    fun fetchBatch_isRefresh_false_successResponse() {
        mockkConstructor(BatchListViewModel::class)
        val callback = mockk<Result<Void?, String?>>(relaxed = true)
        val batchListData = MockProvider.listOfBatch()
        val response = mockk<GraphQLData<List<Batch>>>()

        every { anyConstructed<BatchListViewModel>().bindViewModel(any(), any()) } returns mockk()
        every { dataService.fetchBatch(any(), any()) } answers {
            every { response.data.result } returns batchListData
            secondArg<APICallback<GraphQLData<List<Batch>>, String>>().onSuccess(response)
        }

        batchViewModel.fetchBatch(false, callback)

        verify(exactly = 1) { callback.success(null) }
    }

    @Test
    fun fetchBatch_isRefresh_false_successNullResponse() {
        val callback = mockk<Result<Void?, String?>>(relaxed = true)

        every { dataService.fetchBatch(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<List<Batch>>, String>>().onSuccess(null)
        }

        batchViewModel.fetchBatch(false, callback)

        verify(exactly = 1) { callback.success(null) }
    }

    @Test
    fun fetchBatch_isRefresh_false_errorResponse() {
        val callback = mockk<Result<Void?, String?>>(relaxed = true)
        val errorMessage = "Error Bro"

        every { dataService.fetchBatch(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<List<Batch>>, String>>().onError(errorMessage)
        }

        batchViewModel.fetchBatch(false, callback)

        verify { callback.failure(errorResponse.getErrorBodyDescription(errorMessage)) }
    }

    @Test
    fun getBatchListItems() {
        val item = MockProvider.emptyBatchListViewModel()
        batchViewModel.items.addAll(item)
        batchViewModel.getBatchListItems() shouldBeEqualTo item
    }

    @Test
    fun getViewModelAtPosition_getBatchItemType() {
        val items = listOf(BatchListViewModel(), BatchListViewModel())
        batchViewModel.items.addAll(items)
        batchViewModel.getViewModelAtPosition<BatchListViewModel>(1) shouldBeEqualTo items[0]
    }

    @Test
    fun getViewModelAtPosition_getHeaderItemType() {
        val headerItem = HeaderTotalBatchViewModel()
        batchViewModel.headerTotalBatchViewModel = headerItem
        batchViewModel.items.addAll(MockProvider.emptyBatchListViewModel())
        batchViewModel.getViewModelAtPosition<BatchListViewModel>(0) shouldBeEqualTo headerItem
    }

    @Test
    fun getLayoutIdForItemType_getHeaderItem() {
        batchViewModel.getLayoutIdForItemType(0) shouldBeEqualTo R.layout.header_total_batch_item_view
    }

    @Test
    fun getLayoutIdForItemType_getBatchItem() {
        batchViewModel.getLayoutIdForItemType(1) shouldBeEqualTo R.layout.batch_list_item
    }

}