package co.styletheory.ops.outbound.android.feature.goSend

import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.feature.goSend.event.GojekSelectedTabChangeEvent
import co.styletheory.ops.outbound.android.feature.goSend.viewModel.impl.GoSendViewModelImpl
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import org.amshove.kluent.shouldBeEqualTo
import org.junit.Test

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 19 June 2018.
 * Description
 *
 * <EMAIL>
 */
class GoSendViewModelTest : BaseKotlinTest() {
    @InjectMockKs
    lateinit var viewModel: GoSendViewModelImpl

    @Test
    fun onRadioButtonSelected() {
        val slot = slot<GojekSelectedTabChangeEvent>()
        every { eventBus.post(capture(slot)) } just Runs
        viewModel.onRadioButtonSelected(0)

        slot.captured.position shouldBeEqualTo 0
        verify { eventBus.post(any<GojekSelectedTabChangeEvent>()) }

    }
}