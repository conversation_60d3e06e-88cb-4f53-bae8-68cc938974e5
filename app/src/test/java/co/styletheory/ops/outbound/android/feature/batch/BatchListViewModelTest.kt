package co.styletheory.ops.outbound.android.feature.batch

import android.view.View
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.batch.viewModel.BatchListViewModel
import co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel.impl.LogisticProviderNameViewModelImpl
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.model.enums.LabelType
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.util.GenerateBatchByPerStatus
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.RelaxedMockK
import org.amshove.kluent.shouldBeEqualTo
import org.amshove.kluent.shouldBeInstanceOf
import org.junit.Test

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 15 December 2017.
 * Description
 *
 * <EMAIL>
 */

class BatchListViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: BatchListViewModel

    @RelaxedMockK
    lateinit var generateBatch: GenerateBatchByPerStatus

    @RelaxedMockK
    lateinit var logisticProviderNameVM: LogisticProviderNameViewModelImpl

    override fun setup() {
        super.setup()
        generateBatch = mockk(relaxed = true)
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        viewModel.generateBatch = generateBatch
        viewModel.logisticProviderNameSectionVM = logisticProviderNameVM
    }

    @Test
    fun firstState() {
        viewModel.batchTitle.get() shouldBeEqualTo ""
        viewModel.batchTitle.get() shouldBeEqualTo ""
        viewModel.totalItem.get() shouldBeEqualTo ""
        viewModel.totalBoxAndItem.get() shouldBeEqualTo ""
        viewModel.totalBox.get() shouldBeEqualTo ""
        viewModel.rack.get() shouldBeEqualTo ""
        viewModel.itemLeft.get() shouldBeEqualTo ""
        viewModel.labelItems.size shouldBeEqualTo 0
        viewModel.batchStepItems.size shouldBeEqualTo 0
    }

    @Test
    fun checkBindViewModel() {
        val batchStatus = BatchStatus.PICKING
        val batchItem = MockProvider.batchItem().copy(status = ProductStatus.PICKING)
        val batch = MockProvider.batch(batchItem)
        every { generateBatch.createStepFromBatch(any(), any()) } returns emptyList()
        viewModel.logisticProviderNameSectionVM = logisticProviderNameVM
        every { logisticProviderNameVM.bindViewModel(any()) } just Runs
        viewModel.bindViewModel(batch, batchStatus)

        viewModel.batchTitle.get() shouldBeEqualTo "${context.getString(R.string.rack_label)} ${batch.name}"
        viewModel.totalBoxAndItem.get() shouldBeEqualTo "1 ${context.getString(R.string.boxes_label)} - 1 ${
            context.getString(
                    R.string.items_label
            )
        }"
        viewModel.itemLeft.get() shouldBeEqualTo "1 ${context.getString(R.string.items_left_label)}"
        viewModel.batchStepItems.size shouldBeEqualTo 0
        viewModel.logisticProviderNameVisibility.get() shouldBeEqualTo View.VISIBLE
        verify { logisticProviderNameVM.bindViewModel(any()) }
    }

    @Test
    fun setupTotalBoxAndItem_picking() {
        val batchStatus = BatchStatus.PICKING
        val batchItem = MockProvider.batchItem().copy(status = ProductStatus.PICKING)
        val batch = MockProvider.batch(batchItem)

        viewModel.batch = batch
        viewModel.setupTotalBoxAndItem(batchStatus)

        viewModel.totalBox.get() shouldBeEqualTo "0 ${context.getString(R.string.boxes_picked_label)}"
        viewModel.totalItem.get() shouldBeEqualTo "0 ${context.getString(R.string.items_picked_label)}"
    }

    @Test
    fun setupTotalBoxAndItem_qaprocessing() {
        val batchStatus = BatchStatus.QA_PROCESSING
        val batchItem = MockProvider.batchItem().copy(status = ProductStatus.QA)
        val batch = MockProvider.batch(batchItem)

        viewModel.batch = batch
        viewModel.setupTotalBoxAndItem(batchStatus)

        viewModel.totalBox.get() shouldBeEqualTo "0 ${context.getString(R.string.boxes_qa_label)}"
        viewModel.totalItem.get() shouldBeEqualTo "0 ${context.getString(R.string.items_qa_label)}"
    }

    @Test
    fun setupTotalBoxAndItem_photo() {
        val batchStatus = BatchStatus.PHOTO
        val batchItem = MockProvider.batchItem().copy(status = ProductStatus.PHOTO_QA)
        val batch = MockProvider.batch(batchItem)

        viewModel.batch = batch
        viewModel.setupTotalBoxAndItem(batchStatus)

        viewModel.totalBox.get() shouldBeEqualTo "0 ${context.getString(R.string.boxes_photo_label)}"
        viewModel.totalItem.get() shouldBeEqualTo "0 ${context.getString(R.string.items_photo_label)}"
    }

    @Test
    fun setupTotalBoxAndItem_packing() {
        val batchStatus = BatchStatus.PACKING
        val batchItem = MockProvider.batchItem().copy(status = ProductStatus.PACKING)
        val batch = MockProvider.batch(batchItem)

        viewModel.batch = batch
        viewModel.setupTotalBoxAndItem(batchStatus)

        viewModel.totalBox.get() shouldBeEqualTo "0 ${context.getString(R.string.boxes_packed_label)}"
        viewModel.totalItem.get() shouldBeEqualTo "0 ${context.getString(R.string.items_packed_label)}"
    }

    @Test
    fun setupTotalBoxAndItem_else() {
        val batchStatus = BatchStatus.PHOTO_QA_DONE
        val batchItem = MockProvider.batchItem().copy(status = ProductStatus.PHOTO_QA_DONE)
        val batch = MockProvider.batch(batchItem)

        viewModel.batch = batch
        viewModel.setupTotalBoxAndItem(batchStatus)

        viewModel.totalBox.get() shouldBeEqualTo "0 ${context.getString(R.string.boxes_picked_label)}"
        viewModel.totalItem.get() shouldBeEqualTo "0 ${context.getString(R.string.items_picked_label)}"
    }

    @Test
    fun checkSetupLabelItems_isVip() {
        val batchItem = MockProvider.batchItem().copy(status = ProductStatus.PICKING)
        val batch = MockProvider.batch(batchItem).copy(isVip = true)
        viewModel.setupLabelItems(batch)
        viewModel.labelItems[0].labelType.get() shouldBeEqualTo LabelType.VIP
    }

    @Test
    fun checkSetupLabelItems_isNoPaper() {
        val batchItem = MockProvider.batchItem().copy(status = ProductStatus.PICKING)
        val batch = MockProvider.batch(batchItem).copy(isNoPaper = true)
        viewModel.setupLabelItems(batch)
        viewModel.labelItems[0].labelType.get() shouldBeEqualTo LabelType.NO_PAPER
    }

    @Test
    fun checkSetupLabelItems_isFirstBox() {
        val batchItem = MockProvider.batchItem().copy(status = ProductStatus.PICKING)
        val batch = MockProvider.batch(batchItem).copy(isFirstBox = true)
        viewModel.setupLabelItems(batch)
        viewModel.labelItems[0].labelType.get() shouldBeEqualTo LabelType.FIRST_BOX
    }

    @Test
    fun setupLogisticProviderNameViewModel() {
        viewModel.setupLogisticProviderNameViewModel()
        viewModel.setupLogisticProviderNameViewModel() shouldBeInstanceOf LogisticProviderNameViewModelImpl::class
    }
}