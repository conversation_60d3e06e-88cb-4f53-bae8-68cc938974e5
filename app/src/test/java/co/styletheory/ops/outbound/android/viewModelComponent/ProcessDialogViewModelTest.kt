package co.styletheory.ops.outbound.android.viewModelComponent

import android.content.Context
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.general.event.DismissDialogEvent
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.mockk
import io.mockk.mockkConstructor
import io.mockk.verify
import org.amshove.kluent.shouldBeEqualTo
import org.junit.Test

/**
 * <AUTHOR>
 * @since June 24, 2021
 * Created by <PERSON><PERSON><PERSON> on 24/06/2021.
 * Copyright (c) 2021 Style Theory. All rights reserved.
 */
class ProcessDialogViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: ProcessDialogViewModel

    @Test
    fun firstState() {
        viewModel.dialogTitle.get() shouldBeEqualTo ""
        viewModel.showSuccess.get() shouldBeEqualTo false
        viewModel.showError.get() shouldBeEqualTo false
        viewModel.showProgress.get() shouldBeEqualTo false
    }

    @Test
    fun check_containerClick() {
        viewModel.containerClick()
        verify(exactly = 1) { eventBus.post(any<DismissDialogEvent>()) }
    }

    @Test
    fun check_showLoading() {
        viewModel.showLoading(true)
        viewModel.showProgress.get() shouldBeEqualTo true
        viewModel.showSuccess.get() shouldBeEqualTo false
        viewModel.showError.get() shouldBeEqualTo false

        viewModel.showLoading(false)
        viewModel.showProgress.get() shouldBeEqualTo false
        viewModel.showSuccess.get() shouldBeEqualTo true
        viewModel.showError.get() shouldBeEqualTo true
    }

    @Test
    fun check_showSuccess() {
        viewModel.showSuccess(true)
        viewModel.showSuccess.get() shouldBeEqualTo true
        viewModel.showProgress.get() shouldBeEqualTo false
        viewModel.showError.get() shouldBeEqualTo false

        viewModel.showSuccess(false)
        viewModel.showSuccess.get() shouldBeEqualTo false
        viewModel.showProgress.get() shouldBeEqualTo true
        viewModel.showError.get() shouldBeEqualTo true
    }

    @Test
    fun check_showFailed() {
        viewModel.showFailed(true)
        viewModel.showError.get() shouldBeEqualTo true
        viewModel.showSuccess.get() shouldBeEqualTo false
        viewModel.showProgress.get() shouldBeEqualTo false

        viewModel.showFailed(false)
        viewModel.showError.get() shouldBeEqualTo false
        viewModel.showSuccess.get() shouldBeEqualTo true
        viewModel.showProgress.get() shouldBeEqualTo true
    }

    @Test
    fun check_resetSendShipment() {
        viewModel.resetSendShipment()
        viewModel.dialogTitle.get() shouldBeEqualTo "Currently sending box"
        viewModel.showProgress.get() shouldBeEqualTo true
    }

    @Test
    fun check_success() {
        mockkConstructor(ProcessDialogViewModel::class)
        val mockContext = mockk<Context>()
        every { mockContext.getString(any()) } returns "test_success"
        val model = ProcessDialogViewModel.success("test", mockContext)
        model.dialogTitle.get() shouldBeEqualTo "test_success"
        model.showSuccess.get() shouldBeEqualTo true
    }

    @Test
    fun check_failed() {
        mockkConstructor(ProcessDialogViewModel::class)
        val mockContext = mockk<Context>()
        every { mockContext.getString(any()) } returns "test_success"
        val model = ProcessDialogViewModel.failed(mockContext)
        model.dialogTitle.get() shouldBeEqualTo "test_success"
        model.showError.get() shouldBeEqualTo true
    }

    @Test
    fun check_sendShipment() {
        mockkConstructor(ProcessDialogViewModel::class)
        val model = ProcessDialogViewModel.sendShipment()
        verify(exactly = 1) { model.resetSendShipment() }
        model.dialogTitle.get() shouldBeEqualTo "Currently sending box"
        model.showProgress.get() shouldBeEqualTo true
    }
}