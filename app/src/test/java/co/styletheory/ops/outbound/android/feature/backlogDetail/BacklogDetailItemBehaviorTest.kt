package co.styletheory.ops.outbound.android.feature.backlogDetail

import android.view.View
import co.styletheory.android.network.core.APICallback
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.BaseBehaviorTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.backlogDetail.event.BacklogItemSwapClickEvent
import co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel.impl.BacklogDetailItemViewModelImpl
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.viewModelComponent.PhotoWithLabelViewModel
import io.kotlintest.Spec
import io.kotlintest.extensions.TopLevelTest
import io.mockk.*
import org.amshove.kluent.shouldBeEqualTo
import org.amshove.kluent.shouldBeFalse
import org.amshove.kluent.shouldBeTrue
import org.amshove.kluent.shouldNotBeEqualTo

/**
 * Created by Eminarti Sianturi on 2019-09-24.
 */
class BacklogDetailItemBehaviorTest : BaseBehaviorTest<BacklogDetailItemViewModelImpl>(BacklogDetailItemViewModelImpl()) {

    lateinit var userStorage: UserStorage
    lateinit var dataService: DataService
    lateinit var errorResponse: ErrorResponse
    private lateinit var photoWithLabelVM: PhotoWithLabelViewModel

    override fun beforeSpecClass(spec: Spec, tests: List<TopLevelTest>) {
        super.beforeSpecClass(spec, tests)
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        userStorage = mockk(relaxed = true)
        dataService = mockk(relaxed = true)
        errorResponse = mockk(relaxed = true)
        photoWithLabelVM = mockk(relaxed = true)

        viewModel.userStorage = userStorage
        viewModel.dataService = dataService
        viewModel.errorResponse = errorResponse
        viewModel.photoWithLabelVM = photoWithLabelVM
    }

    override fun resetFieldState() {
        super.resetFieldState()
        setPrivateField("rackSection", "")
        setPrivateField("shipmentId", "")

        viewModel.batch = null
        viewModel.boxId = ""
        viewModel.customerEmail = ""
        viewModel.batchItem = null

        viewModel.location.set("")
        viewModel.itemName.set("")
        viewModel.brand.set("")
        viewModel.itemSize.set("")
        viewModel.category.set("")
        viewModel.parts.set("")
        viewModel.rack.set("")
        viewModel.soldTo.set("")
        viewModel.errorMessage.set("")
        viewModel.swap.set(false)
        viewModel.picked.set(false)
        viewModel.missing.set(false)
        viewModel.showOverlayLoading.set(false)
        viewModel.notes.set("")
        viewModel.productOrder.set("")
        viewModel.colorItems.clear()
        viewModel.detachable.set("")
        viewModel.partsVisibility.set(View.GONE)
        viewModel.detachableVisibility.set(View.GONE)
        viewModel.scannerIconVisibility.set(View.GONE)
        viewModel.isPickedButtonEnabled.set(true)
        viewModel.swapButtonVisibility.set(View.VISIBLE)
    }

    init {
        given("I open the backlog detail with vertical type is bags") {
            `when`("I see backlog detail") {
                every { userStorage.isVerticalTypeBags() } returns true

                then("Batch is loading") {
                    viewModel.batch shouldBeEqualTo null
                    viewModel.batchItem shouldBeEqualTo null
                    viewModel.location.get() shouldBeEqualTo ""
                    viewModel.itemName.get() shouldBeEqualTo ""
                    viewModel.brand.get() shouldBeEqualTo ""
                    viewModel.itemSize.get() shouldBeEqualTo ""
                    viewModel.category.get() shouldBeEqualTo ""
                    viewModel.parts.get() shouldBeEqualTo ""
                    viewModel.rack.get() shouldBeEqualTo ""
                    viewModel.errorMessage.get() shouldBeEqualTo ""
                    viewModel.notes.get() shouldBeEqualTo ""
                    viewModel.productOrder.get() shouldBeEqualTo ""
                    viewModel.detachable.get() shouldBeEqualTo ""
                    viewModel.colorItems.size shouldBeEqualTo 0
                    viewModel.partsVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.detachableVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.scannerIconVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.swap.get().shouldBeFalse()
                    viewModel.picked.get().shouldBeFalse()
                    viewModel.missing.get().shouldBeFalse()
                    viewModel.showOverlayLoading.get().shouldBeFalse()
                }
            }

            and("Batch has shown") {
                val batchItem = MockProvider.batchItem(ProductStatus.AVAILABLE)
                val batch = MockProvider.batch(batchItem)
                val rackName = "A"
                val rackSection = "11"
                val shipmentId = "123-123-abc"
                val eventSlot = slot<String>()
                every { photoWithLabelVM.bindPhotoWithLabel(capture(eventSlot), any()) } just Runs

                `when`("Batch item already mapped") {
                    viewModel.setRackSection(rackSection)
                    viewModel.setShipmentId(shipmentId)
                    viewModel.bindViewModel(batch, batchItem)
                    viewModel.showBagsItems()

                    then("Item attribut has set") {
                        viewModel.location.get() shouldBeEqualTo batchItem.rowName
                        viewModel.itemName.get() shouldBeEqualTo "Apri"
                        viewModel.brand.get() shouldBeEqualTo "Apri"
                        viewModel.productOrder.get() shouldBeEqualTo "[9]"
                        viewModel.rack.get() shouldBeEqualTo mContext.getString(R.string.rack_name_regular)
                            .format(rackName, rackSection)
                        viewModel.category.get() shouldBeEqualTo "dress"
                        viewModel.parts.get() shouldBeEqualTo "Belt"
                        viewModel.notes.get() shouldBeEqualTo "notes"
                        viewModel.colorItems.size shouldBeEqualTo 0
                        viewModel.missing.get().shouldBeFalse()
                        viewModel.picked.get().shouldBeFalse()
                        viewModel.swap.get().shouldBeFalse()
                        viewModel.swapButtonVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.detachableVisibility.get() shouldBeEqualTo View.VISIBLE
                        viewModel.scannerIconVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.isPickedButtonEnabled.get().shouldBeTrue()
                        verify { photoWithLabelVM.bindPhotoWithLabel(any(), any()) }
                        eventSlot.captured shouldBeEqualTo "Url"
                    }
                }
            }
        }

        given("I open the backlog detail with vertical type is apparel") {

            `when`("I see backlog detail") {
                resetFieldState()
                every { userStorage.isVerticalTypeBags() } returns false

                then("Batch is loading") {
                    viewModel.batch shouldBeEqualTo null
                    viewModel.batchItem shouldBeEqualTo null
                    viewModel.location.get() shouldBeEqualTo ""
                    viewModel.itemName.get() shouldBeEqualTo ""
                    viewModel.brand.get() shouldBeEqualTo ""
                    viewModel.itemSize.get() shouldBeEqualTo ""
                    viewModel.category.get() shouldBeEqualTo ""
                    viewModel.parts.get() shouldBeEqualTo ""
                    viewModel.rack.get() shouldBeEqualTo ""
                    viewModel.errorMessage.get() shouldBeEqualTo ""
                    viewModel.notes.get() shouldBeEqualTo ""
                    viewModel.productOrder.get() shouldBeEqualTo ""
                    viewModel.detachable.get() shouldBeEqualTo ""
                    viewModel.colorItems.size shouldBeEqualTo 0
                    viewModel.partsVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.detachableVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.scannerIconVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.swap.get().shouldBeFalse()
                    viewModel.picked.get().shouldBeFalse()
                    viewModel.missing.get().shouldBeFalse()
                    viewModel.showOverlayLoading.get().shouldBeFalse()
                    verify { photoWithLabelVM.bindPhotoWithLabel(any(), any()) }
                }
            }

            and("Batch has shown") {
                val eventSlot = slot<String>()
                every { photoWithLabelVM.bindPhotoWithLabel(capture(eventSlot), any()) } just Runs

                every { userStorage.isVerticalTypeApparel() } returns true
                val batchItem = MockProvider.batchItem(ProductStatus.AVAILABLE)
                val batch = MockProvider.batch(batchItem)
                val rackName = "A"
                val rackSection = "11"
                val shipmentId = "123-123-abc"

                `when`("Batch item already mapped") {
                    viewModel.setRackSection(rackSection)
                    viewModel.setShipmentId(shipmentId)
                    viewModel.bindViewModel(batch, batchItem)
                    viewModel.showApparelItems()

                    then("Item attribut has set") {
                        viewModel.location.get() shouldBeEqualTo batchItem.rowName
                        viewModel.itemName.get() shouldBeEqualTo batchItem.rackName
                        viewModel.brand.get() shouldBeEqualTo "Apri"
                        viewModel.productOrder.get() shouldBeEqualTo "[9]"
                        viewModel.rack.get() shouldBeEqualTo mContext.getString(R.string.rack_name_regular)
                            .format(rackName, rackSection)
                        viewModel.category.get() shouldBeEqualTo "dress"
                        viewModel.parts.get() shouldBeEqualTo "Belt"
                        viewModel.notes.get() shouldBeEqualTo "notes"
                        viewModel.colorItems.size shouldBeEqualTo 0
                        viewModel.missing.get().shouldBeFalse()
                        viewModel.picked.get().shouldBeFalse()
                        viewModel.swap.get().shouldBeFalse()
                        viewModel.swapButtonVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.detachableVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.scannerIconVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.isPickedButtonEnabled.get().shouldBeTrue()
                        verify { photoWithLabelVM.bindPhotoWithLabel(any(), any()) }
                        eventSlot.captured shouldBeEqualTo "Url"
                    }
                }
            }
        }

        given("I open the backlog detail with vertical type is apparel and user is on demand region ID") {

            `when`("I see backlog detail") {
                resetFieldState()
                every { userStorage.isVerticalTypeBags() } returns false
                every { userStorage.isUserOnDemandAndRegionID() } returns true

                then("Batch is loading") {
                    viewModel.batch shouldBeEqualTo null
                    viewModel.batchItem shouldBeEqualTo null
                    viewModel.location.get() shouldBeEqualTo ""
                    viewModel.itemName.get() shouldBeEqualTo ""
                    viewModel.brand.get() shouldBeEqualTo ""
                    viewModel.itemSize.get() shouldBeEqualTo ""
                    viewModel.category.get() shouldBeEqualTo ""
                    viewModel.parts.get() shouldBeEqualTo ""
                    viewModel.rack.get() shouldBeEqualTo ""
                    viewModel.errorMessage.get() shouldBeEqualTo ""
                    viewModel.notes.get() shouldBeEqualTo ""
                    viewModel.productOrder.get() shouldBeEqualTo ""
                    viewModel.detachable.get() shouldBeEqualTo ""
                    viewModel.colorItems.size shouldBeEqualTo 0
                    viewModel.partsVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.detachableVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.scannerIconVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.swap.get().shouldBeFalse()
                    viewModel.picked.get().shouldBeFalse()
                    viewModel.missing.get().shouldBeFalse()
                    viewModel.showOverlayLoading.get().shouldBeFalse()
                    verify { photoWithLabelVM.bindPhotoWithLabel(any(), any()) }
                }
            }

            and("Batch has shown") {

                val batchItem = MockProvider.batchItem(ProductStatus.AVAILABLE)
                val batch = MockProvider.batch(batchItem)
                val rackName = "A"
                val rackSection = "11"
                val shipmentId = "123-123-abc"
                val eventSlot = slot<String>()
                every { photoWithLabelVM.bindPhotoWithLabel(capture(eventSlot), any()) } just Runs

                `when`("Batch item already mapped") {
                    viewModel.setRackSection(rackSection)
                    viewModel.setShipmentId(shipmentId)
                    viewModel.bindViewModel(batch, batchItem)
                    viewModel.showApparelItems()

                    then("Item attribut has set") {
                        viewModel.location.get() shouldBeEqualTo batchItem.rowName
                        viewModel.itemName.get() shouldBeEqualTo batchItem.rackName
                        viewModel.brand.get() shouldBeEqualTo "Apri"
                        viewModel.productOrder.get() shouldBeEqualTo "[9]"
                        viewModel.rack.get() shouldBeEqualTo mContext.getString(R.string.rack_name_on_demand)
                            .format(rackName, rackSection)
                        viewModel.category.get() shouldBeEqualTo "dress"
                        viewModel.parts.get() shouldBeEqualTo "Belt"
                        viewModel.notes.get() shouldBeEqualTo "notes"
                        viewModel.colorItems.size shouldBeEqualTo 0
                        viewModel.missing.get().shouldBeFalse()
                        viewModel.picked.get().shouldBeFalse()
                        viewModel.swap.get().shouldBeFalse()
                        viewModel.detachableVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.scannerIconVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.isPickedButtonEnabled.get().shouldBeTrue()
                        verify { photoWithLabelVM.bindPhotoWithLabel(any(), any()) }
                        eventSlot.captured shouldBeEqualTo "Url"
                    }
                }
            }

            `when`("The item is picking") {
                resetFieldState()

                val batchItem = MockProvider.batchItemStyleColor(ProductStatus.PICKING, null)
                val batch = MockProvider.batch(batchItem)
                val rackName = "A"
                val rackSection = "11"
                val shipmentId = "123-123-abc"
                val eventSlot = slot<String>()
                every { photoWithLabelVM.bindPhotoWithLabel(capture(eventSlot), any()) } just Runs

                every { userStorage.isVerticalTypeBags() } returns false
                viewModel.setRackSection(rackSection)
                viewModel.setShipmentId(shipmentId)
                viewModel.bindViewModel(batch, batchItem)
                viewModel.showApparelItems()

                then("Item attribut will be picking") {
                    viewModel.location.get() shouldBeEqualTo batchItem.rowName
                    viewModel.itemName.get() shouldBeEqualTo batchItem.rackName
                    viewModel.brand.get() shouldBeEqualTo "Apri"
                    viewModel.productOrder.get() shouldBeEqualTo "[9]"
                    viewModel.rack.get() shouldBeEqualTo mContext.getString(R.string.rack_name_regular)
                        .format(rackName, rackSection)
                    viewModel.category.get() shouldBeEqualTo "dress"
                    viewModel.parts.get() shouldBeEqualTo "Belt"
                    viewModel.notes.get() shouldBeEqualTo "notes"
                    viewModel.colorItems.size shouldNotBeEqualTo 0
                    viewModel.missing.get().shouldBeFalse()
                    viewModel.picked.get().shouldBeFalse()
                    viewModel.swap.get().shouldBeFalse()
                    viewModel.partsVisibility.get() shouldBeEqualTo View.VISIBLE
                    verify { photoWithLabelVM.bindPhotoWithLabel(any(), any()) }
                    eventSlot.captured shouldBeEqualTo "Url"
                }
            }

            and("I want to pick item") {
                `when`("Item is success to pick") {
                    viewModel.picked.set(false)

                    every { dataService.updateBatchItemStatus(any(), any()) } answers {
                        secondArg<APICallback<GraphQLData<Void>, String>>().onSuccess(null)
                    }

                    viewModel.pickedClick()

                    then("Batch status updated") {
                        viewModel.showOverlayLoading.get().shouldBeFalse()
                        viewModel.errorMessage.get() shouldBeEqualTo ""
                        viewModel.picked.get() shouldBeEqualTo true
                        viewModel.missing.get() shouldBeEqualTo false
                        viewModel.swap.get() shouldBeEqualTo false
                    }

                }

                `when`("Item is failed to pick") {
                    viewModel.picked.set(false)
                    val errorMessage = "Error"
                    every { dataService.updateBatchItemStatus(any(), any()) } answers {
                        secondArg<APICallback<GraphQLData<Void>, String>>().onError(errorMessage)
                    }

                    viewModel.pickedClick()

                    then("Batch status failed to update") {
                        viewModel.showOverlayLoading.get().shouldBeFalse()
                        viewModel.errorMessage.get() shouldBeEqualTo errorResponse.getErrorBodyDescription("Error")
                    }

                }
            }

            `when`("The item is picked") {
                resetFieldState()

                val batchItem = MockProvider.batchItemStyleColor(ProductStatus.PICKED, null)
                val batch = MockProvider.batch(batchItem)
                val rackName = "A"
                val rackSection = "11"
                val shipmentId = "123-123-abc"
                val eventSlot = slot<String>()
                every { photoWithLabelVM.bindPhotoWithLabel(capture(eventSlot), any()) } just Runs

                every { userStorage.isVerticalTypeBags() } returns false
                viewModel.setRackSection(rackSection)
                viewModel.setShipmentId(shipmentId)
                viewModel.bindViewModel(batch, batchItem)
                viewModel.showApparelItems()

                then("Item attribut will be picked") {
                    viewModel.location.get() shouldBeEqualTo batchItem.rowName
                    viewModel.itemName.get() shouldBeEqualTo batchItem.rackName
                    viewModel.brand.get() shouldBeEqualTo "Apri"
                    viewModel.productOrder.get() shouldBeEqualTo "[9]"
                    viewModel.rack.get() shouldBeEqualTo mContext.getString(R.string.rack_name_regular)
                        .format(rackName, rackSection)
                    viewModel.category.get() shouldBeEqualTo "dress"
                    viewModel.parts.get() shouldBeEqualTo "Belt"
                    viewModel.notes.get() shouldBeEqualTo "notes"
                    viewModel.colorItems.size shouldNotBeEqualTo 0
                    viewModel.missing.get().shouldBeFalse()
                    viewModel.picked.get().shouldBeTrue()
                    viewModel.swap.get().shouldBeFalse()
                    viewModel.partsVisibility.get() shouldBeEqualTo View.VISIBLE
                    verify { photoWithLabelVM.bindPhotoWithLabel(any(), any()) }
                    eventSlot.captured shouldBeEqualTo "Url"
                }
            }

            `when`("The item is missing") {
                resetFieldState()

                val batchItem = MockProvider.batchItemStyleColor(ProductStatus.MISSING, null)
                val batch = MockProvider.batch(batchItem)
                val rackName = "A"
                val rackSection = "11"
                val shipmentId = "123-123-abc"
                val eventSlot = slot<String>()
                every { photoWithLabelVM.bindPhotoWithLabel(capture(eventSlot), any()) } just Runs

                every { userStorage.isVerticalTypeBags() } returns false
                viewModel.setRackSection(rackSection)
                viewModel.setShipmentId(shipmentId)
                viewModel.bindViewModel(batch, batchItem)
                viewModel.showApparelItems()

                then("Item attribut will be missing") {
                    viewModel.location.get() shouldBeEqualTo batchItem.rowName
                    viewModel.itemName.get() shouldBeEqualTo batchItem.rackName
                    viewModel.brand.get() shouldBeEqualTo "Apri"
                    viewModel.productOrder.get() shouldBeEqualTo "[9]"
                    viewModel.rack.get() shouldBeEqualTo mContext.getString(R.string.rack_name_regular)
                        .format(rackName, rackSection)
                    viewModel.category.get() shouldBeEqualTo "dress"
                    viewModel.parts.get() shouldBeEqualTo "Belt"
                    viewModel.notes.get() shouldBeEqualTo "notes"
                    viewModel.colorItems.size shouldNotBeEqualTo 0
                    viewModel.missing.get().shouldBeTrue()
                    viewModel.picked.get().shouldBeFalse()
                    viewModel.swap.get().shouldBeTrue()
                    viewModel.partsVisibility.get() shouldBeEqualTo View.VISIBLE
                    verify { photoWithLabelVM.bindPhotoWithLabel(any(), any()) }
                    eventSlot.captured shouldBeEqualTo "Url"
                }
            }

            and("The item is not found") {
                `when`("Update status item success") {
                    viewModel.missing.set(false)

                    every { dataService.updateBatchItemStatus(any(), any()) } answers {
                        secondArg<APICallback<GraphQLData<Void>, String>>().onSuccess(null)
                    }

                    viewModel.missingClick()

                    then("Batch status updated") {
                        viewModel.showOverlayLoading.get().shouldBeFalse()
                        viewModel.errorMessage.get() shouldBeEqualTo ""
                        viewModel.picked.get() shouldBeEqualTo false
                        viewModel.missing.get() shouldBeEqualTo true
                        viewModel.swap.get() shouldBeEqualTo true
                    }
                }

                `when`("Update status failed") {
                    viewModel.missing.set(false)
                    val errorMessage = "Error"
                    every { dataService.updateBatchItemStatus(any(), any()) } answers {
                        secondArg<APICallback<GraphQLData<Void>, String>>().onError(errorMessage)
                    }

                    viewModel.missingClick()

                    then("Batch status failed to update") {
                        viewModel.showOverlayLoading.get().shouldBeFalse()
                        viewModel.errorMessage.get() shouldBeEqualTo errorResponse.getErrorBodyDescription("Error")
                    }
                }

                `when`("I want to swap item") {
                    viewModel.swapClick()

                    then("Swap dialog appear") {
                        verify { eventBus.post(any<BacklogItemSwapClickEvent>()) }
                    }
                }
            }
        }

        given("I open the backlog detail with vertical type is apparel and RFID is on") {

            `when`("I see backlog detail") {
                resetFieldState()

                every { featureFlagUtil.isBarcodeSettingIsOn() } returns true
                every { userStorage.isVerticalTypeBags() } returns false

                then("Batch is loading") {
                    viewModel.batch shouldBeEqualTo null
                    viewModel.batchItem shouldBeEqualTo null
                    viewModel.location.get() shouldBeEqualTo ""
                    viewModel.itemName.get() shouldBeEqualTo ""
                    viewModel.brand.get() shouldBeEqualTo ""
                    viewModel.itemSize.get() shouldBeEqualTo ""
                    viewModel.category.get() shouldBeEqualTo ""
                    viewModel.parts.get() shouldBeEqualTo ""
                    viewModel.rack.get() shouldBeEqualTo ""
                    viewModel.errorMessage.get() shouldBeEqualTo ""
                    viewModel.notes.get() shouldBeEqualTo ""
                    viewModel.productOrder.get() shouldBeEqualTo ""
                    viewModel.detachable.get() shouldBeEqualTo ""
                    viewModel.colorItems.size shouldBeEqualTo 0
                    viewModel.partsVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.detachableVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.scannerIconVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.swap.get().shouldBeFalse()
                    viewModel.picked.get().shouldBeFalse()
                    viewModel.missing.get().shouldBeFalse()
                    viewModel.showOverlayLoading.get().shouldBeFalse()
                    verify { photoWithLabelVM.bindPhotoWithLabel(any(), any()) }
                }
            }

            and("Batch has shown") {

                val batchItem = MockProvider.batchItem(ProductStatus.AVAILABLE)
                val batch = MockProvider.batch(batchItem)
                val rackName = "A"
                val rackSection = "11"
                val shipmentId = "123-123-abc"
                val eventSlot = slot<String>()
                every { photoWithLabelVM.bindPhotoWithLabel(capture(eventSlot), any()) } just Runs

                `when`("Batch item already mapped") {
                    viewModel.setupItemContainRfid()
                    viewModel.setRackSection(rackSection)
                    viewModel.setShipmentId(shipmentId)
                    viewModel.bindViewModel(batch, batchItem)
                    viewModel.showApparelItems()

                    then("Item attribut has set") {
                        viewModel.location.get() shouldBeEqualTo batchItem.rowName
                        viewModel.itemName.get() shouldBeEqualTo batchItem.rackName
                        viewModel.brand.get() shouldBeEqualTo "Apri"
                        viewModel.productOrder.get() shouldBeEqualTo "[9]"
                        viewModel.rack.get() shouldBeEqualTo mContext.getString(R.string.rack_name_regular)
                            .format(rackName, rackSection)
                        viewModel.category.get() shouldBeEqualTo "dress"
                        viewModel.parts.get() shouldBeEqualTo "Belt"
                        viewModel.notes.get() shouldBeEqualTo "notes"
                        viewModel.colorItems.size shouldBeEqualTo 0
                        viewModel.missing.get().shouldBeFalse()
                        viewModel.picked.get().shouldBeFalse()
                        viewModel.swap.get().shouldBeFalse()
                        viewModel.detachableVisibility.get() shouldBeEqualTo View.GONE
                        viewModel.scannerIconVisibility.get() shouldBeEqualTo View.VISIBLE
                        verify { photoWithLabelVM.bindPhotoWithLabel(any(), any()) }
                        eventSlot.captured shouldBeEqualTo "Url"
                    }
                }

                `when`("Picked is false") {
                    viewModel.picked.set(false)
                    viewModel.isComplete()
                    then("Not complete yet") {
                        viewModel.isComplete().shouldBeFalse()
                    }
                }

                `when`("Picked is true") {
                    viewModel.picked.set(true)
                    viewModel.isComplete()
                    then("Already complete") {
                        viewModel.isComplete().shouldBeTrue()
                    }
                }
            }
        }

        given("I open Reselling picking detail") {
            `when`("I have a purchased item") {
                resetFieldState()
                val purchasedBy = "<NAME_EMAIL>"
                val batchItem = MockProvider.batchItem(ProductStatus.PACKING).copy(label = purchasedBy)
                viewModel.setupPurchasedItem(batchItem)

                then("I should be able to see purchased label") {
                    viewModel.itemPurchasedVisibility.get() shouldBeEqualTo View.VISIBLE
                    viewModel.soldTo.get() shouldBeEqualTo purchasedBy
                }
            }

            `when`("The purchased item is empty") {
                resetFieldState()
                val batchItem = MockProvider.batchItem(ProductStatus.PICKING).copy(label = "")
                viewModel.setupPurchasedItem(batchItem)

                then("I should NOT be able to see purchased label") {
                    viewModel.itemPurchasedVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.soldTo.get() shouldBeEqualTo ""
                }
            }

            `when`("I don't have a purchased item") {
                resetFieldState()
                val batchItem = MockProvider.batchItem(ProductStatus.PICKING).copy(label = null)
                viewModel.setupPurchasedItem(batchItem)

                then("I should NOT be able to see purchased label") {
                    viewModel.itemPurchasedVisibility.get() shouldBeEqualTo View.GONE
                    viewModel.soldTo.get() shouldBeEqualTo ""
                }
            }

            `when`("I have reselling item") {
                resetFieldState()
                every { featureFlagUtil.isPickingSwapButtonEnabled() } returns true
                every { featureFlagUtil.isQualitySwapResellingDisabled(any()) } returns true
                val batchItem = MockProvider.batchItem(ProductStatus.PICKING).copy(label = "<NAME_EMAIL>")
                viewModel.setupSwapButtonVisibility(batchItem)

                Then("I should NOT see the swap button") {
                    viewModel.swapButtonVisibility.get() shouldBeEqualTo View.GONE
                }
            }

            `when`("I don't have reselling item") {
                resetFieldState()
                every { userStorage.isUserOnDemandAndRegionID() } returns false
                every { userStorage.isVerticalTypeBags() } returns false
                every { featureFlagUtil.isQualitySwapResellingDisabled(any()) } returns false
                every { featureFlagUtil.isPickingSwapButtonEnabled() } returns true
                val batchItem = MockProvider.batchItem(ProductStatus.PICKING).copy(label = "Vip")
                viewModel.setupSwapButtonVisibility(batchItem)

                Then("I should see the swap button") {
                    viewModel.swapButtonVisibility.get() shouldBeEqualTo View.VISIBLE
                }
            }
            `when`("Feature flag Picking Swap is OFF") {
                resetFieldState()
                every { featureFlagUtil.isPickingSwapButtonEnabled() } returns false
                val batchItem = MockProvider.batchItem(ProductStatus.PICKING).copy(label = "<NAME_EMAIL>")
                viewModel.setupSwapButtonVisibility(batchItem)

                Then("I should NOT see the swap button") {
                    viewModel.swapButtonVisibility.get() shouldBeEqualTo View.GONE
                }
            }
        }

        given("I see photo item") {
            `when`("I want to see photo with label") {
                viewModel.setPhotoWithLabelViewModel() shouldBeEqualTo viewModel.photoWithLabelVM
            }

        }

        given("I'm not picking item") {
            `when`("I haven't picking item and button is disabled") {
                viewModel.picked.set(false)
                viewModel.setupPickedButton(false)

                then("I should see button enabled") {
                    viewModel.isPickedButtonEnabled.get() shouldBeEqualTo false
                }
            }

            `when`("I haven't picking item and button is enabled") {
                viewModel.picked.set(false)
                viewModel.setupPickedButton(true)

                then("I should see button enabled") {
                    viewModel.isPickedButtonEnabled.get() shouldBeEqualTo true
                }
            }

        }
    }

}