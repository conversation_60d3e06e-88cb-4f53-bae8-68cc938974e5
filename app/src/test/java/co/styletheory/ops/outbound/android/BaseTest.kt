package co.styletheory.ops.outbound.android

import android.content.Context
import co.styletheory.android.network.core.APIManager
import co.styletheory.android.testkit.unitTest.BaseUnitTest
import co.styletheory.ops.outbound.android.general.storage.UserStorage
import co.styletheory.ops.outbound.android.networking.DataService
import co.styletheory.ops.outbound.android.util.ErrorResponse
import co.styletheory.ops.outbound.android.util.featureflag.FeatureFlagUtil
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import org.greenrobot.eventbus.EventBus
import org.junit.Ignore
import org.mockito.Mock

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 13 December 2017.
 * Description
 *
 * <EMAIL>
 */

@Ignore(("Base test no need to be tested"))
abstract class BaseTest : BaseUnitTest() {
    @Mock
    protected lateinit var context: Context

    @Mock
    protected lateinit var eventBus: EventBus

    @Mock
    protected lateinit var dataService: DataService

    @Mock
    protected lateinit var apiManager: APIManager

    @Mock
    lateinit var userStorage: UserStorage

    @Mock
    lateinit var featureFlagUtil: FeatureFlagUtil

    @Mock
    lateinit var errorResponse: ErrorResponse

    override fun setup() {
        super.setup()
        whenever(dataService.apiManager).thenReturn(mock())
    }
}