package co.styletheory.ops.outbound.android.feature.logisticProviderName

import androidx.recyclerview.widget.LinearLayoutManager
import co.styletheory.ops.outbound.android.BaseBehaviorTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel.impl.LogisticProviderNameViewModelImpl
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.general.recyclerView.RecyclerViewSetting
import org.amshove.kluent.shouldBeEqualTo

class LogisticProviderNameBehaviorTest : BaseBehaviorTest<LogisticProviderNameViewModelImpl>(LogisticProviderNameViewModelImpl()) {

    init {
        given("user want to see logistic provider name list") {
            `when`("have logistic provider") {
                val provider = MockProvider.logisticProvider()
                viewModel.bindViewModelProvider(provider)
                then("user must see logistic provider name list") {
                    viewModel.apply {
                        recyclerViewLogisticName.layoutId shouldBeEqualTo R.layout.item_logistic_provider_name
                        recyclerViewLogisticName.orientation shouldBeEqualTo LinearLayoutManager.HORIZONTAL
                        recyclerViewLogisticName.layoutManagerType shouldBeEqualTo RecyclerViewSetting.LayoutManagerType.LINEAR
                        logisticNameItems.size shouldBeEqualTo 1
                    }
                }
            }

            `when`("have logistic provider list") {
                val provider = MockProvider.logisticProvider()
                viewModel.bindViewModel(listOf(provider))
                then("user must see logistic provider name list") {
                    viewModel.apply {
                        recyclerViewLogisticName.layoutId shouldBeEqualTo R.layout.item_logistic_provider_name
                        recyclerViewLogisticName.orientation shouldBeEqualTo LinearLayoutManager.HORIZONTAL
                        recyclerViewLogisticName.layoutManagerType shouldBeEqualTo RecyclerViewSetting.LayoutManagerType.LINEAR
                        logisticNameItems.size shouldBeEqualTo 1
                    }
                }
            }
        }

    }
}