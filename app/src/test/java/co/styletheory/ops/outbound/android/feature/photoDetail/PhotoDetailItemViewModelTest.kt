package co.styletheory.ops.outbound.android.feature.photoDetail

import android.view.View
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.photoDetail.viewModel.impl.PhotoDetailItemViewModelImpl
import co.styletheory.ops.outbound.android.feature.photoDetail.viewModel.impl.PhotoItemViewModel
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.util.GeneralCallback
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.mockk
import io.mockk.mockkConstructor
import org.amshove.kluent.shouldBeEqualTo
import org.amshove.kluent.shouldBeFalse
import org.amshove.kluent.shouldBeTrue
import org.amshove.kluent.shouldNotBeEqualTo
import org.apache.commons.lang3.reflect.FieldUtils
import org.junit.Test

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 17 December 2017.
 * Description
 *
 * <EMAIL>
 */

class PhotoDetailItemViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: PhotoDetailItemViewModelImpl

    private val shipment = MockProvider.shipment()
    private val rackSection = "12"
    private val rackName = "A"

    private fun setPrivateField(fieldName: String, value: Any?) {
        FieldUtils.writeDeclaredField(viewModel, fieldName, value, true)
    }

    private fun getPrivateField(fieldName: String): Any? {
        return FieldUtils.readDeclaredField(viewModel, fieldName, true)
    }

    override fun setup() {
        super.setup()
        viewModel.setRackName(rackName)
        viewModel.setRackSection(rackSection)
        viewModel.setShipment(shipment)
    }

    @Test
    fun firstState() {
        viewModel.title.get() shouldBeEqualTo ""
        viewModel.titleVisibility.get() shouldBeEqualTo View.VISIBLE
        viewModel.customerName.get() shouldBeEqualTo ""
        viewModel.completeButtonText.get() shouldBeEqualTo ""
        viewModel.isComplete.get().shouldBeFalse()
        viewModel.labelItems.size shouldBeEqualTo 0
        viewModel.qaPhotoItems.size shouldBeEqualTo 0
    }

    @Test
    fun checkMapShipment() {
        val shipment = shipment.copy(isComplete = true)
        viewModel.setShipment(shipment)

        viewModel.mapShipment()

        viewModel.customerName.get() shouldBeEqualTo context.getString(R.string.rack_name_photo_detail)
            .format("John", "$rackName$rackSection")
        viewModel.title.get() shouldBeEqualTo "12"
        viewModel.isComplete.get().shouldBeTrue()
        viewModel.qaPhotoItems.size shouldBeEqualTo 0
        viewModel.labelItems.size shouldBeEqualTo 0
        viewModel.completeButtonText.get() shouldBeEqualTo context.getString(R.string.complete_label)
    }

    @Test
    fun checkMapShipment_WithStatus_PhotoQA() {
        val batchItem = MockProvider.batchItem(ProductStatus.PHOTO_QA)
        val shipment = MockProvider.shipment(listOf(batchItem))

        val photoItem = mockk<PhotoItemViewModel>()

        mockkConstructor(photoItem::class)

        every { anyConstructed<PhotoItemViewModel>().bindViewModel(any(), any(), any(), any()) } returns mockk()
        every { anyConstructed<PhotoItemViewModel>().setBatchId(any()) } returns mockk()

        val call = mockk<GeneralCallback>()
        every { photoItem.refreshCompleteState } returns call

        viewModel.setShipment(shipment)

        viewModel.mapShipment()

        viewModel.customerName.get() shouldBeEqualTo context.getString(R.string.rack_name_photo_detail)
            .format("John", "$rackName$rackSection")
        viewModel.title.get() shouldBeEqualTo "12"
        viewModel.isComplete.get().shouldBeFalse()
        viewModel.qaPhotoItems.size shouldNotBeEqualTo 0
        viewModel.labelItems.size shouldBeEqualTo 0
        viewModel.completeButtonText.get() shouldBeEqualTo context.getString(R.string.complete_label)
    }

    @Test
    fun checkMapShipment_WithStatus_PhotoQaDone() {
        val batchItem = MockProvider.batchItem(ProductStatus.PHOTO_QA_DONE)
        val shipment = MockProvider.shipment(listOf(batchItem))

        mockkConstructor(PhotoItemViewModel::class)
        every { anyConstructed<PhotoItemViewModel>().bindViewModel(any(), any(), any(), any()) } returns mockk()
        every { anyConstructed<PhotoItemViewModel>().setBatchId(any()) } returns mockk()

        viewModel.setShipment(shipment)

        viewModel.mapShipment()

        viewModel.customerName.get() shouldBeEqualTo context.getString(R.string.rack_name_photo_detail)
            .format("John", "$rackName$rackSection")
        viewModel.title.get() shouldBeEqualTo "12"
        viewModel.isComplete.get().shouldBeFalse()
        viewModel.qaPhotoItems.size shouldNotBeEqualTo 0
        viewModel.labelItems.size shouldBeEqualTo 0
        viewModel.completeButtonText.get() shouldBeEqualTo context.getString(R.string.complete_label)
    }

    @Test
    fun checkMapShipment_WithStatus_QA_PASSED() {
        val batchItem = MockProvider.batchItem(ProductStatus.QA_PASSED)
        val shipment = MockProvider.shipment(listOf(batchItem))

        mockkConstructor(PhotoItemViewModel::class)
        every { anyConstructed<PhotoItemViewModel>().bindViewModel(any(), any(), any(), any()) } returns mockk()
        every { anyConstructed<PhotoItemViewModel>().setBatchId(any()) } returns mockk()

        viewModel.setShipment(shipment)

        viewModel.mapShipment()

        viewModel.customerName.get() shouldBeEqualTo context.getString(R.string.rack_name_photo_detail)
            .format("John", "$rackName$rackSection")
        viewModel.title.get() shouldBeEqualTo "12"
        viewModel.isComplete.get().shouldBeFalse()
        viewModel.qaPhotoItems.size shouldNotBeEqualTo 0
        viewModel.labelItems.size shouldBeEqualTo 0
        viewModel.completeButtonText.get() shouldBeEqualTo context.getString(R.string.complete_label)
    }

    @Test
    fun checkMapShipment_WithTrackingIdEmpty() {
        val batchItem = MockProvider.batchItem(ProductStatus.PHOTO_QA)
        val shipment = MockProvider.shipmentEmptyTrackingId(listOf(batchItem))

        mockkConstructor(PhotoItemViewModel::class)
        every { anyConstructed<PhotoItemViewModel>().bindViewModel(any(), any(), any(), any()) } returns mockk()
        every { anyConstructed<PhotoItemViewModel>().setBatchId(any()) } returns mockk()

        viewModel.setShipment(shipment)

        viewModel.mapShipment()

        viewModel.customerName.get() shouldBeEqualTo context.getString(R.string.rack_name_photo_detail)
            .format("John", "$rackName$rackSection")
        viewModel.title.get() shouldBeEqualTo ""
        viewModel.titleVisibility.get() shouldBeEqualTo View.GONE
        viewModel.isComplete.get().shouldBeFalse()
        viewModel.qaPhotoItems.size shouldNotBeEqualTo 0
        viewModel.labelItems.size shouldBeEqualTo 0
        viewModel.completeButtonText.get() shouldBeEqualTo context.getString(R.string.complete_label)
    }

    @Test
    fun getRackName_isUserOnDemand() {
        val rackName = "M1"
        val rackSection = "A"

        every { userStorage.isUserOnDemandAndRegionID() } returns true

        viewModel.setRackName(rackName)
        viewModel.setRackSection(rackSection)
        viewModel.getRackName() shouldBeEqualTo context.getString(R.string.rack_name_on_demand)
            .format(rackName, rackSection)
    }

    @Test
    fun getRackName_isUserNotOnDemand() {
        val rackName = "A"
        val rackSection = "11"

        every { userStorage.isUserOnDemandAndRegionID() } returns false

        viewModel.setRackName(rackName)
        viewModel.setRackSection(rackSection)
        viewModel.getRackName() shouldBeEqualTo context.getString(R.string.rack_name_regular)
            .format(rackName, rackSection)
    }

    @Test
    fun setBatchId() {
        viewModel.setBatchId("ABC")

        getPrivateField("batchId") shouldBeEqualTo "ABC"
    }

    @Test
    fun setBatchId_WithNull() {
        viewModel.setBatchId(null)

        getPrivateField("batchId") shouldBeEqualTo ""
    }

    @Test
    fun setRackSection() {
        viewModel.setRackSection("Rack A")
        getPrivateField("rackSection") shouldBeEqualTo "Rack A"
    }

    @Test
    fun setRackSection_WithNull() {
        viewModel.setRackSection(null)
        getPrivateField("rackSection") shouldBeEqualTo ""
    }
}