package co.styletheory.ops.outbound.android.general

import android.view.View
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel.impl.LogisticProviderNameViewModelImpl
import co.styletheory.ops.outbound.android.general.event.DismissDialogEvent
import co.styletheory.ops.outbound.android.viewModelComponent.AlertDialogViewModel
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.amshove.kluent.shouldBeEmpty
import org.amshove.kluent.shouldBeEqualTo
import org.amshove.kluent.shouldBeInstanceOf
import org.junit.Test

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 07 March 2018.
 * Description
 *
 * <EMAIL>
 */
class AlertDialogDialogViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: AlertDialogViewModel

    @RelaxedMockK
    lateinit var rightButtonOnClickListener: AlertDialogViewModel.OnClickListener

    @RelaxedMockK
    lateinit var leftButtonOnClickListener: AlertDialogViewModel.OnClickListener

    @RelaxedMockK
    lateinit var logisticProviderNameVM: LogisticProviderNameViewModelImpl

    override fun setup() {
        super.setup()
        logisticProviderNameVM = mockk(relaxed = true)
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        viewModel.logisticProviderNameSectionVM = logisticProviderNameVM
        every { context.getString(R.string.send_shipment_dialog_body) } returns "The process may take a while, please wait until the process is done"
    }

    @Test
    fun initialState() {
        viewModel.dialogTitle.get().shouldBeEmpty()
        viewModel.dialogBody.get().shouldBeEmpty()
        viewModel.leftButtonText.get().shouldBeEmpty()
        viewModel.rightButtonText.get().shouldBeEmpty()
        viewModel.leftButtonVisibility.get() shouldBeEqualTo View.GONE
    }

    @Test
    fun bindViewModel() {
        val provider = MockProvider.logisticProvider()
        every {
            context.getString(
                    R.string.send_shipment_dialog_title, "10", provider.name
            )
        } returns ("You will sending 10 boxes, using ${provider.name}")
        viewModel.bindViewModel(10, provider)

        viewModel.logisticNameVisibility.get() shouldBeEqualTo View.VISIBLE
        viewModel.dialogTitle.get() shouldBeEqualTo "You will sending 10 boxes, using ${provider.name}"
        viewModel.dialogBody.get() shouldBeEqualTo "The process may take a while, please wait until the process is done"
        verify { logisticProviderNameVM.bindViewModelProvider(any()) }
    }

    @Test
    fun bindViewModel_logisticNameVisibility_Gone() {
        val provider = MockProvider.logisticProvider()
        callPrivateFun(viewModel, "setupLogisticVisibility", provider)

        viewModel.logisticNameVisibility.get() shouldBeEqualTo View.GONE
    }

    @Test
    fun setTitle() {
        viewModel.setTitle("Test")
        viewModel.dialogTitle.get() shouldBeEqualTo "Test"
    }

    @Test
    fun setBody() {
        viewModel.setBody("Test")
        viewModel.dialogBody.get() shouldBeEqualTo "Test"
    }

    @Test
    fun setBatchGeneration() {
        viewModel.setBatchGeneration("region", "vertical", "time")
        viewModel.batchRegion.get() shouldBeEqualTo "region"

    }

    @Test
    fun setRightButtonText() {
        viewModel.setRightButtonText("Test")
        viewModel.rightButtonText.get() shouldBeEqualTo "Test"
    }

    @Test
    fun setLeftButtonText() {
        viewModel.setLeftButtonText("Test")
        viewModel.leftButtonVisibility.get() shouldBeEqualTo View.VISIBLE
        viewModel.leftButtonText.get() shouldBeEqualTo "Test"
    }

    @Test
    fun rightButtonClicked() {
        viewModel.rightButtonClicked()
        viewModel.rightButtonOnClickListener?.onClick()
    }

    @Test
    fun leftButtonClick() {
        viewModel.leftButtonClicked()
        viewModel.leftButtonOnClickListener?.onClick()

        viewModel.leftButtonOnClickListener = null
        viewModel.leftButtonClicked()
        verify(exactly = 1) { eventBus.post(any<DismissDialogEvent>()) }
    }

    @Test
    fun setupLogisticProviderNameViewModel() {
        viewModel.setupLogisticProviderNameViewModel()
        viewModel.setupLogisticProviderNameViewModel() shouldBeInstanceOf LogisticProviderNameViewModelImpl::class
    }
}