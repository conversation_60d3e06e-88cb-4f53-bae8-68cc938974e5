package co.styletheory.ops.outbound.android.feature.packed

import co.styletheory.android.network.core.APICallback
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLResult
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.feature.packed.viewModel.impl.PackedChildViewModelImpl
import co.styletheory.ops.outbound.android.feature.packed.viewModel.impl.PackedItemViewModel
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.response.OrderGojekResponse
import co.styletheory.ops.outbound.android.model.response.SendShipmentResponse
import co.styletheory.ops.outbound.android.model.response.ShipmentResponse
import co.styletheory.ops.outbound.android.util.Result
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.amshove.kluent.*
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Test

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 20 February 2018.
 * Description
 *
 * <EMAIL>
 */
class PackedChildViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: PackedChildViewModelImpl

    @Test
    fun firstState() {
        viewModel.packedItems.shouldBeEmpty()
        viewModel.shipmentIds.shouldBeEmpty()
        viewModel.selectedItemIds.shouldBeEmpty()
        viewModel.logisticProvider.shouldBeNull()
        viewModel.isAllSelected.shouldBeFalse()
    }

    @Test
    fun selectAllItem() {
        val item = PackedItemViewModel()
        viewModel.packedItems.add(item)
        viewModel.shipmentIds.add("123")

        viewModel.selectedItemIds.shouldBeEmpty()
        viewModel.selectedItemIds.shouldNotContainAny(viewModel.shipmentIds)
        for(it in viewModel.packedItems) it.isSelected.get().shouldBeFalse()

        viewModel.selectAllItem()
        viewModel.selectedItemIds.shouldNotBeEmpty()
        viewModel.selectedItemIds.shouldContainAll(viewModel.shipmentIds)
        for(it in viewModel.packedItems) it.isSelected.get().shouldBeTrue()

        viewModel.selectAllItem()
        viewModel.selectedItemIds.shouldBeEmpty()
        viewModel.selectedItemIds.shouldNotContainAny(viewModel.shipmentIds)
        for(it in viewModel.packedItems) it.isSelected.get().shouldBeFalse()
    }

    @Test
    fun fetchFirstPage() {
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        val callback = mockk<Result<Void?, String?>>(relaxed = true)
        viewModel.fetchFirstPage(callback)
        verify(exactly = 1) {
            viewModel["fetchShipment"](callback, 1, true)
        }
    }

    @Test
    fun fetchNextPage() {
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        val callback = mockk<Result<Void?, String?>>(relaxed = true)
        viewModel.fetchNextPage(callback, 2)
        verify(exactly = 1) {
            viewModel["fetchShipment"](callback, 2, false)
        }
    }

    @Test
    fun fetchShipment_onSuccess_withResultNull() {
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        val callback = mockk<Result<Void?, String?>>(relaxed = true)
        every { dataService.fetchShipmentByLogisticProvider(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<ShipmentResponse>, String>>().onSuccess(null)
        }

        viewModel.fetchFirstPage(callback)
        verify(atLeast = 1) {
            viewModel.refreshData(true)
            callback.success(null)
        }
        verify(inverse = true) {
            viewModel.mapPackedItems(any())
        }
        verify(exactly = 1) {
            dataService.fetchShipmentByLogisticProvider(any(), any())
        }
    }

    @Test
    fun fetchShipment_onSuccess_withResultNotNull() {
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        val callback = mockk<Result<Void?, String?>>(relaxed = true)
        every { dataService.fetchShipmentByLogisticProvider(any(), any()) } answers {
            val response: GraphQLData<ShipmentResponse> = mockk()
            val data = ShipmentResponse(listOf(MockProvider.shipment()), MockProvider.shipmentMeta())
            val result = GraphQLResult(data)
            every { response.data } returns result
            secondArg<APICallback<GraphQLData<ShipmentResponse>, String>>().onSuccess(response)
        }

        viewModel.fetchFirstPage(callback)
        verify(atLeast = 1) {
            viewModel.refreshData(true)
            callback.success(null)
        }
        verify(exactly = 1) {
            viewModel.mapPackedItems(any())
            dataService.fetchShipmentByLogisticProvider(any(), any())
        }
    }

    @Test
    fun fetchShipment_onError() {
        viewModel = spyk(viewModel, recordPrivateCalls = true)
        val callback = mockk<Result<Void?, String?>>(relaxed = true)
        every { dataService.fetchShipmentByLogisticProvider(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<ShipmentResponse>, String>>().onError("error")
        }

        viewModel.fetchFirstPage(callback)
        verify(exactly = 1) { callback.failure(errorResponse.getErrorBodyDescription("error")) }
    }

    @Test
    fun sendShipmentBox() {
        val callback = mockk<Result<String?, String?>>(relaxed = true)
        every { dataService.sendShipment(any(), any()) } answers {
            val response = mockk<GraphQLData<SendShipmentResponse>>()
            val data = SendShipmentResponse(mapOf(Pair("message", "test")))
            val result = GraphQLResult(data)
            every { response.data } returns result
            secondArg<APICallback<GraphQLData<SendShipmentResponse>, String>>().onSuccess(response)
            secondArg<APICallback<GraphQLData<SendShipmentResponse>, String>>().onError("error")
        }
        viewModel.sendShipmentBox(callback)

        verify(exactly = 1) {
            dataService.sendShipment(any(), any())
            callback.success("test")
            callback.failure(errorResponse.getErrorBodyDescription("error"))
        }
    }

    @Test
    fun orderGojek_onSuccess() {
        val callback: Result<String?, String?> = mockk(relaxed = true)
        every { dataService.orderGojekShipments(any(), any()) } answers {
            val response = mockk<GraphQLData<OrderGojekResponse>>()
            val data = OrderGojekResponse(mapOf(Pair("message", "Test")))
            val result = GraphQLResult(data)
            every { response.data } returns result
            secondArg<APICallback<GraphQLData<OrderGojekResponse>, String>>().onSuccess(response)
        }
        viewModel.orderGojek(callback)
        verify(exactly = 1) {
            dataService.orderGojekShipments(any(), any())
            callback.success("Test")
        }
    }

    @Test
    fun orderGojek_onError() {
        val callback: Result<String?, String?> = mockk(relaxed = true)
        every { dataService.orderGojekShipments(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<OrderGojekResponse>, String>>().onError("error")
        }
        viewModel.orderGojek(callback)
        verify(exactly = 1) {
            dataService.orderGojekShipments(any(), any())
            callback.failure(errorResponse.getErrorBodyDescription("error"))
        }
    }

    @Test
    fun cancelGojek_onSuccess() {
        val callback: Result<Void?, String?> = mockk(relaxed = true)
        every { dataService.cancelGojekShipments(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<List<Batch>>, String>>().onSuccess(null)
        }
        viewModel.cancelGojek(callback)
        verify(exactly = 1) {
            dataService.cancelGojekShipments(any(), any())
            callback.success(null)
        }
    }

    @Test
    fun cancelGojek_onError() {
        val callback: Result<Void?, String?> = mockk(relaxed = true)
        every { dataService.cancelGojekShipments(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<List<Batch>>, String>>().onError("error")
        }
        viewModel.cancelGojek(callback)
        verify(exactly = 1) {
            dataService.cancelGojekShipments(any(), any())
            callback.failure(errorResponse.getErrorBodyDescription("error"))
        }
    }

    @Test
    fun refreshData() {
        viewModel.packedItems.add(PackedItemViewModel())
        viewModel.selectedItemIds.add("12")
        viewModel.shipmentIds.add("12")

        viewModel.refreshData(false)
        viewModel.packedItems.shouldNotBeEmpty()
        viewModel.selectedItemIds.shouldNotBeEmpty()
        viewModel.shipmentIds.shouldNotBeEmpty()

        viewModel.refreshData(true)
        viewModel.packedItems.shouldBeEmpty()
        viewModel.selectedItemIds.shouldBeEmpty()
        viewModel.shipmentIds.shouldBeEmpty()

    }

    @Test
    fun mapPackedItems() {
        viewModel.logisticProvider = MockProvider.logisticProvider().copy("Versafleet")
        viewModel.mapPackedItems(listOf(MockProvider.shipment()))
        viewModel.packedItems.size shouldBeEqualTo 1
    }

    @Test
    fun itsContainOnSelectedShipment() {
        viewModel.selectedItemIds.add("12")
        assertTrue(viewModel.itsContainOnSelectedShipment("12"))
        assertFalse(viewModel.itsContainOnSelectedShipment("15"))
    }

    @Test
    fun onCheckedItemChange() {
        val shipmentId = "12"
        viewModel.selectedItemIds.shouldBeEmpty()

        viewModel.onCheckedItemChange(true, shipmentId)
        viewModel.selectedItemIds.shouldNotBeEmpty()


        viewModel.onCheckedItemChange(false, shipmentId)
        viewModel.selectedItemIds.shouldBeEmpty()
    }

    @Test
    fun isSameLogisticProvider_false() {
        viewModel.isSameLogisticProvider("12") shouldBeEqualTo false
    }

    @Test
    fun isSameLogisticProvider_true() {
        viewModel.logisticProvider = MockProvider.logisticProvider()
        viewModel.isSameLogisticProvider("12") shouldBeEqualTo true
    }

    @Test
    fun totalSelectedShipment() {
        val item = PackedItemViewModel()
        viewModel.packedItems.add(item)
        viewModel.selectAllItem()
        viewModel.totalSelectedShipment() shouldBeEqualTo 1
    }
}