package co.styletheory.ops.outbound.android.feature.qualitySwap

import android.view.View
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.qualitySwap.event.QualitySwapUIEvent
import co.styletheory.ops.outbound.android.feature.qualitySwap.viewModel.impl.QualitySwapViewModelImpl
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.model.enums.BatchStatus
import co.styletheory.ops.outbound.android.viewModelComponent.FooterButtonViewModel
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.amshove.kluent.shouldBeEqualTo
import org.junit.Test

/**
 * Created by Yoga C. Pranata on 22/06/20.
 * Android Engineer
 */
class QualitySwapViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var footerButtonVM: FooterButtonViewModel

    @InjectMockKs
    lateinit var viewModel: QualitySwapViewModelImpl

    override fun setup() {
        super.setup()
        viewModel = spyk(viewModel, recordPrivateCalls = true)

        viewModel.footerButtonViewModel = footerButtonVM
    }

    @Test
    fun checkAfterInject_resellingItem() {
        val swapItem = mockk<SwapItem>()
        every { swapItem.item?.isResellingItem() } returns true
        viewModel.swapItem = swapItem

        viewModel.afterInject()
        viewModel.footerButtonViewModel.buttonTitle.get() shouldBeEqualTo context.getString(R.string.quality_swap_now_confirm_title)
        viewModel.footerButtonViewModel.enable.get() shouldBeEqualTo false
        viewModel.toolbarTitle.get() shouldBeEqualTo R.string.quality_check_title
    }

    @Test
    fun checkAfterInject_bagsItem() {
        every { userStorage.isVerticalTypeBags() } returns true
        viewModel.afterInject()
        viewModel.footerButtonViewModel.buttonTitle.get() shouldBeEqualTo context.getString(R.string.quality_swap_now_confirm_title)
        viewModel.footerButtonViewModel.enable.get() shouldBeEqualTo false
        viewModel.toolbarTitle.get() shouldBeEqualTo R.string.quality_check_title
    }

    @Test
    fun checkAfterInject_onDemandItem() {
        every { userStorage.isUserOnDemandAndRegionID() } returns true
        viewModel.afterInject()
        viewModel.footerButtonViewModel.buttonTitle.get() shouldBeEqualTo context.getString(R.string.quality_swap_now_confirm_title)
        viewModel.footerButtonViewModel.enable.get() shouldBeEqualTo false
        viewModel.toolbarTitle.get() shouldBeEqualTo R.string.quality_check_title
    }

    @Test
    fun checkAfterInject() {
        val swapItem = mockk<SwapItem>()
        every { swapItem.item?.isResellingItem() } returns false
        viewModel.swapItem = swapItem

        viewModel.afterInject()
        viewModel.footerButtonViewModel.buttonTitle.get() shouldBeEqualTo context.getString(R.string.quality_swap_now_title)
        viewModel.footerButtonViewModel.enable.get() shouldBeEqualTo false
        viewModel.toolbarTitle.get() shouldBeEqualTo R.string.quality_swap_title
    }

    @Test
    fun checkFooterViewModel() {
        viewModel.footerButtonViewModel() shouldBeEqualTo footerButtonVM
    }

    @Test
    fun checkSelectedSwapReason_selectOneOfTheReason() {
        viewModel.selectedSwapReasonPosition(1)
        verify { viewModel.checkRequiredFields() }
    }

    @Test
    fun checkSelectedSwapReason_selectTheFirstList() {
        viewModel.selectedSwapReasonPosition(0)
        viewModel.footerButtonViewModel.enable.get() shouldBeEqualTo false
    }

    @Test
    fun checkUploadPhotoButton() {
        viewModel.uploadPhotoClick()
        verify { eventBus.post(any<QualitySwapUIEvent.TakePictureEvent>()) }
    }

    @Test
    fun checkRequiredFields_enableButton() {
        viewModel.selectedFailReason.set("Sewing")
        viewModel.photos = listOf("Image_Yoga_Ganteng.jpg")
        viewModel.checkRequiredFields()
        viewModel.footerButtonViewModel.enable.get() shouldBeEqualTo true
    }

    @Test
    fun checkRequiredFields_disableButton_notFilled_failReason() {
        viewModel.selectedFailReason.set("Select fail reason")
        viewModel.checkRequiredFields()
        viewModel.footerButtonViewModel.enable.get() shouldBeEqualTo false
    }

    @Test
    fun checkRequiredFields_disableButton_notFilled_photos() {
        viewModel.photos = listOf()
        viewModel.checkRequiredFields()
        viewModel.footerButtonViewModel.enable.get() shouldBeEqualTo false
    }

    @Test
    fun checkGetFailReasonList_bags() {
        every { userStorage.isVerticalTypeBags() } returns true
        viewModel.getFailReasonList() shouldBeEqualTo context.resources.getStringArray(R.array.bags_fail_reason_list)
    }

    @Test
    fun checkGetFailReasonList_apparel() {
        every { userStorage.isVerticalTypeBags() } returns false
        viewModel.getFailReasonList() shouldBeEqualTo context.resources.getStringArray(R.array.apparel_fail_reason_list)
    }

    @Test
    fun checkClearAllFields() {
        viewModel.batchId = "123"
        viewModel.shipmentId = "1234"
        viewModel.swapItem = SwapItem(shipmentId = "1234")
        viewModel.batchStatus = BatchStatus.QA
        viewModel.selectedFailReason.set("Sewing")
        viewModel.inputNotes.set("Rusak")
        viewModel.photos = listOf("image_yoga_ganteng.jpg")

        viewModel.clearAllFields()

        viewModel.batchId shouldBeEqualTo ""
        viewModel.shipmentId shouldBeEqualTo ""
        viewModel.swapItem shouldBeEqualTo SwapItem()
        viewModel.batchStatus shouldBeEqualTo null
        viewModel.selectedFailReason.get() shouldBeEqualTo ""
        viewModel.inputNotes.get() shouldBeEqualTo ""
        viewModel.photos shouldBeEqualTo listOf()
    }

    @Test
    fun checkSetupInfoVisibility_reselling() {
        val swapItem = mockk<SwapItem>()
        every { swapItem.item?.isResellingItem() } returns true
        viewModel.swapItem = swapItem
        viewModel.setupInfoVisibility()
        viewModel.infoVisibility.get() shouldBeEqualTo View.GONE
    }

    @Test
    fun checkSetupInfoVisibility_bags() {
        every { userStorage.isVerticalTypeBags() } returns true
        viewModel.setupInfoVisibility()
        viewModel.infoVisibility.get() shouldBeEqualTo View.GONE
    }

    @Test
    fun checkSetupInfoVisibility_onDemand() {
        every { userStorage.isUserOnDemandAndRegionID() } returns true
        viewModel.setupInfoVisibility()
        viewModel.infoVisibility.get() shouldBeEqualTo View.GONE
    }

    @Test
    fun checkSetupInfoVisibility() {
        viewModel.setupInfoVisibility()
        viewModel.infoVisibility.get() shouldBeEqualTo View.VISIBLE
    }
}