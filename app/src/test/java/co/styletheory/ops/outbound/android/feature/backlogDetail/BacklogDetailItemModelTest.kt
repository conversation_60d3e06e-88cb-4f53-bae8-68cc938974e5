package co.styletheory.ops.outbound.android.feature.backlogDetail

import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.backlogDetail.viewModel.impl.BacklogDetailItemViewModelImpl
import co.styletheory.ops.outbound.android.general.MockProvider
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.spyk
import org.amshove.kluent.shouldBeEqualTo
import org.junit.Test

/**
 * Created by <PERSON> on 22/10/20.
 */
class BacklogDetailItemModelTest : BaseKotlinTest() {
    @InjectMockKs
    lateinit var viewModel: BacklogDetailItemViewModelImpl

    override fun setup() {
        super.setup()
        viewModel = spyk(viewModel, recordPrivateCalls = true)
    }

    @Test
    fun setupRackName_onDemand_batch_empty() {
        every { userStorage.isUserOnDemandAndRegionID() } returns true
        viewModel.setupRackName(null)

        viewModel.rack.get() shouldBeEqualTo context.getString(R.string.rack_name_on_demand).format(null, "")
    }

    @Test
    fun setupRackName_regular_rack_empty() {
        every { userStorage.isUserOnDemandAndRegionID() } returns false
        val batch = MockProvider.batch().copy(rack = null)
        viewModel.setupRackName(batch)

        viewModel.rack.get() shouldBeEqualTo context.getString(R.string.rack_name_regular).format(null, "")
    }


}