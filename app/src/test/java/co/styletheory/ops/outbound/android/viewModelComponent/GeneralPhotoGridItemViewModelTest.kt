package co.styletheory.ops.outbound.android.viewModelComponent

import android.view.View
import androidx.databinding.ObservableBoolean
import co.styletheory.android.network.core.APICallback
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.feature.photoManager.event.PhotoManagerDeleteEvent
import co.styletheory.ops.outbound.android.feature.photoManager.event.RequestErrorEvent
import co.styletheory.ops.outbound.android.general.binding.ObservableString
import co.styletheory.ops.outbound.android.general.event.GeneralPhotoGridItemClickedEvent
import co.styletheory.ops.outbound.android.general.event.GeneralPhotoGridItemUploadedEvent
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.mockk
import io.mockk.verify
import org.amshove.kluent.shouldBeEqualTo
import org.amshove.kluent.shouldBeFalse
import org.amshove.kluent.shouldBeTrue
import org.junit.Test

/**
 * Created by Eminarti Sianturi on 2019-09-30.
 */
class GeneralPhotoGridItemViewModelTest : BaseKotlinTest() {
    @InjectMockKs
    lateinit var viewModel: GeneralPhotoGridItemViewModel

    @Test
    fun firstState() {
        viewModel.position shouldBeEqualTo 0
        viewModel.imageUrl.get() shouldBeEqualTo ""
        viewModel.editable.get().shouldBeFalse()
        viewModel.isLocalUrl.shouldBeFalse()
        viewModel.shipmentId shouldBeEqualTo null
        viewModel.loadingIndicatorStatus.get() shouldBeEqualTo View.GONE
        viewModel.localImageUrl.get() shouldBeEqualTo ""
    }

    @Test
    fun bindPhoto_WithLocalUrlTrue() {
        val position = 0
        val imageUrl = ObservableString("image.png")
        val shipmentId = "ABC"
        val isLocalUrl = true
        val editable = ObservableBoolean(true)

        every { dataService.fetchImageUploadUrl(any(), any()) } answers {
            val response: GraphQLData<String> = mockk()
            val url = "http://image.png"
            every { response.data.result } returns url
            secondArg<APICallback<GraphQLData<String>, String>>().onSuccess(response)
        }

        every { dataService.uploadUrlImage(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<String>, String>>().onSuccess(null)
        }


        viewModel.bindPhoto(position, imageUrl, shipmentId, isLocalUrl, editable)

        viewModel.position shouldBeEqualTo 0
        viewModel.imageUrl.get() shouldBeEqualTo "http://image.png"
        viewModel.localImageUrl.get() shouldBeEqualTo "image.png"
        viewModel.shipmentId shouldBeEqualTo "ABC"
        viewModel.editable.get().shouldBeTrue()
        viewModel.loadingIndicatorStatus.get() shouldBeEqualTo View.GONE

        verify { dataService.uploadUrlImage(any(), any()) }
        verify { eventBus.post(any<GeneralPhotoGridItemUploadedEvent>()) }

    }

    @Test
    fun bindPhoto_WithLocalUrlFalse() {
        val position = 0
        val imageUrl = ObservableString("image.png")
        val shipmentId = "ABC"
        val isLocalUrl = false
        val editable = ObservableBoolean(true)

        every { dataService.fetchImageUploadUrl(any(), any()) } answers {
            val response: GraphQLData<String> = mockk()
            val url = "http://image.png"
            every { response.data.result } returns url
            secondArg<APICallback<GraphQLData<String>, String>>().onSuccess(response)
        }

        every { dataService.uploadUrlImage(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<String>, String>>().onSuccess(null)
        }


        viewModel.bindPhoto(position, imageUrl, shipmentId, isLocalUrl, editable)

        viewModel.position shouldBeEqualTo 0
        viewModel.imageUrl.get() shouldBeEqualTo "image.png"
        viewModel.localImageUrl.get() shouldBeEqualTo ""
        viewModel.shipmentId shouldBeEqualTo "ABC"
        viewModel.editable.get().shouldBeTrue()
        viewModel.loadingIndicatorStatus.get() shouldBeEqualTo View.GONE

    }

    @Test
    fun bindPhoto_WithLocalUrlTrue_ButUrlEmpty() {
        val position = 0
        val imageUrl = ObservableString("image.png")
        val shipmentId = "ABC"
        val isLocalUrl = true
        val editable = ObservableBoolean(true)

        every { dataService.fetchImageUploadUrl(any(), any()) } answers {
            val response: GraphQLData<String> = mockk()
            val url = ""
            every { response.data.result } returns url
            secondArg<APICallback<GraphQLData<String>, String>>().onSuccess(response)
        }

        every { dataService.uploadUrlImage(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<String>, String>>().onSuccess(null)
        }


        viewModel.bindPhoto(position, imageUrl, shipmentId, isLocalUrl, editable)

        viewModel.position shouldBeEqualTo 0
        viewModel.imageUrl.get() shouldBeEqualTo ""
        viewModel.localImageUrl.get() shouldBeEqualTo "image.png"
        viewModel.shipmentId shouldBeEqualTo "ABC"
        viewModel.editable.get().shouldBeTrue()
        viewModel.loadingIndicatorStatus.get() shouldBeEqualTo View.VISIBLE

        verify { eventBus.post(any<RequestErrorEvent>()) }

    }

    @Test
    fun bindPhoto_WithLocalUrlTrue_ButErrorWhenCreateUpload() {
        val position = 0
        val imageUrl = ObservableString("image.png")
        val shipmentId = "ABC"
        val isLocalUrl = true
        val editable = ObservableBoolean(true)

        every { dataService.fetchImageUploadUrl(any(), any()) } answers {
            val response: GraphQLData<String> = mockk()
            val url = ""
            every { response.data.result } returns url
            secondArg<APICallback<GraphQLData<String>, String>>().onSuccess(response)
        }

        every { dataService.uploadUrlImage(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<String>, String>>().onError(null)
        }


        viewModel.bindPhoto(position, imageUrl, shipmentId, isLocalUrl, editable)

        viewModel.position shouldBeEqualTo 0
        viewModel.imageUrl.get() shouldBeEqualTo ""
        viewModel.localImageUrl.get() shouldBeEqualTo "image.png"
        viewModel.shipmentId shouldBeEqualTo "ABC"
        viewModel.editable.get().shouldBeTrue()
        viewModel.loadingIndicatorStatus.get() shouldBeEqualTo View.VISIBLE

        verify { eventBus.post(any<RequestErrorEvent>()) }

    }

    @Test
    fun bindPhoto_WithLocalUrlTrue_ButErrorWhenUpload() {
        val position = 0
        val imageUrl = ObservableString("image.png")
        val shipmentId = "ABC"
        val isLocalUrl = true
        val editable = ObservableBoolean(true)

        every { dataService.fetchImageUploadUrl(any(), any()) } answers {
            val response: GraphQLData<String> = mockk()
            val url = "http://image.png"
            every { response.data.result } returns url
            secondArg<APICallback<GraphQLData<String>, String>>().onSuccess(response)
        }

        every { dataService.uploadUrlImage(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<String>, String>>().onError(null)
        }


        viewModel.bindPhoto(position, imageUrl, shipmentId, isLocalUrl, editable)

        viewModel.position shouldBeEqualTo 0
        viewModel.imageUrl.get() shouldBeEqualTo ""
        viewModel.localImageUrl.get() shouldBeEqualTo "image.png"
        viewModel.shipmentId shouldBeEqualTo "ABC"
        viewModel.editable.get().shouldBeTrue()
        viewModel.loadingIndicatorStatus.get() shouldBeEqualTo View.VISIBLE

        verify { dataService.uploadUrlImage(any(), any()) }
        verify { eventBus.post(any<RequestErrorEvent>()) }
    }

    @Test
    fun getIsLoadImage_ReturnFalse() {
        viewModel.loadingIndicatorStatus.set(View.GONE)
        viewModel.getIsLoadImage().shouldBeFalse()
    }

    @Test
    fun getIsLoadImage_ReturnTrue() {
        viewModel.loadingIndicatorStatus.set(View.VISIBLE)
        viewModel.getIsLoadImage().shouldBeTrue()
    }

    @Test
    fun onButtonDeleteClicked_WillDeleteImage() {
        viewModel.onButtonDeleteClicked()

        verify { eventBus.post(any<PhotoManagerDeleteEvent>()) }
    }

    @Test
    fun onImageClicked_WithLocalUrl_WillPostEvent() {
        viewModel.localImageUrl.set("image.png")
        viewModel.onImageClicked()

        verify { eventBus.post(any<GeneralPhotoGridItemClickedEvent>()) }
    }

    @Test
    fun onImageClicked_WithImageUrl_WillPostEvent() {
        viewModel.imageUrl.set("image.png")
        viewModel.onImageClicked()

        verify { eventBus.post(any<GeneralPhotoGridItemClickedEvent>()) }
    }
}