package co.styletheory.ops.outbound.android.feature.logisticProviderName

import co.styletheory.ops.outbound.android.BaseBehaviorTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.logisticProviderName.viewModel.impl.ItemLogisticProviderNameViewModelImpl
import co.styletheory.ops.outbound.android.general.MockProvider
import org.amshove.kluent.shouldBeEqualTo

/**
 * Created by <PERSON> on 06/01/22.
 */
class ItemLogisticProviderNameBehaviorTest : BaseBehaviorTest<ItemLogisticProviderNameViewModelImpl>(ItemLogisticProviderNameViewModelImpl()) {

    init {
        given("user want to see logistic provider name") {
            `when`("do not have logistic provider") {
                viewModel.bindViewModel(null)
                then("user must see logistic provider name list") {
                    viewModel.apply {
                        backgroundColorResource.get() shouldBeEqualTo R.drawable.rect_black_26_rad_4
                    }
                }
            }

            `when`("do not have logistic provider name") {
                val provider = MockProvider.logisticProvider()
                viewModel.bindViewModel(provider)
                then("user must see logistic provider name list") {
                    viewModel.apply {
                        backgroundColorResource.get() shouldBeEqualTo R.drawable.rect_black_26_rad_4
                        providerName.get() shouldBeEqualTo provider.name
                        providerTextColor.get() shouldBeEqualTo R.color.black_26
                    }
                }
            }

            `when`("have logistic provider") {
                val provider = MockProvider.logisticProvider().copy("singpost")
                viewModel.bindViewModel(provider)
                then("user must see logistic provider name list") {
                    viewModel.apply {
                        backgroundColorResource.get() shouldBeEqualTo R.drawable.rect_black_26_rad_4
                        providerName.get() shouldBeEqualTo ""
                        providerTextColor.get() shouldBeEqualTo R.color.light_green
                    }
                }
            }
        }

    }
}