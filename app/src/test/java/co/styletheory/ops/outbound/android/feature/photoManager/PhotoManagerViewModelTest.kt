package co.styletheory.ops.outbound.android.feature.photoManager

import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.feature.photoManager.viewModel.PhotoManagerViewModel
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.model.enums.AttachPhotoType
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralPhotoGridItemViewModel
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import org.amshove.kluent.shouldBeEqualTo
import org.amshove.kluent.shouldBeFalse
import org.apache.commons.lang3.reflect.FieldUtils
import org.junit.Test

/**
 * Created by <PERSON>inarti <PERSON> on 2019-10-01.
 */
class PhotoManagerViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: PhotoManagerViewModel

    @InjectMockKs
    lateinit var toolbarVM: GeneralToolbarViewModel

    private fun setPrivateField(fieldName: String, value: Any?) {
        FieldUtils.writeDeclaredField(viewModel, fieldName, value, true)
    }

    private fun getPrivateField(fieldName: String): Any? {
        return FieldUtils.readDeclaredField(viewModel, fieldName, true)
    }

    override fun setup() {
        super.setup()
        viewModel.toolbarViewModel = toolbarVM
        viewModel = spyk(viewModel, recordPrivateCalls = true)
    }

    @Test
    fun firstState() {
        viewModel.shipmentId shouldBeEqualTo ""
        viewModel.batchItem shouldBeEqualTo null
        viewModel.customListener shouldBeEqualTo null
        viewModel.existingImages.isEmpty()
        viewModel.photoViewModels.size shouldBeEqualTo 0
        viewModel.attachmentViewModel.size shouldBeEqualTo 0
        viewModel.productFeaturedImageUrl.get() shouldBeEqualTo ""
        viewModel.designerName.get() shouldBeEqualTo ""
        viewModel.itemName.get() shouldBeEqualTo ""
        viewModel.isEditingMode.get().shouldBeFalse()
        viewModel.toolbarViewModel.title.get() shouldBeEqualTo ""
        viewModel.toolbarViewModel.subtitle.get() shouldBeEqualTo ""
    }

    @Test
    fun afterInject_ToolbarWillHaveTitle() {
        viewModel.afterInject()

        viewModel.toolbarViewModel.title.get() shouldBeEqualTo "Photo Gallery Manager"
    }

    @Test
    fun addNewPhotoUrls_WithListUrl() {
        val urlList = listOf("image.png")
        mockkConstructor(GeneralPhotoGridItemViewModel::class)

        every {
            anyConstructed<GeneralPhotoGridItemViewModel>().bindPhoto(
                any(), any(), any(), any(), any()
            )
        } returns mockk()

        viewModel.addNewPhotoUrls(urlList)

        viewModel.photoViewModels.size shouldBeEqualTo 1
        viewModel.attachmentViewModel.size shouldBeEqualTo 1
    }

    @Test
    fun fillNecessaryFields() {
        val orderId = "#1"
        val batchItem = MockProvider.batchItem(ProductStatus.PHOTO_QA)
        val isEditMode = false
        val listener = mockk<PhotoManagerViewModel.Listener>()

        mockkConstructor(GeneralPhotoGridItemViewModel::class)

        every {
            anyConstructed<GeneralPhotoGridItemViewModel>().bindPhoto(
                any(), any(), any(), any(), any()
            )
        } returns mockk()


        viewModel.fillNecessaryFields(orderId, batchItem, isEditMode, listener)

        viewModel.shipmentId shouldBeEqualTo "#1"
        viewModel.batchItem shouldBeEqualTo batchItem
        viewModel.isEditingMode.get().shouldBeFalse()
        viewModel.customListener shouldBeEqualTo listener
        viewModel.designerName.get() shouldBeEqualTo "Apri"
        viewModel.itemName.get() shouldBeEqualTo "StyleName"
        viewModel.productFeaturedImageUrl.get() shouldBeEqualTo "Url"

    }

    @Test
    fun fillNecessaryFields_WithExistingImages() {
        val orderId = "#1"
        val batchItem = MockProvider.batchItem(ProductStatus.PHOTO_QA)
        val isEditMode = false
        val listener = mockk<PhotoManagerViewModel.Listener>()

        mockkConstructor(GeneralPhotoGridItemViewModel::class)

        every {
            anyConstructed<GeneralPhotoGridItemViewModel>().bindPhoto(
                any(), any(), any(), any(), any()
            )
        } returns mockk()

        val urlList = listOf("image.png")
        viewModel.existingImages = urlList

        viewModel.fillNecessaryFields(orderId, batchItem, isEditMode, listener)

        viewModel.shipmentId shouldBeEqualTo "#1"
        viewModel.batchItem shouldBeEqualTo batchItem
        viewModel.isEditingMode.get().shouldBeFalse()
        viewModel.customListener shouldBeEqualTo listener
        viewModel.designerName.get() shouldBeEqualTo "Apri"
        viewModel.itemName.get() shouldBeEqualTo "StyleName"
        viewModel.productFeaturedImageUrl.get() shouldBeEqualTo "Url"
        viewModel.photoViewModels.size shouldBeEqualTo 1
    }

    @Test
    fun deletePhoto_WithEmptyPhotoViewModels() {
        val photo = mockk<GeneralPhotoGridItemViewModel>()

        viewModel.deleteViewModel(photo)
        viewModel.photoViewModels.size shouldBeEqualTo 0
    }

    @Test
    fun deletePhoto_WithPhotoViewModels() {
        val photo = mockk<GeneralPhotoGridItemViewModel>()
        every { photo.imageUrl.get() } returns ""

        viewModel.photoViewModels.add(photo)

        viewModel.deleteViewModel(photo)
        viewModel.photoViewModels.size shouldBeEqualTo 0
    }

    @Test
    fun deletePhoto_WithManyPhotoViewModels() {
        val photo = mockk<GeneralPhotoGridItemViewModel>()
        every { photo.imageUrl.get() } returns ""

        val otherPhoto = mockk<GeneralPhotoGridItemViewModel>()
        every { otherPhoto.imageUrl.get() } returns "test"

        viewModel.photoViewModels.add(photo)
        viewModel.photoViewModels.add(otherPhoto)

        viewModel.deleteViewModel(photo)
        viewModel.photoViewModels.size shouldBeEqualTo 1
    }

    @Test
    fun deletePhoto_WithManyPhotoViewModels_ButNotFound() {
        val photo = mockk<GeneralPhotoGridItemViewModel>()
        every { photo.imageUrl.get() } returns ""

        val otherPhoto = mockk<GeneralPhotoGridItemViewModel>()
        every { otherPhoto.imageUrl.get() } returns "test"

        viewModel.photoViewModels.add(otherPhoto)

        viewModel.deleteViewModel(photo)
        viewModel.photoViewModels.size shouldBeEqualTo 1
    }

    @Test
    fun removePhotoAfterUploaded_WithEmptyPhotoViewModels() {
        val photo = mockk<GeneralPhotoGridItemViewModel>()

        viewModel.removePhotoAfterUploaded(photo)
        viewModel.attachmentViewModel.size shouldBeEqualTo 0
    }

    @Test
    fun removePhotoAfterUploaded_WithPhotoViewModels() {
        val photo = mockk<GeneralPhotoGridItemViewModel>()
        every { photo.imageUrl.get() } returns ""

        viewModel.attachmentViewModel.add(photo)

        viewModel.removePhotoAfterUploaded(photo)
        viewModel.attachmentViewModel.size shouldBeEqualTo 0
    }

    @Test
    fun removePhotoAfterUploaded_WithManyPhotoViewModels() {
        val photo = mockk<GeneralPhotoGridItemViewModel>()
        every { photo.imageUrl.get() } returns ""

        val otherPhoto = mockk<GeneralPhotoGridItemViewModel>()
        every { otherPhoto.imageUrl.get() } returns "test"

        viewModel.attachmentViewModel.add(photo)
        viewModel.attachmentViewModel.add(otherPhoto)

        viewModel.removePhotoAfterUploaded(photo)
        viewModel.attachmentViewModel.size shouldBeEqualTo 1
    }

    @Test
    fun removePhotoAfterUploaded_WithManyPhotoViewModels_ButNotFound() {
        val photo = mockk<GeneralPhotoGridItemViewModel>()
        every { photo.imageUrl.get() } returns ""

        val otherPhoto = mockk<GeneralPhotoGridItemViewModel>()
        every { otherPhoto.imageUrl.get() } returns "test"

        viewModel.attachmentViewModel.add(otherPhoto)

        viewModel.removePhotoAfterUploaded(photo)
        viewModel.attachmentViewModel.size shouldBeEqualTo 1
    }

    @Test
    fun attachImageToItem() {
        val attachPhotoType = mockk<AttachPhotoType>()
        val callback = mockk<Result<Void?, String?>>()

        viewModel.attachImageToItem(attachPhotoType, callback)
        verify { dataService.attachImageToItem(any(), any()) }
    }
}
