package co.styletheory.ops.outbound.android.feature.signin

import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLResult
import co.styletheory.ops.outbound.android.BaseTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.signIn.SignInViewModelImpl
import co.styletheory.ops.outbound.android.general.auth.event.AuthFailedEvent
import co.styletheory.ops.outbound.android.general.auth.event.AuthSuccessEvent
import co.styletheory.ops.outbound.android.model.Session
import co.styletheory.ops.outbound.android.model.User
import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.times
import com.nhaarman.mockitokotlin2.whenever
import org.junit.Assert.*
import org.junit.Test
import org.mockito.InjectMocks
import org.mockito.Mockito.verify


/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */

class SignInViewModelTest : BaseTest() {

    @InjectMocks
    lateinit var signInViewModel: SignInViewModelImpl

    override fun setup() {
        super.setup()
        whenever(context.getString(R.string.err_update_user_data)).thenReturn("Please update user data")
    }

    @Test
    fun doSignIn_WhenAdditionalFieldNotShown() {
        whenever(userStorage.getUserLogin()).thenReturn(null)
        signInViewModel.emailText.set("<EMAIL>")
        signInViewModel.passwordText.set("123456")
        signInViewModel.showAdditionalField.set(false)

        signInViewModel.doSignIn()
        verify(dataService, times(1)).login(any(), any())
    }

    @Test
    fun doSignIn_WhenAdditionalFieldShown() {
        whenever(userStorage.getUserLogin()).thenReturn(null)
        signInViewModel.emailText.set("<EMAIL>")
        signInViewModel.passwordText.set("123456")
        signInViewModel.showAdditionalField.set(true)

        signInViewModel.doSignIn()
        verify(dataService, times(1)).createPassword(any(), any())
    }

    @Test
    fun doSignIn_UserLoginIsNotNull() {
        val user = mock<User>()
        whenever(userStorage.getUserLogin()).thenReturn(user)
        signInViewModel.doSignIn()
        verify(dataService, times(1)).userDetail(any(), any())
    }

    @Test
    fun firstTimeSignIn() {
        assertFalse(signInViewModel.showAdditionalField.get())
        signInViewModel.firstTimeSignIn()
        assertTrue(signInViewModel.showAdditionalField.get())
        verify(eventBus, times(1)).post(any<AuthFailedEvent>())
    }

    @Test
    fun sessionCallback_WhenError() {
        whenever(errorResponse.getErrorBodyDescription(any())).thenReturn("")
        signInViewModel.sessionCallback.onError("")

        verify(eventBus, times(1)).post(any())
    }

    @Test
    fun sessionCallback_WhenSuccessAndResultIsNull() {
        assertNull(signInViewModel.userStorage.getUserLogin())
        signInViewModel.sessionCallback.onSuccess(null)

        verify(dataService, times(0)).userDetail(any(), any())
        verify(userStorage, times(0)).setUserSession(any())
        verify(eventBus, times(0)).post(any<AuthFailedEvent>())
    }

    @Test
    fun sessionCallback_WhenSuccessAndIsNewUserIsTrue() {
        assertNull(signInViewModel.userStorage.getUserLogin())
        val data = mock<GraphQLData<Session>>()
        val result = mock<GraphQLResult<Session>>()
        val session = mock<Session>()
        whenever(data.data).thenReturn(result)
        whenever(result.result).thenReturn(session)
        whenever(session.isNewUser).thenReturn(true)

        signInViewModel.sessionCallback.onSuccess(data)

        assertTrue(signInViewModel.showAdditionalField.get())
        verify(eventBus, times(1)).post(any<AuthFailedEvent>())
    }

    @Test
    fun sessionCallback_WhenSuccessAndIsNewUserIsFalse() {
        assertNull(signInViewModel.userStorage.getUserLogin())
        val data = mock<GraphQLData<Session>>()
        val result = mock<GraphQLResult<Session>>()
        val session = mock<Session>()
        whenever(data.data).thenReturn(result)
        whenever(result.result).thenReturn(session)
        whenever(session.isNewUser).thenReturn(false)

        signInViewModel.sessionCallback.onSuccess(data)

        verify(dataService, times(1)).userDetail(any(), any())
        verify(userStorage, times(1)).setUserSession(session)
    }


    @Test
    fun fetchUserDetailCallback_WhenError() {
        whenever(errorResponse.getErrorBodyDescription(any())).thenReturn("")
        signInViewModel.userDetailCallback.onError("")

        verify(eventBus, times(1)).post(any<AuthFailedEvent>())
    }

    @Test
    fun fetchUserDetailCallback_WhenSuccessAndResultNotNull() {
        assertNull(signInViewModel.userStorage.getUserLogin())
        val data = mock<GraphQLData<User>>()
        val result = mock<GraphQLResult<User>>()
        val user = mock<User>()
        whenever(data.data).thenReturn(result)
        whenever(result.result).thenReturn(user)

        signInViewModel.userDetailCallback.onSuccess(data)
        verify(userStorage, times(1)).setUserLogin(user)
        verify(userStorage, times(1)).setUserRegion(any())
        verify(eventBus, times(1)).post(any<AuthSuccessEvent>())
    }

    @Test
    fun fetchUserDetailCallback_WhenSuccessAndResultNull() {
        assertNull(userStorage.getUserLogin())
        val data = mock<GraphQLData<User>>()

        signInViewModel.userDetailCallback.onSuccess(data)
        assertNull(signInViewModel.userStorage.getUserLogin())
        verify(eventBus, times(0)).post(any<AuthSuccessEvent>())
    }

    @Test
    fun isAllFieldSuccess_WithNoAdditionalField() {
        signInViewModel.emailText.set("<EMAIL>")
        signInViewModel.passwordText.set("123456")
        assertTrue(signInViewModel.isAllFieldValid())
    }

    @Test
    fun isAllFieldSuccess_WithAdditionalField() {
        signInViewModel.emailText.set("<EMAIL>")
        signInViewModel.passwordText.set("123456")
        signInViewModel.showAdditionalField.set(true)
        signInViewModel.nameText.set("James")
        signInViewModel.newPasswordText.set("123456")
        signInViewModel.newConfirmPasswordText.set("123456")
        assertTrue(signInViewModel.isAllFieldValid())
    }

    @Test
    fun isAllFieldFailed_WithNoAdditionalField() {
        signInViewModel.emailText.set("<EMAIL>")
        signInViewModel.passwordText.set("")
        assertFalse(signInViewModel.isAllFieldValid())
    }

    @Test
    fun isAllFieldFailed_WithAdditionalField() {
        signInViewModel.emailText.set("<EMAIL>")
        signInViewModel.passwordText.set("123456")
        signInViewModel.showAdditionalField.set(true)
        signInViewModel.nameText.set("")
        signInViewModel.newPasswordText.set("")
        signInViewModel.newConfirmPasswordText.set("")
        assertFalse(signInViewModel.isAllFieldValid())
    }

    @Test
    fun getUserRegionByRole() {
        assertEquals("sg", signInViewModel.getUserRegionByRole(listOf("sg_staff")).id)
        assertEquals("id", signInViewModel.getUserRegionByRole(listOf("id_staff")).id)
        assertEquals("hk", signInViewModel.getUserRegionByRole(listOf("hk_staff")).id)
        assertEquals("sg", signInViewModel.getUserRegionByRole(listOf("ops_manager")).id)
        assertEquals("sg", signInViewModel.getUserRegionByRole(listOf()).id)
        assertEquals("sg", signInViewModel.getUserRegionByRole(null).id)
    }
}

