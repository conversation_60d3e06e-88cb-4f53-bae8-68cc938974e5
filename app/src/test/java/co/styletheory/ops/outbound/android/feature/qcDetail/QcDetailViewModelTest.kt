package co.styletheory.ops.outbound.android.feature.qcDetail

import co.styletheory.android.network.core.APICallback
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.qcDetail.event.QcDetailUIEvent
import co.styletheory.ops.outbound.android.feature.qcDetail.view.QcDetailItemFragment
import co.styletheory.ops.outbound.android.feature.qcDetail.viewModel.impl.QcDetailViewModelImpl
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.model.Batch
import co.styletheory.ops.outbound.android.model.BatchItem
import co.styletheory.ops.outbound.android.model.Shipment
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import co.styletheory.ops.outbound.android.util.Result
import co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.amshove.kluent.shouldBeEqualTo
import org.amshove.kluent.shouldBeInstanceOf
import org.amshove.kluent.shouldBeTrue
import org.junit.Test

/**
 * styletheory-ops-outbound-android
 * Created by Jessica Setyani on 12 March 2019.
 */

class QcDetailViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: QcDetailViewModelImpl

    @InjectMockKs
    lateinit var toolbarViewModel: GeneralToolbarViewModel

    override fun setup() {
        super.setup()
        viewModel.toolbarViewModel = toolbarViewModel
        viewModel = spyk(viewModel, recordPrivateCalls = true)
    }

    @Test
    fun changeToolbarTitle() {
        viewModel.changeToolbarTitle("Text")
        viewModel.toolbarViewModel.title.get() shouldBeEqualTo "Text"
    }

    @Test
    fun changeToolbarSubtitle() {
        viewModel.changeToolbarSubtitle("Text")
        viewModel.toolbarViewModel.subtitle.get() shouldBeEqualTo "Text"
    }

    @Test
    fun fetchBatchDetail_ReturnSuccess() {
        val callbackMock = mockk<Result<Void?, String?>>()
        every { callbackMock.success(any()) } answers { nothing }
        every { dataService.fetchBatchDetail(any(), any()) } answers {
            val response: GraphQLData<Batch> = mockk()
            val batchItem = MockProvider.batchItem(ProductStatus.AVAILABLE)
            val batch = MockProvider.batch(batchItem)
            every { response.data.result } returns batch
            secondArg<APICallback<GraphQLData<Batch>, String>>().onSuccess(response)
        }

        viewModel.fetchBatchDetail(null)
        viewModel.fetchBatchDetail(callbackMock)

        verify(exactly = 2) { dataService.fetchBatchDetail(any(), any()) }
        verify(exactly = 1) { callbackMock.success(null) }
    }

    @Test
    fun fetchBatchDetail_ReturnError() {
        val callbackMock = mockk<Result<Void?, String?>>()
        every { callbackMock.failure(any()) } answers { nothing }
        every { dataService.fetchBatchDetail(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Batch>, String>>().onError(null)
        }

        viewModel.fetchBatchDetail(null)
        viewModel.fetchBatchDetail(callbackMock)

        verify(exactly = 2) { dataService.fetchBatchDetail(any(), any()) }
        verify(exactly = 1) { callbackMock.failure(any()) }
    }

    @Test
    fun onFetchBatchDetailSuccess() {
        viewModel = spyk(viewModel)
        val batch = mockk<Batch>()
        every { batch.shipments } returns listOf()
        every { batch.name } returns "A"

        // when shipments batch is empty
        viewModel.onFetchBatchDetailSuccess(batch)

        viewModel.shipments.size shouldBeEqualTo 0
        viewModel.toolbarViewModel.title.get() shouldBeEqualTo context.getString(R.string.qc_title).format(batch.name)
        viewModel.toolbarViewModel.subtitle.get() shouldBeEqualTo "empty picked box"

        // when shipments batch is not empty
        val shipment = mockk<Shipment>()
        val batchItem = MockProvider.batchItem()
        every { shipment.items } returns listOf(batchItem)
        every { userStorage.isUserRegionSG() } returns true
        every { shipment.isQAPickedItemsAvailableSG() } returns true
        every { batch.shipments } returns listOf(shipment)

        viewModel.onFetchBatchDetailSuccess(batch)

        viewModel.shipments.size shouldBeEqualTo 1
        viewModel.toolbarViewModel.title.get() shouldBeEqualTo context.getString(R.string.qc_title).format(batch.name)
        viewModel.toolbarViewModel.subtitle.get() shouldBeEqualTo "1 of 1 Box"

        // when shipments batch is not empty ID
        every { shipment.items } returns listOf(batchItem)
        every { userStorage.isUserRegionID() } returns true
        every { shipment.isQAPickedItemsAvailableID() } returns true
        every { batch.shipments } returns listOf(shipment)

        viewModel.onFetchBatchDetailSuccess(batch)

        viewModel.shipments.size shouldBeEqualTo 1
        viewModel.toolbarViewModel.title.get() shouldBeEqualTo context.getString(R.string.qc_title).format(batch.name)
        viewModel.toolbarViewModel.subtitle.get() shouldBeEqualTo "1 of 1 Box"
    }

    @Test
    fun completeBatch_ReturnSuccess() {
        val callbackMock = mockk<Result<Void?, String?>>()
        every { callbackMock.success(any()) } answers { nothing }
        every { dataService.completeBatch(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Batch>, String>>().onSuccess(null)
        }

        viewModel.completeBatch(null)
        viewModel.completeBatch(callbackMock)

        verify(exactly = 2) { dataService.completeBatch(any(), any()) }
        verify(exactly = 1) { callbackMock.success(null) }
    }

    @Test
    fun completeBatch_ReturnError() {
        val callbackMock = mockk<Result<Void?, String?>>()
        every { callbackMock.failure(any()) } answers { nothing }
        every { dataService.completeBatch(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Batch>, String>>().onError(null)
        }

        viewModel.completeBatch(null)
        viewModel.completeBatch(callbackMock)

        verify(exactly = 2) { dataService.completeBatch(any(), any()) }
        verify(exactly = 1) { callbackMock.failure(any()) }
    }

    @Test
    fun isAllBatchItemShipmentCompletedQA() {
        viewModel.shipments.clear()
        // when total view count is empty should return false
        viewModel.isAllBatchItemShipmentCompletedQA() shouldBeEqualTo false

        // when shipment isComplete is true
        val shipment = mockk<Shipment>()

        val batchItems = mutableListOf<BatchItem>()
        val item = mockk<BatchItem>()
        batchItems.add(item)
        every { shipment.items } returns batchItems
        every { item.status } returns ProductStatus.QA_PASSED
        batchItems.size shouldBeEqualTo 1

        viewModel.shipments.add(shipment)


        // when shipment isComplete is true and QABoxItem Complete is true then should be true
        every { shipment.isComplete } returns true
        every { shipment.isQABoxItemComplete() } returns true
        viewModel.isAllBatchItemShipmentCompletedQA() shouldBeEqualTo true

        // when shipment isComplete is false and QABoxItem Complete is true then should be true
        every { shipment.isComplete } returns false
        every { shipment.isQABoxItemComplete() } returns true
        viewModel.isAllBatchItemShipmentCompletedQA() shouldBeEqualTo true

        // when shipment isComplete is true and QABoxItem Complete is false then should be true
        every { shipment.isComplete } returns true
        every { shipment.isQABoxItemComplete() } returns false
        viewModel.isAllBatchItemShipmentCompletedQA() shouldBeEqualTo true

        // when shipment isComplete and is QABox Item Complete is false then should be false
        every { shipment.isComplete } returns false
        every { shipment.isQABoxItemComplete() } returns false
        viewModel.isAllBatchItemShipmentCompletedQA() shouldBeEqualTo false
    }


    @Test
    fun onKeyTapped_isBarcodeSettingOn() {
        every { featureFlagUtil.isBarcodeSettingIsOn() } returns true
        viewModel.onKeyTapped()
        verify(exactly = 1) { viewModel.checkScannedText() }
    }

    @Test
    fun checkScannedText() {
        val batchItem = BatchItem(rfid = MockProvider.getScannedRfidTag())
        val shipment = Shipment(items = listOf(batchItem))
        viewModel.scannedRfidText.set(MockProvider.getScannedRfidTag())
        viewModel.shipments.add(shipment)

        viewModel.checkScannedText()

        verify(exactly = 1) { viewModel.checkBatchItemStatus(any()) }
    }

    @Test
    fun checkScannedText_BatchInQA() {
        val batchItem = MockProvider.batchItem(rfid = MockProvider.getScannedRfidTag())
        val batch = MockProvider.batch(batchItem)
        val shipment = Shipment(items = listOf(batchItem))

        viewModel.batch = batch
        viewModel.scannedRfidText.set(MockProvider.getScannedRfidTag())
        viewModel.shipments.add(shipment)

        viewModel.checkScannedText()

        verify(exactly = 1) { viewModel.checkBatchItemStatus(any()) }
    }

    @Test
    fun checkScannedText_notFound() {
        every { viewModel.scannedRfidText.get() } returns "123123123123"

        viewModel.checkScannedText()

        verify(exactly = 1) { viewModel.setErrorMessage() }
    }

    @Test
    fun checkBatchItemStatus_inCurrentTab() {
        every { viewModel.batch } returns MockProvider.batch()
        every { viewModel.scannedRfidText.get() } returns MockProvider.getScannedRfidTag()

        viewModel.checkBatchItemStatus(0)

        verify(exactly = 1) { eventBus.post(any<QcDetailUIEvent.OnScannedRfidText>()) }
    }

    @Test
    fun scheckBatchItemStatus_notInCurrentTab() {
        every { viewModel.batch } returns MockProvider.batch()
        every { viewModel.scannedRfidText.get() } returns "123123123123"

        viewModel.checkBatchItemStatus(-1)

        verify(exactly = 1) { eventBus.post(any<QcDetailUIEvent.OnScannedRfidTextNotFound>()) }
    }


    @Test
    fun setErrorMessage_inCurrentBatch_butInAnotherTab() {
        every { viewModel.batch } returns MockProvider.batch()
        every { viewModel.scannedRfidText.get() } returns MockProvider.getScannedRfidTag()

        viewModel.setErrorMessage()

        verify(exactly = 1) { eventBus.post(any<QcDetailUIEvent.OnScannedRfidTextNotFound>()) }
    }

    @Test
    fun setErrorMessage_WhenItemIsNull() {
        val batchItem = MockProvider.batchItem(rfid = MockProvider.getScannedRfidTag())
        val batch = MockProvider.batch(batchItem)
        val shipment = Shipment(items = listOf(batchItem))

        viewModel.batch = batch
        viewModel.scannedRfidText.set(MockProvider.getScannedRfidTag())
        viewModel.shipments.add(shipment)

        viewModel.setErrorMessage()

        verify(exactly = 1) { eventBus.post(any<QcDetailUIEvent.OnScannedRfidTextNotFound>()) }
    }

    @Test
    fun setErrorMessage_notInCurrentBatch() {
        every { viewModel.batch } returns MockProvider.batch()
        every { viewModel.scannedRfidText.get() } returns "123123123123"

        viewModel.setErrorMessage()

        verify(exactly = 1) { eventBus.post(any<QcDetailUIEvent.OnScannedRfidTextNotFound>()) }
    }

    @Test
    fun getBatchName() {
        val batch = MockProvider.batch()
        viewModel.batch = batch

        viewModel.batchName() shouldBeEqualTo "Batch A"
    }

    @Test
    fun getToolbar() {
        viewModel.toolbarViewModel() shouldBeEqualTo viewModel.toolbarViewModel
    }

    @Test
    fun cacheFragment_shouldReturnTrue() {
        viewModel.cacheFragment().shouldBeTrue()
    }

    @Test
    fun getPageTitleAtPosition_shouldReturn_EmptyString() {
        val position = 2
        viewModel.getPageTitleAtPosition(position) shouldBeEqualTo context.getString(R.string.box_title).format(position + 1)
    }

    @Test
    fun getItemTypeAtPosition_ShouldReturnQCDetailFragment_WithSpecificRackTitle() {
        val shipment = MockProvider.shipment()
        viewModel.shipments.add(shipment)

        viewModel.getItemTypeAtPosition(0) shouldBeInstanceOf QcDetailItemFragment::class
    }

    @Test
    fun getItemTypeAtPosition_ShouldReturnQCDetailFragment() {
        val shipment: Shipment = mockk()
        every { shipment.items } returns emptyList()
        viewModel.shipments.add(shipment)

        viewModel.getItemTypeAtPosition(0) shouldBeInstanceOf QcDetailItemFragment::class
    }

    @Test
    fun updateStatusCallback_onSuccessBatchItem() {
        viewModel.updateStatusCallback.onSuccess(null)
        verify { eventBus.post(any<QcDetailUIEvent.OnUpdateItemSuccess>()) }
    }

    @Test
    fun updateStatusCallback_onError() {
        viewModel.updateStatusCallback.onError("")
        verify { eventBus.post(any<QcDetailUIEvent.OnErrorSwapItem>()) }
    }

    @Test
    fun updateStatusCallback_onErrorNull() {
        viewModel.updateStatusCallback.onError(null)
        verify { eventBus.post(any<QcDetailUIEvent.OnErrorSwapItem>()) }
    }

    @Test
    fun updateItemToFailed() {
        viewModel.updateItemToFailed()
        verify { dataService.updateBatchItemStatus(any(), any()) }
    }

}