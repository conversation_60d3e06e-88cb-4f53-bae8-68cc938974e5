package co.styletheory.ops.outbound.android.feature.photoDetail

import android.view.View
import co.styletheory.android.network.core.APICallback
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.photoDetail.viewModel.impl.PhotoItemViewModel
import co.styletheory.ops.outbound.android.feature.photoManager.event.TakePictureEvent
import co.styletheory.ops.outbound.android.feature.photoManager.viewModel.AttachImageViewModel
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.general.event.PreviewImageEvent
import co.styletheory.ops.outbound.android.model.enums.ProductStatus
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import org.amshove.kluent.shouldBeEqualTo
import org.amshove.kluent.shouldBeFalse
import org.amshove.kluent.shouldBeTrue
import org.amshove.kluent.shouldNotBeEqualTo
import org.apache.commons.lang3.reflect.FieldUtils
import org.junit.Test

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 18 December 2017.
 * Description
 *
 * <EMAIL>
 */
class PhotoItemViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: PhotoItemViewModel

    private fun setPrivateField(fieldName: String, value: Any?) {
        FieldUtils.writeDeclaredField(viewModel, fieldName, value, true)
    }

    private fun getPrivateField(fieldName: String): Any? {
        return FieldUtils.readDeclaredField(viewModel, fieldName, true)
    }

    @Test
    fun firstState() {
        viewModel.imageUrl.get() shouldBeEqualTo ""
        viewModel.refreshCompleteState shouldBeEqualTo null
        viewModel.itemName.get() shouldBeEqualTo ""
        viewModel.itemSize.get() shouldBeEqualTo ""
        viewModel.rack.get() shouldBeEqualTo ""
        viewModel.category.get() shouldBeEqualTo ""
        viewModel.parts.get() shouldBeEqualTo ""
        viewModel.notes.get() shouldBeEqualTo ""
        viewModel.soldTo.get() shouldBeEqualTo ""
        viewModel.productOrder.get() shouldBeEqualTo ""
        viewModel.qaItemImages.size shouldBeEqualTo 0
        viewModel.colorItems.size shouldBeEqualTo 0

        viewModel.errorMessage.get() shouldBeEqualTo ""
        viewModel.photoDone.get().shouldBeFalse()
        viewModel.showOverlayLoading.get().shouldBeFalse()
    }

    @Test
    fun checkBindViewModel_WithPhotoQaDone() {
        val batchItem = MockProvider.batchItemStyleColor(ProductStatus.PHOTO_QA_DONE)
        val rackName = "A"
        val rackSection = "11"
        val shipmentId = "A1"

        viewModel.bindViewModel(batchItem, rackSection, rackName, shipmentId)

        getPrivateField("batchItem") shouldBeEqualTo batchItem
        getPrivateField("rackSection") shouldBeEqualTo rackSection
        getPrivateField("rackName") shouldBeEqualTo rackName
        getPrivateField("shipmentId") shouldBeEqualTo shipmentId

        viewModel.imageUrl.get() shouldBeEqualTo "Url"
        viewModel.itemName.get() shouldBeEqualTo "Apri"
        viewModel.itemSize.get() shouldBeEqualTo "XS"
        viewModel.rack.get() shouldBeEqualTo context.getString(R.string.rack_name_regular).format(rackName, rackSection)
        viewModel.category.get() shouldBeEqualTo "dress"
        viewModel.parts.get() shouldBeEqualTo "Belt"
        viewModel.notes.get() shouldBeEqualTo "notes"
        viewModel.soldTo.get() shouldBeEqualTo "Vip"
        viewModel.productOrder.get() shouldBeEqualTo "[9]"
        viewModel.qaItemImages.size shouldBeEqualTo 0
        viewModel.colorItems.size shouldNotBeEqualTo 0
        viewModel.photoDone.get().shouldBeTrue()
    }

    @Test
    fun checkBindViewModel_WithPhotoQaNotDone_AndUserIsOnDemandRegionId() {
        val batchItem = MockProvider.batchItemStyleColor(ProductStatus.PHOTO_QA)
        val rackName = "A"
        val rackSection = "11"
        val shipmentId = "A1"

        every { userStorage.isUserOnDemandAndRegionID() } returns true

        viewModel.bindViewModel(batchItem, rackSection, rackName, shipmentId)

        getPrivateField("batchItem") shouldBeEqualTo batchItem
        getPrivateField("rackSection") shouldBeEqualTo rackSection
        getPrivateField("rackName") shouldBeEqualTo rackName
        getPrivateField("shipmentId") shouldBeEqualTo shipmentId

        viewModel.imageUrl.get() shouldBeEqualTo "Url"
        viewModel.itemName.get() shouldBeEqualTo "Apri"
        viewModel.itemSize.get() shouldBeEqualTo "XS"
        viewModel.rack.get() shouldBeEqualTo context.getString(R.string.rack_name_on_demand)
            .format(rackName, rackSection)
        viewModel.category.get() shouldBeEqualTo "dress"
        viewModel.parts.get() shouldBeEqualTo "Belt"
        viewModel.notes.get() shouldBeEqualTo "notes"
        viewModel.soldTo.get() shouldBeEqualTo "Vip"
        viewModel.productOrder.get() shouldBeEqualTo "[9]"
        viewModel.qaItemImages.size shouldBeEqualTo 0
        viewModel.colorItems.size shouldNotBeEqualTo 0
        viewModel.photoDone.get().shouldBeFalse()
    }

    @Test
    fun checkPhotoDoneClick_NotDoneButAttachmentIsEmpty() {
        viewModel.photoDone.set(false)
        viewModel.qaItemImages.clear()
        viewModel.photoDoneClick()

        viewModel.errorMessage.get() shouldBeEqualTo context.getString(R.string.err_empty_attachment_picture)
    }

    @Test
    fun checkPhotoDoneClick_HasDoneButAttachmentIsEmpty() {
        viewModel.photoDone.set(true)
        viewModel.qaItemImages.clear()
        viewModel.photoDoneClick()

        viewModel.errorMessage.get() shouldBeEqualTo ""
    }

    @Test
    fun checkPhotoDoneClick_HasDone() {
        val batchItem = MockProvider.batchItemStyleColor(ProductStatus.PHOTO_QA_DONE)
        val rackName = "A"
        val rackSection = "11"
        val shipmentId = "A1"

        viewModel.bindViewModel(batchItem, rackSection, rackName, shipmentId)
        viewModel.photoDoneClick()

        viewModel.errorMessage.get() shouldBeEqualTo ""
    }

    @Test
    fun checkPhotoDoneClick_UpdateBatchStatusSuccess() {
        val batchItem = MockProvider.batchItemStyleColor(ProductStatus.PHOTO_QA)
        val rackName = "A"
        val rackSection = "11"
        val shipmentId = "A1"

        every { dataService.updateBatchItemStatus(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Void>, String>>().onSuccess(null)
        }

        viewModel.bindViewModel(batchItem, rackSection, rackName, shipmentId)
        viewModel.photoDoneClick()

        viewModel.errorMessage.get() shouldBeEqualTo ""
        viewModel.photoDone.get().shouldBeFalse()
        viewModel.showOverlayLoading.get().shouldBeFalse()
    }

    @Test
    fun checkPhotoDoneClick_UpdateBatchStatusSuccess_WithAttachment() {
        val batchItem = MockProvider.batchItemStyleColor(ProductStatus.PHOTO_QA)
        val rackName = "A"
        val rackSection = "11"
        val shipmentId = "A1"

        val photo = mockk<AttachImageViewModel>()
        mockkConstructor(photo::class)
        every { photo.imageUrl } returns mockk()
        every { anyConstructed<AttachImageViewModel>().imageUrl.set(any()) } returns mockk()

        every { dataService.updateBatchItemStatus(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Void>, String>>().onSuccess(null)
        }

        viewModel.bindViewModel(batchItem, rackSection, rackName, shipmentId)
        viewModel.qaItemImages.add(photo)
        viewModel.photoDoneClick()

        viewModel.errorMessage.get() shouldBeEqualTo ""
        viewModel.photoDone.get().shouldBeTrue()
        viewModel.showOverlayLoading.get().shouldBeFalse()
    }

    @Test
    fun checkPhotoDoneClick_UpdateBatchStatusError() {
        val batchItem = MockProvider.batchItemStyleColor(ProductStatus.PHOTO_QA)
        val rackName = "A"
        val rackSection = "11"
        val shipmentId = "A1"

        every { dataService.updateBatchItemStatus(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Void>, String>>().onError(null)
        }

        viewModel.bindViewModel(batchItem, rackSection, rackName, shipmentId)
        viewModel.photoDoneClick()

        viewModel.errorMessage.get() shouldBeEqualTo context.getString(R.string.err_failed_to_update_status)
        viewModel.photoDone.get().shouldBeFalse()
        viewModel.showOverlayLoading.get().shouldBeFalse()
    }

    @Test
    fun checkPhotoDoneClick_UpdateBatchStatusErrorNull() {
        viewModel = spyk(viewModel)
        every { viewModel.canDoPhotoDone() } returns true
        every { errorResponse.getErrorBodyDescription(any()) } returns "errorMessage"
        every { dataService.updateBatchItemStatus(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<Void>, String>>().onError("errorMessage")
        }

        viewModel.photoDoneClick()

        viewModel.errorMessage.get() shouldBeEqualTo "errorMessage"
        viewModel.photoDone.get().shouldBeFalse()
        viewModel.showOverlayLoading.get().shouldBeFalse()
    }

    @Test
    fun addQAImages() {
        val photo = mockk<AttachImageViewModel>()
        mockkConstructor(photo::class)
        every { photo.imageUrl } returns mockk()
        every { anyConstructed<AttachImageViewModel>().imageUrl.set(any()) } returns mockk()

        viewModel.addQAImages(listOf("image"))

        viewModel.qaItemImages.size shouldNotBeEqualTo 0
    }

    @Test
    fun clickImage() {
        viewModel.imageClick()
        verify(exactly = 1) { eventBus.post(any<PreviewImageEvent>()) }
    }

    @Test
    fun cameraClick() {
        viewModel.cameraClick()
        verify(exactly = 1) { eventBus.post(any<TakePictureEvent>()) }
    }

    @Test
    fun setBatchId() {
        viewModel.setBatchId("Batch#1")
        getPrivateField("batchId") shouldBeEqualTo "Batch#1"
    }

    @Test
    fun setupRackName_isUserOnDemand() {
        val batchItem = MockProvider.batchItem(ProductStatus.PICKING, null)
        val rack = MockProvider.rack().copy(name = "M1")
        val batch = MockProvider.batch(batchItem).copy(rack = rack)
        val rackSection = "A"

        every { userStorage.isUserOnDemandAndRegionID() } returns true

        viewModel.setupRackName(batch.rack?.name, rackSection)
        viewModel.rack.get() shouldBeEqualTo context.getString(R.string.rack_name_on_demand)
            .format(rack.name, rackSection)
    }

    @Test
    fun setupRackName_isUserNotOnDemand() {
        val rackName = "A"
        val rackSection = "11"

        every { userStorage.isUserOnDemandAndRegionID() } returns false

        viewModel.setupRackName(rackName, rackSection)
        viewModel.rack.get() shouldBeEqualTo context.getString(R.string.rack_name_regular).format(rackName, rackSection)
    }

    @Test
    fun setupPurchasedItem_haveResellingItem() {
        val purchasedBy = "<NAME_EMAIL>"
        val batchItem = MockProvider.batchItem(ProductStatus.PACKING).copy(label = purchasedBy)

        viewModel.setupPurchasedItem(batchItem)

        viewModel.itemPurchasedVisibility.get() shouldBeEqualTo View.VISIBLE
        viewModel.soldTo.get() shouldBeEqualTo purchasedBy
    }

    @Test
    fun setupPurchasedItem_labelIsEmpty() {
        val batchItem = MockProvider.batchItem(ProductStatus.PACKING).copy(label = "")
        viewModel.setupPurchasedItem(batchItem)
        viewModel.itemPurchasedVisibility.get() shouldBeEqualTo View.GONE
        viewModel.soldTo.get() shouldBeEqualTo ""
    }

    @Test
    fun setupPurchasedItem_labelIsNull() {
        val batchItem = MockProvider.batchItem(ProductStatus.PACKING).copy(label = null)
        viewModel.setupPurchasedItem(batchItem)
        viewModel.itemPurchasedVisibility.get() shouldBeEqualTo View.GONE
        viewModel.soldTo.get() shouldBeEqualTo ""
    }

}