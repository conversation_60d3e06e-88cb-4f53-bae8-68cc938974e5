package co.styletheory.ops.outbound.android.feature.packed

import android.view.View
import co.styletheory.android.network.core.APICallback
import co.styletheory.android.network.resource.graphql.GraphQLData
import co.styletheory.android.network.resource.graphql.GraphQLResult
import co.styletheory.ops.outbound.android.BaseKotlinTest
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.feature.goSend.event.GojekOrderClickedEvent
import co.styletheory.ops.outbound.android.feature.packed.event.SelectAllShipmetEvent
import co.styletheory.ops.outbound.android.feature.packed.event.ShipmentDateClickEvent
import co.styletheory.ops.outbound.android.feature.packed.viewModel.impl.PackedViewModelImpl
import co.styletheory.ops.outbound.android.general.MockProvider
import co.styletheory.ops.outbound.android.model.LogisticProvider
import co.styletheory.ops.outbound.android.util.Result
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import org.amshove.kluent.shouldBeEmpty
import org.amshove.kluent.shouldBeEqualTo
import org.amshove.kluent.shouldBeFalse
import org.amshove.kluent.shouldBeTrue
import org.junit.Test

/**
 * styletheory-ops-outbound-android
 * Created by dwiaprianto on 19 February 2018.
 * Description
 *
 * <EMAIL>
 */
class PackedViewModelTest : BaseKotlinTest() {

    @InjectMockKs
    lateinit var viewModel: PackedViewModelImpl

    override fun setup() {
        super.setup()
        every { context.getString(R.string.send) } returns "Send"
    }

    @Test
    fun firstState() {
        viewModel.tabPosition shouldBeEqualTo 0
        viewModel.logisticItems.shouldBeEmpty()
        viewModel.rightButtonText.isEmpty().shouldBeTrue()
    }

    @Test
    fun checkSetTabPosition() {
        viewModel.tabPosition shouldBeEqualTo 0
        viewModel.tabPosition = 1
        viewModel.tabPosition shouldBeEqualTo 1
    }

    @Test
    fun checkGetTitleTabAtIndex() {
        val logisticProvider = LogisticProvider(id = "SingPost", name = "SingPost")
        viewModel.logisticItems.add(logisticProvider)
        viewModel.getTitleTabAtIndex(0) shouldBeEqualTo "SingPost"
    }

    @Test
    fun checkFetchLogisticProvider_whenSuccess_WithDataNotNull() {
        viewModel = spyk(viewModel)
        val callback = mockk<Result<Void?, String?>>(relaxed = true)

        every { dataService.fetchLogisticProvider(any(), any()) } answers {
            val data = MockProvider.logisticProvider()
            val graphQLData = GraphQLData(GraphQLResult(listOf(data)), emptyList())
            secondArg<APICallback<GraphQLData<List<LogisticProvider>>, String>>().onSuccess(graphQLData)
        }

        viewModel.fetchLogisticProviders(callback)

        verify(exactly = 1) {
            viewModel.addLogisticProvider(any())
            dataService.fetchLogisticProvider(any(), any())
            callback.success(null)
        }
    }


    @Test
    fun checkFetchLogisticProvider_whenSuccess_WithDataNull() {
        viewModel = spyk(viewModel)
        val callback = mockk<Result<Void?, String?>>(relaxed = true)

        every { dataService.fetchLogisticProvider(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<List<LogisticProvider>>, String>>().onSuccess(null)
        }

        viewModel.fetchLogisticProviders(callback)

        verify(inverse = true) { viewModel.addLogisticProvider(any()) }
        verify(exactly = 1) {
            dataService.fetchLogisticProvider(any(), any())
            callback.success(null)
        }
    }


    @Test
    fun checkFetchLogisticProvider_whenFailure() {
        viewModel = spyk(viewModel)
        val callback = mockk<Result<Void?, String?>>(relaxed = true)

        every { dataService.fetchLogisticProvider(any(), any()) } answers {
            secondArg<APICallback<GraphQLData<List<LogisticProvider>>, String>>().onError("error")
        }

        viewModel.fetchLogisticProviders(callback)

        verify(exactly = 1) {
            dataService.fetchLogisticProvider(any(), any())
            callback.failure(errorResponse.getErrorBodyDescription("error"))
        }
    }

    @Test
    fun showGojekActionButtons() {

        viewModel.showGojekActionButtons(true, true)
        viewModel.gojekToBeOrderedActionVisibility.get() shouldBeEqualTo View.GONE

        viewModel.showGojekActionButtons(false, true)
        viewModel.gojekToBeOrderedActionVisibility.get() shouldBeEqualTo View.GONE

        viewModel.showGojekActionButtons(true, false)
        viewModel.gojekToBeOrderedActionVisibility.get() shouldBeEqualTo View.VISIBLE

        viewModel.showGojekActionButtons(false, false)
        viewModel.gojekToBeOrderedActionVisibility.get() shouldBeEqualTo View.GONE
    }

    @Test
    fun checkAddLogisticProvider() {
        val logisticProvider = LogisticProvider(id = "singpost", region = "sg")
        every { userStorage.getUserRegion().id } returns ("sg")

        viewModel.addLogisticProvider(listOf(logisticProvider))
        viewModel.logisticItems.size shouldBeEqualTo 1
    }

    @Test
    fun checkUpdateTotalSelectedPackedShipment() {
        viewModel.rightButtonText.get().shouldBeEmpty()
        viewModel.updateTotalSelectedPackedShipment(0)
        viewModel.rightButtonText.get() shouldBeEqualTo "Send"
        viewModel.updateTotalSelectedPackedShipment(2)
        viewModel.rightButtonText.get() shouldBeEqualTo "Send (2)"

    }

    @Test
    fun checkOnSelectAllButtonClicked() {
        viewModel.onSelectAllClicked()
        verify(inverse = true) { eventBus.post(any<SelectAllShipmetEvent>()) }

        viewModel.logisticItems.add(LogisticProvider())
        viewModel.onSelectAllClicked()
        verify(exactly = 1) { eventBus.post(any<SelectAllShipmetEvent>()) }
    }

    @Test
    fun shipmentDateClicked() {
        viewModel.shipmentDateClicked()
        verify(exactly = 1) { eventBus.post(any<ShipmentDateClickEvent>()) }
    }

    @Test
    fun onSelectAllClicked() {
        viewModel.logisticItems.add(MockProvider.logisticProvider())
        viewModel.logisticItems.add(MockProvider.logisticProvider().copy(id = "Gocoy"))
        viewModel.tabPosition = 3

        val arg = slot<SelectAllShipmetEvent>()
        every { eventBus.post(capture(arg)) } just Runs


        viewModel.onSelectAllClicked()
        verify(inverse = true) { eventBus.post(any<SelectAllShipmetEvent>()) }

        viewModel.tabPosition = 0
        viewModel.onSelectAllClicked()
        arg.captured.logisticProvider shouldBeEqualTo viewModel.logisticItems[0].id
        verify(exactly = 1) { eventBus.post(any<SelectAllShipmetEvent>()) }
    }

    @Test
    fun orderGosendClicked() {
        viewModel.logisticItems.add(MockProvider.logisticProvider())
        viewModel.logisticItems.add(MockProvider.logisticProvider().copy(id = "Gocoy"))
        viewModel.tabPosition = 3

        val arg = slot<GojekOrderClickedEvent>()
        every { eventBus.post(capture(arg)) } just Runs

        viewModel.onOrderGosendClicked()
        verify(inverse = true) { eventBus.post(any<GojekOrderClickedEvent>()) }

        viewModel.tabPosition = 0
        viewModel.onOrderGosendClicked()
        arg.captured.logisticProvider shouldBeEqualTo viewModel.logisticItems[0]
        verify(exactly = 1) { eventBus.post(any<GojekOrderClickedEvent>()) }
    }

    @Test
    fun checkIsGojek() {
        viewModel.logisticItems.add(MockProvider.logisticProvider().copy(id = "Gojek"))
        viewModel.logisticItems.add(MockProvider.logisticProvider().copy(id = "Gocoy"))

        viewModel.isGoJek(0).shouldBeTrue()
        viewModel.isGoJek(1).shouldBeFalse()
    }
}