import groovy.time.TimeCategory

apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.appdistribution'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'kotlin-parcelize'
// Deprecated
apply from: '../jacoco.gradle'
apply from: '../sonarqube.gradle'
apply from: '../env.gradle'
apply from: '../versioning.gradle'

android {
    namespace 'co.styletheory.outbound.ops.android'
    buildToolsVersion = '35.0.0'
    compileSdk = 35
    defaultConfig {
        applicationId "co.styletheory.outbound.ops.android"
        minSdk 24
        targetSdk = 35
        versionCode buildVersionCode()
        versionName buildVersionName()
        multiDexEnabled true
        testInstrumentationRunner "co.styletheory.ops.android.runner.OPSInstrumentMockRunner"
    }
    buildTypes {
        apiary {
            applicationIdSuffix ".apiary"
            debuggable true
            signingConfig signingConfigs.debug
            buildConfigField "String", "BASE_URL", '"https://private-561b97-styletheoryv2.apiary-mock.com/"'
            firebaseAppDistribution {
                releaseNotesFile = "$project.projectDir/src/apiary/release_notes.txt"
                groups = "engineering-team, qa"
            }
        }
        debug {
            applicationIdSuffix ".debug"
            debuggable true
            signingConfig signingConfigs.debug
            buildConfigField "String", "BASE_URL", '"https://graphql.dev.devops.my.id/graphql/"'
            firebaseAppDistribution {
                releaseNotesFile = "$project.projectDir/src/debug/release_notes.txt"
                groups = "engineering-team, qa, product, data"
            }
            enableUnitTestCoverage true
            enableAndroidTestCoverage true
        }
        staging {
            applicationIdSuffix ".staging"
            debuggable true
            signingConfig signingConfigs.debug
            buildConfigField "String", "BASE_URL", '"https://graphql.staging.devops.my.id/graphql/"'
            firebaseAppDistribution {
                releaseNotesFile = "$project.projectDir/src/staging/release_notes.txt"
                groups = "engineering-team, product, qa"
            }
        }
        release {
            minifyEnabled false
            debuggable false
            signingConfig signingConfigs.debug
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            buildConfigField "String", "BASE_URL", '"https://graphql.styletheory.co/graphql/"'
            firebaseAppDistribution {
                releaseNotesFile = "$project.projectDir/src/release/release_notes.txt"
                groups = "engineering-team, operation-id, operation-sg, operation-hk, product, qa, external-user"
            }
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17.toString()
    }
    java {
        toolchain {
            languageVersion = JavaLanguageVersion.of(17)
        }
    }
    configurations.configureEach {
        exclude group: 'com.google.guava', module: 'listenablefuture'
        resolutionStrategy.force 'com.google.code.findbugs:jsr305:2.0.1'
        resolutionStrategy.force 'com.google.guava:guava:33.4.0-android'
        resolutionStrategy.force 'com.google.errorprone:error_prone_annotations:2.18.0'
    }
    buildFeatures {
        dataBinding true
        viewBinding true
        buildConfig true
    }
    sourceSets { debug { assets.srcDirs = ['src/debug/assets', 'src/androidTest/assets'] } }
    packagingOptions {
        resources {
            excludes += ['AndroidManifest.xml', 'res/values/values.xml']
        }
    }
    testOptions {
        unitTests.all {
            testLogging {
                outputs.upToDateWhen { false }
                events "failed", "standardError"
                exceptionFormat = "full"
                showCauses true
                showExceptions true
                afterSuite { desc, result ->
                    if (!desc.parent) { // will match the outermost suite
                        def startDate = new Date(result.startTime)
                        def endDate = new Date(result.endTime)
                        use(TimeCategory) {
                            def duration = endDate - startDate
                            println "Unit testing duration -> Hours: ${duration.hours}," +
                                    " Minutes: ${duration.minutes}," +
                                    " Seconds: ${duration.seconds}."
                        }
                        println "Results: ${result.resultType} (${result.testCount} tests, ${result.successfulTestCount} successes, ${result.failedTestCount} failures, ${result.skippedTestCount} skipped)"
                    }
                }
            }
            useJUnitPlatform()
        }
    }
    bundle {
        language {
            enableSplit true
        }
        density {
            enableSplit true
        }
        abi {
            enableSplit true
        }
    }
    namespace 'co.styletheory.ops.outbound.android'
    lint {
        abortOnError false
        checkReleaseBuilds false
        quiet true
    }
}

dependencies {
    implementation fileTree(include: ['*.aar', '*.jar'], dir: 'libs')
    implementation 'com.google.guava:guava:33.4.0-android'
    //Core
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
    implementation 'androidx.annotation:annotation:1.9.1'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'

    implementation 'com.google.android.gms:play-services-gcm:17.0.0'
    implementation 'com.google.android.material:material:1.12.0'
    // Firebase BoM
    implementation platform('com.google.firebase:firebase-bom:32.7.4')

    // Firebase dependencies (no version needed with BoM)
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-crashlytics-ktx'

    // Kotlin
    implementation "org.jetbrains.kotlin:kotlin-reflect:$kotlinVersion"
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlinVersion"

    implementation 'com.gu.android:toolargetool:0.3.0@aar'

    // AndroidX Lifecycle dependencies
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-common:$lifecycle_version"

    // Dependency Injection
    implementation "com.google.dagger:dagger:$daggerVersion"
    kapt "com.google.dagger:dagger-compiler:$daggerVersion"
    implementation "com.google.dagger:dagger-android:$daggerVersion"
    kapt "com.google.dagger:dagger-android-processor:$daggerVersion"
    implementation 'javax.inject:javax.inject:1'
    implementation 'javax.annotation:javax.annotation-api:1.3.2'

    //Parcel
    implementation "org.parceler:parceler-api:$parcelerVersion"
    kapt "org.parceler:parceler:$parcelerVersion"

    //View
    implementation 'com.malinskiy:superrecyclerview:1.1.4'
    implementation 'com.nex3z:flow-layout:1.3.0'
    implementation 'com.makeramen:roundedimageview:2.3.0'
    implementation 'com.github.okaybroda:ImageZoom:1.1.0'
    implementation 'com.github.skydoves:powermenu:2.2.0'
    implementation 'com.github.savvyapps:ToggleButtonLayout:1.2.0'
    implementation 'de.hdodenhof:circleimageview:3.1.0'
    implementation 'com.github.chrisbanes:PhotoView:2.3.0'
    implementation 'com.github.viethoa:fastscroller:1.2.0'
    implementation 'com.github.jetradarmobile:android-snowfall:1.2.0'

    //Utility
    implementation 'joda-time:joda-time:2.12.7'
    implementation 'org.greenrobot:eventbus:3.3.1'
    implementation 'com.orhanobut:hawk:2.0.1'
    implementation 'pub.devrel:easypermissions:3.0.0'
    implementation 'com.github.nguyenhoanglam:ImagePicker:1.4.3'
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    implementation 'com.facebook.shimmer:shimmer:0.5.0'
    implementation 'com.jakewharton.timber:timber:5.0.1'
    implementation 'com.jakewharton:butterknife:10.2.3'
    kapt 'com.jakewharton:butterknife-compiler:10.2.3'

    implementation('com.github.jainsahab:Snooper:1.5.6@aar', {
        transitive = true
    })
    implementation('com.github.jainsahab:Snooper-Okhttp:1.5.6@aar', {
        transitive = true
    })

    implementation('styletheory.contributor.package:style-camera:1.4.0@aar', {
        exclude group: 'com.jakewharton:butterknife'
        transitive = true
    })
    implementation('styletheory.contributor.package:style-network:1.2.0@aar', {
        transitive = true
    })

    //Testing kit
    testRuntimeOnly 'org.junit.vintage:junit-vintage-engine:5.10.2'
    testImplementation('org.mockito:mockito-core:3.12.4')
    testImplementation 'org.slf4j:slf4j-nop:2.0.13'
    testImplementation 'org.apache.commons:commons-lang3:3.14.0'
    testImplementation('com.nhaarman.mockitokotlin2:mockito-kotlin:2.2.0')
//    testImplementation('styletheory.contributor.package:android-unit-test-kit:0.2.0@aar', {
//        exclude group: 'org.mockito:mockito-core'
//        exclude group: 'com.nhaarman.mockitokotlin2:mockito-kotlin'
//        transitive = true
//    })
//    androidTestImplementation('styletheory.contributor.package:android-ui-test-kit:0.1.3@aar', {
//        transitive = true
//    })
//    kaptAndroidTest "com.google.dagger:dagger-compiler:$daggerVersion"
}

//task createMockitoConfig {
//    doLast {
//        def mockMakerFile = new File("$projectDir/src/test/resources/mockito-extensions/org.mockito.plugins.MockMaker")
//        if (System.env.MOCK_MAKER != null) {
//            logger.info("Using MockMaker ${System.env.MOCK_MAKER}")
//            mockMakerFile.parentFile.mkdirs()
//            mockMakerFile.createNewFile()
//            mockMakerFile.write(System.env.MOCK_MAKER)
//        } else {
//            logger.info("Using default MockMaker")
//        }
//    }
//}
//
//task updateSonarkuy(type: GradleBuild) {
//    tasks = ['testDebugUnitTest', 'connectedDebugAndroidTest', 'jacocoTestReport', 'sonarqube']
//}

//tasks.withType(Test) {
//    forkEvery = 100
//    maxParallelForks = Runtime.runtime.availableProcessors().intdiv(2) ?: 1
//}

configurations.all {
    resolutionStrategy.eachDependency { details ->
        def requested = details.requested
        if (requested.group == 'com.jakewharton') {
            details.useVersion "10.2.3"
        }
    }
}