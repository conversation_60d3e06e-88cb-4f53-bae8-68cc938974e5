# Export Android Home

echo "Type 1 For Start, Type 2 for Stop"
# read param

param="$1"

if [ "$param" = "1" ]; then
    #Start the emulator
    $ANDROID_HOME/tools/emulator -avd Nexus_5X_API_24 -netdelay none -netspeed full -port 5555 &
    EMULATOR_PID=$!

    # Wait for Android to finish booting
    WAIT_CMD="$ANDROID_HOME/platform-tools/adb wait-for-device shell getprop init.svc.bootanim"
    until $WAIT_CMD | grep -m 1 stopped; do
      echo "Waiting..."
      sleep 1
    done
elif [ "$param" = "2" ]; then
    # Stop the emulator
    $ANDROID_HOME/platform-tools/adb -s emulator-5555 emu kill
else
   echo "Invalid Command..."
fi
