{"README_schema": "Specifies how to load the GraphQL schema that completion, error highlighting, and documentation is based on in the IDE", "schema": {"request": {"url": "http://dev.graphql.styletheory.co/schema.json", "method": "POST", "README_postIntrospectionQuery": "Whether to POST an introspectionQuery to the url. If the url always returns the schema JSON, set to false and consider using GET", "postIntrospectionQuery": false, "README_options": "See the 'Options' section at https://github.com/then/then-request", "options": {"headers": {"user-agent": "JS GraphQL"}}}}, "README_endpoints": "A list of GraphQL endpoints that can be queried from '.graphql' files in the IDE", "endpoints": [{"name": "Dev ( http://dev.graphql.styletheory.co/graphql )", "url": "http://dev.graphql.styletheory.co/graphql", "options": {"headers": {"user-agent": "JS GraphQL"}}}]}