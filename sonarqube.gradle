apply plugin: 'org.sonarqube'

def fileFilter = [
        '**/R.class',
        '**/R$*.class',
        '**/BuildConfig.*',
        '**/Manifest*.*',
        '**/*Test*.*',
        'android/**/*.*',
        'androidx/**/*.*',
        '**/*Builder.*',
        '**/*_MembersInjector.class', //Dagger2 generated code
        '**/*_MembersInjector*.*', //Dagger2 generated code
        '**/*_*Factory*.*', //Dagger2 generated code
        '**/*Component*.*', //Dagger2 generated code
        '**/*Module*.*', //Dagger2 generated code
        '**/*Parcelable*.*', // Parcel generated code
        '**/BR.class',
        '**/databinding/*.*',

        '**/*Activity*',
        '**/*Fragment*',
        '**/*Application*',
        '**/*Adapter*',
        '**/*Dialog.class',
        '**/*Listener*',
        '**/*Event*',
        '**/general/**',
        '**/injection/*',
        '**/util/**',
        '**/networking/**',
        '**/view/**',
        '**/resources/**',
        '**/model/**',

        'build/**',
        '**/*.png',
        '*.iml',
        '**/*generated*',
        '**.xml',

        //Class excluded from test coverage
        //Because it mustn't be tested
        '**/PackingDetailViewModel.*',
        '**/PhotoDetailViewModel.*',
        '**/QcDetailViewModel.*'
]
sonarqube {
    androidVariant 'debug'
    properties {
        property "sonar.login", "****************************************"
        property "sonar.verbose", true
        property "sonar.host.url", "https://sonarcloud.io"
        property "sonar.organization", "styletheory"
        property "sonar.java.source", "1.8"
        property "sonar.java.target", "1.8"
        property "sonar.projectKey", "styletheory_android_outbound_app"
        property "sonar.projectName", "android_outbound_app"
        property "sonar.projectVersion", android.defaultConfig.versionName
        property "sonar.issuesReport.html.enable", "true"
        property "sonar.issuesReport.console.enable", "true"
        property "sonar.sources", "src/main"
        property "sonar.binaries", "build/intermediates/javac/debug"
        property "sonar.exclusions", "src/androidTest/**,**/res/**,src/test/**,**/AndroidManifest.xml"
        property "sonar.coverage.exclusions", fileFilter.join(',')
        property "sonar.android.lint.report", "build/reports/lint-results-debug.xml"
        property "sonar.tests", "src/test"
        property "sonar.java.test.binaries", "build/tmp/kotlin-classes/debugUnitTest"
        property "sonar.java.coveragePlugin", "jacoco"
        property "sonar.java.binaries", "build/intermediates/javac/debug,build/tmp/kotlin-classes/debug"
        property "sonar.coverage.jacoco.xmlReportPaths", "${project.buildDir}/reports/jacoco/jacocoTestReport/jacocoTestReport.xml"
        property "sonar.junit.reportPaths", "${project.buildDir}/test-results/testDebugUnitTest"
        property "sonar.import_unknown_files", true
        property "sonar.androidLint.reportPaths", "${project.buildDir}/reports/lint-results-debug.xml"

        if (System.env.PULL_REQUEST_ID) {
            property "sonar.pullrequest.key", System.getProperty("PULL_REQUEST_ID")
            property "sonar.pullrequest.branch", System.getProperty("PULL_REQUEST_ID")
            property "sonar.pullrequest.base", System.getProperty("DESTINATION_BRANCH")
            property "sonar.pullrequest.provider", "bitbucketcloud"
            property "sonar.pullrequest.bitbucketcloud.owner", "{34c831dc-8dc1-43cc-b60d-911ea5d5ec5f}"
            property "sonar.pullrequest.bitbucketcloud.repository", "{6ddb0202-1cc6-40dc-ae26-614511f1175c}"
        }
    }
}