def notifyGChat(String buildStatus = 'STARTED') {
    buildStatus = buildStatus ?: 'STARTED'
    def msg = "*OUTBOUND APP TEST RUNNER* : *${buildStatus}* on `${SELECTED_BRANCH}`"
    generateChatCard(buildStatus, msg)
    executeNotifyGoogleChat()
}

def startEmulator() {
    sh 'export ANDROID_HOME=~/;export PATH=${PATH}:${ANDROID_HOME}/toolsexport;PATH=${PATH}:${ANDROID_HOME}/tools/bin;export PATH=${PATH}:${ANDROID_HOME}/platform-tools; sh emulator.script 1'
}

def stopEmulator() {
    sh 'export ANDROID_HOME=~/;export PATH=${PATH}:${ANDROID_HOME}/toolsexport;PATH=${PATH}:${ANDROID_HOME}/tools/bin;export PATH=${PATH}:${ANDROID_HOME}/platform-tools; sh emulator.script 2'
}

node('ubuntu-android-cloud') {
    notifyGChat('STARTED')
    try {
        def selectedBranch = SELECTED_BRANCH.substring(7)
        stage('Checkout') {
            git branch: selectedBranch, url: '*****************:styletheory/styletheory-ops-outbound.git'
            sh 'rm local.properties ||:'
            sh 'echo "sdk.dir=/home/<USER>/Android/SDK" >> local.properties'
            sh 'yes | /home/<USER>/Android/SDK/tools/bin/sdkmanager --licenses'
        }

        stage('Unit Test') {
            sh './gradlew testDebugUnitTest'
        }

        //stage('UI Test') {
        //    startEmulator()
        //    sh './gradlew connectedDebugAndroidTest'
        //    stopEmulator()
        //}

        stage('Lint Test') {
            sh './gradlew app:lintDebug'
        }

        stage('Generate Coverage Report') {
            sh 'rm -R reports/unit-test ||:'
            sh 'mkdir -p reports/unit-test'
            sh 'cp -R app/build/reports/tests/testDebugUnitTest/* reports/unit-test'
            publishHTML([allowMissing: false, alwaysLinkToLastBuild: false, keepAll: true, reportDir: 'reports/unit-test', reportFiles: 'index.html', reportName: 'Affected Test Report', reportTitles: ''])

            //sh 'rm -R reports/ui-test ||:'
            //sh 'mkdir -p reports/ui-test'
            //sh 'cp -R app/build/reports/androidTests/connected/* reports/ui-test'
            //publishHTML([allowMissing: false, alwaysLinkToLastBuild: false, keepAll: false, reportDir: 'reports/ui-test', reportFiles: 'index.html', reportName: 'UI Test Report', reportTitles: ''])

            sh 'rm -R reports/test-results ||:'
            sh 'mkdir -p reports/test-results'
            sh 'cp -R app/build/test-results/testDebugUnitTest/* reports/test-results'
            junit testResults: 'reports/test-results/*xml'

            sh './gradlew jacocoTestReport'
            sh 'rm -R reports/jacoco ||:'
            sh 'mkdir -p reports/jacoco'
            sh 'cp -R app/build/reports/jacoco/jacocoTestReport/* reports/jacoco'
            publishHTML([allowMissing: false, alwaysLinkToLastBuild: false, keepAll: false, reportDir: 'reports/jacoco/html', reportFiles: 'index.html', reportName: 'Jacoco Test Report', reportTitles: ''])
            jacoco classPattern: 'app/build/intermediates/classes/debug, app/build/tmp/kotlin-classes/debug', exclusionPattern: '**/R.class, **/R$*.class, **/BuildConfig.*, **/Manifest*.*, **/*Test*.*, android/**/*.*, androidx/**/*.*, **/*Builder.*, **/*_MembersInjector.class, **/*_MembersInjector*.*, **/*_*Factory*.*, **/*Component*.*,  **/*Module*.*,  **/*Parcelable*.*, **/BR.class, co/styletheory/ops/outbound/android/databinding/*.*', execPattern: '**/**.exec, **/**.ec', sourcePattern: '**/src/main/java, **/src/main/kotlin'
        }

        //stage('Sonarqube') {
        //    sh './gradlew -Dorg.gradle.java.home=/opt/jdk15/jdk-15.0.2 sonarqube -x compileDebugAndroidTestKotlin -x compileDebugAndroidTestJavaWithJavac'
        //}
    } catch (ex) {
        //stopEmulator()
        notifyGChat('FAILED')
        throw ex
    } finally {
        sh './gradlew --stop'
        sh './gradlew clean'
    }
    notifyGChat('SUCCESS')
}

def generateChatCard(String buildStatus, String msg) {
    def iconBuild = ""
    if(buildStatus == 'STARTED')
        iconBuild = "https:\\/\\/cdn2.iconfinder.com\\/data\\/icons\\/navigation-set-arrows-part-two\\/32\\/Double_Loop-512.png"
    else if(buildStatus == 'SUCCESS')
        iconBuild = "https:\\/\\/cdn3.iconfinder.com\\/data\\/icons\\/flat-actions-icons-9\\/792\\/Tick_Mark_Dark-512.png"
    else
        iconBuild = "https:\\/\\/cdn0.iconfinder.com\\/data\\/icons\\/shift-free\\/32\\/Error-512.png"

    contentData = """'{
        "text": "$msg",
        "cards": [
                {
                    "header": {
                        "title": "OUTBOUND APP TEST RUNNER",
                        "subtitle": "$SELECTED_BRANCH",
                        "imageUrl": "https:\\/\\/cdn1.iconfinder.com\\/data\\/icons\\/android-ui\\/154\\/android-accept-512.png",
                        "imageStyle": "IMAGE"
                    },
                    "sections": [
                    {
                        "widgets": [
                            {
                                "keyValue": {
                                    "topLabel": "Status",
                                    "content": "$buildStatus",
                                    "contentMultiline": "true",
                                    "iconUrl": "$iconBuild"
                                    }
                            }
                        ],
                    },
                    {
                        "widgets": [
                            {
                                "keyValue": {
                                    "topLabel": "Number",
                                    "content": "#$env.BUILD_NUMBER",
                                    "contentMultiline": "true",
                                    "button": {
                                        "textButton": {
                                           "text": "VIEW",
                                           "onClick": {
                                               "openLink": {
                                                    "url": "$env.BUILD_URL"
                                               }
                                           }
                                        }
                                    }
                                }
                            },
                            {
                                "keyValue": {
                                    "topLabel": "Pipeline",
                                    "content": "$env.JOB_NAME"
                                }
                            }
                        ]
                    }]
                }]
    }'"""
}

def executeNotifyGoogleChat() {
    script {
        sh """curl -i \\
        -H \"Accept: application/json\" \\
        -H \"Content-Type:application/json\" \\
        -X POST --data $contentData \\
        \"https://chat.googleapis.com/v1/spaces/AAAA95BKJx4/messages?threadKey='$env.JOB_NAME'&key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=jZVohZdtriABEGBzYQIomjxamy670qryKgNWhvx--KE%3D\""""
    }
}