// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext.kotlinVersion = '1.8.20'
    ext.daggerVersion = '2.45'
    ext.parcelerVersion = '1.1.13'
    ext.lifecycle_version = "2.6.2"
    repositories {
        google()
        mavenCentral()
        maven { url "https://plugins.gradle.org/m2/" }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.6.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion"
        classpath "org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:3.2.0"
        classpath 'com.google.firebase:firebase-appdistribution-gradle:5.1.1'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.3'
        classpath 'com.google.gms:google-services:4.4.2'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://www.jitpack.io" }
        maven {
            url "${artifactUrl}"
            credentials {
                username = "${artifactUsername}"
                password = "${artifactPassword}"
            }
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

//task runUnitTests(dependsOn: [':app:testDebugUnitTest']) {
//    description 'Run all unit tests'
//}
//
//task runAndroidTests(dependsOn: [':app:connectedAndroidTest']) {
//    description 'Run all acceptance tests.'
//}