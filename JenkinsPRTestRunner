def notifyGChat(String buildStatus = 'STARTED') {
    buildStatus = buildStatus ?: 'STARTED'
    def msg = "*OUTBOUND APP PR TEST RUNNER* : *${buildStatus}* on `${SELECTED_BRANCH}`"
    generateChatCard(buildStatus, msg)
    executeNotifyGoogleChat()
}

node('ubuntu-android-cloud') {
    notifyGChat('STARTED')
    bitbucketStatusNotify buildState: 'INPROGRESS', repoSlug: 'styletheory-ops-outbound', commitId: COMMIT_HASH
    try {
        stage('Checkout') {
            git branch: SELECTED_BRANCH, url: '*****************:styletheory/styletheory-ops-outbound.git'
            sh 'rm local.properties ||:'
            sh 'echo "sdk.dir=/home/<USER>/Android/SDK" >> local.properties'
            sh 'yes | /home/<USER>/Android/SDK/tools/bin/sdkmanager --licenses'
        }

        stage('Unit Test') {
            sh './gradlew testDebugUnitTest'
        }

        stage('Lint Test') {
            sh './gradlew app:lintDebug'
        }

        stage('Generate Coverage') {
            sh './gradlew jacocoTestReport'
        }

        //stage('Sonarqube') {
        //    sh "./gradlew -Dorg.gradle.java.home=/opt/jdk15/jdk-15.0.2 sonarqube -x compileDebugAndroidTestKotlin -x compileDebugAndroidTestJavaWithJavac -DPULL_REQUEST_ID=$PULL_REQUEST_ID -DSOURCE_BRANCH=$SELECTED_BRANCH -DDESTINATION_BRANCH=$DESTINATION_BRANCH"
        //}
    } catch (ex) {
        notifyGChat('FAILED')
        bitbucketStatusNotify buildState: 'FAILED', repoSlug: 'styletheory-ops-outbound', commitId: COMMIT_HASH
        throw ex
    } finally {
        sh './gradlew --stop'
    }
    notifyGChat('SUCCESS')
    bitbucketStatusNotify buildState: 'SUCCESSFUL', repoSlug: 'styletheory-ops-outbound', commitId: COMMIT_HASH
}

def generateChatCard(String buildStatus, String msg) {
    def iconBuild = ""
    if(buildStatus == 'STARTED')
        iconBuild = "https:\\/\\/cdn2.iconfinder.com\\/data\\/icons\\/navigation-set-arrows-part-two\\/32\\/Double_Loop-512.png"
    else if(buildStatus == 'SUCCESS')
        iconBuild = "https:\\/\\/cdn3.iconfinder.com\\/data\\/icons\\/flat-actions-icons-9\\/792\\/Tick_Mark_Dark-512.png"
    else
        iconBuild = "https:\\/\\/cdn0.iconfinder.com\\/data\\/icons\\/shift-free\\/32\\/Error-512.png"

    contentData = """'{
        "text": "$msg",
        "cards": [
                {
                    "header": {
                        "title": "OUTBOUND APP PR TEST RUNNER",
                        "subtitle": "$SELECTED_BRANCH",
                        "imageUrl": "https:\\/\\/cdn1.iconfinder.com\\/data\\/icons\\/android-ui\\/154\\/android-accept-512.png",
                        "imageStyle": "IMAGE"
                    },
                    "sections": [
                    {
                        "widgets": [
                            {
                                "keyValue": {
                                    "topLabel": "Status",
                                    "content": "$buildStatus",
                                    "contentMultiline": "true",
                                    "iconUrl": "$iconBuild"
                                    }
                            }
                        ],
                    },
                    {
                        "widgets": [
                            {
                                "keyValue": {
                                    "topLabel": "Number",
                                    "content": "#$env.BUILD_NUMBER",
                                    "contentMultiline": "true",
                                    "button": {
                                        "textButton": {
                                           "text": "VIEW",
                                           "onClick": {
                                               "openLink": {
                                                    "url": "$env.BUILD_URL"
                                               }
                                           }
                                        }
                                    }
                                }
                            },
                            {
                                "keyValue": {
                                    "topLabel": "Title",
                                    "content": "$PULL_REQUEST_TITLE",
                                    "contentMultiline": "true",
                                    "button": {
                                        "textButton": {
                                           "text": "VIEW",
                                           "onClick": {
                                               "openLink": {
                                                    "url": "$PULL_REQUEST_URL"
                                               }
                                           }
                                        }
                                    }
                                }
                            },
                            {
                                 "keyValue": {
                                    "topLabel": "Author",
                                    "content": "$PULL_REQUEST_AUTHOR_NAME",
                                    "contentMultiline": "true"
                                 }
                            },
                            {
                                "keyValue": {
                                    "topLabel": "Pipeline",
                                    "content": "$env.JOB_NAME"
                                }
                            }
                        ]
                    }]
                }]
    }'"""
}

def executeNotifyGoogleChat() {
    script {
        sh """curl -i \\
        -H \"Accept: application/json\" \\
        -H \"Content-Type:application/json\" \\
        -X POST --data $contentData \\
        \"https://chat.googleapis.com/v1/spaces/AAAA95BKJx4/messages?threadKey='$env.JOB_NAME'&key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=jZVohZdtriABEGBzYQIomjxamy670qryKgNWhvx--KE%3D\""""
    }
}